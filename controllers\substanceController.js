const Company = require('../models/company').schema;
const EntryModel = require('../models/entry').EntryModel;
const settings = require('../settings');
const sessionUtils = require('../utils/sessionUtils');
const mailController = require('../controllers/mailController');
const mailFormatter = require('../controllers/mailFormatController');
const taxResidencyValidator = require('../validators/taxResidencyValidator');
const financialPeriodValidator = require('../validators/financialPeriodValidator');
const entityDetailsValidator = require('../validators/entityDetailsValidator');
const relevantActivitiesValidator = require('../validators/relevantActivitiesValidator');
const confirmationValidator = require('../validators/confirmationValidator');
const businessValidator = require('../validators/businessValidator');
const supportDetailsValidator = require('../validators/supportDetailsValidator');
const premisesValidator = require('../validators/premisesValidator');
const managerValidator = require('../validators/directorValidator');
const moment = require('moment');
const appInsightsClient = require("applicationinsights").defaultClient;
const { CURRENCIES, COUNTRIES, CIGA_CODES, DEFAULT_CURRENCY } = require('../utils/constants');
const httpConstants = require('http2').constants;
const fs = require('fs');
const path = require('path');

function createBusinessObject(req, activity, version, activityType) {

    // data for business object v4
    let businessObj = {
        evidence_high_risk_ip: (req.body.evidence_high_risk_ip == "Yes"),
        high_risk_ip: (req.body.entity_high_risk == "Yes"),
        management_in_bvi: (req.body.ActivityDirectedinBVI == "Yes"),
        number_of_board_meetings: req.body.NumberOfBoardMeetings,
        number_or_board_meetings_outside_bvi: req.body.AmountOfBoardMeetingsOutsideBVI,
        total_turnover: req.body.TotalTurnOverFinancialPeriod ? req.body.TotalTurnOverFinancialPeriod.split(',').join('') : undefined,
        total_expenditure: req.body.TotalExpenditureIncured ? req.body.TotalExpenditureIncured.split(',').join('') : undefined,
        total_expenditure_bvi: req.body.TotalExpenditureIncurredInBVI ? req.body.TotalExpenditureIncurredInBVI.split(',').join('') : undefined,
        total_employees: req.body.TotalSuitableFTEInBVI ? req.body.TotalSuitableFTEInBVI.split(',').join('') : undefined,
        full_total_employees: req.body.TotalEmployees ? req.body.TotalEmployees.split(',').join('') : undefined,
        total_employees_adequate: req.body.NumberEmployeesAdequate == "Yes",
        income_generating_premises: (req.body.PremisesAppropriateForIncome == "Yes"),
        equipment_in_bvi: (req.body.EquipmentLocatedWithinBVI == "Yes"),
        core_income_generating_outsourced: (req.body.CoreIncomeGeneratingOutsourced == "Yes"),
        outsourced_activity_undertaken_in_BVI: (req.body.OutsourcedActivityundertakenInBVI == "Yes"),
        explanation_outsourced_activity_undertaken_in_BVI: req.body.ExplanationOutsourcedActivityundertakenInBVI,
        demonstrate_monitoring_outsourced_activity: (req.body.LegalEntityDemonstrateMonitoring == "Yes"),
        explanation_demonstrate_monitoring_outsourced_activity: req.body.ExplainMonitoring,
        manage_equity_participations: (req.body.manage_equity_participations == "Yes"),
        compliant_with_statutory_obligations: (req.body.compliant_with_statutory_obligations == "Yes"),
        managers: activity?.managers || [],
        premises: activity?.premises || [],
        evidence_equipment: activity?.evidence_equipment || [],
        outsourcing_evidence: activity?.outsourcing_evidence || [],
        board_meetings: activity?.board_meetings || [],
        employees: activity?.employees || [],
        outsourcing_providers: activity?.outsourcing_providers || [],
        high_risk_ip_evidence: activity?.high_risk_ip_evidence || [],
        tangible_assets_explanation_files: activity?.tangible_assets_explanation_files || [],
        intangible_assets_decisions_files: activity?.intangible_assets_decisions_files || [],
        intangible_assets_nature_files: activity?.intangible_assets_nature_files || [],
        intangible_assets_trading_nature_files: activity?.intangible_assets_trading_nature_files || [],
        other_ciga_business_files: activity?.other_ciga_business_files || [],
        other_ciga_decisions_files: activity?.other_ciga_decisions_files || [],
        other_ciga_evidence_files:activity?.other_ciga_evidence_files || [],
        other_ciga_files: activity?.other_ciga_files || [],
    }

    let businessValues = {}


    if (parseFloat(version) >= 5) {
        if (activityType === settings.substance_business_types.HOLDING) {
            const isManageEquityParticipations = (req.body.manage_equity_participations == "Yes");
            businessValues = {
                gross_income_total: req.body.activityTotalGrossIncome ? req.body.activityTotalGrossIncome.split(',').join('') : undefined,
                gross_income_type: req.body.activityTypeOfGrossIncome,
                total_expenditure: req.body.TotalExpenditureIncured.split(',').join(''),
                total_expenditure_bvi: req.body.totalExpenditureIncurredInBVI.split(',').join(''),
                manage_equity_participations: isManageEquityParticipations,
                full_total_employees: isManageEquityParticipations ? req.body.TotalEmployees.split(',').join('') : undefined,
                total_employees: isManageEquityParticipations ? req.body.TotalSuitableFTEInBVI.split(',').join('') : undefined,
                total_employees_engaged: isManageEquityParticipations ? req.body.TotalEmployeesEngaged.split(',').join('') : undefined,
                compliant_with_statutory_obligations: !isManageEquityParticipations ?
                    (req.body.compliant_with_statutory_obligations === "Yes") : null
            }
        }else{

            const generalBusinessValues = {
                gross_income_total: req.body.activityTotalGrossIncome.split(',').join(''),
                gross_income_type: req.body.activityTypeOfGrossIncome,
                activity_assets_amount: req.body.activityAssetsAmount ? req.body.activityAssetsAmount.split(',').join('') : undefined,
                activity_assets_type: req.body.activityAssetsType,
                activity_netbook_values: req.body.activityNetBookValues ? req.body.activityNetBookValues.split(',').join('') : undefined,
                total_expenditure: req.body.TotalExpenditureIncured.split(',').join(''),
                total_expenditure_bvi: req.body.totalExpenditureIncurredInBVI.split(',').join(''),
                full_total_employees: req.body.TotalEmployees.split(',').join(''),
                total_employees: req.body.TotalSuitableFTEInBVI.split(',').join(''),
                total_employees_engaged: req.body.TotalEmployeesEngaged.split(',').join(''),
                number_of_board_meetings: req.body.NumberOfBoardMeetings,
                number_of_board_meetings_in_bvi: req.body.AmountOfBoardMeetingsInBVI,
                are_minutes_for_board_meetings: (req.body.ActivityAreMinutesForBoardMeetings == "Yes"),
                quorum_of_board_meetings: req.body.quorumOfBoardMeetings.split(',').join(''),
                are_quorum_of_directors: (req.body.areQuorumOfDirectors == "Yes"),
                activity_ciga_core: req.body.activityCIGACore,
                activity_ciga_core_other: req.body.activityCIGACore === "0.2" ? req.body.activityCIGACoreOther : "",
                core_income_generating_outsourced: req.body.CoreIncomeGeneratingOutsourced === "Yes" ? true :
                    req.body.CoreIncomeGeneratingOutsourced === "No" ? false : undefined,
                outsourcing_total_expenditure: req.body.outsourcingTotalExpenditure ? req.body.outsourcingTotalExpenditure.split(',').join('') : undefined,
            }

            if (activityType === settings.substance_business_types.INTELLECTUAL_PROPERTY) {
                const isHighRiskEntity = req.body.isHighRiskIntellectualProperty === "Yes" ? true :
                    req.body.isHighRiskIntellectualProperty === 'No' ? false : undefined;
                const hasHighRiskEvidence = isHighRiskEntity ? (req.body.providedPresumptionEvidences === "Yes" ? true : 
                    req.body.providedPresumptionEvidences === "No" ? false : undefined) : undefined;
                const isOtherCigaLegalEntity = isHighRiskEntity === false ? (req.body.otherCigaLegalEntityConductYesNo === "Yes" ? true : 
                    req.body.otherCigaLegalEntityConductYesNo === "No" ? false : undefined) : null;
                const hasOtherCigaEvidences = isOtherCigaLegalEntity ? 
                    (req.body.otherCigaProvideEvidencesYesNo === "Yes" ? true : req.body.otherCigaProvideEvidencesYesNo === "No" ? false : undefined) : null;
                
                businessValues = {
                    ...generalBusinessValues,
                    high_risk_ip: isHighRiskEntity,
                    evidence_high_risk_ip: hasHighRiskEvidence,
                    is_other_ciga_legal_entity: isOtherCigaLegalEntity,
                    has_other_ciga_evidences: hasOtherCigaEvidences,
                    equipment_nature_description: req.body.equipmentNatureDescription || "",
                }

                if (isHighRiskEntity === true){
                    const highRiskTrueValues = {
                        high_risk_gross_income_total:  req.body.highRiskGrossIncome.split(',').join(''),
                        high_risk_gross_income_assets: req.body.highRiskGrossIncomeAssets.split(',').join('') ,
                        high_risk_gross_income_others: req.body.highRiskGrossIncomeOthers.split(',').join(''),
                        tangible_assets_name: req.body.tangibleAssetsName,
                        tangible_assets_explanation: req.body.tangibleAssetsExplanation,
                        intangible_assets_decisions: req.body.intangibleAssetsDecisions,
                        intangible_assets_nature: req.body.intangibleAssetsNature,
                        intangible_assets_trading_nature: req.body.intangibleAssetsTradingNature.split(',').join(''),
                        high_risk_ip_evidence: hasHighRiskEvidence ? businessObj.high_risk_ip_evidence : [],
                        other_ciga_business_files:  [],
                        other_ciga_decisions_files:  [],
                        other_ciga_evidence_files: [],
                        other_ciga_files:[],
                        activity_ciga_core: "",
                        activity_ciga_core_other: "",
                    }
                    businessValues = {
                        ...businessValues,
                        ...highRiskTrueValues
                    }
                }

                if (isHighRiskEntity === false && isOtherCigaLegalEntity === true){
                    const otherCigaTrueValues  = {
                        high_risk_ip_evidence: [],
                        tangible_assets_explanation_files:  [],
                        intangible_assets_decisions_files:  [],
                        intangible_assets_nature_files: [],
                        intangible_assets_trading_nature_files:  [],
                        other_ciga_ip_asset: req.body.otherCigaIPAsset,
                        other_ciga_business_details: req.body.otherCigaDetailBusiness,
                        other_ciga_decisions: req.body.otherCigaDecisions,
                        other_ciga_evidence_details: req.body.otherCigaEvidenceDetails,
                        other_ciga_files: businessObj.other_ciga_files,
                        activity_ciga_core: "",
                        activity_ciga_core_other: "",
                    }
                    businessValues = {
                        ...businessValues,
                        ...otherCigaTrueValues
                    }
                }

                if (isHighRiskEntity === false && isOtherCigaLegalEntity === false){
                    const otherCigaNoValues = {
                        high_risk_ip_evidence: [],
                        tangible_assets_explanation_files: [],
                        intangible_assets_decisions_files: [],
                        intangible_assets_nature_files: [],
                        intangible_assets_trading_nature_files: [],
                        other_ciga_business_files:  [],
                        other_ciga_decisions_files: [],
                        other_ciga_evidence_files: [],
                        other_ciga_files:  [],
                        
                    }
                    businessValues = {
                        ...businessValues,
                        ...otherCigaNoValues
                    }
                }
            }
            else {
                businessValues = generalBusinessValues;
            }
        }

    }

    businessObj = {...businessObj, ...businessValues};
    return businessObj;
}

function GoNextRelevantActivity(entry, currentActivity, res)
{
        //go to first relevant activity
        if ( currentActivity === '' && entry.relevant_activities.holding_business.selected)
        {
            res.redirect('/substance/entry/'+entry.id+'/holding-business');
        } else if ( (currentActivity === '' || currentActivity == 'holding-business' ) &&
                    entry.relevant_activities.finance_leasing_business.selected) {
            res.redirect('/substance/entry/'+entry.id+'/finance-leasing-business');
        } else if ( (currentActivity === '' || ['holding-business','finance-leasing-business'].indexOf(currentActivity) >= 0 ) &&
                    entry.relevant_activities.banking_business.selected) {
            res.redirect('/substance/entry/'+entry.id+'/banking-business');
        } else if ( (currentActivity === '' || ['holding-business','finance-leasing-business','banking-business'].indexOf(currentActivity) >= 0) &&
                    entry.relevant_activities.insurance_business.selected) {
            res.redirect('/substance/entry/'+entry.id+'/insurance-business');
        } else if ( (currentActivity === '' || ['holding-business','finance-leasing-business','banking-business','insurance-business'].indexOf(currentActivity) >= 0) &&
                    entry.relevant_activities.fund_management_business.selected) {
            res.redirect('/substance/entry/'+entry.id+'/fund-management-business');
        } else if ( (currentActivity === '' || ['holding-business','finance-leasing-business','banking-business','insurance-business','fund-management-business'].indexOf(currentActivity) >= 0) &&
                    entry.relevant_activities.headquarters_business.selected) {
            res.redirect('/substance/entry/'+entry.id+'/headquarters-business');
        } else if ( (currentActivity === '' || ['holding-business','finance-leasing-business','banking-business','insurance-business','fund-management-business', 'headquarters-business'].indexOf(currentActivity) >= 0) &&
                    entry.relevant_activities.shipping_business.selected) {
            res.redirect('/substance/entry/'+entry.id+'/shipping-business');
        } else if ( (currentActivity === '' || ['holding-business','finance-leasing-business','banking-business','insurance-business','fund-management-business', 'headquarters-business','shipping-business'].indexOf(currentActivity) >= 0) &&
                    entry.relevant_activities.intellectual_property_business.selected) {
            res.redirect('/substance/entry/'+entry.id+'/intellectual-property-business');
        } else if ( (currentActivity === '' || ['holding-business','finance-leasing-business','banking-business','insurance-business','fund-management-business', 'headquarters-business','shipping-business','intellectual-property-business'].indexOf(currentActivity) >= 0) &&
                    entry.relevant_activities.service_centre_business.selected) {
            res.redirect('/substance/entry/'+entry.id+'/service-centre-business');
        } else {
            res.redirect('/substance/entry/'+entry.id+'/supporting-details');
        }
}

function GoPreviousRelevantActivity(entry, currentActivity, res)
{
        //go to first relevant activity
        if (currentActivity === '' && entry.relevant_activities.service_centre_business.selected) {
            res.redirect('/substance/entry/'+entry.id+'/service-centre-business');
        } else if ((currentActivity === '' || ['service-centre-business'].indexOf(currentActivity) >= 0) &&
            entry.relevant_activities.intellectual_property_business.selected) {
                res.redirect('/substance/entry/'+entry.id+'/intellectual-property-business');
        } else if ((currentActivity === '' || ['service-centre-business','intellectual-property-business'].indexOf(currentActivity) >= 0) &&
            entry.relevant_activities.shipping_business.selected) {
                res.redirect('/substance/entry/'+entry.id+'/shipping-business');
        }  else if ((currentActivity === '' || ['service-centre-business','intellectual-property-business','shipping-business'].indexOf(currentActivity) >= 0) &&
            entry.relevant_activities.headquarters_business.selected) {
                res.redirect('/substance/entry/'+entry.id+'/headquarters-business');
        } else if ((currentActivity === '' || ['service-centre-business', 'intellectual-property-business', 'shipping-business', 'headquarters-business'].indexOf(currentActivity) >= 0) &&
            entry.relevant_activities.fund_management_business.selected) {
                res.redirect('/substance/entry/'+entry.id+'/fund-management-business');
        } else if ((currentActivity === '' || ['service-centre-business','intellectual-property-business', 'shipping-business', 'headquarters-business', 'fund-management-business'].indexOf(currentActivity) >= 0) &&
            entry.relevant_activities.insurance_business.selected) {
                res.redirect('/substance/entry/'+entry.id+'/insurance-business');
        } else if ((currentActivity === '' || ['service-centre-business', 'intellectual-property-business', 'shipping-business', 'headquarters-business', 'fund-management-business', 'insurance-business'].indexOf(currentActivity) >= 0) &&
            entry.relevant_activities.banking_business.selected) {
                res.redirect('/substance/entry/'+entry.id+'/banking-business');
        } else if ((currentActivity === '' || ['service-centre-business', 'intellectual-property-business', 'shipping-business', 'headquarters-business', 'fund-management-business', 'insurance-business', 'banking-business'].indexOf(currentActivity) >= 0) &&
            entry.relevant_activities.finance_leasing_business.selected) {
                res.redirect('/substance/entry/'+entry.id+'/finance-leasing-business');
        } else if ((currentActivity === '' || ['service-centre-business', 'intellectual-property-business', 'shipping-business', 'headquarters-business', 'fund-management-business', 'insurance-business', 'banking-business', 'finance-leasing-business'].indexOf(currentActivity) >= 0) &&
            entry.relevant_activities.holding_business.selected) {
                res.redirect('/substance/entry/'+entry.id+'/holding-business');
        } else {
            res.redirect('/substance/entry/'+entry.id+'/tax-residency');
        }

}

async function loadEntry(req, res, next, view, currentStep) {
    try {
        const entry = await EntryModel.findById(req.params.id);

        if (entry?.status !== 'SAVED' && entry?.status !== 'RE-OPEN') {
            const err = new Error('Entry not found');
            err.status = 404;
            return next(err);
        }
        let financialPeriodChanged = false;
        let pre2022Submission = false;
        let prePaid =false;

        if (view === 'Tax-Residency' && !entry.tax_residency) {
            entry.pending_tax_resident = true;
        }
        if (currentStep !== null){
            await EntryModel.findByIdAndUpdate(entry._id,{"$set": {"currentStepForm": currentStep}});
        }

        const entityDetailsInfo = await getDefaultEntityDetailsData(currentStep, entry);

        if (entry.status === "RE-OPEN"){
            const lastReopen = entry.reopened.details[entry.reopened.details.length -1];
            financialPeriodChanged = lastReopen?.change_financial_period_dates === true;
        }

        if (entry.entity_details?.financial_period_ends && entry.entity_details?.financial_period_ends < new Date(2022, 0, 1)) {
            pre2022Submission = true;
        }


        if (currentStep === settings.substance_form_steps.CONFIRMATION_FORM){

            const financialPeriodEndYear = moment(entry.entity_details.financial_period_ends).utc().format('YYYY');
            const company = await Company.findOne({
                code: entry.company_data.code, masterclientcode: entry.company_data.masterclientcode,
                paymentYears: {"$in": [financialPeriodEndYear] }
            });

            if (company){
                prePaid = true
            }
        }

        let renderView = getViewByVersion(entry.version, view);
        
        res.render(renderView, {
            title: 'Substance submission form',
            company: req.session.company,
            entry: entry,
            user: req.user, messages: req.session.messages,
            alreadyPaid: (entry.payment && entry.payment.payment_received_at),
            hasError: false,
            scheduledSubmission: !!(entityDetailsInfo.scheduledSubmission && (entry.entity_details ? entry.entity_details.financial_period_ends > new Date() : false)),
            isSameITADate: entityDetailsInfo.isSameITADate,
            previousEndDate: entityDetailsInfo.previousEndDate,
            hasApplicationITADate:  !!entityDetailsInfo.applicationITADate,
            applicationITADate: entityDetailsInfo.applicationITADate,
            financialPeriodChanged: financialPeriodChanged,
            pre2022Submission: pre2022Submission,
            prePaid: prePaid || entry.payment?.payment_received_at,
            isReopened: entry.reopened?.details?.length > 0,
            currencies: CURRENCIES,
            countries: COUNTRIES
        });

    } catch (e) {
        console.log(e);
        const err = new Error('Internal server error');
        err.status = 500;
        return next(err);
    }


}

function createFinancialPeriods(dbEntry, partlyPeriod, startDates, endDates) {
    const financialPeriods = [];

    if (partlyPeriod == false) {
        //no partly period is specified, so return the financial start/end dates
        financialPeriods.push({
            financial_period_begins: dbEntry.entity_details.financial_period_begins,
            financial_period_ends: dbEntry.entity_details.financial_period_ends
        });
    } else {
        financialPeriods.push({
            financial_period_begins: moment(startDates).isValid() ? moment.utc(moment(startDates)) : null,
            financial_period_ends: moment(endDates).isValid() ? moment.utc(moment(endDates)) : null,
        })
    }
    return financialPeriods;
}

exports.getUploadedFiles = function(req, res, next) {
    EntryModel.findById(req.params.id, function(err, entry) {
        if (err) { return next(err); }
        if (entry==null) { // No results.
            err = new Error('Entry not found');
            err.status = 404;
            return next(err);
        }

        const data = {
          evidence_non_residency: (entry.tax_residency ? entry.tax_residency.evidence_non_residency : null),
          evidence_provisional_treatment_non_residency: (entry.tax_residency ? entry.tax_residency.evidence_provisional_treatment_non_residency : null),
          evidence_equipment_banking_business: (entry.banking_business ? entry.banking_business.evidence_equipment : null),
          evidence_equipment_insurance_business: (entry.insurance_business ? entry.insurance_business.evidence_equipment : null),
          evidence_equipment_fund_management_business: (entry.fund_management_business ? entry.fund_management_business.evidence_equipment : null),
          evidence_equipment_finance_leasing_business: (entry.finance_leasing_business ? entry.finance_leasing_business.evidence_equipment : null),
          evidence_equipment_headquarters_business: (entry.headquarters_business ? entry.headquarters_business.evidence_equipment : null),
          evidence_equipment_shipping_business: (entry.shipping_business ? entry.shipping_business.evidence_equipment : null),
          evidence_equipment_intellectual_property_business: (entry.intellectual_property_business ? entry.intellectual_property_business.evidence_equipment : null),
          evidence_equipment_holding_business: (entry.holding_business ? entry.holding_business.evidence_equipment : null),
          evidence_equipment_service_centre_business: (entry.service_centre_business ? entry.service_centre_business.evidence_equipment : null),
          evidence_outsourcing_banking_business: (entry.banking_business ? entry.banking_business.outsourcing_evidence : null),
          evidence_outsourcing_insurance_business: (entry.insurance_business ? entry.insurance_business.outsourcing_evidence : null),
          evidence_outsourcing_fund_management_business: (entry.fund_management_business ? entry.fund_management_business.outsourcing_evidence : null),
          evidence_outsourcing_finance_leasing_business: (entry.finance_leasing_business ? entry.finance_leasing_business.outsourcing_evidence : null),
          evidence_outsourcing_headquarters_business: (entry.headquarters_business ? entry.headquarters_business.outsourcing_evidence : null),
          evidence_outsourcing_shipping_business: (entry.shipping_business ? entry.shipping_business.outsourcing_evidence : null),
          evidence_outsourcing_intellectual_property_business: (entry.intellectual_property_business ? entry.intellectual_property_business.outsourcing_evidence : null),
          evidence_outsourcing_holding_business: (entry.holding_business ? entry.holding_business.outsourcing_evidence : null),
          evidence_outsourcing_service_centre_business: (entry.service_centre_business ? entry.service_centre_business.outsourcing_evidence : null),
          support_documents_insurance_business: (entry.insurance_business ? entry.insurance_business.support_documents : null),
          support_documents_banking_business: (entry.banking_business ? entry.banking_business.support_documents : null),
          support_documents_fund_management_business: (entry.fund_management_business ? entry.fund_management_business.support_documents : null),
          support_documents_finance_leasing_business: (entry.finance_leasing_business ? entry.finance_leasing_business.support_documents : null),
          support_documents_headquarters_business: (entry.headquarters_business ? entry.headquarters_business.support_documents : null),
          support_documents_shipping_business: (entry.shipping_business ? entry.shipping_business.support_documents : null),
          support_documents_intellectual_property_business: (entry.intellectual_property_business ? entry.intellectual_property_business.support_documents : null),
          support_documents_holding_business: (entry.holding_business ? entry.holding_business.support_documents : null),
          support_documents_service_centre_business: (entry.service_centre_business ? entry.service_centre_business.support_documents : null),
          evidence_high_risk_intellectual_property_business: (entry.intellectual_property_business ? entry.intellectual_property_business.high_risk_ip_evidence : null),
          evidence_none_activities:  (entry.relevant_activities?.evidence_none_activities ? entry.relevant_activities.evidence_none_activities : null),
          support_attachments: (entry.supporting_details ? entry.supporting_details.support_attachments : null),
          // v5 files
          evidence_tangible_assets_explanation: (entry.intellectual_property_business ? entry.intellectual_property_business.tangible_assets_explanation_files : null),
          evidence_intangible_assets_decisions: (entry.intellectual_property_business ? entry.intellectual_property_business.intangible_assets_decisions_files : null),
          evidence_intangible_assets_nature: (entry.intellectual_property_business ? entry.intellectual_property_business.intangible_assets_nature_files : null),
          evidence_intangible_assets_trading_nature: (entry.intellectual_property_business ? entry.intellectual_property_business.intangible_assets_trading_nature_files : null),
          evidence_other_ciga_business: (entry.intellectual_property_business ? entry.intellectual_property_business.other_ciga_business_files : null),
          evidence_other_ciga_decisions: (entry.intellectual_property_business ? entry.intellectual_property_business.other_ciga_decisions_files : null),
          evidence_other_ciga_evidence: (entry.intellectual_property_business ? entry.intellectual_property_business.other_ciga_evidence_files : null),
          evidence_other_ciga_files: (entry.intellectual_property_business ? entry.intellectual_property_business.other_ciga_files : null),
        };
        res.json(data);
    });
};

exports.getManagers = function(req, res, next) {
    EntryModel.findById(req.params.id, function(err, entry) {
        if (err) { return next(err); }
        if (entry==null) { // No results.
            err = new Error('Entry not found');
            err.status = 404;
            return next(err);
        }

        const data = {
            entryVersion: parseFloat(entry.version),
            banking_business_managers: (entry.banking_business ? entry.banking_business.managers : null),
            insurance_business_managers: (entry.insurance_business ? entry.insurance_business.managers : null),
            fund_management_business_managers: (entry.fund_management_business ? entry.fund_management_business.managers : null),
            finance_leasing_business_managers: (entry.finance_leasing_business ? entry.finance_leasing_business.managers : null),
            headquarters_business_managers: (entry.headquarters_business ? entry.headquarters_business.managers : null),
            shipping_business_managers: (entry.shipping_business ? entry.shipping_business.managers : null),
            intellectual_property_business_managers: (entry.intellectual_property_business ? entry.intellectual_property_business.managers : null),
            holding_business_managers: (entry.holding_business ? entry.holding_business.managers : null),
            service_centre_business_managers: (entry.service_centre_business ? entry.service_centre_business.managers : null),
        };
        res.json(data);
    });
};

exports.getPremises = function(req, res, next) {
    EntryModel.findById(req.params.id, function(err, entry) {
        if (err) { return next(err); }
        if (entry==null) { // No results.
            err = new Error('Entry not found');
            err.status = 404;
            return next(err);
        }
        const version = parseFloat(entry.version);
        let data;
        if(version < 5){
            data = {
                entryVersion: parseFloat(entry.version),
                banking_business_premises: (entry.banking_business ? entry.banking_business.premises : null),
                insurance_business_premises: (entry.insurance_business ? entry.insurance_business.premises : null),
                fund_management_business_premises: (entry.fund_management_business ? entry.fund_management_business.premises : null),
                finance_leasing_business_premises: (entry.finance_leasing_business ? entry.finance_leasing_business.premises : null),
                headquarters_business_premises: (entry.headquarters_business ? entry.headquarters_business.premises : null),
                shipping_business_premises: (entry.shipping_business ? entry.shipping_business.premises : null),
                intellectual_property_business_premises: (entry.intellectual_property_business ? entry.intellectual_property_business.premises : null),
                holding_business_premises: (entry.holding_business ? entry.holding_business.premises : null),
                service_centre_business_premises: (entry.service_centre_business ? entry.service_centre_business.premises : null),
            };
        }else{
            let bankingBusinessPremises = [];
            let insuranceBusinessPremises = [];
            let fundManagementBusinessPremises = [];
            let financeBusinessPremises = [];
            let headquartersBusinessPremises = [];
            let shippingBusinessPremises = [];
            let ipBusinessPremises = [];
            let holdingBusinessPremises = [];
            let serviceCentreBusinessPremises = [];
            
            if (entry.banking_business?.premises?.length > 0){
                bankingBusinessPremises = entry.banking_business.premises.map((premise) => {
                    const country = COUNTRIES.find((c) => c.alpha_3_code === premise.country);
                    premise.country = country ? country.name : premise.country;
                    return premise
                })
            }

            if (entry.insurance_business?.premises?.length > 0) {
                insuranceBusinessPremises = entry.insurance_business.premises.map((premise) => {
                    const country = COUNTRIES.find((c) => c.alpha_3_code === premise.country);
                    premise.country = country ? country.name : premise.country;
                    return premise
                })
            }

            if (entry.fund_management_business?.premises?.length > 0) {
                fundManagementBusinessPremises = entry.fund_management_business.premises.map((premise) => {
                    const country = COUNTRIES.find((c) => c.alpha_3_code === premise.country);
                    premise.country = country ? country.name : premise.country;
                    return premise
                })
            }

            if (entry.finance_leasing_business?.premises?.length > 0) {
                financeBusinessPremises = entry.finance_leasing_business.premises.map((premise) => {
                    const country = COUNTRIES.find((c) => c.alpha_3_code === premise.country);
                    premise.country = country ? country.name : premise.country;
                    return premise
                })
            }
            if (entry.headquarters_business?.premises?.length > 0) {
                headquartersBusinessPremises = entry.headquarters_business.premises.map((premise) => {
                    const country = COUNTRIES.find((c) => c.alpha_3_code === premise.country);
                    premise.country = country ? country.name : premise.country;
                    return premise
                })
            }

            if (entry.shipping_business?.premises?.length > 0) {
                shippingBusinessPremises = entry.shipping_business.premises.map((premise) => {
                    const country = COUNTRIES.find((c) => c.alpha_3_code === premise.country);
                    premise.country = country ? country.name : premise.country;
                    return premise
                })
            }

            if (entry.intellectual_property_business?.premises?.length > 0) {
                ipBusinessPremises = entry.intellectual_property_business.premises.map((premise) => {
                    const country = COUNTRIES.find((c) => c.alpha_3_code === premise.country);
                    premise.country = country ? country.name : premise.country;
                    return premise
                })
            }

            if (entry.holding_business?.premises?.length > 0) {
                holdingBusinessPremises = entry.holding_business.premises.map((premise) => {
                    const country = COUNTRIES.find((c) => c.alpha_3_code === premise.country);
                    premise.country = country ? country.name : premise.country;
                    return premise
                })
            }

            if (entry.service_centre_business?.premises?.length > 0) {
                serviceCentreBusinessPremises = entry.service_centre_business.premises.map((premise) => {
                    const country = COUNTRIES.find((c) => c.alpha_3_code === premise.country);
                    premise.country = country ? country.name : premise.country;
                    return premise
                })
            }

            data = {
                entryVersion: parseFloat(entry.version),
                banking_business_premises: bankingBusinessPremises ? bankingBusinessPremises : null,
                insurance_business_premises: insuranceBusinessPremises ? insuranceBusinessPremises : null,
                fund_management_business_premises: fundManagementBusinessPremises ? fundManagementBusinessPremises : null,
                finance_leasing_business_premises: financeBusinessPremises ? financeBusinessPremises : null,
                headquarters_business_premises: headquartersBusinessPremises ? headquartersBusinessPremises : null,
                shipping_business_premises: shippingBusinessPremises ? shippingBusinessPremises : null,
                intellectual_property_business_premises: ipBusinessPremises ? ipBusinessPremises : null,
                holding_business_premises: holdingBusinessPremises ? holdingBusinessPremises : null,
                service_centre_business_premises: serviceCentreBusinessPremises ? serviceCentreBusinessPremises : null,
            }; 
        }
        res.json(data);
    });
};

function removeManager(managers, id)
{
    for(let idx = 0; idx < managers.length; idx++) {
        if ( managers[idx]._id == id) {
            managers.splice(idx, 1);
        }
    }
    return managers;
}

exports.deleteManager = function(req, res, next)
{
    EntryModel.findById(req.params.id, function(err, entry) {
        if (err) { return next(err); }
        if (entry==null) { // No results.
            err = new Error('Entry not found');
            err.status = 404;
            return next(err);
        }
        const sessData = req.session;
        switch (req.body.field) {
            case "banking-business" : {
                entry.banking_business.managers = removeManager(entry.banking_business.managers, req.body._id);
                break;
            }
            case "insurance-business" : {
                entry.insurance_business.managers = removeManager(entry.insurance_business.managers, req.body._id);
                break;
            }
            case "fund-management-business" : {
                entry.fund_management_business.managers = removeManager(entry.fund_management_business.managers, req.body._id);
                break;
            }
            case "finance-leasing-business" : {
                entry.finance_leasing_business.managers = removeManager(entry.finance_leasing_business.managers, req.body._id);
                break;
            }
            case "headquarters-business" : {
                entry.headquarters_business.managers = removeManager(entry.headquarters_business.managers, req.body._id);
                break;
            }
            case "shipping-business" : {
                entry.shipping_business.managers = removeManager(entry.shipping_business.managers, req.body._id);
                break;
            }
            case "intellectual-property-business" : {
                entry.intellectual_property_business.managers = removeManager(entry.intellectual_property_business.managers, req.body._id);
                break;
            }
            case "holding-business" : {
                entry.holding_business.managers = removeManager(entry.holding_business.managers, req.body._id);
                break;
            }
            case "service-centre-business" : {
                entry.service_centre_business.managers = removeManager(entry.service_centre_business.managers, req.body._id);
                break;
            }
            default: {
                return res.json({result:false, message: "Unable to delete manager. Please try again"});
            }
        }

        EntryModel.findOneAndUpdate({_id : req.params.id, company:sessData.company.code}, entry, {}, function (err) {
            if (err) {
                err = new Error('Unable to delete manager. Please try again...');
                err.status = 500;
                res.send(err)
            } else {
                // Successful - redirect to payment if necessary
                res.json({result:true});
            }
        });
    });
}

function removePremises(premises, id)
{
    for(let idx = 0; idx < premises.length; idx++) {
        if ( premises[idx]._id == id) {
            premises.splice(idx, 1);
        }
    }
    return premises;
}

exports.deletePremises = function(req, res, next)
{
    EntryModel.findById(req.params.id, function(err, entry) {
        if (err) { return next(err); }
        if (entry==null) { // No results.
            err = new Error('Entry not found');
            err.status = 404;
            return next(err);
        }
        const sessData = req.session;
        switch (req.body.field) {
            case "banking-business" : {
                entry.banking_business.premises = removePremises(entry.banking_business.premises, req.body._id);
                break;
            }
            case "insurance-business" : {
                entry.insurance_business.premises = removePremises(entry.insurance_business.premises, req.body._id);
                break;
            }
            case "fund-management-business" : {
                entry.fund_management_business.premises = removePremises(entry.fund_management_business.premises, req.body._id);
                break;
            }
            case "finance-leasing-business" : {
                entry.finance_leasing_business.premises = removePremises(entry.finance_leasing_business.premises, req.body._id);
                break;
            }
            case "headquarters-business" : {
                entry.headquarters_business.premises = removePremises(entry.headquarters_business.premises, req.body._id);
                break;
            }
            case "shipping-business" : {
                entry.shipping_business.premises = removePremises(entry.shipping_business.premises, req.body._id);
                break;
            }
            case "intellectual-property-business" : {
                entry.intellectual_property_business.premises = removePremises(entry.intellectual_property_business.premises, req.body._id);
                break;
            }
            case "holding-business" : {
                entry.holding_business.premises = removePremises(entry.holding_business.premises, req.body._id);
                break;
            }
            case "service-centre-business" : {
                entry.service_centre_business.premises = removePremises(entry.service_centre_business.premises, req.body._id);
                break;
            }
            default: {
                return res.json({result:false, message: "Unable to delete manager. Please try again"});
            }
        }

        EntryModel.findOneAndUpdate({_id : req.params.id, company:sessData.company.code}, entry, {}, function (err) {
            if (err) {
                err = new Error('Unable to delete premises. Please try again...');
                err.status = 500;
                res.send(err)
            } else {
                // Successful - redirect to payment if necessary
                res.json({result:true});
            }
        });
    });
}

exports.deleteTaxResidencyEvidences = async function (req, res) {
    try {
        let entry = await EntryModel.findById(req.params.id);

        if (!entry) {
            return res.status(404).json({status: 404, error: "Entry not found"});
        }
        let evidenceFields = {};

        if (entry.tax_residency?.evidence_non_residency?.length > 0) {
            evidenceFields["tax_residency.evidence_non_residency"] = [];
        }
        if (entry.tax_residency?.evidence_provisional_treatment_non_residency?.length > 0) {
            evidenceFields["tax_residency.evidence_provisional_treatment_non_residency"] = [];
        }

        EntryModel.findByIdAndUpdate(req.params.id, {"$set": evidenceFields}, {}, function (err) {
            if (err) {
                return res.status(400).json({status: 200, error: "Error removing evidences"});
            } else {
                return res.status(200).json({status: 200, error: "Evidences removed successfully"});
            }
        });

    } catch (e) {
        console.log(e);
        return res.status(500).json({status: 500, error: "Internal Server Error"});
    }
};

function createManagerObject(businessObj, manager, _id) {
    if (businessObj.managers) {
        //update existing or push
        if (_id) {
            businessObj.managers.forEach(function(item, index){
                if (item._id == _id) {
                    manager._id = _id
                    businessObj.managers[index] = manager;
                }
            });
        } else {
            businessObj.managers = businessObj.managers.push(manager)
        }
    } else {
        businessObj.managers = [manager]
    }
    return businessObj;
}

function createPremisesObject(businessObj, premises, _id) {
    if (businessObj.premises) {
        //update existing or push
        if (_id) {
            businessObj.premises.forEach(function(item, index){
                if (item._id == _id) {
                    premises._id = _id
                    businessObj.premises[index] = premises;
                }
            });
        } else {
            businessObj.premises = businessObj.premises.push(premises)
        }
    } else {
        businessObj.premises = [premises]
    }
    return businessObj;
}

exports.addManager = async function(req, res, next) {

    try{
        const entry = await EntryModel.findById(req.params.id);

        if (entry?.status !== 'SAVED' && entry?.status !== 'RE-OPEN') {
            const err = new Error('Entry not found');
            err.status = 404;
            return next(err);
        }

        let renderView = getViewByVersion(entry.version, 'Manager');
        res.render(renderView, { layout: 'modal', title: 'Add Director' });

    }catch(e){
        console.log(e);
        const err = new Error('Internal server error');
        err.status = 500;
        return next(err);
    }
   
}

exports.editManager = function(req, res, next) {
    EntryModel.findById(req.params.id, function(err, entry) {
        if (err) { return next(err); }
        if (entry==null) { // No results.
            err = new Error('Entry not found');
            err.status = 404;
            return next(err);
        }
        let managerObj = null;
        let renderView = getViewByVersion(entry.version, 'Manager');
        switch (req.params.business) {
            case "banking-business" : {
                if (entry.banking_business) {
                    entry.banking_business.managers.forEach(function(item) {
                        if (item._id == req.params.managerId) {
                            managerObj = item;
                            return;
                        }
                    });
                }
                break;
            }
            case "insurance-business" : {
                if (entry.insurance_business) {
                    entry.insurance_business.managers.forEach(function(item) {
                        if (item._id == req.params.managerId) {
                            managerObj = item;
                            return;
                        }
                    });
                }
                break;
            }
            case "fund-management-business" : {
                if (entry.fund_management_business) {
                    entry.fund_management_business.managers.forEach(function(item) {
                        if (item._id == req.params.managerId) {
                            managerObj = item;
                            return;
                        }
                    });
                }
                break;
            }
            case "finance-leasing-business" : {
                if (entry.finance_leasing_business) {
                    entry.finance_leasing_business.managers.forEach(function(item) {
                        if (item._id == req.params.managerId) {
                            managerObj = item;
                            return;
                        }
                    });
                }
                break;
            }
            case "headquarters-business" : {
                if (entry.headquarters_business) {
                    entry.headquarters_business.managers.forEach(function(item) {
                        if (item._id == req.params.managerId) {
                            managerObj = item;
                            return;
                        }
                    });
                }
                break;
            }
            case "shipping-business" : {
                if (entry.shipping_business) {
                    entry.shipping_business.managers.forEach(function(item) {
                        if (item._id == req.params.managerId) {
                            managerObj = item;
                            return;
                        }
                    });
                }
                break;
            }
            case "intellectual-property-business" : {
                if (entry.intellectual_property_business) {
                    entry.intellectual_property_business.managers.forEach(function(item) {
                        if (item._id == req.params.managerId) {
                            managerObj = item;
                            return;
                        }
                    });
                }
                break;
            }
            case "holding-business" : {
                if (entry.holding_business) {
                    entry.holding_business.managers.forEach(function(item) {
                        if (item._id == req.params.managerId) {
                            managerObj = item;
                            return;
                        }
                    });
                }
                break;
            }
            case "service-centre-business" : {
                if (entry.service_centre_business) {
                    entry.service_centre_business.managers.forEach(function(item) {
                        if (item._id == req.params.managerId) {
                            managerObj = item;
                            return;
                        }
                    });
                }
                break;
            }
            default: {
                return res.render(renderView, {
                    layout: 'modal', 
                    title: 'Manager',
                    data: null, 
                    hasError: true, 
                    validationErrors: [{msg: "Unknown business"}]
                });
            }
        }

        res.render(renderView, { layout: 'modal',  data: managerObj, title: 'Manager'});

    });
}

exports.saveManager = function(req, res, next)
{
    EntryModel.findById(req.params.id, function(err, entry) {
        if (err) { return next(err); }
        if (entry==null) { // No results.
            err = new Error('Entry not found');
            err.status = 404;
            return next(err);
        }
        //create manager object
        const manager = {
            is_corporate_director: (req.body.is_corporate_director == "Yes"),
            first_name: req.body.FirstNameDirector,
            middle_name: req.body.MiddleNameDirector,
            last_name: req.body.LastNameDirector,
            date_of_birth: (req.body.DateOfBirth && req.body.DateOfBirth.length > 0 ? moment.utc(moment(req.body.DateOfBirth, 'MM/DD/YYYY')) : undefined),
            address_line1: req.body.StreetAndNumber,
            address_line2: req.body.OptionalAddress,
            city: req.body.TowneOrCityName,
            country: req.body.StateOrCountryName,
            postalcode: req.body.ZIPCodePerson,
            resident_in_bvi: (req.body.ResidentinBVIYesNo == "Yes"),
            position_held: req.body.PositionHeld
        };


        let customErrors  = managerValidator.validate(manager);
        let renderView = getViewByVersion(entry.version, 'Manager');

        if (customErrors.length > 0) {
            res.render(renderView, {layout: 'modal', title: 'Director', data: manager, hasError: true, validationErrors: customErrors});
        } else {

            switch (req.params.business) {
                case "banking-business" : {
                    if (!entry.banking_business) {
                        entry.banking_business = {};
                    }
                    entry.banking_business  = createManagerObject(entry.banking_business, manager, req.params.managerId)
                    break;
                }
                case "insurance-business" : {
                    if (!entry.insurance_business) {
                        entry.insurance_business = {};
                    }
                    entry.insurance_business  = createManagerObject(entry.insurance_business, manager, req.params.managerId)
                    break;
                }
                case "fund-management-business" : {
                    if (!entry.fund_management_business) {
                        entry.fund_management_business = {};
                    }
                    entry.fund_management_business  = createManagerObject(entry.fund_management_business, manager, req.params.managerId)
                    break;
                }
                case "finance-leasing-business" : {
                    if (!entry.finance_leasing_business) {
                        entry.finance_leasing_business = {};
                    }
                    entry.finance_leasing_business  = createManagerObject(entry.finance_leasing_business, manager, req.params.managerId)
                    break;
                }
                case "headquarters-business" : {
                    if (!entry.headquarters_business) {
                        entry.headquarters_business = {};
                    }
                    entry.headquarters_business  = createManagerObject(entry.headquarters_business, manager, req.params.managerId)
                    break;
                }
                case "shipping-business" : {
                    if (!entry.shipping_business) {
                        entry.shipping_business = {};
                    }
                    entry.shipping_business  = createManagerObject(entry.shipping_business, manager, req.params.managerId)
                    break;
                }
                case "intellectual-property-business" : {
                    if (!entry.intellectual_property_business) {
                        entry.intellectual_property_business = {};
                    }
                    entry.intellectual_property_business  = createManagerObject(entry.intellectual_property_business, manager, req.params.managerId)
                    break;
                }
                case "holding-business" : {
                    if (!entry.holding_business) {
                        entry.holding_business = {};
                    }
                    entry.holding_business  = createManagerObject(entry.holding_business, manager, req.params.managerId)
                    break;
                }
                case "service-centre-business" : {
                    if (!entry.service_centre_business) {
                        entry.service_centre_business = {};
                    }
                    entry.service_centre_business  = createManagerObject(entry.service_centre_business, manager, req.params.managerId)
                    break;
                }
                default: {
                    return res.render(renderView, {layout: 'modal', title: 'Premises', data: null, hasError: true, validationErrors: [{msg: "Unknown business"}]});
                }
            }
            let sessData = req.session;
            EntryModel.findOneAndUpdate({_id : req.params.id, company:sessData.company.code}, entry, {}, function (err) {
                if (err) {
                    return next(err);
                } else {
                    res.render(renderView, {layout: 'modal', title: 'Director', data: manager, hasError: false, validationErrors: [{msg: "Director is saved."}]});
                }
            });
        }
    });

}

exports.addPremises = async function(req, res, next) {
    const entry = await EntryModel.findById(req.params.id);
    if (entry == null) { 
        const err = new Error('Entry not found');
        err.status = 404;
        return next(err);
    }

    let renderView = getViewByVersion(entry.version, 'Premises');
    res.render(renderView, {layout: 'modal', title: 'Add Premises', data: {country: "BVI"}});
}

exports.editPremises = function(req, res, next) {
    EntryModel.findById(req.params.id, function(err, entry) {
        if (err) { return next(err); }
        if (entry==null) { // No results.
            err = new Error('Entry not found');
            err.status = 404;
            return next(err);
        }
        let premisesObj = null;

        let renderView = getViewByVersion(entry.version, 'Premises');

        switch (req.params.business) {
            case "banking-business" : {
                if (entry.banking_business) {
                    entry.banking_business.premises.forEach(function(item) {
                        if (item._id == req.params.premisesId) {
                            premisesObj = item;
                            return;
                        }
                    });
                }
                break;
            }
            case "insurance-business" : {
                if (entry.insurance_business) {
                    entry.insurance_business.premises.forEach(function(item) {
                        if (item._id == req.params.premisesId) {
                            premisesObj = item;
                            return;
                        }
                    });
                }
                break;
            }
            case "fund-management-business" : {
                if (entry.fund_management_business) {
                    entry.fund_management_business.premises.forEach(function(item) {
                        if (item._id == req.params.premisesId) {
                            premisesObj = item;
                            return;
                        }
                    });
                }
                break;
            }
            case "finance-leasing-business" : {
                if (entry.finance_leasing_business) {
                    entry.finance_leasing_business.premises.forEach(function(item) {
                        if (item._id == req.params.premisesId) {
                            premisesObj = item;
                            return;
                        }
                    });
                }
                break;
            }
            case "headquarters-business" : {
                if (entry.headquarters_business) {
                    entry.headquarters_business.premises.forEach(function(item) {
                        if (item._id == req.params.premisesId) {
                            premisesObj = item;
                            return;
                        }
                    });
                }
                break;
            }
            case "shipping-business" : {
                if (entry.shipping_business) {
                    entry.shipping_business.premises.forEach(function(item) {
                        if (item._id == req.params.premisesId) {
                            premisesObj = item;
                            return;
                        }
                    });
                }
                break;
            }
            case "intellectual-property-business" : {
                if (entry.intellectual_property_business) {
                    entry.intellectual_property_business.premises.forEach(function(item) {
                        if (item._id == req.params.premisesId) {
                            premisesObj = item;
                            return;
                        }
                    });
                }
                break;
            }
            case "holding-business" : {
                if (entry.holding_business) {
                    entry.holding_business.premises.forEach(function(item) {
                        if (item._id == req.params.premisesId) {
                            premisesObj = item;
                            return;
                        }
                    });
                }
                break;
            }
            case "service-centre-business" : {
                if (entry.service_centre_business) {
                    entry.service_centre_business.premises.forEach(function(item) {
                        if (item._id == req.params.premisesId) {
                            premisesObj = item;
                            return;
                        }
                    });
                }
                break;
            }
            default: {
                return res.render(renderView, {layout: 'modal', title: 'Premises', data: null, hasError: true, validationErrors: [{msg: "Unknown business"}]});
            }
        }

        res.render(renderView, {layout: 'modal', data: premisesObj, title: 'Edit Premises'});

    });
}

exports.savePremises = function(req, res, next)
{
    EntryModel.findById(req.params.id, function(err, entry) {
        if (err) { return next(err); }
        if (entry==null) { // No results.
            err = new Error('Entry not found');
            err.status = 404;
            return next(err);
        }
        const entryVersion = parseFloat(entry.version);

        //create manager object
        const premises = {
            address_line1: req.body.PremisesAddressLine1,
            address_line2: req.body.PremisesAddressLine2,
            city: req.body.PremisesIsland,
            country: entryVersion < 5 ? req.body.PremisesCountry : "VGB",
            postalcode: req.body.PremisesZIPCodePerson,
            are_physical_offices: req.body.arePhysicalOffices === "Yes" ? true : req.body.arePhysicalOffices === 'No' ? false : undefined
        };

        let previousPremises = [];
        let editPremiseId = req.params.premisesId || "";

        if (req.params.business && entryVersion >= 5){
            const activityType = req.params.business.replace(/-/g, "_");
            const activityBusiness = entry[activityType];
            previousPremises = activityBusiness?.premises || [];
        }


        let customErrors = premisesValidator.validate(premises, entryVersion, previousPremises, editPremiseId);
        let renderView = getViewByVersion(entryVersion, 'Premises');
        if (customErrors.length > 0) {
            res.render(renderView, {layout: 'modal', title: 'Premises', data: premises, hasError: true, validationErrors: customErrors});
        } else {

            switch (req.params.business) {
                case "banking-business" : {
                    if (!entry.banking_business) {
                        entry.banking_business = {};
                    }
                    entry.banking_business  = createPremisesObject(entry.banking_business, premises, req.params.premisesId)
                    break;
                }
                case "insurance-business" : {
                    if (!entry.insurance_business) {
                        entry.insurance_business = {};
                    }
                    entry.insurance_business  = createPremisesObject(entry.insurance_business, premises, req.params.premisesId)
                    break;
                }
                case "fund-management-business" : {
                    if (!entry.fund_management_business) {
                        entry.fund_management_business = {};
                    }
                    entry.fund_management_business  = createPremisesObject(entry.fund_management_business, premises, req.params.premisesId)
                    break;
                }
                case "finance-leasing-business" : {
                    if (!entry.finance_leasing_business) {
                        entry.finance_leasing_business = {};
                    }
                    entry.finance_leasing_business  = createPremisesObject(entry.finance_leasing_business, premises, req.params.premisesId)
                    break;
                }
                case "headquarters-business" : {
                    if (!entry.headquarters_business) {
                        entry.headquarters_business = {};
                    }
                    entry.headquarters_business  = createPremisesObject(entry.headquarters_business, premises, req.params.premisesId)
                    break;
                }
                case "shipping-business" : {
                    if (!entry.shipping_business) {
                        entry.shipping_business = {};
                    }
                    entry.shipping_business  = createPremisesObject(entry.shipping_business, premises, req.params.premisesId)
                    break;
                }
                case "intellectual-property-business" : {
                    if (!entry.intellectual_property_business) {
                        entry.intellectual_property_business = {};
                    }
                    entry.intellectual_property_business  = createPremisesObject(entry.intellectual_property_business, premises, req.params.premisesId)
                    break;
                }
                case "holding-business" : {
                    if (!entry.holding_business) {
                        entry.holding_business = {};
                    }
                    entry.holding_business  = createPremisesObject(entry.holding_business, premises, req.params.premisesId)
                    break;
                }
                case "service-centre-business" : {
                    if (!entry.service_centre_business) {
                        entry.service_centre_business = {};
                    }
                    entry.service_centre_business  = createPremisesObject(entry.service_centre_business, premises, req.params.premisesId)
                    break;
                }
                default: {

                    return res.render(renderView, {layout: 'modal', title: 'Premises', data: premises, hasError: true, validationErrors: [{msg: "Unknown business"}]});
                }
            }
            let sessData = req.session;
            EntryModel.findOneAndUpdate({_id : req.params.id, company:sessData.company.code}, entry, {}, function (err) {
                if (err) {
                    return next(err);
                } else {

                    res.render(renderView, {layout: 'modal', title: 'Premises', data: premises, hasError: false, validationErrors: [{msg: "Premises is saved."}]});
                }
            });
        }
    });

}

exports.entryFinancialPeriod = async function (req, res, next) {
    try {
        await loadEntry(req, res, next, 'Financial-Period', settings.substance_form_steps.FINANCIAL_PERIOD_FORM);
    } catch (e) {
        console.log(e);
        next(e);
    }

};


exports.entryEntityDetails = async function (req, res, next) {
    try{
        await loadEntry(req, res, next, 'Entity-Details', settings.substance_form_steps.ENTITY_DETAILS_FORM);
    }catch(e){
        console.log(e);
        next(e);
    }

};

exports.entryTaxResidency = async function (req, res, next) {
    try {
        await loadEntry(req, res, next, 'Tax-Residency', settings.substance_form_steps.TAX_RESIDENCY_FORM);
    } catch (e) {
        console.log(e);
        next(e);
    }

};

exports.entryRelevantActivities = async function (req, res, next) {
    try {
        await loadEntry(req, res, next, 'Relevant-Activities', settings.substance_form_steps.RELEVANT_ACTIVITIES_FORM);
    }catch(e){
        console.log(e);
        next(e);
    }

};

exports.entryBankingBusiness = async function (req, res, next) {
    try {
        const entry = await EntryModel.findById(req.params.id);

        if (entry?.status !== 'SAVED' && entry?.status !== 'RE-OPEN') { // No results.
            const err = new Error('Entry not found');
            err.status = 404;
            return next(err);
        }
        const sessData = req.session;
        if (!entry.banking_business) {
            entry.banking_business = {
                core_income_generating_outsourced: true
            }
        }

        await EntryModel.findByIdAndUpdate(entry._id, {"$set": {"currentStepForm":
                settings.substance_form_steps.BANKING_BUSINESS_FORM}});

        const entryCurrency = entry.entity_details.totalAnnualGrossCurrency;
        let selectedCurrency;
        if(entryCurrency){
            selectedCurrency = CURRENCIES.find((currency) => currency.cc === entryCurrency)
        }

        let renderView = getViewByVersion(entry.version, 'Business');
        res.render(renderView, {
            title: 'Banking Business',
            data: entry.banking_business,
            entryId: entry.id,
            company: sessData.company,
            user: req.user,
            messages: req.session.messages,
            subtitle: settings.subtitle_banking_business,
            activityType: settings.substance_business_types.BANKING,
            isNotActivityHldOrIp: true,
            currencies: CURRENCIES,
            selectedCurrency: selectedCurrency,
            defaultCurrency: DEFAULT_CURRENCY,
            cigaCodes: getCIGACodeList(settings.substance_business_types.BANKING)
        });
    } catch (e) {
        console.log(e);
        const err = new Error('Internal server error');
        err.status = 500;
        return next(err);
    }


};

exports.entryInsuranceBusiness = async function (req, res, next) {
    try {
        const entry = await EntryModel.findById(req.params.id);

        if (entry?.status !== 'SAVED' && entry?.status !== 'RE-OPEN') { // No results.
            const err = new Error('Entry not found');
            err.status = 404;
            return next(err);
        }
        const sessData = req.session;
        if (!entry.insurance_business) {
            entry.insurance_business = {
                core_income_generating_outsourced: true
            }
        }

        await EntryModel.findByIdAndUpdate(entry._id, {"$set": {"currentStepForm":
                settings.substance_form_steps.INSURANCE_BUSINESS_FORM}});

        const entryCurrency = entry.entity_details.totalAnnualGrossCurrency;
        let selectedCurrency;
        if (entryCurrency) {
            selectedCurrency = CURRENCIES.find((currency) => currency.cc === entryCurrency)
        }
        let renderView = getViewByVersion(entry.version, 'Business');
        res.render(renderView, {
            title: 'Insurance Business',
            data: entry.insurance_business,
            company: sessData.company,
            entryId: entry.id,
            user: req.user,
            messages: req.session.messages,
            activityType: settings.substance_business_types.INSURANCE,
            isNotActivityHldOrIp: true,
            currencies: CURRENCIES,
            selectedCurrency: selectedCurrency,
            defaultCurrency: DEFAULT_CURRENCY,
            cigaCodes: getCIGACodeList(settings.substance_business_types.INSURANCE)
        });
    } catch (e) {
        console.log(e);
        const err = new Error('Internal server error');
        err.status = 500;
        return next(err);
    }
};

exports.entryFundManagementBusiness = async function (req, res, next) {
    try {
        const entry = await EntryModel.findById(req.params.id);

        if (entry?.status !== 'SAVED' && entry?.status !== 'RE-OPEN') { // No results.
            const err = new Error('Entry not found');
            err.status = 404;
            return next(err);
        }
        const sessData = req.session;
        if (!entry.fund_management_business) {
            entry.fund_management_business = {
                core_income_generating_outsourced: true
            }
        }

        await EntryModel.findByIdAndUpdate(entry._id, {"$set": {"currentStepForm":
                settings.substance_form_steps.FUND_MANAGEMENT_BUSINESS_FORM}});

        const entryCurrency = entry.entity_details.totalAnnualGrossCurrency;
        let selectedCurrency;
        if (entryCurrency) {
            selectedCurrency = CURRENCIES.find((currency) => currency.cc === entryCurrency)
        }

        let renderView = getViewByVersion(entry.version, 'Business');
        res.render(renderView, {
            title: 'Fund Management Business',
            data: entry.fund_management_business,
            company: sessData.company,
            entryId: entry.id,
            user: req.user,
            messages: req.session.messages,
            activityType: settings.substance_business_types.FUND_MANAGEMENT,
            isNotActivityHldOrIp: true,
            currencies: CURRENCIES,
            selectedCurrency: selectedCurrency,
            defaultCurrency: DEFAULT_CURRENCY,
            cigaCodes: getCIGACodeList(settings.substance_business_types.FUND_MANAGEMENT)
        });
    } catch (e) {
        console.log(e);
        const err = new Error('Internal server error');
        err.status = 500;
        return next(err);
    }
};

exports.entryFinanceLeasingBusiness = async function (req, res, next) {
    try {
        const entry = await EntryModel.findById(req.params.id);

        if (entry?.status !== 'SAVED' && entry?.status !== 'RE-OPEN') { // No results.
            const err = new Error('Entry not found');
            err.status = 404;
            return next(err);
        }
        const sessData = req.session;
        if (!entry.finance_leasing_business) {
            entry.finance_leasing_business = {
                core_income_generating_outsourced: true
            }
        }

        await EntryModel.findByIdAndUpdate(entry._id, {"$set": {"currentStepForm":
                settings.substance_form_steps.FINANCE_LEASING_BUSINESS_FORM}});

        const entryCurrency = entry.entity_details.totalAnnualGrossCurrency;
        let selectedCurrency;
        if (entryCurrency) {
            selectedCurrency = CURRENCIES.find((currency) => currency.cc === entryCurrency)
        }

        let renderView = getViewByVersion(entry.version, 'Business');
        res.render(renderView, {
            title: 'Finance Leasing Business',
            data: entry.finance_leasing_business,
            company: sessData.company,
            entryId: entry.id,
            user: req.user,
            messages: req.session.messages,
            activityType: settings.substance_business_types.FINANCE_LEASING,
            isNotActivityHldOrIp: true,
            currencies: CURRENCIES,
            selectedCurrency: selectedCurrency,
            defaultCurrency: DEFAULT_CURRENCY,
            cigaCodes: getCIGACodeList(settings.substance_business_types.FINANCE_LEASING)
        });
    } catch (e) {
        console.log(e);
        const err = new Error('Internal server error');
        err.status = 500;
        return next(err);
    }
};

exports.entryHeadquartersBusiness = async function (req, res, next) {
    try {
        const entry = await EntryModel.findById(req.params.id);

        if (entry?.status !== 'SAVED' && entry?.status !== 'RE-OPEN') { // No results.
            const err = new Error('Entry not found');
            err.status = 404;
            return next(err);
        }
        const sessData = req.session;
        if (!entry.headquarters_business) {
            entry.headquarters_business = {
                core_income_generating_outsourced: true
            }
        }

        await EntryModel.findByIdAndUpdate(entry._id, {
            "$set": {
                "currentStepForm":
                settings.substance_form_steps.HEADQUARTERS_BUSINESS_FORM
            }
        });

        const entryCurrency = entry.entity_details.totalAnnualGrossCurrency;
        let selectedCurrency;
        if (entryCurrency) {
            selectedCurrency = CURRENCIES.find((currency) => currency.cc === entryCurrency)
        }
        let renderView = getViewByVersion(entry.version, 'Business');
        res.render(renderView, {
            title: 'Headquarters Business',
            data: entry.headquarters_business,
            company: sessData.company,
            entryId: entry.id,
            user: req.user,
            messages: req.session.messages,
            activityType: settings.substance_business_types.HEADQUARTERS,
            isNotActivityHldOrIp: true,
            currencies: CURRENCIES,
            selectedCurrency: selectedCurrency,
            defaultCurrency: DEFAULT_CURRENCY,
            cigaCodes: getCIGACodeList(settings.substance_business_types.HEADQUARTERS)
        });
    } catch (e) {
        console.log(e);
        const err = new Error('Internal server error');
        err.status = 500;
        return next(err);
    }
};

exports.entryShippingBusiness = async function (req, res, next) {
    try {
        const entry = await EntryModel.findById(req.params.id);

        if (entry?.status !== 'SAVED' && entry?.status !== 'RE-OPEN') { // No results.
            const err = new Error('Entry not found');
            err.status = 404;
            return next(err);
        }
        const sessData = req.session;
        if (!entry.shipping_business) {
            entry.shipping_business = {
                core_income_generating_outsourced: true
            }
        }

        await EntryModel.findByIdAndUpdate(entry._id, {
            "$set": {
                "currentStepForm":
                settings.substance_form_steps.SHIPPING_BUSINESS_FORM
            }
        });

        const entryCurrency = entry.entity_details.totalAnnualGrossCurrency;
        let selectedCurrency;
        if (entryCurrency) {
            selectedCurrency = CURRENCIES.find((currency) => currency.cc === entryCurrency)
        }

        let renderView = getViewByVersion(entry.version, 'Business');
        res.render(renderView, {
            title: 'Shipping Business',
            data: entry.shipping_business,
            company: sessData.company,
            entryId: entry.id,
            user: req.user,
            messages: req.session.messages,
            activityType: settings.substance_business_types.SHIPPING,
            isNotActivityHldOrIp: true,
            currencies: CURRENCIES,
            selectedCurrency: selectedCurrency,
            defaultCurrency: DEFAULT_CURRENCY,
            cigaCodes: getCIGACodeList(settings.substance_business_types.SHIPPING)
        });
    } catch (e) {
        console.log(e);
        const err = new Error('Internal server error');
        err.status = 500;
        return next(err);
    }

};

exports.entryIntellectualPropertyBusiness = async function (req, res, next) {
    try {
        const entry = await EntryModel.findById(req.params.id);

        if (entry?.status !== 'SAVED' && entry?.status !== 'RE-OPEN') { // No results.
            const err = new Error('Entry not found');
            err.status = 404;
            return next(err);
        }
        const sessData = req.session;
        if (!entry.intellectual_property_business) {
            entry.intellectual_property_business = {
                core_income_generating_outsourced: true
            }
        }

        await EntryModel.findByIdAndUpdate(entry._id, {
            "$set": {
                "currentStepForm":
                settings.substance_form_steps.INTELLECTUAL_PROPERTY_BUSINESS_FORM
            }
        });

        const entryCurrency = entry.entity_details.totalAnnualGrossCurrency;
        let selectedCurrency;
        if (entryCurrency) {
            selectedCurrency = CURRENCIES.find((currency) => currency.cc === entryCurrency)
        }

        let renderView = getViewByVersion(entry.version, 'Business');
        res.render(renderView, {
            title: 'Intellectual Property Business',
            data: entry.intellectual_property_business,
            company: sessData.company,
            entryId: entry.id,
            user: req.user,
            messages: req.session.messages,
            activityType: settings.substance_business_types.INTELLECTUAL_PROPERTY,
            isNotActivityHldOrIp: false,
            currencies: CURRENCIES,
            selectedCurrency: selectedCurrency,
            defaultCurrency: DEFAULT_CURRENCY,
            cigaCodes: getCIGACodeList(settings.substance_business_types.INTELLECTUAL_PROPERTY)
        });
    } catch (e) {
        console.log(e);
        const err = new Error('Internal server error');
        err.status = 500;
        return next(err);
    }
};

exports.entryHoldingBusiness = async function (req, res, next) {
    try {
        const entry = await EntryModel.findById(req.params.id);

        if (entry?.status !== 'SAVED' && entry?.status !== 'RE-OPEN') { // No results.
            const err = new Error('Entry not found');
            err.status = 404;
            return next(err);
        }
        const sessData = req.session;
        if (!entry.holding_business) {
            entry.holding_business = {
                core_income_generating_outsourced: true
            }
        }

        await EntryModel.findByIdAndUpdate(entry._id, {
            "$set": {
                "currentStepForm":
                settings.substance_form_steps.HOLDING_BUSINESS_FORM
            }
        });

        const entryCurrency = entry.entity_details.totalAnnualGrossCurrency;
        let selectedCurrency;
        if (entryCurrency) {
            selectedCurrency = CURRENCIES.find((currency) => currency.cc === entryCurrency)
        }
        let renderView = getViewByVersion(entry.version, 'Business');
        res.render(renderView, {
            title: 'Holding Business (Pure Equity Holding entities)',
            data: entry.holding_business,
            company: sessData.company,
            entryId: entry.id,
            user: req.user,
            messages: req.session.messages,
            activityType: settings.substance_business_types.HOLDING,
            isNotActivityHldOrIp: false,
            currencies: CURRENCIES,
            selectedCurrency: selectedCurrency,
            defaultCurrency: DEFAULT_CURRENCY,
        });
    } catch (e) {
        console.log(e);
        const err = new Error('Internal server error');
        err.status = 500;
        return next(err);
    }
};

exports.entryServiceCentreBusiness = async function (req, res, next) {
    try {
        const entry = await EntryModel.findById(req.params.id);

        if (entry?.status !== 'SAVED' && entry?.status !== 'RE-OPEN') { // No results.
            const err = new Error('Entry not found');
            err.status = 404;
            return next(err);
        }
        const sessData = req.session;
        if (!entry.service_centre_business) {
            entry.service_centre_business = {
                core_income_generating_outsourced: true
            }
        }

        await EntryModel.findByIdAndUpdate(entry._id, {
            "$set": {
                "currentStepForm":
                settings.substance_form_steps.SERVICE_CENTRE_BUSINESS_FORM
            }
        });

        const entryCurrency = entry.entity_details.totalAnnualGrossCurrency;
        let selectedCurrency;
        if (entryCurrency) {
            selectedCurrency = CURRENCIES.find((currency) => currency.cc === entryCurrency)
        }
        let renderView = getViewByVersion(entry.version, 'Business');
        res.render(renderView, {
            title: 'Distribution and Service Centre Business',
            data: entry.service_centre_business,
            company: sessData.company,
            entryId: entry.id,
            user: req.user,
            messages: req.session.messages,
            activityType: settings.substance_business_types.DISTRIBUTION_SERVICE_CENTRE,
            isNotActivityHldOrIp: true,
            currencies: CURRENCIES,
            selectedCurrency: selectedCurrency,
            defaultCurrency: DEFAULT_CURRENCY,
            cigaCodes: getCIGACodeList(settings.substance_business_types.DISTRIBUTION_SERVICE_CENTRE)
        });
    } catch (e) {
        console.log(e);
        const err = new Error('Internal server error');
        err.status = 500;
        return next(err);
    }
};

exports.entrySupportingDetails = async function (req, res, next) {
  try {
      await loadEntry(req, res, next, 'Supporting-Details', settings.substance_form_steps.SUPPORTING_DETAILS_FORM);
  } catch (e) {
      console.log(e);
      next(e);
  }

};

exports.entryConfirmation = async function (req, res, next) {
    try{
        await loadEntry(req, res, next, 'Confirmation', settings.substance_form_steps.CONFIRMATION_FORM);
    }catch(e){
        console.log(e);
        next(e);
    }

};

exports.entryPayment = async function (req, res, next) {
    try {

        await loadEntry(req, res, next, 'Payment');
    }catch(e){
        console.log(e);
        next(e);
    }

};

exports.ensureAuthenticated = function(req, res, next) {
    //only authenticated when the user is logged in and allowed to open the substance entry
    if (!req.user || req.session.id != req.user.sessionId) {
        req.logout(function (err) {
            if (err) { return next(err) }
            req.session.destroy(function () {
                // cannot access session here
                sessionUtils.onSessionDestroyed(req, res);
            });
        });
    } else if ((req.user && req.session.id == req.user.sessionId) && !req.session.auth2fa) {
        if (req.user.secret_2fa) {
            res.redirect('/users/2fa-code');
        } else {
            res.redirect('/users/2fa-setup');
        }
    } else {

        if (req.session.entryId && (req.params.id == req.session.entryId || req.body.entryId == req.session.entryId)) {
            next();
        } else {
            req.logout(function (err) {
                if (err) { return next(err) }
                req.session.destroy(function () {
                    // cannot access session here
                    sessionUtils.onSessionDestroyed(req, res);
                });
            });
        }
    }
}

exports.saveFinancialPeriod = async function(req, res, next){
    try {
        const sessData = req.session;

        if (req.body.submit === settings.settings.previous_page_action) {
            res.redirect('/masterclients/' + sessData.company.masterclientcode + '/substance/companies/' + sessData.company.code + '/forms');
        } else {

            const dbEntry = await EntryModel.findById(req.params.id);

            
            if(!dbEntry){
                const err = new Error('Entry not found');
                err.status = 404;
                return next(err);    
            }

            const hasFinancialPeriodChanged = req.body.HasFinancialPeriodChanged === "Yes" ? true :
                req.body.HasFinancialPeriodChanged === "No" ? false : undefined;

            const entryInformation = await getDefaultEntityDetailsData(settings.substance_form_steps.FINANCIAL_PERIOD_FORM, dbEntry);

            const dateOfApplicationToITA = req.body.DateOfApplicationToITA ? 
              moment.utc(moment(req.body.DateOfApplicationToITA)) :
              entryInformation.applicationITADate;

            const entry = new EntryModel({
                _id: req.params.id,
                version: dbEntry.version,
                company: dbEntry.company,
                entity_details: {
                    ...dbEntry.entity_details.toObject(),
                    financial_period_changed: req.body.HasFinancialPeriodChanged === "Yes" ? true :
                        req.body.HasFinancialPeriodChanged === "No" ? false : undefined,
                    previous_financial_period_ends: hasFinancialPeriodChanged === true ? moment.utc(moment(entryInformation.previousEndDate)) : undefined,
                    date_of_application_ITA: hasFinancialPeriodChanged === true ? dateOfApplicationToITA : undefined
                }
            });

            const validationV5Date = moment('31/12/2022', 'DD/MM/YYYY').toDate();
            const isEndDateGt2022 = entry.entity_details.financial_period_ends >= validationV5Date;

            if (isEndDateGt2022){
                entry.version = "5.0";
            }else{
                entry.version = "4.5";
            }

            const entryVersion = parseFloat(entry.version);

            const customErrors = await financialPeriodValidator.validate(entry);

            if (customErrors.length > 0) {
                let renderView = getViewByVersion(entryVersion,'Financial-Period');
                return res.render(renderView, {
                    entry: entry,
                    company: req.session.company,
                    hasError: true,
                    validationErrors: customErrors,
                    user: req.user,
                    messages: req.session.messages,
                    scheduledSubmission: entryInformation.scheduledSubmission && (entry.entity_details ? entry.entity_details.financial_period_ends > new Date() : false),
                    isSameITADate: entryInformation.isSameITADate,
                    previousEndDate: entryInformation.previousEndDate,
                    hasApplicationITADate: !!entryInformation.applicationITADate,
                    applicationITADate: entryInformation.applicationITADate ? entryInformation.applicationITADate : entry.entity_details.date_of_application_ITA
                });
            } 

            //save the entry
            await EntryModel.findOneAndUpdate({ _id: req.params.id, company: sessData.company.code }, entry, { runValidators: true, context: 'query' });

            if (entryVersion < 5){
                res.redirect('/substance/entry/' + req.params.id + '/relevant-activities');
            }else{
                res.redirect('/substance/entry/' + req.params.id + '/entity-details');
            }
        }
    } catch (e) {
        console.log(e);
        next(e);
    }  
}


exports.saveEntityDetails = async function (req, res, next) {
    try {
        const sessData = req.session;

        if (req.body.submit === settings.settings.previous_page_action) {
            res.redirect('/substance/entry/' + req.params.id + '/financial-period');
        } else {

            const dbEntry = await EntryModel.findById(req.params.id);
            const entryVersion = parseFloat(dbEntry.version);

            if (!dbEntry) {
                const err = new Error('Entry not found');
                err.status = 404;
                return next(err);
            }

            const isSameBusinessAddress = req.body.isSameBusinessAddress === "Yes" ? true :
                req.body.isSameBusinessAddress === "No" ? false : undefined;
            const hasUltimateParents = req.body.hasUltimateParents === "Yes" ? true :
                req.body.hasUltimateParents === "No" ? false : undefined;
            const hasImmediateParents = req.body.hasImmediateParents === "Yes" ? true :
                req.body.hasImmediateParents === "No" ? false : undefined;

            const entry = new EntryModel({
                _id: req.params.id,
                version: dbEntry.version,
                company: dbEntry.company,
                entity_details: {
                    ...dbEntry.entity_details.toObject(),
                    TIN: req.body.TIN,
                    totalAnnualGrossCurrency: req.body.totalAnnualGrossCurrency,
                    totalAnnualGross: req.body.totalAnnualGross ? req.body.totalAnnualGross.split(',').join('') : null,
                    isSameBusinessAddress: isSameBusinessAddress,
                    businessAddress: {
                        address_line1: isSameBusinessAddress === true ? "Trident Chambers, Wickhams Cay 1" : req.body.businessAddress1,
                        address_line2: isSameBusinessAddress === true ? "" : req.body.businessAddress2,
                        country: isSameBusinessAddress === true ? "VGB" : req.body.businessAddressCountry
                    },
                    nameOfMNEGroup: req.body.nameOfMNEGroup,
                    hasUltimateParents: hasUltimateParents,
                    ultimateParents: hasUltimateParents === true ? dbEntry.entity_details.ultimateParents : [],
                    hasImmediateParents: hasImmediateParents,
                    immediateParents: hasImmediateParents ? dbEntry.entity_details.immediateParents : [],
                    
                }
            });

            const customErrors = await entityDetailsValidator.validate(entry);

            if (customErrors.length > 0) {
                const entityDetailsInfo = await getDefaultEntityDetailsData(settings.substance_form_steps.FINANCIAL_PERIOD_FORM, dbEntry);

                let renderView = getViewByVersion(entryVersion, 'Entity-Details');
                return res.render(renderView, {
                    entry: entry,
                    company: req.session.company,
                    hasError: true,
                    validationErrors: customErrors,
                    user: req.user,
                    messages: req.session.messages,
                    scheduledSubmission: entityDetailsInfo.scheduledSubmission && (entry.entity_details ? entry.entity_details.financial_period_ends > new Date() : false),
                    isSameITADate: entityDetailsInfo.isSameITADate,
                    previousEndDate: entityDetailsInfo.previousEndDate,
                    hasApplicationITADate: !!entityDetailsInfo.applicationITADate,
                    applicationITADate: entityDetailsInfo.applicationITADate ? entityDetailsInfo.applicationITADate : entry.entity_details.date_of_application_ITA,
                    currencies: CURRENCIES,
                    countries: COUNTRIES
                });
            }

            //save the entry
            await EntryModel.findOneAndUpdate({ _id: req.params.id, company: sessData.company.code }, entry, 
                { runValidators: true, context: 'query' });
            
            res.redirect('/substance/entry/' + req.params.id + '/relevant-activities');
        }
    } catch (e) {
        console.log(e);
        next(e);
    }
}

exports.saveTaxResidency = [
    (req, res, next) => {
        try {
            const sessData = req.session;

            if (req.body.submit === settings.settings.previous_page_action) {
                res.redirect('/substance/entry/' + req.params.id + '/relevant-activities');
            } else {

                EntryModel.findById(req.params.id, function (err, dbEntry) {
                    if (err) { return next(err); }
                    if (dbEntry == null) { // No results.
                        err = new Error('Entry not found');
                        err.status = 404;
                        return next(err);
                    }

                    let entry = new EntryModel({
                        _id: req.params.id,
                        version: dbEntry.version,
                        tax_residency: {
                            resident_in_BVI: req.body.ResidencyOutsideBVIYesOrNo === "Yes" ? false :
                                req.body.ResidencyOutsideBVIYesOrNo === "No" ? true : undefined,
                            entity_jurisdiction: req.body.EntityJurisdiction,
                            foreign_tax_id_number: req.body.foreign_tax_id_number,
                            MNE_group_name: req.body.MNE_group_name,
                            evidence_type: req.body.evidenceType,
                            have_parent_entity: req.body.haveParentEntityYesOrNo === "Yes" ? true :
                                req.body.haveParentEntityYesOrNo === "No" ? false : undefined,
                            have_parent_entity_not_answered:  !req.body.haveParentEntityYesOrNo,
                            parent_entity_name: req.body.haveParentEntityYesOrNo === "Yes" ? req.body.parent_entity_name : '',
                            parent_entity_alternative_name: req.body.haveParentEntityYesOrNo === "Yes" ? req.body.parent_entity_alternative_name : '',
                            parent_entity_jurisdiction: req.body.haveParentEntityYesOrNo === "Yes" ? req.body.parent_entity_jurisdiction : '',
                            parent_entity_incorporation_number: req.body.haveParentEntityYesOrNo === "Yes" ? req.body.parent_entity_incorporation_number : '',
                        }
                    });



                    if (dbEntry.tax_residency) {
                        //restore file uploads
                        const evidenceFiles = [...dbEntry.tax_residency?.evidence_non_residency || [],
                        ...dbEntry.tax_residency?.evidence_provisional_treatment_non_residency || []];

                        entry.tax_residency.evidence_non_residency = entry.tax_residency.evidence_type === "non residency" ?
                            evidenceFiles : [];
                        entry.tax_residency.evidence_provisional_treatment_non_residency = entry.tax_residency.evidence_type === "provisional treatment" ?
                            evidenceFiles : [];
                    }

                    if ((dbEntry.tax_residency?.resident_in_BVI === true && dbEntry.relevant_activities) && entry.tax_residency?.resident_in_BVI === false) {
                        entry = {
                            _id: entry._id,
                            version: dbEntry.version,
                            tax_residency: entry.tax_residency,
                            $unset: {
                                banking_business: 1,
                                insurance_business: 1,
                                fund_management_business: 1,
                                finance_leasing_business: 1,
                                headquarters_business: 1,
                                shipping_business: 1,
                                holding_business: 1,
                                intellectual_property_business: 1,
                                service_centre_business: 1,
                            }
                        }
                    }

                    const customErrors = taxResidencyValidator.validate(entry);

                    if (customErrors.length > 0) {
                        let renderView = getViewByVersion(entry.version, 'Tax-Residency');
                        return res.render(renderView, {
                            title: 'Substance submission form',
                            company: sessData.company,
                            entry: entry,
                            hasError: true,
                            validationErrors: customErrors,
                            user: req.user,
                            messages: req.session.messages,
                        });
                    } else {
                        //save the entry

                        EntryModel.findOneAndUpdate({ _id: req.params.id, company: sessData.company.code }, entry, { new: true }, function (err, updatedEntry) {
                            if (err) { return next(err); }

                            if (!updatedEntry){
                                console.error("Entry not found for update tax residency: ", updatedEntry._id);
                                err = new Error('Entry not found');
                                err.status = 404;
                                return next(err);
                            }

                            // Successful - redirect to payment if necessary
                            if (updatedEntry.tax_residency?.resident_in_BVI === true && updatedEntry.relevant_activities.none?.selected !== true) {
                                GoNextRelevantActivity(updatedEntry, '', res);
                            } else {
                                res.redirect('/substance/entry/' + req.params.id + '/supporting-details');
                            }

                        });
                    }
                })
            }
        }catch(e){
            console.log(e);
            next(e);
        }
    }
] 

exports.saveRelevantActivities = [
    (req, res, next) => {
        try{
            const sessData = req.session;
            
            EntryModel.findById(req.params.id, function (err, dbEntry) {
                if (err) { return next(err); }
                if (dbEntry == null) { // No results.
                    err = new Error('Entry not found');
                    err.status = 404;
                    return next(err);
                }

                const entryVersion = parseFloat(dbEntry.version);

                if (req.body.submit == settings.settings.previous_page_action) {
                    if (entryVersion < 5){
                        return res.redirect('/substance/entry/' + req.params.id + '/financial-period');
                    }else{
                        return res.redirect('/substance/entry/' + req.params.id + '/entity-details');
                    }
                        
                } 
                const entry = new EntryModel({
                    _id: req.params.id,
                    version: dbEntry.version,
                    relevant_activities: {
                        banking_business: {
                            selected: (req.body.BankingBusinessCheckBox == "on"),
                            part_of_financial_period: (req.body.BankingBusinessPart == null ? null : (req.body.BankingBusinessPart == "Yes")),
                            financial_periods: null
                        },
                        insurance_business: {
                            selected: (req.body.InsuranceBusinessCheckBox == "on"),
                            part_of_financial_period: (req.body.InsuranceBusinessPart == null ? null : (req.body.InsuranceBusinessPart == "Yes")),
                            financial_periods: null
                        },
                        fund_management_business: {
                            selected: (req.body.FundManagementBusinessCheckBox == "on"),
                            part_of_financial_period: (req.body.FundManagementBusinessPart == null ? null : (req.body.FundManagementBusinessPart == "Yes")),
                            financial_periods: null
                        },
                        finance_leasing_business: {
                            selected: (req.body.FinanceAndLeasingBusinessCheckBox == "on"),
                            part_of_financial_period: (req.body.FinanceAndLeasingBusinessPart == null ? null : (req.body.FinanceAndLeasingBusinessPart == "Yes")),
                            financial_periods: null
                        },
                        headquarters_business: {
                            selected: (req.body.HeadquartersBusinessCheckBox == "on"),
                            part_of_financial_period: (req.body.HeadquartersBusinessPart == null ? null : (req.body.HeadquartersBusinessPart == "Yes")),
                            financial_periods: null
                        },
                        shipping_business: {
                            selected: (req.body.ShippingBusinessCheckBox == "on"),
                            part_of_financial_period: (req.body.ShippingBusinessPart == null ? null : (req.body.ShippingBusinessPart == "Yes")),
                            financial_periods: null
                        },
                        holding_business: {
                            selected: (req.body.HoldingBusinessCheckBox == "on"),
                            part_of_financial_period: (req.body.HoldingBusinessPart == null ? null : (req.body.HoldingBusinessPart == "Yes")),
                            financial_periods: null
                        },
                        intellectual_property_business: {
                            selected: (req.body.IntellectualPropertyBusinessCheckBox == "on"),
                            part_of_financial_period: (req.body.IntellectualPropertyBusinessPart == null ? null : (req.body.IntellectualPropertyBusinessPart == "Yes")),
                            financial_periods: null
                        },
                        service_centre_business: {
                            selected: (req.body.DistributionAndServiceCheckBox == "on"),
                            part_of_financial_period: (req.body.DistributionAndServicePart == null ? null : (req.body.DistributionAndServicePart == "Yes")),
                            financial_periods: null
                        },
                        none: {
                            selected: (req.body.NoneCheckBox == "on"),
                            part_of_financial_period: (req.body.NonePart == null ? null : (req.body.NonePart == "Yes")),
                            financial_periods: null
                        }
                    }
                }).toObject();


                if (entry.relevant_activities.none.selected) {
                    entry.relevant_activities.evidence_none_activities = dbEntry.relevant_activities?.evidence_none_activities?.length > 0 ?
                        dbEntry.relevant_activities.evidence_none_activities : [];

                    if (entry.relevant_activities.none.part_of_financial_period != null) {
                        entry.relevant_activities.none.financial_periods = createFinancialPeriods(dbEntry, entry.relevant_activities.none.part_of_financial_period, req.body.NonePartStartDate, req.body.NonePartEndDate);
                    }
                    entry["$unset"] = {
                        tax_residency: 1,
                        banking_business: 1,
                        insurance_business: 1,
                        fund_management_business: 1,
                        finance_leasing_business: 1,
                        headquarters_business: 1,
                        shipping_business: 1,
                        holding_business: 1,
                        intellectual_property_business: 1,
                        service_centre_business: 1,
                    };
                } else {
                    entry["$unset"] = {}
                    // check if entry before save was none and reset tax residency
                    if (dbEntry?.relevant_activities?.none?.selected){
                        entry["$unset"]['tax_residency'] = 1;
                    }

                    if (entry.relevant_activities.banking_business.selected && entry.relevant_activities.banking_business.part_of_financial_period != null)
                        entry.relevant_activities.banking_business.financial_periods = createFinancialPeriods(dbEntry, entry.relevant_activities.banking_business.part_of_financial_period, req.body.BankingBusinessStartDate, req.body.BankingBusinessEndDate);

                    if (entry.relevant_activities.insurance_business.selected && entry.relevant_activities.insurance_business.part_of_financial_period != null)
                        entry.relevant_activities.insurance_business.financial_periods = createFinancialPeriods(dbEntry, entry.relevant_activities.insurance_business.part_of_financial_period, req.body.InsuranceBusinessStartDate, req.body.InsuranceBusinessEndDate);

                    if (entry.relevant_activities.fund_management_business.selected && entry.relevant_activities.fund_management_business.part_of_financial_period != null)
                        entry.relevant_activities.fund_management_business.financial_periods = createFinancialPeriods(dbEntry, entry.relevant_activities.fund_management_business.part_of_financial_period, req.body.FundManagementBusinesStartDate, req.body.FundManagementBusinesEndDate);

                    if (entry.relevant_activities.finance_leasing_business.selected && entry.relevant_activities.finance_leasing_business.part_of_financial_period != null)
                        entry.relevant_activities.finance_leasing_business.financial_periods = createFinancialPeriods(dbEntry, entry.relevant_activities.finance_leasing_business.part_of_financial_period, req.body.FinanceAndLeasingBusinessStartDate, req.body.FinanceAndLeasingBusinessEndDate);

                    if (entry.relevant_activities.headquarters_business.selected && entry.relevant_activities.headquarters_business.part_of_financial_period != null)
                        entry.relevant_activities.headquarters_business.financial_periods = createFinancialPeriods(dbEntry, entry.relevant_activities.headquarters_business.part_of_financial_period, req.body.HeadquartersBusinessStartDate, req.body.HeadquartersBusinessEndDate);

                    if (entry.relevant_activities.shipping_business.selected && entry.relevant_activities.shipping_business.part_of_financial_period != null)
                        entry.relevant_activities.shipping_business.financial_periods = createFinancialPeriods(dbEntry, entry.relevant_activities.shipping_business.part_of_financial_period, req.body.ShippingBusinessStartDate, req.body.ShippingBusinessEndDate);

                    if (entry.relevant_activities.holding_business.selected && entry.relevant_activities.holding_business.part_of_financial_period != null)
                        entry.relevant_activities.holding_business.financial_periods = createFinancialPeriods(dbEntry, entry.relevant_activities.holding_business.part_of_financial_period, req.body.HoldingBusinessStartDate, req.body.HoldingBusinessEndDate);

                    if (entry.relevant_activities.intellectual_property_business.selected && entry.relevant_activities.intellectual_property_business.part_of_financial_period != null)
                        entry.relevant_activities.intellectual_property_business.financial_periods = createFinancialPeriods(dbEntry, entry.relevant_activities.intellectual_property_business.part_of_financial_period, req.body.IntellectualPropertyBusinessStartDate, req.body.IntellectualPropertyBusinessEndDate);

                    if (entry.relevant_activities.service_centre_business.selected && entry.relevant_activities.service_centre_business.part_of_financial_period != null)
                        entry.relevant_activities.service_centre_business.financial_periods = createFinancialPeriods(dbEntry, entry.relevant_activities.service_centre_business.part_of_financial_period, req.body.DistributionAndServiceStartDate, req.body.DistributionAndServiceEndDate);

                    // clear the data if exists for unchecked activities
                    Object.entries(entry.relevant_activities).forEach(([key, activity]) => {
  

                        if (["_id", "none", "none_remarks", "evidence_none_activities"].includes(key)) {
                            return;
                        }

                        if (!activity.selected && dbEntry[key]) {
                            entry["$unset"][key] = 1
                        }
                    });

                }


                const customErrors = relevantActivitiesValidator.validate(entry, dbEntry);

                if (customErrors.length > 0) {
                    entry.company_data = dbEntry.company_data;
                    entry.entity_details = dbEntry.entity_details;

                    let renderView = getViewByVersion(dbEntry.version, 'Relevant-Activities');
                    return res.render(renderView, {
                        entry: entry,
                        company: sessData.company,
                        hasError: true,
                        validationErrors: customErrors,
                        user: req.user,
                        messages: req.session.messages
                    });
                } else {

                    EntryModel.findOneAndUpdate({ _id: req.params.id, company: sessData.company.code }, entry, { new: true }, function (err) {
                        if (err) { return next(err); }
                        // Successful - redirect to payment if necessary
                        if (entry.relevant_activities.none.selected && entry.relevant_activities.none.part_of_financial_period != null) {
                            res.redirect('/substance/entry/' + req.params.id + '/supporting-details');
                        } else {
                            res.redirect('/substance/entry/' + req.params.id + '/tax-residency');
                        }

                    });
                }
            });

        }catch(e){
            console.log(e);
            next(e);
        }
    }
]


exports.saveBankingBusiness = [
     (req, res, next) => {
        try{
            const sessData = req.session;

            EntryModel.findById(req.params.id, function (err, dbEntry) {
                if (err) { return next(err); }
                if (dbEntry == null) { // No results.
                    err = new Error('Entry not found');
                    err.status = 404;
                    return next(err);
                }

                if (req.body.submit == settings.settings.previous_page_action) {
                    GoPreviousRelevantActivity(dbEntry, 'banking-business', res);
                } else {

                   const entry = new EntryModel({
                        _id: req.params.id,
                        version: dbEntry.version,
                        banking_business: createBusinessObject(req, dbEntry.banking_business, 
                            dbEntry.version, settings.substance_business_types.BANKING)
                    });
                    let customErrors = businessValidator.validate(entry.banking_business,
                        settings.substance_business_types.BANKING,
                        dbEntry.version);

                    if (customErrors.length > 0) {
                        const entryCurrency = dbEntry.entity_details.totalAnnualGrossCurrency;
                        let selectedCurrency;
                        if (entryCurrency) {
                            selectedCurrency = CURRENCIES.find((currency) => currency.cc === entryCurrency)
                        }

                        let renderView = getViewByVersion(dbEntry.version, 'Business');
                        return res.render(renderView, {
                            title: 'Banking Business',
                            data: entry.banking_business,
                            company: sessData.company,
                            entryId: dbEntry.id,
                            entryVersion: dbEntry.version, 
                            hasError: true,
                            validationErrors: customErrors,
                            user: req.user,
                            messages: req.session.messages,
                            subtitle: settings.subtitle_banking_business,
                            activityType: settings.substance_business_types.BANKING,
                            isNotActivityHldOrIp: true,
                            currencies: CURRENCIES,
                            selectedCurrency: selectedCurrency,
                            defaultCurrency: DEFAULT_CURRENCY,
                            cigaCodes: getCIGACodeList(settings.substance_business_types.BANKING)
                        });
                    } else {
                        //save the entry
                        EntryModel.findOneAndUpdate({ _id: req.params.id, company: sessData.company.code }, entry, { new: true }, function (err, savedEntry) {
                            if (err) { return next(err); }
                            // Successful - redirect to payment if necessary
                            GoNextRelevantActivity(savedEntry, 'banking-business', res);
                        });
                    }
                }
            });
        }catch(e){
            console.log(e);
            next(e);
        }
     }
]

exports.saveInsuranceBusiness = [
    //body('NumberOfBoardMeetings', 'NumberOfBoardMeetings Value must not be empty.').isLength({ min: 1 }).trim(),
    (req, res, next) => {
        try {
            const sessData = req.session;

            EntryModel.findById(req.params.id, function (err, dbEntry) {
                if (err) { return next(err); }
                if (dbEntry == null) { // No results.
                    err = new Error('Entry not found');
                    err.status = 404;
                    return next(err);
                }

                if (req.body.submit == settings.settings.previous_page_action) {
                    GoPreviousRelevantActivity(dbEntry, 'insurance-business', res);
                } else {


                    const entry = new EntryModel({
                        _id: req.params.id,
                        version: dbEntry.version,
                        insurance_business: createBusinessObject(req, dbEntry.insurance_business, 
                            dbEntry.version, settings.substance_business_types.INSURANCE)
                    });

                    let customErrors = businessValidator.validate(entry.insurance_business,
                        settings.substance_business_types.INSURANCE,
                        dbEntry.version);

                    if (customErrors.length > 0) {
                        const entryCurrency = dbEntry.entity_details.totalAnnualGrossCurrency;
                        let selectedCurrency;
                        if (entryCurrency) {
                            selectedCurrency = CURRENCIES.find((currency) => currency.cc === entryCurrency)
                        }
                        let renderView = getViewByVersion(dbEntry.version, 'Business');
                        return res.render(renderView, { 
                            title: 'Insurance Business', 
                            data: entry.insurance_business,
                            entryId: dbEntry.id, 
                            entryVersion: dbEntry.version, 
                            hasError: true, 
                            validationErrors: 
                            customErrors, 
                            user: req.user, 
                            messages: req.session.messages, 
                            company: sessData.company,
                            activityType: settings.substance_business_types.INSURANCE,
                            isNotActivityHldOrIp: true,
                            currencies: CURRENCIES,
                            selectedCurrency: selectedCurrency,
                            defaultCurrency: DEFAULT_CURRENCY,
                            cigaCodes: getCIGACodeList(settings.substance_business_types.INSURANCE)
                        });
                    } else {
                        //save the entry

                        EntryModel.findOneAndUpdate({ _id: req.params.id, company: sessData.company.code }, entry, { new: true }, function (err, savedEntry) {
                            if (err) { return next(err); }
                            // Successful - redirect to payment if necessary
                            GoNextRelevantActivity(savedEntry, 'insurance-business', res);
                        });
                    }
                }
            });
        }catch(e){
            console.log(e);
            next(e);
        }
    }
]

exports.saveFundManagementBusiness = [
    (req, res, next) => {
        try{
            const sessData = req.session;

            EntryModel.findById(req.params.id, function (err, dbEntry) {
                if (err) { return next(err); }
                if (dbEntry == null) { // No results.
                    err = new Error('Entry not found');
                    err.status = 404;
                    return next(err);
                }

                if (req.body.submit == settings.settings.previous_page_action) {
                    GoPreviousRelevantActivity(dbEntry, 'fund-management-business', res);
                } else {

                    const entry = new EntryModel({
                        _id: req.params.id,
                        version: dbEntry.version,
                        fund_management_business: createBusinessObject(req, dbEntry.fund_management_business, 
                            dbEntry.version, settings.substance_business_types.FUND_MANAGEMENT)
                    });

                    let customErrors = businessValidator.validate(entry.fund_management_business,
                        settings.substance_business_types.FUND_MANAGEMENT,
                        dbEntry.version);

                    if (customErrors.length > 0) {
                        const entryCurrency = dbEntry.entity_details.totalAnnualGrossCurrency;
                        let selectedCurrency;
                        if (entryCurrency) {
                            selectedCurrency = CURRENCIES.find((currency) => currency.cc === entryCurrency)
                        }
                        let renderView = getViewByVersion(dbEntry.version, 'Business');
                        return res.render(renderView, { 
                            title: 'Fund Management Business', 
                            data: entry.fund_management_business, 
                            entryId: dbEntry.id, 
                            entryVersion: dbEntry.version, 
                            hasError: true, 
                            validationErrors: 
                            customErrors, user: req.user, 
                            messages: req.session.messages,
                            company: sessData.company,
                            activityType: settings.substance_business_types.FUND_MANAGEMENT,
                            isNotActivityHldOrIp: true,
                            currencies: CURRENCIES,
                            selectedCurrency: selectedCurrency,
                            defaultCurrency: DEFAULT_CURRENCY,
                            cigaCodes: getCIGACodeList(settings.substance_business_types.FUND_MANAGEMENT)
                        });
                    } else {
                        //save the entry
                        EntryModel.findOneAndUpdate({ _id: req.params.id, company: sessData.company.code }, entry, { new: true }, function (err, savedEntry) {
                            if (err) { return next(err); }
                            // Successful - redirect to payment if necessary
                            GoNextRelevantActivity(savedEntry, 'fund-management-business', res);

                        });
                    }
                }
            });
        }catch(e){
            console.log(e);
            next(e);
        }
    }
]

exports.saveFinanceLeasingBusiness = [
    (req, res, next) => {
        try{
            const sessData = req.session;

            EntryModel.findById(req.params.id, function (err, dbEntry) {
                if (err) { return next(err); }
                if (dbEntry == null) { // No results.
                    err = new Error('Entry not found');
                    err.status = 404;
                    return next(err);
                }

                if (req.body.submit == settings.settings.previous_page_action) {
                    GoPreviousRelevantActivity(dbEntry, 'finance-leasing-business', res);
                } else {

                    const entry = new EntryModel({
                        _id: req.params.id,
                        version: dbEntry.version,
                        finance_leasing_business: createBusinessObject(req, dbEntry.finance_leasing_business, 
                            dbEntry.version, settings.substance_business_types.FINANCE_LEASING)
                    });

                    let customErrors = businessValidator.validate(entry.finance_leasing_business,
                        settings.substance_business_types.FINANCE_LEASING,
                        dbEntry.version);

                    if (customErrors.length > 0) {
                        const entryCurrency = dbEntry.entity_details.totalAnnualGrossCurrency;
                        let selectedCurrency;
                        if (entryCurrency) {
                            selectedCurrency = CURRENCIES.find((currency) => currency.cc === entryCurrency)
                        }
                        let renderView = getViewByVersion(dbEntry.version, 'Business');
                        return res.render(renderView, { 
                            title: 'Finance Leasing Business', 
                            data: entry.finance_leasing_business, 
                            entryId: dbEntry.id, 
                            entryVersion: dbEntry.version, 
                            hasError: true, 
                            validationErrors: 
                            customErrors, user: req.user, 
                            messages: req.session.messages, 
                            company: sessData.company ,
                            activityType: settings.substance_business_types.FINANCE_LEASING,
                            isNotActivityHldOrIp: true,
                            currencies: CURRENCIES,
                            selectedCurrency: selectedCurrency,
                            defaultCurrency: DEFAULT_CURRENCY,
                            cigaCodes: getCIGACodeList(settings.substance_business_types.FINANCE_LEASING)
                        });
                    } else {
                        //save the entry
                        EntryModel.findOneAndUpdate({ _id: req.params.id, company: sessData.company.code }, entry, { new: true }, function (err, savedEntry) {
                            if (err) { return next(err); }
                            // Successful - redirect to payment if necessary
                            GoNextRelevantActivity(savedEntry, 'finance-leasing-business', res);
                        });
                    }
                }

            });
        }catch(e){
            console.log(e);
            next(e);
        }

    }
]

exports.saveHeadquartersBusiness = [
    (req, res, next) => {
        try{
            const sessData = req.session;

            EntryModel.findById(req.params.id, function (err, dbEntry) {
                if (err) { return next(err); }
                if (dbEntry == null) { // No results.
                    err = new Error('Entry not found');
                    err.status = 404;
                    return next(err);
                }

                if (req.body.submit == settings.settings.previous_page_action) {
                    GoPreviousRelevantActivity(dbEntry, 'headquarters-business', res);
                } else {

                    const entry = new EntryModel({
                        _id: req.params.id,
                        version: dbEntry.version,
                        headquarters_business: createBusinessObject(req, dbEntry.headquarters_business, 
                            dbEntry.version, settings.substance_business_types.HEADQUARTERS)
                    });

                    let customErrors = businessValidator.validate(entry.headquarters_business,
                        settings.substance_business_types.HEADQUARTERS,
                        dbEntry.version);

                    if (customErrors.length > 0) {
                        const entryCurrency = dbEntry.entity_details.totalAnnualGrossCurrency;
                        let selectedCurrency;
                        if (entryCurrency) {
                            selectedCurrency = CURRENCIES.find((currency) => currency.cc === entryCurrency)
                        }
                        let renderView = getViewByVersion(dbEntry.version, 'Business');
                        return res.render(renderView, { 
                            title: 'Headquarters Business', 
                            data: entry.headquarters_business, 
                            entryId: dbEntry.id, 
                            entryVersion: dbEntry.version, 
                            hasError: true, 
                            validationErrors: customErrors, 
                            user: req.user,
                            messages: req.session.messages, 
                            company: sessData.company,
                            activityType: settings.substance_business_types.HEADQUARTERS,
                            isNotActivityHldOrIp: true,
                            currencies: CURRENCIES,
                            selectedCurrency: selectedCurrency,
                            defaultCurrency: DEFAULT_CURRENCY,
                            cigaCodes: getCIGACodeList(settings.substance_business_types.HEADQUARTERS)
                        });
                    } else {
                        //save the entry

                        EntryModel.findOneAndUpdate({ _id: req.params.id, company: sessData.company.code }, entry, { new: true }, function (err, savedEntry) {
                            if (err) { return next(err); }
                            // Successful - redirect to payment if necessary
                            GoNextRelevantActivity(savedEntry, 'headquarters-business', res);
                        });
                    }
                }
            });
        }catch(e){
            console.log(e);
            next(e);
        }
    }
]

exports.saveShippingBusiness = [
    (req, res, next) => {
        try{
            const sessData = req.session;

            EntryModel.findById(req.params.id, function (err, dbEntry) {
                if (err) { return next(err); }
                if (dbEntry == null) { // No results.
                    err = new Error('Entry not found');
                    err.status = 404;
                    return next(err);
                }


                if (req.body.submit == settings.settings.previous_page_action) {
                    GoPreviousRelevantActivity(dbEntry, 'shipping-business', res);
                } else {

                    const entry = new EntryModel({
                        _id: req.params.id,
                        version: dbEntry.version,
                        shipping_business: createBusinessObject(req, dbEntry.shipping_business,
                            dbEntry.version, settings.substance_business_types.SHIPPING)
                    });

                    let customErrors = businessValidator.validate(entry.shipping_business,
                        settings.substance_business_types.SHIPPING,
                        dbEntry.version);

                    if (customErrors.length > 0) {
                        const entryCurrency = dbEntry.entity_details.totalAnnualGrossCurrency;
                        let selectedCurrency;
                        if (entryCurrency) {
                            selectedCurrency = CURRENCIES.find((currency) => currency.cc === entryCurrency)
                        }
                        let renderView = getViewByVersion(dbEntry.version, 'Business');
                        return res.render(renderView, { 
                            title: 'Shipping Business', 
                            data: entry.shipping_business, 
                            entryId: dbEntry.id,
                            entryVersion: dbEntry.version, 
                            hasError: true, 
                            validationErrors: customErrors, 
                            user: req.user, 
                            messages: req.session.messages, 
                            company: sessData.company,
                            activityType: settings.substance_business_types.SHIPPING,
                            isNotActivityHldOrIp: true,
                            currencies: CURRENCIES,
                            selectedCurrency: selectedCurrency,
                            defaultCurrency: DEFAULT_CURRENCY,
                            cigaCodes: getCIGACodeList(settings.substance_business_types.SHIPPING)
                        });
                    } else {
                        //save the entry
                        EntryModel.findOneAndUpdate({ _id: req.params.id, company: sessData.company.code }, entry, { new: true }, function (err, savedEntry) {
                            if (err) { return next(err); }
                            // Successful - redirect to payment if necessary
                            GoNextRelevantActivity(savedEntry, 'shipping-business', res);
                        });
                    }
                }
            });
        }catch(e){
            console.log(e);
            next(e);
        }
    }
]

exports.saveIntellectualPropertyBusiness = [
    (req, res, next) => {
        try {
            const sessData = req.session;

            EntryModel.findById(req.params.id, function (err, dbEntry) {
                if (err) { return next(err); }
                if (dbEntry == null) { // No results.
                    err = new Error('Entry not found');
                    err.status = 404;
                    return next(err);
                }


                if (req.body.submit == settings.settings.previous_page_action) {
                    GoPreviousRelevantActivity(dbEntry, 'intellectual-property-business', res);
                } else {
                    const entry = new EntryModel({
                        _id: req.params.id,
                        version: dbEntry.version,
                        intellectual_property_business: createBusinessObject(req, dbEntry.intellectual_property_business,
                            dbEntry.version, settings.substance_business_types.INTELLECTUAL_PROPERTY)
                    });

                    let customErrors = businessValidator.validate(entry.intellectual_property_business, 
                        settings.substance_business_types.INTELLECTUAL_PROPERTY,
                        dbEntry.version);

                    if (customErrors.length > 0) {
                        const entryCurrency = dbEntry.entity_details.totalAnnualGrossCurrency;
                        let selectedCurrency;
                        if (entryCurrency) {
                            selectedCurrency = CURRENCIES.find((currency) => currency.cc === entryCurrency)
                        }
                        let renderView = getViewByVersion(dbEntry.version, 'Business');
                        return res.render(renderView, { 
                            title: 'Intellectual Property Business', 
                            data: entry.intellectual_property_business, 
                            entryId: dbEntry.id, 
                            entryVersion: dbEntry.version, 
                            hasError: true, 
                            validationErrors: customErrors, user: 
                            req.user, 
                            messages: req.session.messages, 
                            company: sessData.company,
                            activityType: settings.substance_business_types.INTELLECTUAL_PROPERTY,
                            isNotActivityHldOrIp: false,
                            currencies: CURRENCIES,
                            selectedCurrency: selectedCurrency,
                            defaultCurrency: DEFAULT_CURRENCY,
                            cigaCodes: getCIGACodeList(settings.substance_business_types.INTELLECTUAL_PROPERTY)
                    });
                    } else {
                        //save the entry

                        EntryModel.findOneAndUpdate({ _id: req.params.id, company: sessData.company.code }, entry, { new: true }, function (err, savedEntry) {
                            if (err) { return next(err); }
                            // Successful - redirect to payment if necessary
                            GoNextRelevantActivity(savedEntry, 'intellectual-property-business', res);
                        });
                    }
                }
            });
        }catch(e){
            console.log(e);
            next(e);
        }

    }
]

exports.saveHoldingBusiness = [
    (req, res, next) => {
        try{
            const sessData = req.session;

            EntryModel.findById(req.params.id, function (err, dbEntry) {
                if (err) { return next(err); }
                if (dbEntry == null) { // No results.
                    err = new Error('Entry not found');
                    err.status = 404;
                    return next(err);
                }


                if (req.body.submit == settings.settings.previous_page_action) {
                    GoPreviousRelevantActivity(dbEntry, 'holding-business', res);
                } else {
                    const entry = new EntryModel({
                        _id: req.params.id,
                        version: dbEntry.version,
                        holding_business: createBusinessObject(req, dbEntry.holding_business, 
                            dbEntry.version, settings.substance_business_types.HOLDING)
                    });

                    let customErrors = businessValidator.validate(entry.holding_business, 
                        settings.substance_business_types.HOLDING,
                        dbEntry.version);

                    if (customErrors.length > 0) {
                        const entryCurrency = dbEntry.entity_details.totalAnnualGrossCurrency;
                        let selectedCurrency;
                        if (entryCurrency) {
                            selectedCurrency = CURRENCIES.find((currency) => currency.cc === entryCurrency)
                        }
                        let renderView = getViewByVersion(dbEntry.version, 'Business');
                        return res.render(renderView, { 
                            title: 'Holding Business (Pure Equity Holding entities)', 
                            data: entry.holding_business, 
                            entryId: dbEntry.id, 
                            entryVersion: dbEntry.version, 
                            hasError: true,
                            validationErrors: customErrors,
                            user: req.user, 
                            messages: req.session.messages, 
                            company: sessData.company,
                            activityType: settings.substance_business_types.HOLDING,
                            isNotActivityHldOrIp: false,
                            currencies: CURRENCIES,
                            selectedCurrency: selectedCurrency,
                            defaultCurrency: DEFAULT_CURRENCY,
                        });
                    } else {
                        //save the entry

                        EntryModel.findOneAndUpdate({ _id: req.params.id, company: sessData.company.code }, entry, { new: true }, function (err, savedEntry) {
                            if (err) { return next(err); }
                            // Successful - redirect to payment if necessary
                            GoNextRelevantActivity(savedEntry, 'holding-business', res);
                        });
                    }
                }
            });
        }catch(e){
            console.log(e);
            next(e);
        }
    }
]

exports.saveServiceCentreBusiness = [
    (req, res, next) => {
        try{
            const sessData = req.session;

            EntryModel.findById(req.params.id, function (err, dbEntry) {
                if (err) { return next(err); }
                if (dbEntry == null) { // No results.
                    err = new Error('Entry not found');
                    err.status = 404;
                    return next(err);
                }

                if (req.body.submit == settings.settings.previous_page_action) {
                    GoPreviousRelevantActivity(dbEntry, 'service-centre-business', res);
                } else {

                    const entry = new EntryModel({
                        _id: req.params.id,
                        version: dbEntry.version,
                        service_centre_business: createBusinessObject(req, dbEntry.service_centre_business,
                            dbEntry.version, settings.substance_business_types.DISTRIBUTION_SERVICE_CENTRE)
                    });

                    let customErrors = businessValidator.validate(entry.service_centre_business,
                        settings.substance_business_types.DISTRIBUTION_SERVICE_CENTRE,
                        dbEntry.version);

                    if (customErrors.length > 0) {
                        const entryCurrency = dbEntry.entity_details.totalAnnualGrossCurrency;
                        let selectedCurrency;
                        if (entryCurrency) {
                            selectedCurrency = CURRENCIES.find((currency) => currency.cc === entryCurrency)
                        }
                        let renderView = getViewByVersion(dbEntry.version, 'Business');
                        return  res.render(renderView, { 
                            title: 'Distribution and Service Centre Business', 
                            data: entry.service_centre_business, 
                            entryId: dbEntry.id, hasError: true,
                            entryVersion: dbEntry.version,  
                            validationErrors: customErrors,
                            user: req.user, messages: req.session.messages, 
                            company: sessData.company,
                            activityType: settings.substance_business_types.DISTRIBUTION_SERVICE_CENTRE,
                            isNotActivityHldOrIp: true,
                            currencies: CURRENCIES,
                            selectedCurrency: selectedCurrency,
                            defaultCurrency: DEFAULT_CURRENCY,
                            cigaCodes: getCIGACodeList(settings.substance_business_types.DISTRIBUTION_SERVICE_CENTRE)
                            
                        });
                    } else {
                        //save the entry

                        EntryModel.findOneAndUpdate({ _id: req.params.id, company: sessData.company.code }, entry, { new: true }, function (err) {
                            if (err) { return next(err); }
                            // Successful - redirect to payment if necessary
                            res.redirect('/substance/entry/' + req.params.id + '/supporting-details');
                        });
                    }
                }
            });
        }catch(e){
            console.log(e);
            next(e);
        }
    }
]

exports.saveSupportingDetails = [
  (req, res, next) => {
      try {
        const sessData = req.session;

        EntryModel.findById(req.params.id, async function (err, dbEntry) {
          if (err) { return next(err); }
          if (dbEntry == null) { // No results.
              err = new Error('Entry not found');
              err.status = 404;
              return next(err);
          }
          if (req.body.submit === settings.settings.previous_page_action) {
              if (dbEntry.relevant_activities?.none?.selected === true) {
                res.redirect('/substance/entry/' + req.params.id + '/relevant-activities');
              }
              else if (dbEntry.tax_residency?.resident_in_BVI === true && dbEntry.relevant_activities?.none?.selected !== true) {
                GoPreviousRelevantActivity(dbEntry, '', res);
              } else {
                  res.redirect('/substance/entry/' + req.params.id + '/tax-residency');
              }
          } else {

            EntryModel.findById(req.params.id, function (err, dbEntry) {
                if (err) { return next(err); }
                if (dbEntry == null) { // No results.
                    err = new Error('Entry not found');
                    err.status = 404;
                    return next(err);
                }

                let entry = new EntryModel({
                    _id: req.params.id,
                    version: dbEntry.version, 
                    supporting_details: {
                      support_comment: req.body.supportComment,
                    }
                });
                
                let customErrors = supportDetailsValidator.validate(entry, dbEntry);

                if (customErrors.length > 0){
                    let renderView = getViewByVersion(dbEntry.version, 'Supporting-Details');
                    entry.relevant_activities = dbEntry.relevant_activities;
                    return res.render(renderView, {
                        entry: entry,
                        entryId: dbEntry.id, 
                        hasError: true,
                        validationErrors: customErrors,
                        user: req.user, messages: req.session.messages,
                        company: sessData.company
                    });
                }


                if (dbEntry.supporting_details) {
                    //restore file uploads
                    const attachmentFiles = [...dbEntry.supporting_details?.support_attachments || []];

                    entry.supporting_details.support_attachments = attachmentFiles;
                }

                //save the entry

                EntryModel.findOneAndUpdate({ _id: req.params.id, company: sessData.company.code }, entry, { new: true }, function (err, updatedEntry) {
                    if (err || !updatedEntry) { return next(err); }
                    res.redirect('/substance/entry/' + req.params.id + '/confirmation');
                });
            })
        }
      });
      }catch(e){
          console.log(e);
          next(e);
      }
  }
]

exports.saveConfirmation = [
    (req, res, next) => {
        try {
            const sessData = req.session;

            EntryModel.findById(req.params.id, async function (err, dbEntry) {
                if (err) { return next(err); }
                if (dbEntry == null) { // No results.
                    err = new Error('Entry not found');
                    err.status = 404;
                    return next(err);
                }

                if (req.body.submitAction === settings.settings.previous_page_action) {
                    res.redirect('/substance/entry/' + req.params.id + '/supporting-details');
                } else {
                    let status = '';
                    let alreadyPaid = false;
                    let scheduledAt = null;
                    let pre2022Submission = false;
                    let prePaid = false;
                    if (dbEntry.entity_details.financial_period_ends < new Date(2022, 0, 1)) {
                        pre2022Submission = true;
                    }

                    const financialPeriodEndYear = moment(dbEntry.entity_details.financial_period_ends).utc().format('YYYY');
                    const company = await Company.findOne({
                        code: dbEntry.company_data.code, masterclientcode: dbEntry.company_data.masterclientcode,
                        paymentYears: { "$in": [financialPeriodEndYear] }
                    });

                    if (company) {
                        prePaid = true;

                        if (!dbEntry.payment?.payment_received_at) {
                            const currentDate = new Date(new Date().toUTCString());
                            dbEntry.payment = {
                                payment_type: "WIRE TRANSFER",
                                payment_reference:
                                    dbEntry.company_data.masterclientcode +
                                    "-" +
                                    dbEntry.company_data.code +
                                    "-" +
                                    currentDate.getFullYear() +
                                    (currentDate.getMonth() + 1).toString() +
                                    currentDate.getDate().toString(),
                                payment_received_at: currentDate,
                                batchpayment_id: "0",
                                batchpayment_transactionId: "",
                                batchpayment_code: "0",
                                amount: dbEntry.company_data.amount,
                            };
                        }
                    }


                    if (dbEntry.entity_details.financial_period_ends > new Date()) {
                        status = 'SCHEDULED';
                        scheduledAt = moment.utc(dbEntry.entity_details.financial_period_ends).add(1, 'd');
                    } else {
                        if (dbEntry.payment && dbEntry.payment.payment_received_at) {
                            status = 'PAID';
                            alreadyPaid = true;
                            try {
                                if (dbEntry.supporting_details?.support_attachments?.length > 0 ||
                                    dbEntry.banking_business?.support_documents?.length > 0 ||
                                    dbEntry.insurance_business?.support_documents?.length > 0 ||
                                    dbEntry.fund_management_business?.support_documents?.length > 0 ||
                                    dbEntry.finance_leasing_business?.support_documents?.length > 0 ||
                                    dbEntry.headquarters_business?.support_documents?.length > 0 ||
                                    dbEntry.shipping_business?.support_documents?.length > 0 || 
                                    dbEntry.holding_business?.support_documents?.length > 0 || 
                                    dbEntry.intellectual_property_business?.support_documents?.length > 0 ||
                                    dbEntry.service_centre_business?.support_documents?.length > 0) {
                                    let email = mailFormatter.generateClientSubmittedAttachmentsEmail(dbEntry.company_data.name);
                                    await mailController.asyncSend(
                                        process.env.SUPER_USER_EMAIL_GROUP,
                                        'Trident Trust Client Notification',
                                        email.textString,
                                        email.htmlString
                                    );
                                }
                            } catch(ex){
                                //ignore catch
                            }
                        } else {
                            status = 'SUBMITTED';

                        }
                    }

                    if (dbEntry.status === "RE-OPEN" && dbEntry.reopened?.details?.length > 0) {
                        const reopened = dbEntry.reopened.details[dbEntry.reopened.details.length - 1];

                        reopened.resubmitted_at = new Date(new Date().toUTCString());
                        reopened.resubmitted_by = req.user.email;
                        dbEntry.reopened.details[dbEntry.reopened.details.length - 1] = reopened;
                    }


                    const entry = new EntryModel({
                        _id: req.params.id,
                        version: dbEntry.version,
                        status,
                        submitted_by: req.user.email,
                        submitted_at: new Date(new Date().toUTCString()),
                        initial_submit_date: dbEntry.initial_submit_date ? 
                            dbEntry.initial_submit_date : 
                            (dbEntry.submitted_at ? 
                                dbEntry.submitted_at : 
                                new Date(new Date().toUTCString())
                            ),
                        confirmation: {
                            confirmed: (req.body.confirmed && req.body.confirmed == "on"),
                            confirmed_authority: (req.body.confirmed_authority && req.body.confirmed_authority == "on"),
                            confirmed_conditions: (req.body.confirmed_conditions && req.body.confirmed_conditions == "on"),
                            confirmed_payment: (req.body.confirmed_payment && req.body.confirmed_payment == "on"),
                            user_phonenumber: req.body.user_phonenumber,
                            user_fullname: req.body.user_fullname,
                            relation_to_entity: req.body.relation_to_entity,
                            relation_to_entity_other: req.body.relation_to_entity_other
                        },
                        reopened: dbEntry.reopened,
                        scheduled_at: scheduledAt,
                        payment: dbEntry.payment || undefined
                    });
                    const customErrors = confirmationValidator.validate(entry);
                    const scheduledSubmission = await EntryModel.findOne({ 'status': 'SCHEDULED', 'company': dbEntry.company });
                    if (scheduledSubmission && status === 'SCHEDULED') {
                        customErrors.push({ msg: "You have a submission scheduled on a future date. Once this submission is sent, you will be able to schedule a new one." })
                    }

                    if (customErrors.length > 0) {
                        res.render('entry/Confirmation', {
                            company: sessData.company,
                            entry: { ...entry.toObject(), entity_details: dbEntry.entity_details, company_data: dbEntry.company_data },
                            entryVersion: dbEntry.version, 
                            hasError: true,
                            validationErrors: customErrors,
                            user: req.user,
                            alreadyPaid: (dbEntry.payment && dbEntry.payment.payment_received_at),
                            scheduledSubmission: !!(scheduledSubmission && (dbEntry.entity_details ? dbEntry.entity_details.financial_period_ends > new Date() : false)),
                            messages: req.session.messages,
                            prePaid: prePaid,
                            isReopened: dbEntry.reopened?.details?.length > 0

                        });
                    } else {
                        //save the entry
                        EntryModel.findOneAndUpdate({ _id: req.params.id, company: sessData.company.code }, entry, { new: true }, async function (err, savedEntry) {
                            if (err) { return next(err); }
                            try {
                                appInsightsClient.trackEvent({ name: "submission submitted", properties: { email: req.user.email, companycode: savedEntry.company_data.code, submissionId: savedEntry.id } });
                                if (savedEntry.relevant_activities.banking_business && savedEntry.relevant_activities.banking_business.selected) {
                                    appInsightsClient.trackEvent({ name: "relevant activity submitted", properties: { email: req.user.email, companycode: savedEntry.company_data.code, submissionId: savedEntry.id, activity: 'banking business' } });
                                }
                                if (savedEntry.relevant_activities.insurance_business && savedEntry.relevant_activities.insurance_business.selected) {
                                    appInsightsClient.trackEvent({ name: "relevant activity submitted", properties: { email: req.user.email, companycode: savedEntry.company_data.code, submissionId: savedEntry.id, activity: 'insurance business' } });
                                }
                                if (savedEntry.relevant_activities.fund_management_business && savedEntry.relevant_activities.fund_management_business.selected) {
                                    appInsightsClient.trackEvent({ name: "relevant activity submitted", properties: { email: req.user.email, companycode: savedEntry.company_data.code, submissionId: savedEntry.id, activity: 'fund management business' } });
                                }
                                if (savedEntry.relevant_activities.finance_leasing_business && savedEntry.relevant_activities.finance_leasing_business.selected) {
                                    appInsightsClient.trackEvent({ name: "relevant activity submitted", properties: { email: req.user.email, companycode: savedEntry.company_data.code, submissionId: savedEntry.id, activity: 'finance and leasing business' } });
                                }
                                if (savedEntry.relevant_activities.headquarters_business && savedEntry.relevant_activities.headquarters_business.selected) {
                                    appInsightsClient.trackEvent({ name: "relevant activity submitted", properties: { email: req.user.email, companycode: savedEntry.company_data.code, submissionId: savedEntry.id, activity: 'headquarters business' } });
                                }
                                if (savedEntry.relevant_activities.shipping_business && savedEntry.relevant_activities.shipping_business.selected) {
                                    appInsightsClient.trackEvent({ name: "relevant activity submitted", properties: { email: req.user.email, companycode: savedEntry.company_data.code, submissionId: savedEntry.id, activity: 'shipping business' } });
                                }
                                if (savedEntry.relevant_activities.holding_business && savedEntry.relevant_activities.holding_business.selected) {
                                    appInsightsClient.trackEvent({ name: "relevant activity submitted", properties: { email: req.user.email, companycode: savedEntry.company_data.code, submissionId: savedEntry.id, activity: 'holding business' } });
                                }
                                if (savedEntry.relevant_activities.intellectual_property_business && savedEntry.relevant_activities.intellectual_property_business.selected) {
                                    appInsightsClient.trackEvent({ name: "relevant activity submitted", properties: { email: req.user.email, companycode: savedEntry.company_data.code, submissionId: savedEntry.id, activity: 'intellectual property business' } });
                                }
                                if (savedEntry.relevant_activities.service_centre_business && savedEntry.relevant_activities.service_centre_business.selected) {
                                    appInsightsClient.trackEvent({ name: "relevant activity submitted", properties: { email: req.user.email, companycode: savedEntry.company_data.code, submissionId: savedEntry.id, activity: 'service centre business' } });
                                }


                            } catch (ex) {
                                console.log("Track Event Error: ", ex)
                            }


                            if (dbEntry.status === "RE-OPEN" && savedEntry.reopened?.details?.length > 0) {
                                const reopened = savedEntry.reopened.details.sort((a, b) => b.date_reopened.getTime() - a.date_reopened.getTime());
                                let email = mailFormatter.generateReOpenSubmittedEmail(savedEntry.company_data.name);
                                await mailController.asyncSend(
                                    reopened[0].reopened_by,
                                    'Completed Re-opened status request',
                                    email.textString,
                                    email.htmlString
                                );

                            }


                            if (status === 'SCHEDULED') {
                                res.render('entry/Confirmation',
                                    {
                                        entry: savedEntry,
                                        entryVersion: dbEntry.version, 
                                        company: sessData.company,
                                        hasError: false,
                                        pre2022Submission,
                                        submitted: true,
                                        scheduled: true,
                                        user: req.user,
                                        messages: req.session.messages,
                                        alreadyPaid: false,
                                        prePaid: prePaid,
                                        isReopened: dbEntry.reopened?.details?.length > 0,
                                        scheduledSubmission: !!(scheduledSubmission && (dbEntry.entity_details ? dbEntry.entity_details.financial_period_ends > new Date() : false)),
                                    });
                            } else {
                                res.render('entry/Confirmation', {
                                    entry: savedEntry,
                                    entryVersion: dbEntry.version, 
                                    company: sessData.company,
                                    hasError: false,
                                    pre2022Submission,
                                    submitted: true,
                                    user: req.user,
                                    messages: req.session.messages,
                                    entryId: dbEntry._id,
                                    prePaid: prePaid,
                                    isReopened: dbEntry.reopened?.details?.length > 0,
                                    alreadyPaid,
                                    scheduledSubmission: !!(scheduledSubmission && (dbEntry.entity_details ? dbEntry.entity_details.financial_period_ends > new Date() : false)),
                                })
                            }
                        });
                    }
                }
            });
        }catch(e){
            console.log(e);
            next(e);
        }
    }
]

exports.savePayment = [
    (req, res, next) => {
        try {
            const sessData = req.session;

            EntryModel.findById(req.params.id, function (err, dbEntry) {
                if (err) { return next(err); }
                if (dbEntry == null) { // No results.
                    err = new Error('Entry not found');
                    err.status = 404;
                    return next(err);
                }
                const entry = new EntryModel({
                    _id: req.params.id,
                    version: dbEntry.version,
                    status: "SUBMITTED",
                    submitted_by: req.user.email,
                    submitted_at: new Date(new Date().toUTCString()),
                    payment: {
                        payment_type: req.body.BankTransferOrCreditcard,
                        payment_reference: dbEntry.payment.payment_reference
                    }
                });

                EntryModel.findOneAndUpdate({ _id: req.params.id, company: sessData.company.code }, entry, {}, function (err) {
                    if (err) { return next(err); }
                    res.render('entry/Payment', { entry: entry, submitted: true, user: req.user, messages: req.session.messages, company: sessData.company });
                });
            });
        }catch(e){
            console.log(e);
            next(e);
        }

    }
]

exports.createParentEntity = async function (req, res) {
    try {
        const entry = await EntryModel.findById(req.params.id, { _id: 1, entity_details: 1 });

        if(!entry){
            return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                status: httpConstants.HTTP_STATUS_BAD_REQUEST,
                error: 'Entry not found.'
            });
        }

        const values = req.body;

        const requiredFields = ['type', 'parentName', 'jurisdiction', 'incorporationNumber', 'TIN'];
        const missingFields = requiredFields.filter(field => !values[field]);

        if (missingFields.length > 0) {
            return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
                status: httpConstants.HTTP_STATUS_BAD_REQUEST,
                error: 'Missing parent entity values.'
            });
        }

        const savedParent = await  addParentEntity(entry, values);

        return res.status(httpConstants.HTTP_STATUS_OK).json({
            status: httpConstants.HTTP_STATUS_OK,
            message: 'Parent entity created successfully.',
            type: values.type,
            parentEntity: savedParent
        });        
    }catch(e){
        console.log("Error creating new parent entity: ", e);
        res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({ 
            status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, 
            error: 'Internal server error'
        });
    }
}

exports.editParentEntity = async function(req, res) {
    try {
        const entry = await EntryModel.findById(req.params.id, {_id:1, entity_details: 1});

        if (!entry) {
            return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                status: httpConstants.HTTP_STATUS_NOT_FOUND,
                error: 'Entry not found.'
            });
        }

        const values = req.body;
        const parentIndex = findParentIndex(entry, req.params.parentEntityId);

        if (parentIndex === -1) {
            return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                status: httpConstants.HTTP_STATUS_NOT_FOUND,
                error: 'Parent entity not found.'
            });
        }

        const updatedParent = await updateParent(entry, parentIndex, values);

        return res.status(httpConstants.HTTP_STATUS_OK).json({
            status: httpConstants.HTTP_STATUS_OK,
            message: 'Parent entity updated successfully.',
            type: values.type,
            parentEntity: updatedParent
        });
    } catch (e) {
        console.log("Error editing entity parent: ", e);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
            status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
            error: 'Internal server error'
        });
    }
}

exports.deleteParentEntity = async function(req, res) {
    try {
        const entry = await EntryModel.findById(req.params.id);

        if (!entry) {
            return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                status: httpConstants.HTTP_STATUS_NOT_FOUND,
                error: 'Entry not found.'
            });
        }
        
        const entityDetails = entry.entity_details;

        if (req.params.type === "ultimate" && entityDetails.ultimateParents?.length > 0){
            entityDetails.ultimateParents = entityDetails.ultimateParents.filter((p) => 
                p._id?.toString() !== req.params.parentEntityId);
        }

        if (req.params.type === "immediate" && entityDetails.immediateParents?.length > 0) {
            entityDetails.immediateParents = entityDetails.immediateParents.filter((p) =>
                p._id?.toString() !== req.params.parentEntityId);
        }

        await EntryModel.findByIdAndUpdate(entry._id, { "entity_details": entityDetails});

        return res.status(httpConstants.HTTP_STATUS_OK).json({
            status: httpConstants.HTTP_STATUS_OK,
            message: 'Parent entity deleted successfully.'
        });
    } catch (e) {
        console.log("Error deleting entity parent: ", e);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
            status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
            error: 'Internal server error'
        });
    }
}


exports.getParentEntities = async function(req, res){
    try {
        const entry = await EntryModel.findById(req.params.id, { _id: 1, entity_details: 1 });

        if (!entry) {
            return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                status: httpConstants.HTTP_STATUS_NOT_FOUND,
                error: 'Entry not found.'
            });
        }
        let parentsEntities = [];

        if (req.query.type === "ultimate" && entry.entity_details.ultimateParents?.length > 0) {
            parentsEntities = entry.entity_details.ultimateParents;
        }

        if (req.query.type === "immediate" && entry.entity_details.immediateParents?.length > 0) {
            parentsEntities = entry.entity_details.immediateParents;
        }

        if (req.query.parentEntityId){
            parentsEntities = parentsEntities.filter((p) => p._id?.toString() === req.query.parentEntityId);

            if (parentsEntities.length === 0) {
                return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                    status: httpConstants.HTTP_STATUS_NOT_FOUND,
                    message: 'Parent entity not found.'
                });
            }
        }

        if (parentsEntities.length > 0) {
            parentsEntities = parentsEntities.map((parent) => {
                const parentObject =parent.toObject();
                const country = COUNTRIES.find((c) => c.alpha_3_code === parentObject.jurisdiction);

                if(country){
                    parentObject.jurisdictionName = country.name;
                }
                return parentObject;
            });
        }

        return res.status(httpConstants.HTTP_STATUS_OK).json({
            status: httpConstants.HTTP_STATUS_OK,
            type: req.params.type,
            data: parentsEntities
        });

    } catch (e) {
        console.log("Error getting parent entity: ", e);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
            status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
            error: 'Internal server error'
        });
    }   
}

async function addParentEntity(entry, values) {
    const newParent = {
        parentName: values.parentName,
        alternativeName: values.alternativeName,
        jurisdiction: values.jurisdiction,
        incorporationNumber: values.incorporationNumber,
        TIN: values.TIN,
    };

    const entityDetails = entry.entity_details;

    if (!entityDetails?.ultimateParents) {
        entityDetails.ultimateParents = [];
    }

    if (!entityDetails?.immediateParents) {
        entityDetails.immediateParents = [];
    }

    let savedParent;

    if (values.type === 'ultimate') {
        const newIndex = entityDetails.ultimateParents.push(newParent) - 1;
        savedParent = entityDetails.ultimateParents[newIndex];
        entityDetails.haveUltimateParents = true;

    } else if (values.type === 'immediate') {
        const newIndex = entityDetails.immediateParents.push(newParent) - 1;
        savedParent = entityDetails.immediateParents[newIndex];
        entityDetails.haveImmediateParents = true
    } else {
        throw new Error('Invalid entity parent type.');
    }

    await EntryModel.findByIdAndUpdate(entry._id, {"entity_details": entityDetails});

    return savedParent;
}

function findParentIndex(entry, parentId) {
    const ultimateParents = entry.entity_details.ultimateParents || [];
    const immediateParents = entry.entity_details.immediateParents || [];

    const parentIndex = ultimateParents.findIndex(parent => parent.id === parentId);

    if (parentIndex !== -1) {
        return parentIndex;
    }

    return immediateParents.findIndex(parent => parent.id === parentId);
}

async function updateParent(entry, parentIndex, values) {
    const updatedValues = {
        parentName: values.parentName,
        alternativeName: values.alternativeName,
        jurisdiction: values.jurisdiction,
        incorporationNumber: values.incorporationNumber,
        TIN: values.TIN,
    };

    const entityDetails = entry.entity_details;
    let parentData;

    if (values.type === 'ultimate') {
        parentData = entry.entity_details.ultimateParents[parentIndex];
        parentData = {_id: parentData._id, ...updatedValues }

        entityDetails.ultimateParents[parentIndex] = parentData;
    } else if (values.type === 'immediate') {
        parentData = entry.entity_details.immediateParents[parentIndex];
        parentData = { _id: parentData._id, ...updatedValues }

        entityDetails.immediateParents[parentIndex] = parentData;

    }else{
        throw new Error('Invalid parent entity type or index.');
    }

    await EntryModel.findByIdAndUpdate(entry._id, { "entity_details": entityDetails });
    return parentData;
}



exports.getActivityBusinessByType = async function (req, res) {
    try {
        const entry = await EntryModel.findById(req.params.id);

        if (!entry) {
            return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                status: httpConstants.HTTP_STATUS_NOT_FOUND,
                error: 'Entry not found.'
            });
        }
        const activityBusiness = getEntryActivityBusiness(entry, req.query.type);
 
        if (!activityBusiness){
            return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                status: httpConstants.HTTP_STATUS_NOT_FOUND,
                error: 'Activity Business not found.'
            }); 
        }

        return res.status(httpConstants.HTTP_STATUS_OK).json({
            status: httpConstants.HTTP_STATUS_OK,
            activityType: req.params.type,
            data: activityBusiness.values
        });

    } catch (e) {
        console.log("Error getting parent entity: ", e);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
            status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
            error: 'Internal server error'
        });
    }
}


exports.saveBoardMeeting = async function (req, res) {
    try {
        const entry = await EntryModel.findById(req.params.id);

        if (!entry) {
            return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                status: httpConstants.HTTP_STATUS_BAD_REQUEST,
                error: 'Entry not found.'
            });
        }

        const values = req.body;


        const requiredFields = ['type', 'directorName', 'physicallyPresent', 'relationToEntity', 'qualification'];
        const missingFields = requiredFields.filter(field => !values[field]);

        if (missingFields.length > 0) {
            return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
                status: httpConstants.HTTP_STATUS_BAD_REQUEST,
                error: 'Missing required board meetings values.'
            });
        }

        const activityBusiness = getEntryActivityBusiness(entry, values.type);
        

        if (!activityBusiness) {
            return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                status: httpConstants.HTTP_STATUS_NOT_FOUND,
                error: 'Activity Business not found.'
            });
        }


        const boardMeetings = activityBusiness.values?.board_meetings || [];
        const boardMeetingValues = {
            meeting_number: parseInt(values.meetingNumber, 10),
            name: values.directorName,
            physically_present: values.physicallyPresent === "Yes" ? true : false,
            relation_to_entity: values.relationToEntity,
            qualification: values.qualification,
        };

        let index = 0;
        if(req.params.boardMeetingId){
            index = boardMeetings.findIndex((board) => board._id?.toString() === req.params.boardMeetingId);
            if(index === -1){
                return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                    status: httpConstants.HTTP_STATUS_NOT_FOUND,
                    error: 'Board Meeting not found.'
                });
            }
            const currentBoardValues = boardMeetings[index];
            boardMeetings[index] = { ...currentBoardValues, ...boardMeetingValues };
        }else{
            boardMeetings.push(boardMeetingValues);
        }
        activityBusiness.values = { ...activityBusiness.values, ...{ board_meetings: boardMeetings } };
        const queryUpdate = { [activityBusiness.field]: activityBusiness.values}
        await EntryModel.findByIdAndUpdate(entry._id, queryUpdate);



        return res.status(httpConstants.HTTP_STATUS_OK).json({
            status: httpConstants.HTTP_STATUS_OK,
            message: 'Board Meeting saved successfully.',
            entryId: entry._id,
            activityType: values.type
        });

    } catch (e) {
        console.log("Error creating new board meeting: ", e);
        res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
            status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
            error: 'Internal server error'
        });
    }
}

exports.deleteBoardMeeting = async function (req, res) {
    try {
        const entry = await EntryModel.findById(req.params.id);

        if (!entry) {
            return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                status: httpConstants.HTTP_STATUS_NOT_FOUND,
                error: 'Entry not found.'
            });
        }
        const activityBusiness = getEntryActivityBusiness(entry, req.params.type);

        if (!activityBusiness) {
            return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                status: httpConstants.HTTP_STATUS_NOT_FOUND,
                error: 'Activity Business not found.'
            });
        }

        let boardMeetings = activityBusiness.values?.board_meetings || [];
        boardMeetings = boardMeetings.filter((board) => board._id?.toString() !== req.params.boardMeetingId);
        activityBusiness.values = { ...activityBusiness.values, ...{ board_meetings: boardMeetings } };

        const queryUpdate = { [activityBusiness.field]: activityBusiness.values }
        await EntryModel.findByIdAndUpdate(entry._id, queryUpdate);

        return res.status(httpConstants.HTTP_STATUS_OK).json({
            status: httpConstants.HTTP_STATUS_OK,
            message: 'Board Meeting deleted successfully.',
            entryId: entry._id,
            activityType: req.params.type
        });
    } catch (e) {
        console.log("Error deleting board meeting: ", e);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
            status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
            error: 'Internal server error'
        });
    }
}

exports.saveEmployee = async function (req, res) {
    try {
        const entry = await EntryModel.findById(req.params.id);

        if (!entry) {
            return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                status: httpConstants.HTTP_STATUS_BAD_REQUEST,
                error: 'Entry not found.'
            });
        }

        const values = req.body;

        const requiredFields = ['type', 'name', 'experienceYears', 'qualification'];
        const missingFields = requiredFields.filter(field => !values[field]);

        if (missingFields.length > 0) {
            return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
                status: httpConstants.HTTP_STATUS_BAD_REQUEST,
                error: 'Missing required employee values.'
            });
        }

        const activityBusiness = getEntryActivityBusiness(entry, values.type);


        if (!activityBusiness) {
            return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                status: httpConstants.HTTP_STATUS_NOT_FOUND,
                error: 'Activity Business not found.'
            });
        }


        const employees = activityBusiness.values?.employees || [];
        const employeeValues = {
            meeting_number: parseInt(values.meetingNumber, 10),
            name: values.name,
            experience_years: values.experienceYears.split(',').join(''),
            qualification: values.qualification,
        };

        let index = 0;
        if (req.params.employeeId) {
            index = employees.findIndex((employee) => employee._id?.toString() === req.params.employeeId);
            if (index === -1) {
                return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                    status: httpConstants.HTTP_STATUS_NOT_FOUND,
                    error: 'Employee not found.'
                });
            }
            const currentEmployee = employees[index];
            employees[index] = { ...currentEmployee, ...employeeValues };
        } else {
            employees.push(employeeValues);
        }
        activityBusiness.values = { ...activityBusiness.values, ...{ employees: employees } };
        const queryUpdate = { [activityBusiness.field]: activityBusiness.values }
        await EntryModel.findByIdAndUpdate(entry._id, queryUpdate);

        return res.status(httpConstants.HTTP_STATUS_OK).json({
            status: httpConstants.HTTP_STATUS_OK,
            message: 'Employee saved successfully.',
            entryId: entry._id,
            activityType: values.type
        });

    } catch (e) {
        console.log("Error creating new employee: ", e);
        res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
            status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
            error: 'Internal server error'
        });
    }
}

exports.deleteEmployee = async function (req, res) {
    try {
        const entry = await EntryModel.findById(req.params.id);

        if (!entry) {
            return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                status: httpConstants.HTTP_STATUS_NOT_FOUND,
                error: 'Entry not found.'
            });
        }
        const activityBusiness = getEntryActivityBusiness(entry, req.params.type);

        if (!activityBusiness) {
            return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                status: httpConstants.HTTP_STATUS_NOT_FOUND,
                error: 'Activity Business not found.'
            });
        }

        let employees = activityBusiness.values?.employees || [];
        employees = employees.filter((employee) => employee._id?.toString() !== req.params.employeeId);
        activityBusiness.values = { ...activityBusiness.values, ...{ employees: employees } };

        const queryUpdate = { [activityBusiness.field]: activityBusiness.values }
        await EntryModel.findByIdAndUpdate(entry._id, queryUpdate);

        return res.status(httpConstants.HTTP_STATUS_OK).json({
            status: httpConstants.HTTP_STATUS_OK,
            message: 'Employee deleted successfully.',
            entryId: entry._id,
            activityType: req.params.type
        });
    } catch (e) {
        console.log("Error deleting employee: ", e);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
            status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
            error: 'Internal server error'
        });
    }
}


exports.saveOutsourcingProvider = async function (req, res) {
    try {
        const entry = await EntryModel.findById(req.params.id);

        if (!entry) {
            return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                status: httpConstants.HTTP_STATUS_BAD_REQUEST,
                error: 'Entry not found.'
            });
        }

        const values = req.body;
        const requiredFields = ['type', 'entityName', 'resourceDetails', 'staffCount', 'hoursPerMonth', 'monitoringControl'];
        const missingFields = requiredFields.filter(field => !values[field]);

        if (missingFields.length > 0) {
            return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
                status: httpConstants.HTTP_STATUS_BAD_REQUEST,
                error: 'Missing required employee values.'
            });
        }

        const activityBusiness = getEntryActivityBusiness(entry, values.type);


        if (!activityBusiness) {
            return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                status: httpConstants.HTTP_STATUS_NOT_FOUND,
                error: 'Activity Business not found.'
            });
        }


        const outsourcingProviders = activityBusiness.values?.outsourcing_providers || [];
        const providerValues = {
            entity_name: values.entityName,
            resource_details: values.resourceDetails,
            staff_count: values.staffCount.split(',').join(''),
            hours_per_month: values.hoursPerMonth.split(',').join(''),
            monitoring_control: values.monitoringControl === "Yes" ? true :false,
        };

        let index = 0;
        if (req.params.outsourcingProviderId) {
            index = outsourcingProviders.findIndex((provider) => provider._id?.toString() === req.params.outsourcingProviderId);
            if (index === -1) {
                return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                    status: httpConstants.HTTP_STATUS_NOT_FOUND,
                    error: 'Outsourcing Provider not found.'
                });
            }
            const currentProvider = outsourcingProviders[index];
            outsourcingProviders[index] = { ...currentProvider, ...providerValues };
        } else {
            outsourcingProviders.push(providerValues);
        }
        activityBusiness.values = { ...activityBusiness.values, ...{ outsourcing_providers: outsourcingProviders } };
        const queryUpdate = { [activityBusiness.field]: activityBusiness.values }
        await EntryModel.findByIdAndUpdate(entry._id, queryUpdate);

        return res.status(httpConstants.HTTP_STATUS_OK).json({
            status: httpConstants.HTTP_STATUS_OK,
            message: 'Outsourcing Provider saved successfully.',
            entryId: entry._id,
            activityType: values.type
        });

    } catch (e) {
        console.log("Error creating new outsourcing provider: ", e);
        res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
            status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
            error: 'Internal server error'
        });
    }
}

exports.deleteOutsourcingProvider = async function (req, res) {
    try {
        const entry = await EntryModel.findById(req.params.id);

        if (!entry) {
            return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                status: httpConstants.HTTP_STATUS_NOT_FOUND,
                error: 'Entry not found.'
            });
        }
        const activityBusiness = getEntryActivityBusiness(entry, req.params.type);

        if (!activityBusiness) {
            return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
                status: httpConstants.HTTP_STATUS_NOT_FOUND,
                error: 'Activity Business not found.'
            });
        }

        let outsourcingProviders = activityBusiness.values?.outsourcing_providers || [];
        outsourcingProviders = outsourcingProviders.filter((provider) => provider._id?.toString() !== req.params.outsourcingProviderId);
        activityBusiness.values = { ...activityBusiness.values, ...{ outsourcing_providers: outsourcingProviders } };

        const queryUpdate = { [activityBusiness.field]: activityBusiness.values }
        await EntryModel.findByIdAndUpdate(entry._id, queryUpdate);

        return res.status(httpConstants.HTTP_STATUS_OK).json({
            status: httpConstants.HTTP_STATUS_OK,
            message: 'Outsourcing Provider deleted successfully.',
            entryId: entry._id,
            activityType: req.params.type
        });
    } catch (e) {
        console.log("Error deleting outsourcing provider: ", e);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
            status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
            error: 'Internal server error'
        });
    }
}


async function getDefaultEntityDetailsData(currentStep, entry) {
    const entityDetailsInfo = {
        isSameITADate: false,
        applicationITADate: null,
        previousEndDate: null,
        scheduledSubmission: null
    };
    try {

        if (currentStep === settings.substance_form_steps.FINANCIAL_PERIOD_FORM) {
            const company = await Company.findOne({ "code": entry.company_data.code, "masterclientcode": entry.company_data.masterclientcode });

            if (company.hasITADate === true && company.approvedITAStartDate && company.approvedITAEndDate) {
                if (moment(company.approvedITAStartDate).utc().format('YYYY-MM-DD') ===
                    moment(entry.entity_details?.financial_period_begins).utc().format('YYYY-MM-DD') &&
                    moment(company.approvedITAEndDate).utc().format('YYYY-MM-DD') ===
                    moment(entry.entity_details?.financial_period_ends).utc().format('YYYY-MM-DD')) {
                    entityDetailsInfo.isSameITADate = true;
                }

                if (company.applicationITADate) {
                    entityDetailsInfo.applicationITADate = company.applicationITADate
                }
            }
            let previousEntries = await EntryModel.find({ 'company': entry.company, _id: { "$ne": entry._id.toString() } });

            if (previousEntries.length > 0) {
                entityDetailsInfo.scheduledSubmission = previousEntries.find((submission) => submission.status === "SCHEDULED");
                previousEntries = previousEntries.sort((a, b) => b.entity_details?.financial_period_ends?.getTime() - a.entity_details?.financial_period_ends?.getTime());
                const previousEndDate = previousEntries[0].entity_details?.financial_period_ends

                if (company.hasITADate && company.approvedITAStartDate && previousEntries?.length > 0 && 
                    company.approvedITAStartDate.getTime() > previousEndDate.getTime()) {
                    entityDetailsInfo.previousEndDate = previousEndDate;
                }

            }
        }
        else {
            entityDetailsInfo.scheduledSubmission = await EntryModel.findOne({
                'status': 'SCHEDULED',
                'company': entry.company
            });
        }
        return entityDetailsInfo
    } catch (e) {
        console.log(e);
        return entityDetailsInfo
    }
}


function getViewByVersion(version, view) {
    let renderView = "";
    let intVersion = parseInt(version, 10);

    if (intVersion >= 5) {
        const viewPath = `entry/v${intVersion}/${view}`
        const searchPath = path.join(__dirname, '..', 'views', viewPath + ".hbs");

        if (fs.existsSync(searchPath)) {
            renderView = viewPath;
        } else {
            renderView = "entry/" + view;
        }

    } else {
        renderView = "entry/" + view;
    }

    return renderView
}


function getEntryActivityBusiness(entry, activityType) {
    switch (activityType) {
        case settings.substance_business_types.BANKING:
            return { 
                field: "banking_business", 
                values: entry.banking_business ? entry.banking_business.toObject() : {} 
            };
        case settings.substance_business_types.INSURANCE:
            return { 
                field: "insurance_business", 
                values: entry.insurance_business ? entry.insurance_business.toObject() : {} 
            };
        case settings.substance_business_types.FUND_MANAGEMENT:
            return { 
                field: "fund_management_business", 
                values: entry.fund_management_business ? entry.fund_management_business.toObject() : {} 
            };
        case settings.substance_business_types.FINANCE_LEASING:
            return { 
                field: "finance_leasing_business", 
                values: entry.finance_leasing_business ? entry.finance_leasing_business.toObject() : {} 
            };
        case settings.substance_business_types.HEADQUARTERS:
            return { 
                field: "headquarters_business", 
                values: entry.headquarters_business ? entry.headquarters_business.toObject() : {} 
            };
        case settings.substance_business_types.SHIPPING:
            return { 
                field: "shipping_business", 
                values: entry.shipping_business ? entry.shipping_business.toObject() : {} 
            };
        case settings.substance_business_types.INTELLECTUAL_PROPERTY:
            return { 
                field: "intellectual_property_business", 
                values: entry.intellectual_property_business ? entry.intellectual_property_business.toObject() : {} 
            };
        case settings.substance_business_types.HOLDING:
            return { 
                field: "holding_business", 
                values: entry.holding_business ? entry.holding_business.toObject() : {} 
            };
        case settings.substance_business_types.DISTRIBUTION_SERVICE_CENTRE:
            return { 
                field: "service_centre_business", 
                values: entry.service_centre_business ? entry.service_centre_business.toObject() : {} 
            };
        default:
            return null;
    }
}

function getCIGACodeList(activityType){
    const standarOptions = CIGA_CODES.filter((c) => c.type === "");
    const activityCigaCodes = CIGA_CODES.filter((c) => c.type === activityType);

    return [...standarOptions, ...activityCigaCodes]
}