const sqlDb = require('../models-sql'); 
const Company = require('../models/company').schema;
const MasterClientCode = require('../models/masterClientCode');
const VpDirectorInfoModel = sqlDb.VpDirectorInfo;
const VpDirectorInfoHistoryModel = sqlDb.VpDirectorInfoHistory;
const VpDirectorInfoStatusModel = sqlDb.VpDirectorInfoStatus;
const MailController = require('../controllers/mailController');
const MailFormatter = require('../controllers/mailFormatController');
const moment = require('moment');
const httpConstants = require('http2').constants;
const { 
    TYPE_OF_DIRECTOR_DIR, 
    TYPE_OF_DIRECTOR_BO, 
    DIRECTOR_REQUIRED_FIELDS, 
    BO_REQUIRED_FIELDS, 
    DIRBO_STATUS 
} = require('../utils/directorAndBOConstants');
const { Op } = require("sequelize");


// Display list of all Companys.
exports.listDirectorAndBoCompanies = async function (req, res, next) {
    try{
        const mcc = await MasterClientCode.findOne({ 'code': req.params.masterclientcode});

        if (mcc == null || mcc.owners.indexOf(req.user.email.toLowerCase()) == -1) {
            const err = new Error('Masterclient not found');
            err.status = 404;
            return next(err);
        }

        let companyfilter = { 
            'masterclientcode': req.params.masterclientcode,
            'dirboModule.active': true,
        };

        if (req.query.filterCompanyName && req.query.filterCompanyName.length > 2) {
            companyfilter['name'] = { $regex: req.query.filterCompanyName, $options: 'i' };
        }
        if (req.query.filterIncorporationCode && req.query.filterIncorporationCode.length > 2) {
            companyfilter['incorporationcode'] = { $regex: req.query.filterIncorporationCode, $options: 'i' };
        }

        let companies = await Company.find(
            companyfilter, 
            { code: 1, name: 1, incorporationcode:1, masterclientcode:1 }
        ).sort({"name":1})



        if (companies.length > 0) {

            // get only the company codes of the portal companies for this mcc
            let portalCompanyCodesForMCC= companies.map((item) => { return item.code});

            //find VPDirectorInfos where in portalCompanyNumbers (no mcc for now).
            // we know this Portal MCC has access to these numbers now

            // TODO: dual-key on foreign-VP MCC when we have a lookup.

            const vpCompanies = await VpDirectorInfoModel.findAll({
                where: {
                    CompanyNumber: portalCompanyCodesForMCC,
                },
                attributes: [
                    [sqlDb.Sequelize.fn('DISTINCT', sqlDb.Sequelize.col('CompanyNumber')), 'CompanyNumber'],
                    "MasterClientCode"
                ],
                group: ['MasterClientCode', "CompanyNumber"],
                raw: true
            });
 
            if (vpCompanies.length > 0) {
                //although we filtered by portalCompanyCodesForMCC we still need to return companies collection from Portal.
                // so intersect back to only show what is in the sync
                const companyNumbers = vpCompanies.map((item) => { return item.CompanyNumber}); 
                
                companies = companies.filter((c) => companyNumbers.includes(c.code) === true);

                let vpDirectorInfoStatusResult = await VpDirectorInfoStatusModel.findAll({
                    where: {
                        CompanyNumber: companyNumbers,
                        Status: {
                            [Op.or]: [DIRBO_STATUS.INITIAL, DIRBO_STATUS.RECEIVED, DIRBO_STATUS.REFRESHED]
                          }
                    },
                    raw: true
                });
            

                companies = companies.map((company) => {
                    let unconfirmedDataBO = vpDirectorInfoStatusResult.filter((c) => c.CompanyNumber == company.code && c.RelationType != 'Director')
                    let unconfirmedDataDirector = vpDirectorInfoStatusResult.filter((c) => c.CompanyNumber == company.code && c.RelationType == 'Director')
    
                    return {
                        company: company,
                        unconfirmedDataBO: unconfirmedDataBO ? unconfirmedDataBO.length > 0 : false ,
                        unconfirmedDataDirector: unconfirmedDataDirector ? unconfirmedDataDirector.length > 0 : false
                    }
                });

            } else {
                // DO NOT SHOW ANY COMPANY THAT IS NOT ALSO IN THE LIST
                 companies = [];
            }
            
        }

        res.render('director-and-bo/companies', { 
            title: 'Companies', 
            masterClientCode: req.params.masterclientcode, 
            companies: companies, 
            user: req.user,
            messages: req.session.messages 
        });

    }catch(e){
        console.log(e);
        const err = new Error('Internal server error');
        err.status = 500;
        return next(err);
    }
};


exports.getDirAndBoEntries = async function (req, res, next) {
    try{

        const company = await getDirectorCompanyData(req.params.code, req.params.masterclientcode, req.user.email);

        if(company?.error){
            const err = new Error(company.error);
            err.status = company.status;
            return next(err);
        }

        let type = "";
        let renderPage = "";
        let directorsWithMissingValues = [];

        if (req.params.type === "directors"){
            type = TYPE_OF_DIRECTOR_DIR;
            renderPage = "director-forms";
        }else{
            type = TYPE_OF_DIRECTOR_BO;
            renderPage = "bo-forms";

        }

        //TODO: Dual Key to VP MCC
        let directors = await VpDirectorInfoModel.findAll({
            where: {
                // MasterClientCode: VP.masterclientcode, was company.masterclientcode
                CompanyNumber: company.code,
                RelationType: type
            },
            raw: true
        });

        
        let entityName = company.name;

        if (directors && directors.length >0){  
            entityName = directors[0].EntityName;
            const directorCodes = [...new Set(directors.map((e) => e.Code))];

            const historyLogs = await VpDirectorInfoHistoryModel.findAll({
                where: { 
                    //MasterClientCode: company.masterclientcode, TODO: VP MCC ID
                    CompanyNumber: company.code, 
                    Code: directorCodes,
                    RelationType: type,
                    //Status: {
                    //    [Op.ne]: DIRBO_STATUS.REFRESHED
                    //}
                },
                raw:true
            });

            directors = directors.map((director) => {
                director.canUpdate = true;
                director.canConfirm = true;
                director.showHistory = false;
                director.showInfoQuestion = true;
                
                //look for explicit match of UniqueRelationId from VP
                const entryLogs = historyLogs.filter((r) => r.UniqueRelationId === director.UniqueRelationId && r.RelationType === director.RelationType);

                if(entryLogs.length > 0){
                    entryLogs.sort((a, b) => b.Id - a.Id);
                    const lastLogCreated = entryLogs[0];
                    
                    if (lastLogCreated.Status !== DIRBO_STATUS.RECEIVED && lastLogCreated.Status !== DIRBO_STATUS.INITIAL && lastLogCreated.Status !== DIRBO_STATUS.REFRESHED){ //comment out Initial if we want Request Update button as default
                        director.showInfoQuestion = false;
                        director.canConfirm = false;
                    } else if (lastLogCreated.Status == DIRBO_STATUS.RECEIVED || lastLogCreated.Status == DIRBO_STATUS.REFRESHED) {
                        //find the last log created for CONFIRMED or PENDING UPDATE REQUEST
                        const confirmedOrUpdateRequestLogs = entryLogs.filter((r) => r.Status == DIRBO_STATUS.CONFIRMED || r.Status == DIRBO_STATUS.PENDING || r.Status == DIRBO_STATUS.INITIAL);
                        if (confirmedOrUpdateRequestLogs && confirmedOrUpdateRequestLogs.length > 0) {
                            confirmedOrUpdateRequestLogs.sort((a, b) => b.Id - a.Id);
                            const lastConfirmedOrUpdateRequestLog = confirmedOrUpdateRequestLogs[0];
                            director.oldData = lastConfirmedOrUpdateRequestLog;
                        }
                        
                    }   
                   
                    director.showHistory = lastLogCreated.Status === DIRBO_STATUS.PENDING;
                    director.canUpdate = lastLogCreated.Status !== DIRBO_STATUS.PENDING;
                    
                    if (lastLogCreated.Status === DIRBO_STATUS.PENDING && lastLogCreated.UpdateRequestDate === null){
                        // find last log created where  UpdateRequestDate is not null, entryLogs is already sorted 
                        const userRequestUpdateLogs = entryLogs.find(r => r.UpdateRequestDate !== null);

                        director.lastChange = {
                            changeType: userRequestUpdateLogs ? userRequestUpdateLogs.TypeOfUpdateRequest : "",
                            changeReason: userRequestUpdateLogs ? userRequestUpdateLogs.UpdateRequestComments : "",
                            status: lastLogCreated.Status
                        }
                    }else{
                        director.lastChange = {
                            changeType: lastLogCreated.TypeOfUpdateRequest,
                            changeReason: lastLogCreated.UpdateRequestComments,
                            status: lastLogCreated.Status
                        }
                    }
                    
    
                }


                if (director.canUpdate) {
                    const listOfRequiredFields = getListFieldsToValidate(type === TYPE_OF_DIRECTOR_BO, director.FileType?.toLowerCase(), director.OfficerType)

                    const missingValues = listOfRequiredFields.filter((item) => director[item.field] == null || director[item.field] == undefined || director[item.field] === "")
                        .map((item) => item.name)

                    if (missingValues.length > 0) {
                        director.hasMissingValues = true;
                        director.showInfoQuestion = false;
                        director.canConfirm = false;
                        directorsWithMissingValues.push({
                            id: director.UniqueRelationId,
                            name: director.Name,
                            missingValues: missingValues.join(', ')
                        })
                    }
                }

                console.log("director ", director)
                return director;
            })
            
        }

        const individualEntries = directors.filter((e) => e.FileType?.toLowerCase() === "individual");
        const corporateEntries = directors.filter((e) => e.FileType?.toLowerCase() !== "individual");


        if (type === TYPE_OF_DIRECTOR_DIR){
            return res.render(`director-and-bo/${renderPage}`, {
                masterClientCode: req.params.masterclientcode,
                company: company,
                entityName,
                type: req.params.type,
                individualEntries: individualEntries,
                corporateEntries: corporateEntries,
                hasCorporateEntries: corporateEntries.length > 0,
                user: req.user,
                messages: req.session.messages,
                directorsWithMissingValues
            });
        }else{
            let boCorpEntriesGpt2 = [];
            let boCorpEntriesGpt3 = [];
            let boCorpEntriesGpt4 = [];
            let boCorpEntriesGpt5 = [];
            let boCorpEntriesGpt6 = [];

            if (corporateEntries.length > 0){
                boCorpEntriesGpt2 = corporateEntries.filter((e) => e.OfficerType === "VGTP02");
                boCorpEntriesGpt3 = corporateEntries.filter((e) => e.OfficerType === "VGTP03");
                boCorpEntriesGpt4 = corporateEntries.filter((e) => e.OfficerType === "VGTP04");
                boCorpEntriesGpt5 = corporateEntries.filter((e) => e.OfficerType === "VGTP05");
                boCorpEntriesGpt6 = corporateEntries.filter((e) => e.OfficerType === "VGTP06");
            }

            return res.render(`director-and-bo/${renderPage}`, {
                masterClientCode: req.params.masterclientcode,
                company: company,
                entityName,
                type: req.params.type,
                individualEntries: individualEntries,
                hasCorporateEntries: corporateEntries.length > 0,
                boCorpEntriesGpt2,
                boCorpEntriesGpt3,
                boCorpEntriesGpt4,
                boCorpEntriesGpt5,
                boCorpEntriesGpt6,
                user: req.user,
                messages: req.session.messages,
                directorsWithMissingValues
            });

        }

    } catch (e) {
        console.log(e);
        const err = new Error('Internal server error');
        err.status = 500;
        return next(err);
    }
}



exports.requestToUpdate = async function (req, res) {
    try {

        const company = await getDirectorCompanyData(req.params.code, req.params.masterclientcode, req.user.email);
        const data = req.body;

        if (company?.error) {
            return res.status(company.status).json({
                "status": company.status,
                "error": company.error
            });
        }


        if (!data.changeType) {
            return res.status(400).json({
                "status": 400,
                "error": 'Please select a valid option'
            });
        }
        const type = req.params.type === "directors" ? TYPE_OF_DIRECTOR_DIR : TYPE_OF_DIRECTOR_BO;
       
        //TODO: Dual Key to VP MCC
        let director = await VpDirectorInfoModel.findOne({ 
            where: { 
                // MasterClientCode: VP.masterclientcode, was company.masterclientcode
                CompanyNumber: company.code,
                UniqueRelationId: req.params.id, 
                RelationType: type 
            },
            raw:true 
        });
        let emailTo;

        if (director.ProductionOffice === 'TBVI') {
            emailTo = process.env.REQUEST_UPDATE_EMAIL_TBVI
        } else if (director.ProductionOffice === 'THKO'){
            emailTo = process.env.REQUEST_UPDATE_EMAIL_THKO
        } else if (director.ProductionOffice === 'TCYP'){
            emailTo = process.env.REQUEST_UPDATE_EMAIL_TCYP
        } else if (director.ProductionOffice === 'TPANVG'){
            emailTo = process.env.REQUEST_UPDATE_EMAIL_TPANVG
        } else {
            emailTo = process.env.REQUEST_UPDATE_EMAIL_THKO
        }

        const noPoductionOffice = director.ProductionOffice !== 'THKO' && director.ProductionOffice !== 'TBVI' && director.ProductionOffice !== 'TCYP' && director.ProductionOffice !== 'TPANVG' ? true : false;
        
        if (!director){
            return res.status(404).json({
                "status": 404,
                "error": 'Director information not found'
            });
        }


        let requestUpdateLog = await VpDirectorInfoHistoryModel.create({
            ...director, //director will have VP MCC, RelationType, FileType and OfficerType, UniqueRelationId
            UpdateRequestDate: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
            ConfirmedDate: null,
            VPDataReceived: null,
            Status: DIRBO_STATUS.PENDING,
            UserEmail: req.user.username, 
            TypeOfUpdateRequest: data.changeType,
            UpdateRequestComments: data.changeReason || "",
        })
        requestUpdateLog = requestUpdateLog.toJSON()

        const subject = `${process.env.EMAIL_SUBJECT_PREFIX} ${company.name} – Request update for Director/BO portal`

        if(requestUpdateLog.Id){
            let email = MailFormatter.generateDirBoRequestUpdateEmail({
                "companyCode": director.EntityCode + " (" +company.code +")",
                "companyName": director.EntityName + " (" + company.name+")",
                "mcc": director.MasterClientCode + " (" + company.masterclientcode +")",
                "directorCode": director.Code,
                "requestor": requestUpdateLog.UserEmail,
                "requestType": requestUpdateLog.TypeOfUpdateRequest,
                "comment": requestUpdateLog.UpdateRequestComments
            });
            let emailResponse = await MailController.asyncSend(
                emailTo,
                noPoductionOffice ? '(!Production office unknown) ' + subject : subject,
                email.textString,
                email.htmlString
            );
            
            if (emailResponse.error) {
                console.log("Send director email error: ", emailResponse);
            }

            return res.status(200).json({ "status": 200, "message": "Request an update has been created successfully" });
        }else{
            return res.status(500).json({ "status": 500, "message": "There was an error creating the request" });
        }
        
    } catch (e) {
        console.log("Error creating request an update: ", e);
        return res.status(500).json({
            "status": 500,
            "error": 'Internal server error'
        });
    }
}


exports.createDirectorConfirmationLog = async function (req, res) {
    try {
        const company = await getDirectorCompanyData(req.params.code, req.params.masterclientcode, req.user.email );

        if (company?.error) {
            return res.status(company.status).json({
                "status": company.status,
                "error": company.error
            });
        }
        const type = req.params.type === "directors" ? TYPE_OF_DIRECTOR_DIR : TYPE_OF_DIRECTOR_BO;
        
        //TODO: Dual Key to VP MCC
        let director = await VpDirectorInfoModel.findOne({
            where: {
                // MasterClientCode: VP.masterclientcode, was company.masterclientcode
                CompanyNumber: company.code,
                UniqueRelationId: req.params.id,
                RelationType: type
        },
            raw: true
        });

        if (!director) {
            return res.status(404).json({
                "status": 404,
                "error": 'Director information not found'
            });
        }


        let confirmDirectorDataLog = await VpDirectorInfoHistoryModel.create({
            ...director, //director will have VP MCC, RelationType, FileType and OfficerType, UniqueRelationId
            ConfirmedDate: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
            VPDataReceived: null,
            Status: DIRBO_STATUS.CONFIRMED,
            UserEmail: req.user.username, 
            TypeOfUpdateRequest: null,
            UpdateRequestComments: null,
        })
        confirmDirectorDataLog = confirmDirectorDataLog.toJSON()
        

        if (confirmDirectorDataLog.Id) {
            return res.status(200).json({ "status": 200, "message": `${req.params.type === "directors" ? "Director" : "BO" } information has been confirmed successfully` });
        } else {
            return res.status(500).json({ "status": 500, "message": "There was an error in the process to confirm the information" });
        }

    } catch (e) {
        console.log("Error in process to confirm director data: ", e);
        return res.status(500).json({
            "status": 500,
            "error": 'Internal server error'
        });
    }
}


async function getDirectorCompanyData(code, mcc, user){
    //this will test the current user has access to the mcc, and thus allowed to the company
    try {
        let masterclient = await MasterClientCode.findOne({ 'code': mcc });
        if (masterclient == null || masterclient.owners?.indexOf(user.toLowerCase()) == -1) {
            return { status: httpConstants.HTTP_STATUS_NOT_FOUND, error: 'Masterclient not found'}
        }
        const company = await Company.findOne({ code: code, masterclientcode: mcc });
        return company || { status: httpConstants.HTTP_STATUS_NOT_FOUND, error: 'Company not found' }

    }catch(e){
        console.log("Error getting director company data: ", e);
        return { status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, error: 'Internal server error' }
    }

}



exports.requestToAssistance = async function (req, res) {
    try {

        const company = await getDirectorCompanyData(req.params.code, req.params.masterclientcode, req.user.email);

        if (company?.error) {
            return res.status(company.status).json({
                "status": company.status,
                "error": company.error
            });
        }


        const type = req.params.type === "directors" ? TYPE_OF_DIRECTOR_DIR : TYPE_OF_DIRECTOR_BO;
        let emailTo = process.env.REQUEST_UPDATE_EMAIL_THKO;
        let requestType = "";

        if (type == TYPE_OF_DIRECTOR_DIR) {
            requestType =  "No Director";            
        }
        else {
            requestType = "No BO";            
        }
        let companyDirectorBo = await VpDirectorInfoModel.findOne({
            where: {
                CompanyNumber: company.code
            },
            raw: true
        });
        let emailTemplate;
        let matchedPO = false;
        if (companyDirectorBo){

            if (companyDirectorBo.ProductionOffice === 'TBVI') {
                emailTo = process.env.REQUEST_UPDATE_EMAIL_TBVI;
                matchedPO = true;
            } else if (companyDirectorBo.ProductionOffice === 'THKO'){
                emailTo = process.env.REQUEST_UPDATE_EMAIL_THKO;
                matchedPO = true; 
            } else if (companyDirectorBo.ProductionOffice === 'TCYP'){
                emailTo = process.env.REQUEST_UPDATE_EMAIL_TCYP;
                matchedPO = true; 
            } else if (companyDirectorBo.ProductionOffice === 'TPANVG'){
                emailTo = process.env.REQUEST_UPDATE_EMAIL_TPANVG;
                matchedPO = true; 
            }


            emailTemplate = MailFormatter.generateDirBoRequestUpdateEmail({
                "companyCode": companyDirectorBo.EntityCode + " (" + company.code + ")",
                "companyName": companyDirectorBo.EntityName + " (" + company.name + ")",
                "mcc": companyDirectorBo.MasterClientCode + " (" + company.masterclientcode + ")",
                "directorCode": '',
                "requestor": req.user.username,
                "requestType": requestType,
                "comment": ""
            });
        }else{
            
            emailTemplate = MailFormatter.generateDirBoRequestUpdateEmail({
                "companyCode": company.code,
                "companyName": company.name,
                "mcc": company.masterclientcode,
                "directorCode": "",
                "requestor": req.user.username,
                "requestType": requestType,
                "comment": ""
            });
        }

        let subject = `${company.name} – No Director/BO found in the portal`

        let emailResponse = await MailController.asyncSend(            
            emailTo,
            matchedPO ? subject : '(!Production office unknown) ' + subject ,
            emailTemplate.textString,
            emailTemplate.htmlString
        );
        if (emailResponse.error) {
            console.log("Send director email error: ", emailResponse);
            return res.status(500).json({ "status": 500, "message": "There was an error sending the request for assistance" });
        }

        return res.status(200).json({ "status": 200, "message": "Request for assistance has been created successfully" });

    } catch (e) {
        console.log("Error creating request assistance: ", e);
        return res.status(500).json({
            "status": 500,
            "error": 'Internal server error'
        });
    }
}


function getListFieldsToValidate(isBeneficialOwner, filetype, officerType){
    const fieldsByType = isBeneficialOwner ? BO_REQUIRED_FIELDS : DIRECTOR_REQUIRED_FIELDS;
    let fieldsToValidate = [];

    if (isBeneficialOwner){
        fieldsToValidate = fieldsByType[officerType];
            
    }else{
        fieldsToValidate = filetype === "individual" ? fieldsByType["individual"] : fieldsByType["company"];
    }
    
    return fieldsToValidate
}