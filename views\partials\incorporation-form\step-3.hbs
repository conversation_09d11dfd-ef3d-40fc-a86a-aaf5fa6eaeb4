<div class="col-lg-12">
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-12 hide-element" id="blacklistedCountryRowStep3" >
                    <div class="alert alert-warning" role="alert">
                        <i class="fa fa-warning mr-2"></i><b>This country is blacklisted
                            and cannot be accepted.</b> Please contact us at <a
                            href="mailto:<EMAIL>"><EMAIL></a>
                    </div>
                </div>
            </div>
            <div class="row">
                <div  id="requireSubstanceServiceRowStep3" class="col-12 {{#ifEquals
                    incorporation.principalBusinessActivity "Holding Business (Pure Equity Holding entities)" }}
                    hide-element{{/ifEquals}} 
                    {{#ifEquals
                    incorporation.principalBusinessActivity "Other" }}
                    hide-element {{/ifEquals}}
                    {{#ifEquals incorporation.principalBusinessActivity "" }}
                   hide-element

                    {{/ifEquals}}

                    {{#unless incorporation.principalBusinessActivity }}
                    hide-element
                    {{/unless}}">

                    <div class="alert alert-warning" role="alert">
                        <i class="fa fa-warning mr-2"></i>This activity requires economic substance services.
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="principalBusinessActivityControl">Principal business activities*</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <select name="principalBusinessActivity" id="principalBusinessActivityControl"
                            class="form-control w-100" data-toggle="select2" required>
                            <option value="" hidden>Select an option</option>
                            <option {{#ifEquals
                                incorporation.principalBusinessActivity "Holding Business (Pure Equity Holding entities)"
                                }} selected {{/ifEquals}}>Holding Business (Pure Equity Holding entities)</option>
                            <option {{#ifEquals incorporation.principalBusinessActivity "Finance and leasing Business"
                                }} selected {{/ifEquals}}>Finance and leasing Business</option>
                            <option {{#ifEquals incorporation.principalBusinessActivity "Banking Business" }} selected
                                {{/ifEquals}}>Banking Business</option>
                            <option {{#ifEquals incorporation.principalBusinessActivity "Insurance Business" }} selected
                                {{/ifEquals}}>Insurance Business</option>
                            <option {{#ifEquals incorporation.principalBusinessActivity "Fund management Business" }}
                                selected {{/ifEquals}}>Fund management Business</option>
                            <option {{#ifEquals incorporation.principalBusinessActivity "Headquarters Business" }}
                                selected {{/ifEquals}}>Headquarters Business</option>
                            <option {{#ifEquals incorporation.principalBusinessActivity "Shipping Business" }} selected
                                {{/ifEquals}}>Shipping Business</option>
                            <option {{#ifEquals incorporation.principalBusinessActivity "Intellectual property Business"
                                }} selected {{/ifEquals}}>Intellectual property Business</option>
                            <option {{#ifEquals
                                incorporation.principalBusinessActivity "Distribution and service centre Business" }}
                                selected {{/ifEquals}}>Distribution and service centre Business</option>
                            <option {{#ifEquals incorporation.principalBusinessActivity "Other" }} selected
                                {{/ifEquals}}>Other</option>
                        </select>
                    </div>
                </div>
            </div>
            <div id="principalBusinessActivityOtherRow"  class="row {{#ifCond
                incorporation.principalBusinessActivity '!=' "Other" }} hide-element {{/ifCond}}">
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="principalBusinessActivityOtherControl">Please specify*</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <textarea class="form-control" name="principalBusinessActivityOther"
                            id="principalBusinessActivityOtherControl" rows="3"
                            required>{{incorporation.principalBusinessActivityOther}}</textarea>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="activityJurisdiction">Jurisdiction where activities take
                            place*</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        {{>file-reviewer/shared/select-country selectId="activityJurisdiction" required="true"
                        value=incorporation.activityJurisdiction}}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="taxResidenceControl">Tax Residence*</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="row">
                        <div class="col-6">
                            <div class="custom-control custom-radio">
                                <input class="custom-control-input" type="radio" id="taxResidenceBVI"
                                    name="taxResidenceControl" value="BVI" {{#ifEquals incorporation.taxResidence "BVI"
                                    }} checked {{/ifEquals}} required>
                                <label class="custom-control-label" for="taxResidenceBVI">BVI</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="custom-control custom-radio">
                                <input class="custom-control-input" type="radio" id="taxResidenceForeign"
                                    name="taxResidenceControl" value="Foreign" {{#ifEquals
                                    incorporation.taxResidence "Foreign" }} checked {{/ifEquals}} required>
                                <label class="custom-control-label" for="taxResidenceForeign">Foreign</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="taxJurisdictionRow" class="row {{#ifCond incorporation.taxResidence '!=' "Foreign" }}
               hide-element {{/ifCond}}">
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="taxResidencyJurisdiction">Jurisdiction of Tax Residency*</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        {{>file-reviewer/shared/select-country selectId="taxResidencyJurisdiction" required="true"
                        value=incorporation.taxResidencyJurisdiction}}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="progress">
                        <div class="progress-bar width-37" role="progressbar"  aria-valuenow="3"
                            aria-valuemin="0" aria-valuemax="8">3 of 8
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="/views-js/partials/incorporate-form/step-3.js"></script>