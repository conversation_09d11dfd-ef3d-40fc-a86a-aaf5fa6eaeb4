(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['createassetrow'] = template({"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data) {
    var stack1, alias1=container.lambda, alias2=container.escapeExpression, alias3=depth0 != null ? depth0 : (container.nullContext || {}), alias4=container.hooks.helperMissing, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "<tr id=\"asset-table-row-"
    + alias2(alias1(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"asset") : stack1)) != null ? lookupProperty(stack1,"_id") : stack1), depth0))
    + "\" class=\"asset-row\">\r\n    <td>\r\n        "
    + alias2(alias1(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"asset") : stack1)) != null ? lookupProperty(stack1,"description") : stack1), depth0))
    + "\r\n    </td>\r\n    <td>\r\n        "
    + alias2(alias1(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"asset") : stack1)) != null ? lookupProperty(stack1,"purchaseYear") : stack1), depth0))
    + "\r\n    </td>\r\n    <td class=\"text-right\">\r\n        $ "
    + alias2((lookupProperty(helpers,"decimalValue")||(depth0 && lookupProperty(depth0,"decimalValue"))||alias4).call(alias3,((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"asset") : stack1)) != null ? lookupProperty(stack1,"purchaseCost") : stack1),{"name":"decimalValue","hash":{},"data":data,"loc":{"start":{"line":9,"column":10},"end":{"line":9,"column":50}}}))
    + "\r\n    </td>\r\n    <td class=\"text-right\">\r\n        $ "
    + alias2((lookupProperty(helpers,"decimalValue")||(depth0 && lookupProperty(depth0,"decimalValue"))||alias4).call(alias3,((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"asset") : stack1)) != null ? lookupProperty(stack1,"assessedCost") : stack1),{"name":"decimalValue","hash":{},"data":data,"loc":{"start":{"line":12,"column":10},"end":{"line":12,"column":50}}}))
    + "\r\n    </td>\r\n    <td class=\"text-right\">\r\n        <button type=\"button\" class=\"btn btn-outline-secondary openEditLoanAssetModal\"\r\n                data-id=\""
    + alias2(alias1(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"asset") : stack1)) != null ? lookupProperty(stack1,"_id") : stack1), depth0))
    + "\">\r\n            <i class=\"fa fa-pencil\"></i>\r\n        </button>\r\n    </td>\r\n    <td class=\"text-left\">\r\n        <button type=\"button\" class=\"delete btn btn-danger deleteAsset\"\r\n                data-id=\""
    + alias2(alias1(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"asset") : stack1)) != null ? lookupProperty(stack1,"_id") : stack1), depth0))
    + "\">\r\n            <i class=\"fa fa-trash\"></i>\r\n        </button>\r\n    </td>\r\n</tr>\r\n\r\n\r\n";
},"useData":true});
})();