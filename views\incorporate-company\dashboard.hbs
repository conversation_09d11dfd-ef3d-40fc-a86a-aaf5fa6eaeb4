<main class="px-4">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <h4 class="mt-3">Company Incorporation</h4>
                    <div class="row justify-content-end mb-2">
                        {{#if notStarted}}
                        <button id="createNew" class="btn solid royal-blue width-xl mr-2">Start New
                            Incorporation
                        </button>
                        {{/if}}
                        {{#unless notStarted}}
                        <a href="/masterclients/{{masterclientcode}}/incorporate-company/new"
                            class="btn solid royal-blue width-xl mr-2">Start New Incorporation</a>
                        {{/unless}}
                    </div>
                    <br>
                    <div class="table-responsive">
                        <table id="horizontal-datatable" class="table table-striped mb-0 w-100 nowrap">
                            <thead>
                                <tr>
                                    <th class="header-30-percent">Company</th>
                                    <th class="header-20-percent">Status</th>
                                    <th class="header-20-percent">Updated at</th>
                                    <th class="header-30-percent"></th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each incorporations}}
                                <tr>
                                    <td>{{name}}</td>
                                    <td>{{status}}</td>
                                    <td>{{formatDate updatedAt "MM/DD/YYYY HH:mm:ss a"}}</td>
                                    <td class="text-right">
                                        {{#ifEquals status 'IN PROGRESS'}}
                                        <a href="/masterclients/{{../masterclientcode}}/incorporate-company/{{_id}}"
                                            class="btn solid royal-blue">Continue</a>
                                        {{#ifCond incorporationStatus '!==' 'NAME IN REVIEW'}}
                                        <button
                                            href="/masterclients/{{../masterclientcode}}/incorporate-company/{{_id}}"
                                            data-id="{{_id}}" class="btn solid btn-danger deleteIncorporation">Delete
                                        </button>
                                        {{/ifCond}}
                                        {{/ifEquals}}
                                        {{#ifEquals status 'NOT STARTED'}}
                                        <a href="/masterclients/{{../masterclientcode}}/incorporate-company/{{_id}}"
                                            class="btn solid royal-blue">Continue</a>
                                        <button
                                            href="/masterclients/{{../masterclientcode}}/incorporate-company/{{_id}}"
                                            data-id="{{_id}}" class="btn solid btn-danger deleteIncorporation">Delete
                                        </button>
                                        {{/ifEquals}}
                                        {{#ifEquals status 'SUBMITTED'}}
                                        <a href="/masterclients/{{../masterclientcode}}/incorporate-company/{{_id}}/incorporation.pdf"
                                            class="btn solid royal-blue downloadBtnIncorporation" 
                                            target="_blank">
                                            Download</a>
                                        <a href="/masterclients/{{../masterclientcode}}/payments"
                                            class="btn solid btn-success px-3">Pay</a>
                                        {{/ifEquals}}
                                        {{#ifEquals status 'PAID'}}
                                        <a href="/masterclients/{{../masterclientcode}}/incorporate-company/{{_id}}/incorporation.pdf"
                                            class="btn solid royal-blue width-xl" target="_blank">Download
                                            PDF</a>
                                        {{/ifEquals}}
                                        {{#ifEquals incorporationStatus 'PENDING INFORMATION'}}
                                        <a href="/masterclients/{{../masterclientcode}}/incorporate-company/{{_id}}/request-information"
                                            class="btn solid royal-blue width-xl">Upload Information</a>
                                        {{/ifEquals}}
                                        {{#ifEquals incorporationStatus 'APPROVED'}}
                                        <a href="/masterclients/{{../masterclientcode}}/incorporate-company/{{_id}}/approved-information"
                                            class="btn btn-success">Show documents</a>
                                        {{/ifEquals}}
                                        {{#ifEquals nameReservationStatus 'DECLINED'}}
                                        <button class="btn btn-warning  waves-effect waves-light" data-toggle="modal"
                                            data-target="#updateNameReservationModal" data-mcc="{{../masterclientcode}}"
                                            data-incorporation-id="{{_id}}">Update Name</button>
                                        {{/ifEquals}}
                                        {{#ifEquals incorporationStatus 'DECLINED'}}
                                        <button class="btn btn-warning   width-xl waves-effect waves-light"
                                            data-toggle="modal" data-target="#showDeclineReasonModal"
                                            data-mcc="{{../masterclientcode}}" data-name="{{name}}"
                                            data-incorporation-id="{{_id}}">Show reason</button>
                                        {{/ifEquals}}

                                    </td>
                                </tr>
                                {{else}}
                                <tr>
                                    <td colspan="4" class="text-center font-italic">
                                        There are no incorporations available
                                    </td>
                                    <td class="hide-element"></td>
                                    <td class="hide-element"></td>
                                    <td class="hide-element"></td>
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                    </div>
                    <br>
                    <a href="/masterclients/{{masterclientcode}}"
                        class="btn btn-secondary waves-effect waves-light width-xl mt-3 mr-2">Back</a>
                    <a href="/masterclients/{{masterclientcode}}/payments"
                        class="btn btn-secondary waves-effect waves-light width-xl mt-3" {{#unless pendingPayment}}class="hide-element"{{/unless}}>Payments</a>
                </div>
            </div>
        </div>
    </div>
</main>
{{>incorporation-form/modals/check-decline-reason-modal}}
{{>incorporation-form/modals/update-name-modal}}

<script type="text/javascript" src="/views-js/incorporate-company/dashboard.js"></script>
