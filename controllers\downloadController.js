const httpConstants = require('http2').constants;
const { BlobServiceClient, StorageSharedKeyCredential } = require("@azure/storage-blob");
const { DefaultAzureCredential } = require("@azure/identity");
const CompanyIncorporationModel = require('../models/clientIncorporation');
const FinancialReportModel = require('../models/financial-report');
const MessageModel = require('../models/message');
const RequestedFileModel = require('../models/requestedFile');
const EntryModel = require('../models/entry').EntryModel;

exports.downloadIncorporationFiles = async function (req, res, next) {
  try {
    let incorporation = await CompanyIncorporationModel.findById(req.params.incorporationId);
    let fileToDownload;
    if (!incorporation) {
      const err = new Error('Review not found');
      err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
      return next(err);
    }

    if (!req.query.relation) {
      if (req.query.requestDataId) {
        const requestInfo = incorporation.requestInformation.find((requestData) =>
          requestData && requestData._id.toString() === req.query.requestDataId);
        if (requestInfo) {
          fileToDownload = requestInfo.files.find((f) => f && f.fileId.toString() === req.params.fileId);
        }
      }
      else if (req.query.informationId) {
        const returnedInformation = incorporation.clientReturnedInformation.find((clientInfo) =>
          clientInfo && clientInfo._id.toString() === req.query.informationId);
        if (returnedInformation) {
          fileToDownload = returnedInformation.files.find((f) => f && f.fileId.toString() === req.params.fileId);
        }
      }
      else if (incorporation.files) {
        const files = [...incorporation.files.structureChartFiles, ...incorporation.files.passportFiles,
        ...incorporation.files.addressProofFiles, ...incorporation.files.otherDeclarationFiles, ...incorporation.files.officialIncorporationFiles];
        fileToDownload = files.find((f) => f && f.fileId.toString() === req.params.fileId);
      }
    }
    else {
      const relation = incorporation.relations.find((r) => r._id && r._id.toString() === req.query.relation);
      if (relation) {
        let relationFiles;
        if (relation.type === "natural") {
          const identificationFiles = relation.identification.files ? relation.identification.files : [];
          const pepFiles = relation.pepDetails.files ? relation.pepDetails.files : [];
          const worldCheckFiles = relation.worldCheck.files ? relation.worldCheck.files : [];
          const electronicIdFiles = relation.electronicIdInfo && relation.electronicIdInfo.files ? relation.electronicIdInfo.files : [];
          relationFiles = [...identificationFiles, ...pepFiles, ...worldCheckFiles, ...electronicIdFiles];
        } else {
          const detailsFiles = relation.details.files ? relation.details.files : [];
          const detailsPartnerFiles = relation.detailsPartner.files ? relation.detailsPartner.files : [];
          const limitedFiles = relation.limitedCompanyDetails.files ? relation.limitedCompanyDetails.files : [];
          const mutualFundFiles = relation.mutualFundDetails.files ? relation.mutualFundDetails.files : [];
          const foundationFiles = relation.foundation.files ? relation.foundation.files : [];
          const worldCheckFiles = relation.worldCheck.files ? relation.worldCheck.files : [];
          relationFiles = [...detailsFiles, ...detailsPartnerFiles, ...limitedFiles, ...mutualFundFiles, ...foundationFiles,
          ...worldCheckFiles];
        }

        if (relationFiles) {
          const files = relationFiles.find((f) => f.id === req.query.fileType);
          if (files && files.uploadFiles) {
            fileToDownload = files.uploadFiles.find((f) => f && f.fileId.toString() === req.params.fileId);
          }
        }



      }
    }
    if (!fileToDownload && req.session.relationFiles && req.query.fileGroup) {
      const sessionFiles = req.session.relationFiles[req.query.fileGroup];
      if (sessionFiles) {
        const tempFiles = sessionFiles[req.query.row] ? sessionFiles[req.query.row] : [];
        if (tempFiles.length) {
          fileToDownload = tempFiles.find((uploadFile) => uploadFile.fileId === req.params.fileId);
        }
      }

    }


    if (fileToDownload && fileToDownload.url) {
      dowloadFile(fileToDownload, res, true);
    }
    else {
      const err = new Error('File not found');
      err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
      return next(err);
    }

  } catch (e) {
    console.log("error ", e);
    const err = new Error('File not found');
    err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
    return next(err);
  }
};


exports.downloadFinancialReportFiles = async function (req, res, next) {
  try {
    const report = await FinancialReportModel.findById(req.params.reportId);
    let fileToDownload;
    if (!report) {
      const err = new Error('Review not found');
      err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
      return next(err);
    }

    if (report.files) {
      const files = [
        ...report.files.exemptEvidenceFiles,
        ...report.files.copyResolutionFiles,
        ...report.files.accountingRecordFiles,
        ...report.files.annualReturnFiles,
      ];
      fileToDownload = files.find((f) => f && f.fileId.toString() === req.params.fileId);
    }


    if (fileToDownload && fileToDownload.url) {
      dowloadFile(fileToDownload, res, true);
    }
    else {
      const err = new Error('File not found');
      err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
      return next(err);
    }

  } catch (e) {
    console.log("error ", e);
    const err = new Error('File not found');
    err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
    return next(err);
  }
};

exports.downloadMessageFile = async function (req, res, next) {
  try {
    let message = await MessageModel.findById(req.params.id);
    let fileToDownload;

    if (message && message.files && message.files.length > 0) {
      fileToDownload = message.files.find((f) => f.fileId === req.params.fileId);
    }
    if (fileToDownload && fileToDownload.url) {
      dowloadFile(fileToDownload, res, true);
    }
    else {
      const err = new Error('File not found');
      err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
      return next(err);
    }

  } catch (e) {
    console.log("error ", e);
    const err = new Error('File not found');
    err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
    return next(err);
  }
};


exports.downloadRequestedFiles = async function (req, res, next) {
  try {
    const companyRequest = await RequestedFileModel.findById(req.params.requestId);
    let fileToDownload;
    if (!companyRequest) {
      const err = new Error('Company request not found');
      err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
      return next(err);
    }

    if (companyRequest.files) {
      const files = companyRequest.files.find((f) => f.id === req.params.fileTypeId);

      if (files && files.uploadFiles && files.uploadFiles.length > 0) {
        fileToDownload = files.uploadFiles.find((f) => f && f.fileId.toString() === req.params.fileId);
      }
    }

    if (!fileToDownload && req.session.requestedTempFiles) {
      const sessionFiles = req.session.requestedTempFiles[req.params.fileTypeId];
      if (sessionFiles && sessionFiles.length > 0) {
        fileToDownload = sessionFiles.find((uploadFile) => uploadFile.fileId === req.params.fileId);
      }
    }


    if (fileToDownload && fileToDownload.url) {
      dowloadFile(fileToDownload, res, true);
    }
    else {
      const err = new Error('File not found');
      err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
      return next(err);
    }

  } catch (e) {
    console.log("error ", e);
    const err = new Error('File not found');
    err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
    return next(err);
  }
};

exports.downloadRFISubstanceFile = async function (req, res, next) {
  try {
    const entry = await EntryModel.findById(req.params.entryId);

    if (!entry) {
      const err = new Error('Entry not found');
      err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
      return next(err);
    }

    const requestedInformationList = entry.requested_information?.details || [];
    let rfiInfo = requestedInformationList.find((requestInfo) => requestInfo.id === req.params.requestId);

    if (!rfiInfo) {
      const err = new Error('File not found');
      err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
      return next(err);
    }

    let fileToDownload = rfiInfo.files?.length > 0 ? rfiInfo.files.find((f) => f.fileId && f.fileId === req.params.fileId) : null;

    if (fileToDownload && fileToDownload.url) {
      dowloadFile(fileToDownload, res, true);
    }
    else {
      const err = new Error('File not found');
      err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
      return next(err);
    }

  } catch (e) {
    console.log("error ", e);
    const err = new Error('File not found');
    err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
    return next(err);
  }
};

exports.downloadRFIFinancialReportFile = async function (req, res, next) {
  try {
    const report = await FinancialReportModel.findById(req.params.reportId);

    if (!report) {
      const err = new Error('report not found');
      err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
      return next(err);
    }

    const requestedInformationList = report.requestedInformation?.details || [];
    let rfiInfo = requestedInformationList.find((requestInfo) => requestInfo.id === req.params.requestId);
    if (!rfiInfo) {
      const err = new Error('File not found');
      err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
      return next(err);
    }

    let fileToDownload = rfiInfo.files?.length > 0 ? rfiInfo.files.find((f) => f.fileId && f.fileId === req.params.fileId) : null;
    if (fileToDownload && fileToDownload.url) {
      dowloadFile(fileToDownload, res, true);
    }
    else {
      const err = new Error('File not found');
      err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
      return next(err);
    }

  } catch (e) {
    console.log("error ", e);
    const err = new Error('File not found');
    err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
    return next(err);
  }
};


function dowloadFile(fileToDownload, res, inlineDownload) {
  let accountName = process.env.AZURE_STORAGE_ACCOUNT;
  let accountKey = process.env.AZURE_STORAGE_ACCESS_KEY;

  let credentials;

  if (!accountKey) {
    credentials = new DefaultAzureCredential();
  } else {
    // Only for local development and testing
    credentials = new StorageSharedKeyCredential(accountName, accountKey);
  }
  const blobServiceClient = new BlobServiceClient(`https://${accountName}.blob.core.windows.net`, credentials);
  const containerClient = blobServiceClient.getContainerClient(fileToDownload.container);


  const pathFile = fileToDownload.url.split(fileToDownload.container + "/");
  const blobClient = containerClient.getBlobClient(pathFile[1]);

  blobClient.download().then((downloadResponse) => {
    if (downloadResponse.contentLength === 0) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).send({ "message": "File not found" });
    }
    res.contentType(fileToDownload.mimeType ? fileToDownload.mimeType : fileToDownload.mimetype);
    if (inlineDownload) {
      res.set('Content-Disposition: inline; filename="' + fileToDownload.blobName + '"');
    }
    downloadResponse.readableStreamBody.pipe(res);
  }).catch((error) => {
    console.error("Error downloading blob:", error);
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).send({ "message": "Error downloading file" });
  });
}