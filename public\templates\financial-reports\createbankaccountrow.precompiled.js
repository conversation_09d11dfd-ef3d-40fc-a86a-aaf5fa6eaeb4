(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['createbankaccountrow'] = template({"1":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, alias5=container.lambda, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "<tr id=\"bank-account-row-"
    + alias4(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":2,"column":25},"end":{"line":2,"column":32}}}) : helper)))
    + "\" class=\"bank-account-row\">\r\n    <td> "
    + alias4(((helper = (helper = lookupProperty(helpers,"description") || (depth0 != null ? lookupProperty(depth0,"description") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"description","hash":{},"data":data,"loc":{"start":{"line":3,"column":9},"end":{"line":3,"column":24}}}) : helper)))
    + " </td>\r\n    <td> "
    + alias4(((helper = (helper = lookupProperty(helpers,"accountType") || (depth0 != null ? lookupProperty(depth0,"accountType") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"accountType","hash":{},"data":data,"loc":{"start":{"line":4,"column":9},"end":{"line":4,"column":24}}}) : helper)))
    + " </td>\r\n    <td>"
    + alias4((lookupProperty(helpers,"decimalValue")||(depth0 && lookupProperty(depth0,"decimalValue"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"openingAmount") : depth0),{"name":"decimalValue","hash":{},"data":data,"loc":{"start":{"line":5,"column":8},"end":{"line":5,"column":38}}}))
    + " </td>\r\n    <td> "
    + alias4((lookupProperty(helpers,"decimalValue")||(depth0 && lookupProperty(depth0,"decimalValue"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"closingAmount") : depth0),{"name":"decimalValue","hash":{},"data":data,"loc":{"start":{"line":6,"column":9},"end":{"line":6,"column":39}}}))
    + "</td>\r\n    <td> "
    + alias4(((helper = (helper = lookupProperty(helpers,"transactionCurrency") || (depth0 != null ? lookupProperty(depth0,"transactionCurrency") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"transactionCurrency","hash":{},"data":data,"loc":{"start":{"line":7,"column":9},"end":{"line":7,"column":32}}}) : helper)))
    + "</td>\r\n    <td> "
    + alias4((lookupProperty(helpers,"decimalValue")||(depth0 && lookupProperty(depth0,"decimalValue"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"cashAtBank") : depth0),{"name":"decimalValue","hash":{},"data":data,"loc":{"start":{"line":8,"column":9},"end":{"line":8,"column":36}}}))
    + "</td>\r\n    <td class=\"justify-content-center  d-flex\">\r\n        <button type=\"button\" class=\"btn btn-sm royal-blue solid mr-1\" data-report-id=\""
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"reportId") : depths[1]), depth0))
    + "\" data-bank-account-id=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":10,"column":126},"end":{"line":10,"column":133}}}) : helper)))
    + "\"\r\n            data-toggle=\"modal\" data-target=\"#newCashTransactionModal\">\r\n            <i class=\"fa fa-pencil\"></i>\r\n        </button>\r\n        <button type=\"button\" class=\"btn btn-sm btn-danger deleteBankAccount\" data-report-id=\""
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"reportId") : depths[1]), depth0))
    + "\" data-bank-account-id=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":14,"column":133},"end":{"line":14,"column":140}}}) : helper)))
    + "\">\r\n            <i class=\"fa fa-times\"></i>\r\n        </button>\r\n    </td>\r\n</tr>\r\n";
},"3":function(container,depth0,helpers,partials,data) {
    return "<tr>\r\n    <td colspan=\"7\">\r\n        No Cash/Bank Accounts found\r\n    </td>\r\n</tr>\r\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"bankAccounts") : depth0),{"name":"each","hash":{},"fn":container.program(1, data, 0, blockParams, depths),"inverse":container.program(3, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":1,"column":0},"end":{"line":25,"column":9}}})) != null ? stack1 : "");
},"useData":true,"useDepths":true});
})();