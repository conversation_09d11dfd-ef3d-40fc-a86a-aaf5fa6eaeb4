<!doctype html>
<html lang="en-us">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="meta-csrf-token" content="{{csrfToken}}">
    <title>{{title}}</title>
    <!-- inject-head:css -->
    <link rel="icon" href="/images/favicon.ico" type="image/x-icon">

    <link href="/javascripts/libs/jquery-nice-select/nice-select.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/switchery/switchery.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/multiselect/multi-select.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/select2/select2.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/bootstrap-touchspin/jquery.bootstrap-touchspin.css" rel="stylesheet"
          type="text/css" />
    <link href="/javascripts/libs/flatpickr/flatpickr.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/bootstrap-colorpicker/dist/css/bootstrap-colorpicker.css" rel="stylesheet"
          type="text/css" />
    <link href="/javascripts/libs/clockpicker/bootstrap-clockpicker.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/bootstrap-datepicker/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/dropzone/dropzone.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/dropify/dropify.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/sweetalert2/sweetalert2.min.css" rel="stylesheet" type="text/css" />
    <link href="/stylesheets/css/font-awesome.min.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/toastr/toastr.min.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="/javascripts/libs/intlTelInput/intlTelInput.min.css">
    <link href="/javascripts/libs/bootstrap-select/bootstrap-select.min.css" rel="stylesheet" type="text/css" />


    <link href="/javascripts/libs/datatables/dataTables.bootstrap4.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/datatables/responsive.bootstrap4.css" rel="stylesheet" type="text/css" />
    <link href="/javascripts/libs/datatables/select.bootstrap4.css" rel="stylesheet" type="text/css" />

    <link rel="stylesheet" href="/stylesheets/app.min.css">    
    <link rel="stylesheet" href="/stylesheets/bootstrap.min.css">
    <link rel="stylesheet" href="/stylesheets/jquery-ui.min.css">
    <link rel="stylesheet" href="/stylesheets/style.css">

    <script src="/javascripts/vendor.min.js"></script>
    <script src="/javascripts/jquery-ui.min.js"></script>
    <script src="/javascripts/popper.min.js"></script>
    <script src="/javascripts/bootstrap.min.js"></script>

    <script src="/javascripts/libs/jquery-nice-select/jquery.nice-select.min.js"></script>
    <script src="/javascripts/libs/switchery/switchery.min.js"></script>
    <script src="/javascripts/libs/multiselect/jquery.multi-select.js"></script>
    <script src="/javascripts/libs/select2/select2.min.js"></script>
    <script src="/javascripts/libs/jquery-mockjax/jquery.mockjax.min.js"></script>
    <script src="/javascripts/libs/autocomplete/jquery.autocomplete.min.js"></script>
    <script src="/javascripts/libs/bootstrap-touchspin/jquery.bootstrap-touchspin.min.js"></script>
    <script src="/javascripts/libs/bootstrap-maxlength/bootstrap-maxlength.min.js"></script>
    <script src="/javascripts/libs/flatpickr/flatpickr.min.js"></script>
    <script src="/javascripts/libs/bootstrap-colorpicker/dist/js/bootstrap-colorpicker.js"></script>
    <script src="/javascripts/libs/clockpicker/bootstrap-clockpicker.min.js"></script>
    <script src="/javascripts/libs/bootstrap-datepicker/bootstrap-datepicker.min.js"></script>
    <script src="/javascripts/libs/dropzone/dropzone.min.js"></script>
    <script src="/javascripts/libs/handlebars/handlebars.runtime.min-v4.7.7.js"></script>
    <script src="/javascripts/libs/sweetalert2/sweetalert2.min.js"></script>
    <script src="/javascripts/libs/moment/moment.min.js"></script>
    <script src="/javascripts/libs/toastr/toastr.min.js"></script>
    <script type="text/javascript" src="/javascripts/jquery.serializejson.js"></script>

    <script src="/javascripts/libs/datatables/jquery.dataTables.min.js"></script>
    <script src="/javascripts/libs/datatables/dataTables.bootstrap4.js"></script>
    <script src="/javascripts/libs/datatables/dataTables.responsive.min.js"></script>
    <script src="/javascripts/libs/datatables/responsive.bootstrap4.min.js"></script>
    <script src="/javascripts/libs/datatables/dataTables.buttons.min.js"></script>
    <script src="/javascripts/libs/datatables/buttons.bootstrap4.min.js"></script>
    <script src="/javascripts/libs/datatables/buttons.html5.min.js"></script>
    <script src="/javascripts/libs/datatables/buttons.flash.min.js"></script>
    <script src="/javascripts/libs/datatables/buttons.print.min.js"></script>
    <script src="/javascripts/libs/datatables/dataTables.keyTable.min.js"></script>
    <script src="/javascripts/libs/datatables/dataTables.select.min.js"></script>
    <script src="/javascripts/libs/bootstrap-select/bootstrap-select.min.js"></script>
    <script src="/javascripts/libs/axios/axios.min.js"></script>

    <script src="/javascripts/libs/intlTelInput/intlTelInput.min.js"></script>
    <script type="text/javascript" src="/views-js/helpers/countries-helper.js"></script>
    <script type="text/javascript" src="/views-js/layouts/main.js"></script>
    <!-- endinject -->
</head>

<body>
    <header class="main-header pt-2">
        <div class="container">


            <div >
                <nav class="navbar navbar-expand-lg navbar-light bg-light p-0">
                    <a class="navbar-brand " href="/">
                        <img src="/images/trident-logo-new.svg" alt="Trident Trust"  height="50" class="d-inline-block align-top">
                    </a>

                    {{#if user}}

                    <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarContent"
                        aria-controls="navbarContent" aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>


                    {{/if}}

                
                    <div class="collapse navbar-collapse" id="navbarContent">
                        <ul class="navbar-nav ml-auto">
                            <li class="nav-item mr-3">
                                Need help? <b><a class="nav-link p-0" href="mailto:<EMAIL>"> <EMAIL></a></b>
                            </li>

                            {{#if user}}

                            <li class="nav-item mr-2 pt-1 cursor-pointer">
                                <a href="/messages" class="fa-stack has-badge align-top fs-1_3" {{#if messages}}data-count="{{messages}}" {{/if}}>
                                    <i class="fa fa-envelope fa-stack-1x fa-inverse rounded-circle bg-dark-blue" ></i>
                                </a>
                            </li>


                            <li class="nav-item dropdown " >
                                <a class="nav-link dropdown-toggle d-flex align-items-center  text-dark-blue " href="#" id="navbarDropdown" role="button"
                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="fa fa-user-circle fs-2_5" aria-hidden="true"></i>
                                    <span class="mx-1"><b>{{user.username}}</b></span>
                                    <i class="fa fa-chevron-down" aria-hidden="true"></i>
                                </a>
                                <div class="dropdown-menu dropdown-menu-right" aria-labelledby="navbarDropdown">
                                    <ul>
                                        <li class="dropdown-item pl-1 ">
                                            <a  href="/users/change-password" class="text-dark"><i class="fa fa-wrench" aria-hidden="true"></i> Change password</a>
                                        </li>
   
                                        <div class="dropdown-divider"></div>
                                        <li class="dropdown-item p-0">
                                            <button id="btnLogout" class="btn  btn-danger w-100">Logout</button>
                                        </li>
                                    </ul>
                                
                                </div>
                            </li>

                            {{/if}}
                        </ul>
                    </div>
                </nav>
                
                
                {{#if user}}
                    {{#if company}}
                    <br><br>
                    <div class="d-flex d-inline-flex mb-0 fs-2">
                        <span class="mr-3">
                            {{company.name}}
                        </span>
                        <span>
                            {{company.incorporationcode}}
                        </span>
                    
                    </div>
                    {{/if}}

                {{/if}}
            </div>

        </div>
        <!-- /.container -->
    </header>
    {{{body}}}
    <script type='text/javascript' src='/javascripts/form-advanced.init.js'></script>
    <script type='text/javascript' src='/javascripts/form-pickers.init.js'></script>
    <script type='text/javascript' src='/javascripts/jquery.disableAutoFill.min.js'></script>

    <div class='ie-div browser-alert'>
        <div class='text-center'>
            To protect your security this portal is only accessible with Google Chrome, safari, Firefox and Edge. Click 
            <a href='microsoft-edge:{{appHost}}' class='browser-alert-link'>here</a> to open in Edge</div>
    </div>
</body>

</html>
