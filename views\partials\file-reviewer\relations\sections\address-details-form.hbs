<!-- PRINCIPAL ADDRESS / RESIDENTIAL ADDRESS -->
<div class="row mt-4">
    <div class="col-2">
        <label for="{{group}}-{{formType}}-primaryAddress" >Address - 1st Line*</label>
    </div>
    <div class="col-4">
        <input name="{{formType}}[primaryAddress]" type="text" class="form-control" required
               id="{{group}}-{{formType}}-primaryAddress" value="{{principalAddress.primaryAddress}}"/>
    </div>
    <div class="col-2">
        <label for="{{group}}-{{formType}}-secondaryAddress">Address - 2nd Line</label>
    </div>
    <div class="col-4">
        <input name="{{formType}}[secondaryAddress]" type="text" class="form-control"
               id="{{group}}-{{formType}}-secondaryAddress" value="{{principalAddress.secondaryAddress}}"/>
    </div>
</div>
<div class="row mt-2">
    <div class="col-2">
        <label for="{{group}}-{{formType}}-country">Country*</label>
    </div>
    <div class="col-4">
        {{>file-reviewer/shared/select-country selectId=(concat formType "[country]") group=group required="true"
                value=principalAddress.country}}
    </div>
    <div class="col-2">
        <label for="{{group}}-{{formType}}-state">State/Province</label>
    </div>
    <div class="col-4">
        <input name="{{formType}}[state]" type="text" class="form-control"
               id="{{group}}-{{formType}}-state" value="{{principalAddress.state}}"/>
    </div>
</div>
<div class="row mt-2">
    <div class="col-2">
        <label for="{{group}}-{{formType}}-city">City*</label>
    </div>
    <div class="col-4">
        <input name="{{formType}}[city]" type="text" class="form-control" required
               id="{{group}}-{{formType}}-city" value="{{principalAddress.city}}"/>
    </div>
    <div class="col-2">
        <label for="{{group}}-{{formType}}-postal">Postal Code*</label>
    </div>
    <div class="col-4">
        <input name="{{formType}}[postalCode]" type="text" class="form-control" required
               id="{{group}}-{{formType}}-postal" value="{{principalAddress.postalCode}}"/>
    </div>
</div>

