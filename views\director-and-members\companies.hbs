<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <h4 class="mt-3">Overview of all companies related to Master Client Code:
                        <B>{{masterClientCode}}</B>
                    </h4>
                    <br>
                    <form method='GET'>
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="filterCompanyName">Company Name</label>
                                <input class='form-control' type='text' name='filterCompanyName' id='filterCompanyName' />
                            </div>
                            <div class="col-md-3">
                                <label for="filterIncorporationCode">Incorporation Code</label>
                                <input class='form-control' type='text' name='filterIncorporationCode'
                                    id='filterIncorporationCode' />
                            </div>
                            <div class="col-md-3 input-div">
                                <input type='SUBMIT' class='btn btn-light waves-effect' value='Search' />
                            </div>
                        </div>
                    </form>
                    <div class="table-responsive">
                        <table class="table table-striped mb-0">
                            <thead>
                                <tr>
                                    <th class="header-30-percent">Entity Name</th>
                                    <th class="header-15-percent">Incorporation Code </th>
                                    <th class="header-15-percent"></th>
                                    <th class="header-15-percent"></th>
                                    <th class="header-15-percent"></th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each companies}}

                                <tr>
                                    <td>{{company.name}}</td>
                                    <td>{{company.incorporationcode}} </td>
                                    <td>
                                        <a href="/masterclients/{{company.masterclientcode}}/director-and-members/{{company.code}}/directors"
                                            class="btn solid royal-blue width-xl submission-btn position-relative">
                                            Manage Directors
                                            {{#if unconfirmedDataDirector}}
                                            <span class="btn-circle" data-count="&nbsp;"></span>
                                            {{/if}}
                                        </a>
                                    </td>
                                    <td>
                                        {{#unless ../hideMembers}}
                                        <a href="/masterclients/{{company.masterclientcode}}/director-and-members/{{company.code}}/members"
                                            class="btn solid royal-blue width-xl submission-btn position-relative">
                                            Manage Members
                                            {{#if unconfirmedDataMember}}
                                            <span class="btn-circle" data-count="&nbsp;"></span>
                                            {{/if}}
                                        </a>
                                        {{/unless}}
                                    </td>
                                    <td>
                                        {{#unless ../hideBeneficialOwners}}
                                        <a href="/masterclients/{{company.masterclientcode}}/director-and-members/{{company.code}}/beneficial-owners"
                                            class="btn solid royal-blue width-xl submission-btn position-relative">
                                            Manage BO
                                            {{#if unconfirmedDataBO}}
                                            <span class="btn-circle" data-count="&nbsp;"></span>
                                            {{/if}}
                                        </a>
                                        {{/unless}}
                                    </td>
                                </tr>
                                {{/each}}
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </tbody>
                        </table>
                    </div> <!-- end .padding -->
                    <br>
                    <a href="/masterclients/{{masterClientCode}}"
                        class="btn btn-secondary waves-effect waves-light width-xl">Back</a>
                </div> <!-- end card-box-->
            </div> <!-- end col -->
        </div> <!-- end table-responsive-->
    </div> <!-- end card-box -->
</main>
