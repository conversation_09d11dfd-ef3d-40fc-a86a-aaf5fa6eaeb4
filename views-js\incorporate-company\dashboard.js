
$(document).ready(function () {
    $("#horizontal-datatable").DataTable({
        scrollX: !0,
        order: [],
        language: {
            paginate: {
                previous: "<i class='mdi mdi-chevron-left'>",
                next: "<i class='mdi mdi-chevron-right'>",
            },
        },
        drawCallback: function () {
            $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
        },
    });
});

$('#createNew').click(function () {
    createNew()
})

$(document).on('click', '.deleteIncorporation', async function () {
    await deleteIncorporation($(this).attr('data-id'))
})

function createNew() {
    Swal.fire(
        {
            title: "Create new submission?",
            text: "You already have a not started submission",
            icon: "question",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            cancelButtonText: "Cancel",
            confirmButtonText: "Create new"
        }).then(function (t) {
            if (t.value) {
                window.location = './incorporate-company/new'
            }
        });
}

async function deleteIncorporation(id) {
    Swal.fire(
        {
            title: "Delete?",
            text: "Are you sure you want to delete this incorporation submission?",
            icon: "question",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            cancelButtonText: "Cancel",
            confirmButtonText: "Ok"
        }).then(function (t) {
            if (t.value) {
                $.ajax({
                    type: "POST",
                    url: "./incorporate-company/" + id + "/delete",
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                        if (data.status === 200) {
                            document.location.reload();
                        } else {
                            Swal.fire(
                                {
                                    title: "Error!",
                                    text: data.message,
                                    icon: "warning",
                                    showCancelButton: false,
                                    confirmButtonColor: "#3085d6",
                                    confirmButtonText: "Ok"
                                });
                        }
                    },
                    error: function (err) {
                        toastr["warning"]('Submission could not be deleted, please try again later.', 'Error!');
                    }
                });
            }
        });
}