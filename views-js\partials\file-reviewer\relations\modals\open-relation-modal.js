$('#openRelationModal').on('show.bs.modal', function (event) {
    let button = $(event.relatedTarget); // Button that triggered the modal
    let reviewId = button.data('review-id');
    let relationId = button.data('relation-id');
    let relationType = button.data('type');
    let relationName = button.data('name');
    let relationGroup = button.data('group');
    let title = "";
    if (relationGroup === "beneficial"){
        title = 'Beneficial Owner: ' + relationName;
    }
    else if (relationGroup === "shareholder"){
        title = 'Shareholder: ' + relationName;
    }
    else{
        title = 'Director: ' + relationName;
    }

    $('#modal-relation-title').text(title);

    $.ajax({
        type: 'GET',
        url: '/file-reviewer/peek-relation/' + reviewId,
        data: { group: relationGroup, type: relationType, relationId: relationId },
        success: function (data) {
            if (data.success){
                let template = Handlebars.templates.peekrelation;
                let d = {
                    relation: data.relation,
                    relationInformation: data.relationInformation,
                    reviewId: reviewId
                };
                let html = template(d);
                $('#modal-relation-body').html(html);
            }

        },
        error: function () {
            Swal.fire(
                    'Error',
                    'There was an error while trying to fetch the relation',
                    'error'
            ).then(() => {
                $('#openRelationModal').modal('hide');
            });
        },
    });
});
