{{!-- EDIT PERCENTAGE MODAL --}}
<form id="modalPercentageForm">
<div class="modal fade" id="editPercentageModal" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title text-capitalize" id="percentageTitle"></h4>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
        </div>
            <div class="modal-body p-3 text-justify">
            <label for="modalInputPercentage">Percentage</label>
            <div class="col-8 input-group mb-3">
            <input 
            name="modalInputPercentage"
            id="modalInputPercentage" 
            type="number" 
            min="0"
            max="100" 
            class="form-control" />
            <div class="input-group-append">
            <span class="input-group-text">%</span>
            </div>
            </div>
      </div>
      <div class="modal-footer justify-content-between">
        <button type="button" class="btn btn-danger" data-dismiss="modal">
          Cancel
        </button>
        <button type="button" class="btn solid royal-blue" id="editPercentageButton">Edit</button>
      </div>
    </div>
  </div>
</div>
</form>
<script type="text/javascript" src="/views-js/partials/file-reviewer/edit-percentage-modal.js"></script>