{"name": "bootstrap-colorpicker", "version": "3.4.0", "description": "Bootstrap Colorpicker is a modular color picker plugin for Bootstrap 4.", "main": "dist/js/bootstrap-colorpicker.js", "homepage": "https://itsjavi.com/bootstrap-colorpicker/", "repository": {"type": "git", "url": "https://github.com/itsjavi/bootstrap-colorpicker.git"}, "bugs": {"url": "https://github.com/itsjavi/bootstrap-colorpicker/issues"}, "keywords": ["bootstrap", "colorpicker"], "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"bootstrap": ">=4.0", "jquery": ">=2.2", "popper.js": ">=1.10"}, "scripts": {"test": "npm run lint && ava --color --verbose", "start": "node_modules/.bin/http-server ./build/docs", "lint": "node_modules/.bin/eslint src/js ./*.js ./tests", "lint-fix": "node_modules/.bin/eslint --fix src/js ./*.js ./tests", "build": "gulp clean && gulp dist && gulp docs && gulp docs:add-v2-docs", "build-dist": "gulp dist", "build-docs": "gulp dist && gulp docs && gulp docs:add-v2-docs", "build-npm": "npm run build && gulp npm-prepublish", "publish-docs": "gh-pages -d build/docs"}, "devDependencies": {"ava": "^0.23.0", "babel-cli": "^6.24.1", "babel-eslint": "^10.0.3", "babel-loader": "^7.1.2", "babel-plugin-add-module-exports": "^0.3.2", "babel-preset-env": "^1.7.0", "color": "^3.1.2", "del": "^3.0.0", "eslint": "^6.7.2", "eslint-loader": "^3.0.2", "gh-pages": "^1.2.0", "gulp": "^3.9.1", "gulp-autoprefixer": "^4.0.0", "gulp-clean-css": "^3.9.4", "gulp-compile-handlebars": "^0.6.1", "gulp-header": "^1.8.12", "gulp-rename": "^1.2.3", "gulp-sass": "^2.3.2", "gulp-shell": "^0.6.5", "gulp-sourcemaps": "^2.6.5", "gulp-string-replace": "^1.1.2", "handlebars-layouts": "^3.1.4", "http-server": "^0.12.3", "jsdoc": "^3.6.3", "jsdom": "^11.12.0", "run-sequence": "^2.2.1", "uglifyjs-webpack-plugin": "^1.3.0", "webpack": "^4.41.2", "webpack-stream": "^5.2.1"}, "ava": {"files": ["tests/**/*test.js"], "source": ["src/js/**/*.{js,jsx}"], "concurrency": 5, "failFast": true, "failWithoutAssertions": false, "tap": false, "powerAssert": false, "require": ["babel-register"], "babel": "inherit"}}