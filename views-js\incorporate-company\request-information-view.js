Dropzone.autoDiscover = false;

$("#btnSubmit").on('click', function (event) {
    event.preventDefault();
    $(this).prop('disabled', true);
    $("#submitForm").submit();
});

$(async function () {
    const csrfToken = $("input[name='csrf-token']").val()
    const myDropZone = new Dropzone("#frmUploadModal", {
        url: "/",
        acceptedFiles: 'application/pdf,image/jpeg,image/png,image/gif',
        autoProcessQueue: true,
        parallelUploads: 1,
        maxFiles: 10,
        maxFilesize: 5,
        addRemoveLinks: true,
        paramName: function () {
            return 'fileUploaded';
        },
        uploadMultiple: true,
        init: function () {
            this.on("processing", function (file) {
                this.options.url =  window.location.pathname + "/upload-files";
            })
            this.on("success", function () {
                $("#btnSubmit").prop('disabled', false);
            })
            this.on("sending", function (file, xhr, formData) {
                $("#btnSubmit").prop('disabled', true);
                if (!formData.has('filetype')) {
                    formData.append("filetype", '');
                }
                formData.append('csrf-token', csrfToken);

            })

            this.on("error", function () {
                $("#btnSubmit").prop('disabled', false);
            })

            this.on('resetFiles', function () {
                if (this.files && this.files.length) {
                    for (let file of this.files) {
                        file.previewElement.remove();
                    }
                    this.files.length = 0;
                }
            });

            this.on("removedfile", async function (file) {
                var name = file.name;

                $.ajax({
                    type: 'POST',
                    url: window.location.pathname + "/delete-files",
                    data: {name: name},

                    success: function (data) {

                    }
                });
            });
        }
    })

})