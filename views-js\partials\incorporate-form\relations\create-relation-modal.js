$('.toggle-section-check').change(function () {
    const name = "content-" + $(this).attr('id');
    $('#' + name).toggle(200);
});

$('select').on('select2:select', function() {
    const select2 = $(this).next('.select2');

    const id = $(this).attr('id');

    $.fn.select2.amd.require(['select2/utils'], function (Utils) {
        const $watchers = select2.parents().filter(Utils.hasScroll);
        $watchers.off('scroll.select2.select2-' + id);
    });
});

$('#newRelationForm input[type="text"][required], textarea[required]').on('keyup', function () {
    const empty = $(this).val() === "";
    $(this).toggleClass("is-invalid", empty);
});

$('#newRelationForm input[type="email"]').on('keyup', function () {
    const valid_email = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    const val = $(this).val();
    const bad = !val.match(valid_email);
    $(this).toggleClass('is-invalid', bad);
});

$('#newRelationForm input[type="date"][required]').on('change', function () {
    const empty = $(this).val() === "";
    $(this).toggleClass("is-invalid", empty);
});

$('#newRelationForm select[required]:visible').on('change', function () {
    const empty = $(this).val() === "";
    // Used for select2 selectors (country select)
    $(`span[aria-labelledby='select2-${$(this).attr('id').replace('[', '').replace(']', '')}-container']`).toggleClass("is-invalid", empty);
    $(this).toggleClass("is-invalid", empty);
});

$("#newRelationForm input[type='radio']:visible").on('change', function () {
    const empty = $('input[name="' + this.name + '"]:checked').val() === "";
    $('input[name="' + this.name + '"]').toggleClass("is-invalid", empty);
});

$("#newRelationForm input[type='checkbox']:visible").on('change', function () {
    const empty = !($('input[name="' + this.name + '"]:checked').val());
    $('input[name="' + this.name + '"]').toggleClass("is-invalid", empty);
});


function hideSections(){
    $('#naturalRow').hide();
    $('#corporateRow').hide();
    $('#foundationRow').hide();
    $('#trustRow').hide();
    $('#limitedRow').hide();
    $('#relationTitle').hide();
    $("#shareholderAdditionalForm").hide();
}

$('#newRelationModal').on('show.bs.modal', async function (event) {
    hideSections();
    $('#ownerShipField').hide();
    
    $.ajax({
        type: "POST",
        url: "./" + window.location.pathname.split('/')[4] + "/clear-relation-temporal-files",
        contentType: 'application/json',
        error: function (err) {
            toastr["warning"]('Submission could not be save it, please try again later.', 'Error!');
        }
    })
});
$('#newRelationModal').on('hidden.bs.modal', function (event) {
    $("#newRelationForm").trigger("reset");
    $('#newRelationForm select').val('').trigger('change');
    hideSections();
    $("#newRelationForm .is-invalid").removeClass("is-invalid");
    $('#ownerShipField').hide();
    $(".upload-file-btn").text('Upload');
    $('#natural-is-electronic-id-no').trigger('change');

});

$("#additional-percentage").on('keyup', function () {
    const value = $(this).val();
    if (value && parseInt(value) < 0 || parseInt(value) >100){
        $('#additional-percentage').toggleClass("is-invalid", true);
    }
    else{
        $('#additional-percentage').toggleClass("is-invalid", false);
    }
});

$('input[name="relationType"]').change(function () {
    const type = $(this).val();
    hideSections();
    if (type === 'natural') {
        $('#ownerShipField').hide();
        $('#naturalRow').show(200);
        $('#relationTitle').show(200);
    } else if (type === 'corporate') {
        $("#ownerShip").val('corporate').trigger('change');
        $('#ownerShipField').show(200);
        $('#corporateRow').show(200);
        $('#relationTitle').show(200);
    }
});


$('#ownerShip').change(function () {
    const ownerShip = $(this).val();
    hideSections();
    if (ownerShip === 'corporate') {
        $('#corporateRow').show(200);
        $('#relationTitle').show(200);
    } else if (ownerShip === 'foundation') {
        $('#relationTitle').show(200);
        $('#foundationRow').show(200);
    } else if (ownerShip === 'trust') {
        $('#relationTitle').show(200);
        $('#trustRow').show(200);
    } else if (ownerShip === 'limited') {
        $('#relationTitle').show(200);
        $('#limitedRow').show(200);
    }
});

$('#shareholderAdditionalForm').hide();

$('#shareholderType').change(function () {
    if ($('#shareholderType').is(':checked')) {
        $('#shareholderAdditionalForm').show(200);
    } else {
        $('#shareholderAdditionalForm').hide(200);
    }
});

$("#newRelationForm").submit(async function (event) {
    event.preventDefault();
    let invalidRadios = false;

    $('input[required]:visible').trigger('keyup');
    $('select[required]:visible').trigger('change');
    $("input[type='checkbox'][required]:visible").each(function () {
        const val = $('input:checkbox[name="' + this.name + '"]:checked').val();
        if (val === undefined) {
            $('input:checkbox[name="' + this.name + '"]').toggleClass("is-invalid", true);
            invalidRadios = true;
        } else {
            $('input:checkbox[name="' + this.name + '"]').toggleClass("is-invalid", false);
        }
    });
    $("select[required]:visible").each(function () {
      const selectInput = $('select[id="' + this.id + '"]');
        const val = selectInput.find(':selected').val();
        if (val === undefined || val === "") {
            selectInput.toggleClass("is-invalid", true);
            invalidRadios = true;
        } else {
            selectInput.toggleClass("is-invalid", false);
        }
    });
    $("input[type='radio'][required]:visible").each(function () {
        const val = $('input[name="' + this.name + '"]:checked').val();
        if (val === undefined) {
            $('input[name="' + this.name + '"]').toggleClass("is-invalid", true);
            invalidRadios = true;
        } else {
            $('input[name="' + this.name + '"]').toggleClass("is-invalid", false);
        }
    });
    const checkedGroups = $('input[name="groups[]"]:checked').length > 0;

    if (!checkedGroups){
        $('input[name="groups[]"]').toggleClass("is-invalid", true);
    }
    
    if ($(".is-invalid:visible").length === 0 && !invalidRadios) {
        
        const formValues = $("#newRelationForm input:visible, select:visible, radio:visible, checkbox:visible, textarea:visible").serializeJSON();
        $.ajax({
            type: "POST",
            url: "./" + window.location.pathname.split('/')[4] + "/create-relation",
            data: JSON.stringify(formValues),
            contentType: 'application/json',
            success: function (data) {
                if (data.status === 200) {
                    reloadRelationTable(data.incorporationId, data.masterClientCode, data.relations);

                    $("#newRelationModal").modal('hide');
                } else {
                    if (data.errors && data.errors.length > 0){
                        for (let i = 0; i < data.errors.length; i++) {
                            toastr["warning"](data.errors[i], 'Error!');
                        }
                    }
                    else{
                        toastr["warning"](data.message, 'Error!');
                    }

                }
            },
            error: function (err) {
                toastr["warning"]('Submission could not be save it, please try again later.', 'Error!');
            }
        })
    }
});


$('.addCertificatePartnerRow').on('click', function () {
        masterClientCode = window.location.pathname
        const tableId = $(this).data("table-id");
        const reviewId = $(this).data("review-id");
        const relationId = $(this).data("relation-id");
        const group = $(this).data("group");
        const fileRow = $(this).data("row");
        const rowCount = $('#' + tableId + ' tr').length - 1;
        const mcc = masterClientCode.split('/')[2]

        $.ajax({
            url: '/masterclients/'+ mcc +'/incorporate-company/relations/get-template-files',
            type: 'GET',
            timeout: 5000,
            data: {
                fieldToSearch: 'relationFiles',
                group: group,
                fileType: 'detailsPartner',
                row: fileRow,
                newFile: true
            },
            success: function (response) {
                if (response.success) {
                    let template = Handlebars.templates.addpartnerfilerow;
                    let d = {
                        file: response.data,
                        row: rowCount,
                        group: group,
                        reviewId: reviewId,
                        relationId: relationId,
                        mcc: mcc,
                        incorporationId: incorporationId
                    };
                    let html = template(d);
                    $("#" + tableId + " > table > tbody").append(html);
                } else {
                    Swal.fire('Error', 'There was an error adding a new row file', 'error');
                }
            },
            error: function () {
                Swal.fire('Error', 'There was an error adding a new row file', 'error');
            },
        });
    });