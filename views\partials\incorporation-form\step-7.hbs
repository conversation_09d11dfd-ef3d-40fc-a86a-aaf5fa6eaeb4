<div class="col-lg-12">
    <div class="card">
        <div class="card-body">
            I declare and affirm that:
            <div class="pl-3 mt-2">
                <div class="row">
                    <div class="col-md-12 custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="declarationInformation"
                            name="declarationInformation" {{#ifEquals incorporation.declaration.information true}}
                            checked {{/ifEquals}} required>
                        <label class="custom-control-label" for="declarationInformation">The information provided in
                            this and any
                            form provided to Tri<PERSON> in conjunction with this form is, to the
                            best of my knowledge and belief, true and correct.</label>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="declarationAssets"
                            name="declarationAssets" {{#ifEquals incorporation.declaration.assets true}} checked
                            {{/ifEquals}} required>
                        <label class="custom-control-label" for="declarationAssets">The assets to be introduced into the
                            entity/structure and the funds used to pay for Tri<PERSON>’s services in
                            relation thereto are from lawful sources.</label>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12 custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="declarationTermsAndConditions"
                            name="declarationTermsAndConditions" {{#ifEquals
                            incorporation.declaration.termsAndConditions true}} checked {{/ifEquals}} required>
                        <label class="custom-control-label" for="declarationTermsAndConditions">I have read, understood
                            and accept
                            Trident’s Terms of Business and agree to comply with all of the terms
                            and conditions outlined therein.</label>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="declarationNameControl">Name*</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <input type="text" name="declarationName" id="declarationNameControl" class="form-control"
                            required value="{{incorporation.declaration.name}}">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="declarationDateControl">Date*</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <input type="date" name="declarationDate" id="declarationDateControl" class="form-control"
                            required value="{{incorporation.declaration.date}}">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="declarationRelationControl">Relation to entity*</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <select name="declarationRelation" id="declarationRelationControl" class="form-control w-100" data-toggle="select2"
                            required>
                            <option value="" hidden>Select an option</option>
                            <option {{#ifEquals incorporation.declaration.relationToEntity "Standard" }} selected
                                {{/ifEquals}} value="Director">Director</option>
                            <option {{#ifEquals incorporation.declaration.relationToEntity "Sole Director" }} selected
                                {{/ifEquals}} value="Sole Director">Sole Director</option>
                            <option {{#ifEquals incorporation.declaration.relationToEntity "Alternate Director" }}
                                selected {{/ifEquals}} value="Alternate Director">Alternate Director</option>
                            <option {{#ifEquals incorporation.declaration.relationToEntity "Secretary" }} selected
                                {{/ifEquals}} value="Secretary">Secretary</option>
                            <option {{#ifEquals incorporation.declaration.relationToEntity "Tax Advisor" }} selected
                                {{/ifEquals}} value="Tax Advisor">Tax Advisor</option>
                            <option {{#ifEquals incorporation.declaration.relationToEntity "Legal Advisor" }} selected
                                {{/ifEquals}} value="Legal Advisor">Legal Advisor</option>
                            <option {{#ifEquals incorporation.declaration.relationToEntity "Banker" }} selected
                                {{/ifEquals}} value="Banker">Banker</option>
                            <option {{#ifEquals incorporation.declaration.relationToEntity "Authorized Agent" }}
                                selected {{/ifEquals}} value="Authorized Agent">Authorized Agent</option>
                            <option {{#ifEquals incorporation.declaration.relationToEntity "Authorized Representative"
                                }} selected {{/ifEquals}} value="Authorized Representative">Authorized Representative
                            </option>
                            <option {{#ifEquals incorporation.declaration.relationToEntity "Accountant" }} selected
                                {{/ifEquals}} value="Accountant">Accountant</option>
                            <option {{#ifEquals incorporation.declaration.relationToEntity "Other" }} selected
                                {{/ifEquals}} value="Other">Other</option>
                        </select>
                    </div>
                </div>
            </div>
            <div id="declarationRelationOtherRow" class="row {{#ifCond
                incorporation.declaration.relationToEntity "!=" "Other" }} hide-element {{/ifCond}}" >
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="declarationRelationOtherControl">Please specify*</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <input type="text" name="declarationRelationOther" id="declarationRelationOtherControl"
                            class="form-control" required value="{{incorporation.declaration.relationToEntityOther}}">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="declarationPhoneNumberControl">Phone number*</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <input type="text" name="declarationPhoneNumber" id="declarationPhoneNumberControl"
                            class="form-control" required value="{{incorporation.declaration.phone}}">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2">Is there any other files you would like to attach to the incorporation progress?</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <button type="button" class="btn btn-primary waves-effect waves-light width-lg" data-toggle="modal"
                            data-target="#upload-modal" id="uploadOtherDeclarationFiles" data-mcc="{{masterclientcode}}"
                            data-incorporation-id="{{ incorporationId }}" data-file-group="Other"
                            data-field="otherDeclarationFiles">Upload</button>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-12">
                    <div class="progress">
                        <div class="progress-bar width-87" role="progressbar" aria-valuenow="7"
                            aria-valuemin="0" aria-valuemax="8">7 of 8
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="/views-js/partials/incorporate-form/step-7.js"></script>

