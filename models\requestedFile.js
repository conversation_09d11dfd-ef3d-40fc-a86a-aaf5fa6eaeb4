const ObjectId = require('mongoose').Types.ObjectId;
const fileSchema = require('./file');
const mongoose = require('mongoose');


// File Type Schema
const fileQuestionSchema = new mongoose.Schema({
  id: { type: String, required: true},
  name: { type: String, required: false },
  present:  { type: Boolean, required: true, default: false },
  explanation: { type: String, required: false, max: 100 },
  uploadFiles: [fileSchema]
});


const requestedFilesSchema = new mongoose.Schema(
  {
      referenceId: {type: ObjectId, required: true},
      status: {type: String, required: true, default: 'NOT STARTED'},
      companyCode: {type: String, required: true},
      companyName: {type: String, required: true},
      masterClientCode: {type: String, required: true},
      files: [fileQuestionSchema],
      type: {type: String, required: false, default: 'filereview'},
      comments: { type: String, required: false},
      reviewerComment:  { type: String, required: false},
      messageId:  {type: ObjectId, required: false},
      submittedAt: {type: Date, required: false},
      submittedBy: { type: String, required: false},
      createdAt: {type: Date, required: true},
      updatedAt: {type: Date, required: true},
  }
);


//Export model
module.exports = mongoose.model('requestedfiles', requestedFilesSchema);
