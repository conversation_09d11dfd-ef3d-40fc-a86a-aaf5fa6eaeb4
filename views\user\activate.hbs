<link href="/javascripts/libs/password-strength-meter/dist/password.min.css" rel="stylesheet" type="text/css" />
<script src="/javascripts/libs/password-strength-meter/dist/password.min.js"></script>
<script src="https://www.recaptcha.net/recaptcha/api.js?render={{RECAPTCHA_SITE_KEY}}"></script>

<main class="">
    <div class="container">
        <div class="row">
            <div class="col-2"></div>
            <div class="col-8">
                <div class='contour'>
                    <div class="header-title-container">
                        <h1>{{title}}</h1>
                    </div>
                    {{#if activated}}
                        <p class="alert alert-success">{{ message }}</p>
                        <p>Click <a href="/">here</a> to login to the portal</p>
                    
                    {{else}}              
                        {{#if message }}                       
                                <p class="alert alert-danger">{{ message }}</p>                        
                        {{/if}}
                        
                        <form id="activateForm" class='enquiry' method="POST" autocomplete="off">
                            <input type="text" hidden name="csrf-token" value="{{csrfToken}}">
                            <input type="hidden" id="g-recaptcha-response" name="g-recaptcha-response">

                            <div class='container-fluid'>
                                <div class='row'>
                                    <div class='col-lg-12'>
                                        <div class='card'>
                                            <div class='card-body'>
                                                To activate your account, please create a password in the form below <br/>and click "Activate my account"<br/><br/>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-group mb-3">                                                
                                                            <label for="password">Password</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-8">
                                                        <div class="form-group mb-3">                                                   
                                                            <input id="password" maxlength="40" name="password" size="20" type="password" required="" aria-required="true" class="form-control" data-toggle="tooltip" placement="top" 
                                                                title="The password should be at least 12 characters long and include the following: lowercase letters, uppercase letters, numbers, and special characters">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-group mb-3">
                                                            <label for="confirm">Confirm Password</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-8">
                                                        <div class="form-group mb-3">
                                                            <input id="confirm" maxlength="40" name="confirm" size="20" type="password" required="" aria-required="true" class="form-control">
                                                        </div>
                                                    </div>
                                                </div>

                                                <button id="activateBtn" type="submit" class="btn btn-primary waves-effect waves-light "
                                                    data-sitekey="{{RECAPTCHA_SITE_KEY}}">
                                                    Activate my account
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>                           
                        </form>
                    {{/if}}
                </div>
            </div>
            <div class="col-2"></div>
        </div>
    </div>
</main>
<script type='text/javascript' src="/views-js/helpers/set-captcha-token.js"></script>
<script type='text/javascript' src="/views-js/user/activate.js"></script>