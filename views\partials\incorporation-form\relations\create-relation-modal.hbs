<div class="modal fade" id="newRelationModal" data-keyboard="false" data-backdrop="static"  role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-scrollable " role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 id="modal-relation-title" class="modal-title">Relation</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div id="modal-relation-body" class="modal-body p-3">
                <form  class="form no-border p-1" id="newRelationForm" novalidate>

                    <div class="row">
                        <div class="col-6 d-flex justify-content-between">
                            <label>Is the company owned by a Natural person or a Corporate entity?</label>
                        </div>
                        <div class="col-6">
                            <div class="custom-control custom-radio custom-control-inline">
                                <input type="radio" class="custom-control-input" id="relation-type-natural-check"
                                       name="relationType"  value="natural" required/>
                                <label class="custom-control-label" for="relation-type-natural-check">Natural</label>
                            </div>
                            <div class="custom-control custom-radio custom-control-inline">
                                <input type="radio" class="custom-control-input" id="relation-type-corporate-check"
                                       name="relationType" value="corporate"/>
                                <label class="custom-control-label" for="relation-type-corporate-check">Corporate</label>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-md-6">
                            <label for="">Select type of relation*</label>
                        </div>
                        <div class="col-md-6 d-flex ">
                            <div class="custom-control custom-checkbox mr-3">
                                <input type="checkbox" class="custom-control-input" id="beneficialType"
                                       name="groups[]"
                                       value="Beneficial Owner" required>
                                <label class="custom-control-label" for="beneficialType">Beneficial Owner</label>
                            </div>
                            <div class="custom-control custom-checkbox mr-3">
                                <input type="checkbox" class="custom-control-input" id="shareholderType"
                                       name="groups[]" value="Shareholder" required>
                                <label class="custom-control-label" for="shareholderType">Shareholder</label>
                            </div>
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="directorType"
                                       name="groups[]" value="Director" required>
                                <label class="custom-control-label" for="directorType">Director</label>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-2 hide-element" id="ownerShipField">
                        <div class="col-md-6">
                            <label for="ownerShip">Select type of Corporation*</label>
                        </div>
                        <div class="col-md-6">
                            <select data-toggle="select2"
                                    class="form-control w-100" id="ownerShip" name="ownerShip" required>
                                <option id="corporate-option" value="corporate">Corporate</option>
                                <option id="foundation-option" value="foundation">Foundation</option>
                                <option id="trust-option" value="trust">Trust</option>
                                <option id="limited-option" value="limited">Limited Partnership</option>
                            </select>
                        </div>
                    </div>
                    <br>

                    <div>
                        <!-- TITLE -->
                        <div id="naturalRow" class="hide-element">
                            {{>file-reviewer/relations/natural-form-component showPep="true"
                                    masterClientCode=masterclientcode
                                    relation=relation files=templateFiles.naturalFiles}}
                        </div>
                        <div id="corporateRow" class="hide-element">
                            {{>file-reviewer/relations/corporate-form-component relation=relation
                                    masterClientCode=masterclientcode
                                     files=templateFiles.corporateFiles}}
                        </div>
                        <div id="foundationRow" class="hide-element">
                            {{>file-reviewer/relations/foundation-form-component relation=relation
                                    masterClientCode=masterclientcode files=templateFiles.foundationFiles}}
                        </div>
                        <div id="trustRow" class="hide-element">
                            {{>file-reviewer/relations/trust-form-component relation=relation
                                    masterClientCode=masterclientcode files=templateFiles.trustFiles}}
                        </div>
                        <div id="limitedRow" class="hide-element">
                            {{>file-reviewer/relations/limited-partnership-form-component relation=relation
                                    masterClientCode=masterclientcode files=templateFiles.limitedFiles}}
                        </div>

                        <div id="shareholderAdditionalForm">
                            <hr class="mt-2"/>
                            <!-- ADITIONAL REQUIRED INFO FOR SHAREHOLDERS  -->
                            <h4>Additional Shareholder Details</h4>
                            <div class="row mt-4">
                                <div class="col-2">
                                    <label for="additional-percentage">Share Percentage</label>
                                </div>
                                <div class="col-4 input-group mb-3">
                                    <input name="additional[percentage]" id="additional-percentage" type="number"
                                           max="100" min="0" class="form-control"
                                           value="{{relation.additional.percentage}}"/>
                                    <div class="input-group-append">
                                        <span class="input-group-text">%</span>
                                    </div>
                                </div>
                            </div>
                            <!-- SHAREHOLDER TABLE END -->
                        </div>
                    </div>

                </form>
            </div>
            <div class="modal-footer justify-content-end">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>

                <button class="btn solid royal-blue" type="submit" form="newRelationForm" name="submit"
                        id="submitNewRelation"
                        value="Submit">Save
                </button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="/templates/addpartnerfilerow.precompiled.js"></script>
<script type="text/javascript" src="/views-js/partials/incorporate-form/relations/create-relation-modal.js"></script>
