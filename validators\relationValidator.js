exports.validateRelation = function (relation) {
  let errors = [];

  let incorrectType;
  let validOwnerType = false;


  if (relation.relationType === undefined || (relation.relationType && relation.relationType.toLowerCase() !== 'natural'
    && relation.relationType.toLowerCase() !== 'corporate')) {
    errors.push('Please select if is owned by Natural person or a Corporate entity.');
  } else {
    validOwnerType = true;
  }

  if (!relation.groups) {
    errors.push('Please provide the type of relation.');
    incorrectType = true;
  } else {
    incorrectType = relation.groups.some((type) => type.toLowerCase() !== 'beneficial owner' && type.toLowerCase() !== 'shareholder'
      && type.toLowerCase() !== 'director');

    if (incorrectType) {
      errors.push('Please select a valid type for the relation (Beneficial Owner, Shareholder, Director).');
    }
  }

  if (validOwnerType && !incorrectType) {
    if (relation.relationType && relation.relationType.toLowerCase() === "natural") {

      if (!relation.details || (relation.details && (!relation.details.firstName || !relation.details.lastName ||
        !relation.details.countryBirth || !relation.details.occupation || !relation.details.source_of_income || !relation.details.birthDate ||
        !relation.details.nationality))) {
        errors.push('Missing values, please provide the details information.');
      }

      if (!relation.electronicIdInfo || (relation.electronicIdInfo && relation.electronicIdInfo.isElectronicId !== 'YES'
        && relation.electronicIdInfo.isElectronicId !== 'NO')) {
        errors.push('Please select YES/NO for the request electronic ID.');
      } else {
        if (relation.electronicIdInfo && relation.electronicIdInfo.isElectronicId === 'YES') {
          if (!relation.electronicIdInfo.email) {
            errors.push('Please provide the identification email.');
          }
        } else {
          if (!relation.identification || (relation.identification && (!relation.identification.identificationType ||
            !relation.identification.issueCountry || !relation.identification.expiryDate))) {
            errors.push('Please provide the identification information.');
          }
        }
      }

      if (!relation.principalAddress || (relation.principalAddress && (!relation.principalAddress.primaryAddress ||
        !relation.principalAddress.country || !relation.principalAddress.city || !relation.principalAddress.postalCode))) {
        errors.push('Missing values, please provide the principal address information.');
      }

      if (!relation.natural || (relation.natural && relation.natural.isSamePrincipalAddress !== 'YES' &&
        relation.natural.isSamePrincipalAddress !== 'NO')) {
        errors.push('Please select YES/NO if the mailing address is the same as principal address.');
      } else {
        if (relation.natural.isSamePrincipalAddress === 'NO') {
          if (!relation.mailingAddress || (relation.mailingAddress && (!relation.mailingAddress.primaryAddress ||
            !relation.mailingAddress.country || !relation.mailingAddress.city || !relation.mailingAddress.postalCode))) {
            errors.push('Missing values, please provide the mailing address information.');
          }
        }
      }

      if (!relation.taxResidence || (relation.taxResidence && relation.taxResidence.confirmation !== 'YES' &&
        relation.taxResidence.confirmation !== 'NO')) {
        errors.push('Please select YES/NO to confirm the tax advice.');
      } else {
        if (relation.taxResidence.confirmation === 'NO') {
          if (!relation.taxResidence.taxResidence) {
            errors.push('Missing values, please select the country of tax residence.');
          }
        }
        if (relation.taxResidence.confirmation === 'YES') {
          if (!relation.advisorDetails || (relation.advisorDetails && (!relation.advisorDetails.firstName ||
            !relation.advisorDetails.lastName || !relation.advisorDetails.firmName || !relation.advisorDetails.phone ||
            !relation.advisorDetails.email ||
            !relation.advisorDetails.nationality || !relation.advisorDetails.incorporationCountry))) {
            errors.push('Missing values, please provide the advisor details information.');
          }

          if (!relation.residentialAddress || (relation.residentialAddress && (!relation.residentialAddress.primaryAddress ||
            !relation.residentialAddress.country || !relation.residentialAddress.city || !relation.residentialAddress.postalCode))) {
            errors.push('Missing values, please provide the principal advisor address information.');
          }
        }
      }

      if (!relation.pepDetails || (relation.pepDetails && relation.pepDetails.confirmation !== 'YES' &&
        relation.pepDetails.confirmation !== 'NO')) {
        errors.push('Please select YES/NO to confirm if this relation is a PEP.');
      }
    } else if (relation.relationType && relation.relationType.toLowerCase() === "corporate") {
      if (!relation.ownerShip || (relation.ownerShip && relation.ownerShip !== 'corporate' &&
        relation.ownerShip !== 'foundation' && relation.ownerShip !== 'trust' && relation.ownerShip !== 'limited')) {
        errors.push('Please select a valid type for the corporation (Corporate, Foundation, Trust, Limited Partnership).');
      } else {
        let ownerShip = relation.ownerShip;

        if (relation[ownerShip].isTridentClient !== 'YES' && relation[ownerShip].isTridentClient !== 'NO'){
          errors.push('Please select YES/NO if is the company a TridentTrust client.');
        }
        else if (relation[ownerShip].isTridentClient === 'YES'){
          if (!relation.details || (relation.details && (!relation.details.organizationName || !relation.details.incorporationNumber))) {
            errors.push('Missing values, please provide the details information.');
          }
        }
        else if(relation[ownerShip].isTridentClient === 'NO'){
          if (!relation.details || (relation.details && (!relation.details.organizationName || !relation.details.incorporationNumber ||
            !relation.details.taxResidence || !relation.details.businessNumber || !relation.details.incorporationDate ||
            !relation.details.incorporationCountry))) {
            errors.push('Missing values, please provide the details information.');
          }

          if (!relation.principalAddress || (relation.principalAddress && (!relation.principalAddress.primaryAddress ||
            !relation.principalAddress.country || !relation.principalAddress.city || !relation.principalAddress.postalCode))) {
            errors.push('Missing values, please provide the principal address information.');
          }

          if (!relation[ownerShip] || (relation[ownerShip] && relation[ownerShip].isSamePrincipalAddress !== 'YES' &&
            relation[ownerShip].isSamePrincipalAddress !== 'NO')) {
            errors.push('Please select YES/NO if the mailing address is the same as principal address.');
          } else {
            if (relation[ownerShip].isSamePrincipalAddress === 'NO') {
              if (!relation.mailingAddress || (relation.mailingAddress && (!relation.mailingAddress.primaryAddress ||
                !relation.mailingAddress.country || !relation.mailingAddress.city || !relation.mailingAddress.postalCode))) {
                errors.push('Missing values, please provide the mailing address information.');
              }
            }
          }

          if (relation.listedCompanyDetails && relation.listedCompanyDetails.active) {
            if (!relation.listedCompanyDetails.stockCode) {
              errors.push('Please provide the listed company details information.');
            }
          }

          if (relation.limitedCompanyDetails && relation.limitedCompanyDetails.active) {
            if (!relation.limitedCompanyDetails.registrationNumber || !relation.limitedCompanyDetails.registrationDate) {
              errors.push('Please provide the regulated limited company details information.');
            }
          }
        }

      }

    }
  }

  return errors;
};
