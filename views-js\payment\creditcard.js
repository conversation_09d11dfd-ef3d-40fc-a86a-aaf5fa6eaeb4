$(document).ready(function(){
    var year = new Date().getFullYear();
    var min = year+1;
    var max = year+8;
    var select = document.getElementById('expiration-year');

    var opt = document.createElement('option');
    opt.value = year.toString().substring(2);
    opt.innerHTML = year;
    select.appendChild(opt);
    $('#billing-cc-exp').val('01'+year.toString().substring(2));;

    for (var i = min; i<=max; i++){
        var opt = document.createElement('option');
        opt.value = i.toString().substring(2);
        opt.innerHTML = i;
        select.appendChild(opt);
    }

    $('#paymentForm').on('submit', function(e) {
        if ($('#billing-country').val() == 'US') {
            $('#billing-state').val($('#state_us').val());
        } else if ($('#billing-country').val() == 'CA')  {
            $('#billing-state').val($('#state_ca').val());
        } else {
            $('#billing-state').val($('#state_other').val());
        }
    });
});

$(document).on('click', '.cancelBtn', function () {
    mcc = $(this).attr('data-mcc')
    document.location='/masterclients/'+mcc+'/payments'
})

function setState() {
    $('#state_us').addClass('hidden');
    $('#state_ca').addClass('hidden');
    $('#state_other').addClass('hidden');
    if ($('#billing-country').val() == 'US')
    {
        $('#state_us').removeClass('hidden');
    } else if ($('#billing-country').val() == 'CA')
    {
        $('#state_ca').removeClass('hidden');
    } else {
        $('#state_other').removeClass('hidden');
    }
}