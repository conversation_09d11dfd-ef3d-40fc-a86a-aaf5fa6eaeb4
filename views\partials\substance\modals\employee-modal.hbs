<div id="employeeModal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="entitParentModalLbl"
    aria-hidden="true">
    <div class="modal-dialog modal-lg contour container-fluid">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="employeeModalLbl">Add Employee Details</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form class="form no-border p-1" novalidate id="employeeForm">
                    <input hidden type="text" name="activityType" id="activityType" value="" />

                    <div class="row">
                        <div class="col-md-6">
                            <label for="employeeName" class="form-label">Name:</label>
                        </div>
                        <div class="col-md-6">
                            <input type="text" name="employeeName" id="employeeName" class="form-control"
                                maxlength="100" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label id="employeeQualificationLbl" for="employeeQualification"
                                class="form-label">Qualification:</label>
                        </div>
                        <div class="col-md-6">
                            <input type="text" name="employeeQualification" id="employeeQualification"
                                class="form-control" maxlength="100" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label for="yearsOfExperience" class="form-label">Years of Relevant Experience:</label>
                        </div>
                        <div class="col-md-6">
                            <input id="yearsOfExperience" name="yearsOfExperience" class="form-control" data-toggle="touchspin"  
                            data-max="1000000000" type="text" data-step="0.25" data-decimals="2" data-min="0.00" placeholder="0.00"
                            data-firstclickvalueifempty="0" data-forcestepdivisibility="none"  required>
                        </div>
                    </div>
                </form>

            </div>

            <div class="modal-footer justify-content-end">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>

                <button id="loadingEmployee" class="btn btn-primary" type="button" disabled>
                    <span class="spinner-border spinner-border-sm" aria-hidden="true"></span>
                    Loading...
                </button>
                <button class="btn solid royal-blue" type="submit" form="employeeForm"
                    id="submitEmployee">Save</button>
            </div>

        </div>
    </div>
</div>

<script type='text/javascript' src='/templates/substance/employees.precompiled.js'></script>
<script type='text/javascript' src="/views-js/partials/substance/modals/employee-modal.js"></script>
