const mongoose = require('mongoose');
const ObjectId = require('mongoose').Types.ObjectId;
const CompanyModel = require('./company').model;
const { v4: uuidv4 } = require('uuid');

const fileSchema = new mongoose.Schema({
  fileId: { type: String, required: false, default: uuidv4},
  fileTypeId: { type: String, required: false},
  fieldname: {type: String, required: true},
  originalname: {type: String, required: true},
  encoding: {type: String, required: true},
  mimetype: {type: String, required: true},
  blobName: {type: String, required: true},
  container: {type: String, required: true},
  blob: {type: String, required: true},
  blobType: {type: String, required: true},
  size: {type: String, required: true},
  etag: {type: String, required: true},
  url: {type: String, required: true}
});

const businessAddressSchema = new mongoose.Schema({
  address_line1: { type: String, required: false },
  address_line2: { type: String, required: false },
  country: { type: String, required: false },
});

const entityParentSchema = new mongoose.Schema({
  parentName: { type: String, required: true },
  alternativeName: { type: String, required: false },
  jurisdiction: { type: String, required: true },
  incorporationNumber: { type: String, required: true },
  TIN: { type: String, required: true },
});

const entityDetailsSchema = new mongoose.Schema({
  financial_period_begins: {type: Date, required:true  },
  financial_period_ends: {type: Date, required: true},
  financial_period_changed: {type: Boolean, required: false},
  previous_financial_period_ends: {type: Date, required: false},
  date_of_application_ITA: {type: Date, required: false},
  TIN: { type: String, required: false },
  totalAnnualGrossCurrency: { type: String, required: false },
  totalAnnualGross: { type: Number, required: false },
  isSameBusinessAddress: { type: Boolean, required: false, default: undefined },
  businessAddress: businessAddressSchema,
  nameOfMNEGroup: { type: String, required: false },
  hasUltimateParents: { type: Boolean, required: false, default: undefined },
  ultimateParents: [entityParentSchema],
  hasImmediateParents: { type: Boolean, required: false, default: undefined },
  immediateParents: [entityParentSchema]
});

const taxResidencySchema = new mongoose.Schema({
  resident_in_BVI: {type: Boolean, required:true, default:false  },
  entity_jurisdiction: {type: String, required: false},
  foreign_tax_id_number: {type: String, required: false},
  MNE_group_name: {type: String, required: false},
  evidence_type: {type: String, required: false},
  evidence_non_residency: [fileSchema],
  evidence_provisional_treatment_non_residency: [fileSchema],
  have_parent_entity: {type: Boolean, required:false},
  parent_entity_name : {type: String, required: false},
  parent_entity_alternative_name : {type: String, required: false},
  parent_entity_jurisdiction : {type: String, required: false},
  parent_entity_incorporation_number : {type: String, required: false},
  have_parent_entity_not_answered: {type: Boolean, required:false, default: false},

});

const financialPeriodSchema = new mongoose.Schema({
  financial_period_begins: {type: Date, required: false},
  financial_period_ends: {type: Date, required: false},
});

const relevantActivitySchema = new mongoose.Schema({
  selected: {type: Boolean, required:true  },
  part_of_financial_period: {type: Boolean, required: true},
  financial_periods: [financialPeriodSchema]
});

const relevantActivitiesSchema = new mongoose.Schema({
  banking_business: relevantActivitySchema,
  insurance_business: relevantActivitySchema,
  fund_management_business: relevantActivitySchema,
  finance_leasing_business: relevantActivitySchema,
  headquarters_business: relevantActivitySchema,
  shipping_business: relevantActivitySchema,
  holding_business: relevantActivitySchema,
  intellectual_property_business: relevantActivitySchema,
  service_centre_business: relevantActivitySchema,
  none: relevantActivitySchema,
  none_remarks: {type: String, required: false},
  evidence_none_activities: [fileSchema],
});

const directorSchema = new mongoose.Schema({
  is_corporate_director: {type:Boolean, required: true},
  first_name: {type:String, required:true},
  middle_name: {type:String, required:true},
  last_name: {type:String, required:true},
  date_of_birth: {type:Date, required:true},
  address_line1: {type:String, required:true},
  address_line2: {type:String, required:true},
  city: {type:String, required:true},
  country: {type:String, required:true},
  postalcode: {type:String, required: true},
  resident_in_bvi: {type:Boolean, required:true},
  position_held: {type:String, required:true}
});

const premisesSchema = new mongoose.Schema({
  address_line1: { type: String, required: true },
  address_line2: { type: String, required: true },
  city: { type: String, required: true },
  country: { type: String, required: true },
  postalcode: { type: String, required: true },
  are_physical_offices: { type: Boolean, required: false, default: undefined }
});

const boardMeetingSchema = new mongoose.Schema({
  meeting_number: { type: Number, required: false },
  name: { type: String, required: true },
  physically_present: { type: Boolean, required: true },
  relation_to_entity: { type: String, required: true },
  qualification: { type: String, required: true },
});

const employeeSchema = new mongoose.Schema({
  name: { type: String, required: true },
  qualification: { type: String, required: true },
  experience_years: { type: Number, required: true },
});

const outsourcingProviderSchema = new mongoose.Schema({
  entity_name: { type: String, required: true },
  resource_details: { type: String, required: true },
  staff_count: { type: Number, required: true },
  hours_per_month: { type: Number, required: true },
  monitoring_control: { type: Boolean, required: true }
});

const supportingDetailsSchema = new mongoose.Schema({
  support_comment: { type: String, required: false },
  support_attachments: [fileSchema],
});


const businessSchema = new mongoose.Schema({
  management_in_bvi: {type: Boolean, required:true  },
  number_of_board_meetings: {type: Number, required: true},
  number_or_board_meetings_outside_bvi: {type: Number, required: false},
  managers:[directorSchema],
  total_turnover: {type: Number, required: true},
  total_expenditure: {type: Number, required: true},
  total_expenditure_bvi: {type: Number, required: true},
  total_employees: {type: Number, required: true},
  full_total_employees: {type: Number, required: true},
  total_employees_adequate: {type: Boolean, required: false}, // optional from v5
  income_generating_premises: { type: Boolean, required: false }, // optional from v5
  premises: [premisesSchema],
  equipment_in_bvi: { type: Boolean, required: false }, // optional from v5
  evidence_equipment: [fileSchema],
  manage_equity_participations: { type: Boolean, required: false }, // optional from v5
  compliant_with_statutory_obligations: {type:Boolean, required:false},
  core_income_generating_outsourced: { type: Boolean, required: false }, // optional from v5
  outsourced_activity_undertaken_in_BVI: { type: Boolean, required: false }, // optional from v5
  explanation_outsourced_activity_undertaken_in_BVI: {type: String, required:false},
  demonstrate_monitoring_outsourced_activity: { type: Boolean, required: false }, // optional from v5
  explanation_demonstrate_monitoring_outsourced_activity: {type: String, required:false},
  outsourcing_evidence:  [fileSchema],
  high_risk_ip: {type: Boolean, required:true },
  evidence_high_risk_ip: {type: Boolean, required:true  },
  // v4.5 require move later because are saving now in supporting_details
  support_comment: {type: String, required: false},
  support_documents: [fileSchema],
  // v5
  number_of_board_meetings_in_bvi: { type: Number, required: false },
  total_employees_engaged: { type: Number, required: false },
  gross_income_total: { type: Number, required: false },
  gross_income_type: { type: String, required: false },
  activity_assets_amount: { type: Number, required: false },
  activity_assets_type: { type: String, required: false },
  activity_netbook_values: { type: Number, required: false },
  are_minutes_for_board_meetings: { type: Boolean, required: false },
  are_quorum_of_directors: { type: Boolean, required: false },
  quorum_of_board_meetings: { type: Number, required: false },
  activity_ciga_core: { type: String, required: false },
  activity_ciga_core_other: { type: String, required: false },
  outsourcing_total_expenditure: { type: Number, required: false },
  high_risk_gross_income_total: { type: Number, required: false },
  high_risk_gross_income_assets: { type: Number, required: false },
  high_risk_gross_income_others: { type: Number, required: false },
  tangible_assets_name: { type: String, required: false },
  tangible_assets_explanation: { type: String, required: false },
  intangible_assets_decisions: { type: String, required: false },
  intangible_assets_nature: { type: String, required: false },
  intangible_assets_trading_nature: { type: String, required: false },
  is_other_ciga_legal_entity: { type: Boolean, required: false },
  has_other_ciga_evidences: { type: Boolean, required: false },
  other_ciga_ip_asset: { type: String, required: false },
  other_ciga_business_details: { type: String, required: false },
  other_ciga_decisions: { type: String, required: false },
  other_ciga_evidence_details: { type: String, required: false },
  equipment_nature_description: { type: String, required: false },
  tangible_assets_explanation_files: [fileSchema],
  intangible_assets_decisions_files: [fileSchema],
  intangible_assets_nature_files: [fileSchema],
  intangible_assets_trading_nature_files: [fileSchema],
  other_ciga_business_files: [fileSchema],
  other_ciga_decisions_files: [fileSchema],
  other_ciga_evidence_files: [fileSchema],
  other_ciga_files: [fileSchema],
  high_risk_ip_evidence: [fileSchema],
  board_meetings: [boardMeetingSchema],
  employees: [employeeSchema],
  outsourcing_providers: [outsourcingProviderSchema]
});

const confirmationSchema = new mongoose.Schema({
  confirmed: {type: Boolean, required:true  },
  confirmed_authority: {type: Boolean, required:true  },
  confirmed_conditions: {type: Boolean, required:true  },
  confirmed_payment: {type: Boolean, required:false },
  user_fullname: {type: String, required: true},
  user_phonenumber: {type: String, required: true},
  relation_to_entity: {type: String, required: true},
  relation_to_entity_other: {type: String, required: true},
  ultimate_parent_entity_name : {type: String, required: false},
  ultimate_parent_entity_address : {type: String, required: false},
  entity_jurisdiction : {type: String, required: false},
  incorporation_number : {type: String, required: false},
});

const paymentSchema = new mongoose.Schema({
  payment_type: {type: String, required: true},
  payment_reference: {type: String, required: true},
  payment_received_at: {type: Date, required: false },
  batchpayment_id: {type: String, required: true},
  batchpayment_transactionId: {type: String, required: true},
  batchpayment_code: {type: String, required: true},
  amount: {type: Number, required: false}
});

const financialPeriodChangeDetailsSchema = new mongoose.Schema({
  date_changed: { type: Date, required: true },
  changed_by: { type: String, required: true },
  old_start_date: { type: Date, required: false },
  old_end_date: { type: Date, required: false },
  new_start_date: { type: Date, required: false },
  new_end_date: { type: Date, required: false },
});


const reopenedDetailsSchema = new mongoose.Schema({
  date_reopened: {type: Date, required: true},
  reopened_by: {type: String, required: true},
  reason:{type: String, required: false},
  resubmitted_at:{type: Date, required: false},
  resubmitted_by:{type: String, required: false},
  change_financial_period_dates: {type: Boolean, required: false},
  old_start_date: {type: Date, required: false},
  old_end_date: {type: Date, required: false},
  new_start_date: {type: Date, required: false},
  new_end_date: {type: Date, required: false},
});

const reopenedSchema = new mongoose.Schema({
  details: [reopenedDetailsSchema]
});

const reminderSchema = new mongoose.Schema({
  description: { type: String, required: true },
  reminder_date:  { type: Date, required: true },
  message_id: {type: ObjectId, required: true},
});

const requestInformationDetailsSchema = new mongoose.Schema({
  id: { type: String, required: true, default: uuidv4 },
  username: { type: String, required: false },
  requested_at: { type: Date, required: false },
  deadline_at: { type: Date, required: false },
  comment: { type: String, required: false },
  status: {type: String, required: false, default: "REQUESTED"},
  message_id: {type: ObjectId, required: false},
  files: [fileSchema],
  reminders: [reminderSchema]
});

const returnedInformationDetailsSchema = new mongoose.Schema({
  request_id: { type: String, required: true }, // request information id
  username: { type: String, required: false },
  returned_at: { type: Date, required: false },
  comment: { type: String, required: false },
  is_canceled: { type: Boolean, required: false, default: false },
  files: [fileSchema]
});

const requestInformationSchema = new mongoose.Schema({
  details: [requestInformationDetailsSchema]
});

const returnedInformationSchema = new mongoose.Schema({
  details: [returnedInformationDetailsSchema]
});

const financialPeriodChangeSchema = new mongoose.Schema({
  details: [financialPeriodChangeDetailsSchema]
});

const EntrySchema = new mongoose.Schema(
  {
    company: {type: String, required: true},
    company_data: CompanyModel,
    version: {type: String, required: true, default: '5.0'},
    created_by: {type: String, required: true},
    submitted_by: {type: String, required: false},
    submitted_at: {type: Date, required: false},
    initial_submit_date: {type: Date, required: false},
    scheduled_at: {type: Date, required: false},
    archived_at: {type: Date, required: false},
    exported_at: {type: Date, required: false},
    exported_by: {type:String, required: false},
    status: {type: String, required: false},
    entity_details: entityDetailsSchema,
    tax_residency: taxResidencySchema,
    relevant_activities: relevantActivitiesSchema,
    banking_business: businessSchema,
    insurance_business: businessSchema,
    fund_management_business: businessSchema,
    finance_leasing_business: businessSchema,
    headquarters_business: businessSchema,
    shipping_business: businessSchema,
    holding_business: businessSchema,
    intellectual_property_business: businessSchema,
    service_centre_business: businessSchema,
    supporting_details: supportingDetailsSchema,
    confirmation: confirmationSchema,
    payment: paymentSchema,
    reopened: reopenedSchema,
    requested_information: requestInformationSchema,
    client_returned_information: returnedInformationSchema,
    financial_period_changes: financialPeriodChangeSchema,
    invoice_number: {type: String, required: false},
    invoice_export_date: {type: Date, required: false},
    currentStepForm:  {type: String, required: false},
  },
  {
    timestamps: { createdAt : 'createdAt', updatedAt : 'updatedAt'}
  }
);

EntrySchema.virtual('lastUserActivityDate').get(function () {
  let activityDates = [this.createdAt, this.submitted_at]
  if (this.reopened?.details?.length > 0) {
    const sortedReopened = this.reopened?.details.sort((a, b) => b.date_reopened - a.date_reopened);
    activityDates.push(sortedReopened[0].date_reopened);
  }
  activityDates = activityDates.filter(Boolean); 
  return activityDates.length > 0 ? new Date(Math.max(...activityDates)) : null;
});


//Export model
module.exports.EntryModel = mongoose.model('Entry', EntrySchema);
module.exports.ArchivedEntryModel = mongoose.model('archivedentries', EntrySchema);
