(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['step4createfundrow'] = template({"1":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "            Name of Financial Institution: "
    + container.escapeExpression(container.lambda(((stack1 = (depth0 != null ? lookupProperty(depth0,"fund") : depth0)) != null ? lookupProperty(stack1,"nameOfFinancialInstitution") : stack1), depth0))
    + "\r\n";
},"3":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "            Details: "
    + container.escapeExpression(container.lambda(((stack1 = (depth0 != null ? lookupProperty(depth0,"fund") : depth0)) != null ? lookupProperty(stack1,"details") : stack1), depth0))
    + "\r\n";
},"5":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "            Average annual turnover/profit: "
    + container.escapeExpression(container.lambda(((stack1 = (depth0 != null ? lookupProperty(depth0,"fund") : depth0)) != null ? lookupProperty(stack1,"profit") : stack1), depth0))
    + "\r\n";
},"7":function(container,depth0,helpers,partials,data) {
    var stack1, alias1=container.lambda, alias2=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "            Name of subsidiary: "
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"fund") : depth0)) != null ? lookupProperty(stack1,"nameOfSubsidiary") : stack1), depth0))
    + " <br>\r\n            Jurisdiction of operation: "
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"fund") : depth0)) != null ? lookupProperty(stack1,"jurisdictionOfOperation") : stack1), depth0))
    + "\r\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data) {
    var stack1, alias1=container.lambda, alias2=container.escapeExpression, alias3=depth0 != null ? depth0 : (container.nullContext || {}), alias4=container.hooks.helperMissing, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "<tr id=\"fund-table-row-"
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"fund") : depth0)) != null ? lookupProperty(stack1,"_id") : stack1), depth0))
    + "\" class=\"fund-row\">\r\n    <td>"
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"fund") : depth0)) != null ? lookupProperty(stack1,"type") : stack1), depth0))
    + "</td>\r\n\r\n    <td>\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||alias4).call(alias3,((stack1 = (depth0 != null ? lookupProperty(depth0,"fund") : depth0)) != null ? lookupProperty(stack1,"type") : stack1),"Loan",{"name":"ifEquals","hash":{},"fn":container.program(1, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":5,"column":8},"end":{"line":7,"column":21}}})) != null ? stack1 : "")
    + "\r\n\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||alias4).call(alias3,((stack1 = (depth0 != null ? lookupProperty(depth0,"fund") : depth0)) != null ? lookupProperty(stack1,"type") : stack1),"Sale of assets",{"name":"ifEquals","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":10,"column":8},"end":{"line":12,"column":21}}})) != null ? stack1 : "")
    + "\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||alias4).call(alias3,((stack1 = (depth0 != null ? lookupProperty(depth0,"fund") : depth0)) != null ? lookupProperty(stack1,"type") : stack1),"Other",{"name":"ifEquals","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":14,"column":8},"end":{"line":16,"column":21}}})) != null ? stack1 : "")
    + "\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||alias4).call(alias3,((stack1 = (depth0 != null ? lookupProperty(depth0,"fund") : depth0)) != null ? lookupProperty(stack1,"type") : stack1),"Business income",{"name":"ifEquals","hash":{},"fn":container.program(5, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":18,"column":8},"end":{"line":20,"column":21}}})) != null ? stack1 : "")
    + "\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||alias4).call(alias3,((stack1 = (depth0 != null ? lookupProperty(depth0,"fund") : depth0)) != null ? lookupProperty(stack1,"type") : stack1),"Dividend from subsidiary",{"name":"ifEquals","hash":{},"fn":container.program(7, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":22,"column":8},"end":{"line":25,"column":21}}})) != null ? stack1 : "")
    + "    </td>\r\n    <td class=\"text-right\">\r\n        <button type=\"button\" class=\"btn btn-outline-secondary openEditFundModal\"\r\n                data-id=\""
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"fund") : depth0)) != null ? lookupProperty(stack1,"_id") : stack1), depth0))
    + "\">\r\n            <i class=\"fa fa-pencil mr-2\"></i>Edit\r\n        </button>\r\n    </td>\r\n    <td class=\"text-left\">\r\n        <button type=\"button\" class=\"delete btn btn-danger deleteFund\" data-id=\""
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"fund") : depth0)) != null ? lookupProperty(stack1,"_id") : stack1), depth0))
    + "\">\r\n            <i class=\"fa fa-trash mr-2\"></i>Delete\r\n        </button>\r\n    </td>\r\n</tr>\r\n";
},"useData":true});
})();