<div id="outsourcingProviderModal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="entitParentModalLbl"
    aria-hidden="true">
    <div class="modal-dialog modal-lg contour container-fluid">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="outsourcingProviderModalLbl">Add Board Meeting</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form class="form no-border p-1" novalidate id="outsourcingProviderForm">
                    <input hidden type="text" name="activityType" id="activityType" value="" />
                    <div class="row ">
                        <div class="col-md-6">
                            <label for="outsourcingEntityName" class="form-label">
                                Name of entity to whom outsourced:
                            </label>
                        </div>
                        <div class="col-md-6">
                            <input type="text" id="outsourcingEntityName" class="form-control" maxlength="150" value=""
                                required />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label for="outsourcingResourceDetails" class="form-label">
                                Details of resources deployed by the entity in carrying out the activity on their
                                behalf:
                            </label>
                        </div>
                        <div class="col-md-6">
                            <textarea id="outsourcingResourceDetails" class="form-control" maxlength="255" rows="6"
                                required></textarea>
                        </div>
                    </div>

                    <div class="row ">
                        <div class="col-md-6">
                            <label for="outsourcingStaffCount" class="form-label">
                                Number of staff employed in carrying out CIGA for the entity:
                            </label>
                        </div>
                        <div class="col-md-6">
                            <input type="text" id="outsourcingStaffCount" class="form-control "
                                data-toggle="touchspin" data-min="0" placeholder="0.00"
                                data-max="1000000000" type="text" data-step="0.5"  data-firstclickvalueifempty="0" data-decimals="2" required />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label for="outsourcingHoursPerMonth" class="form-label">
                                Hours per month each person employed for this relevant activity:
                            </label>
                        </div>
                        <div class="col-md-6">
                            <input type="text" id="outsourcingHoursPerMonth" class="form-control autonumber" data-a-sep="," data-min="0"
                                data-max="1000000000" placeholder="0.0" data-m-dec="2" placeholder="0.00" type="text" required />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group mb-3">
                                <label class="form-label">Is the entity able to monitor and control carrying out of the outsourced activity?</label>
                            </div>
                        </div>
                        <div class="col-md-6" align="right">
                            
                                <div class="radio form-check form-check-inline">
                                    <input type="radio" class="form-check-input" id="outsourcingMonitoringControlYes" 
                                    name="outsourcingMonitoringControl" value="Yes" required>
                                    <label class="form-check-label" for="outsourcingMonitoringControlYes">Yes</label>
                                </div>
                                <div class="radio form-check form-check-inline">
                                    <input type="radio" class="form-check-input" id="outsourcingMonitoringControlNo"
                                     name="outsourcingMonitoringControl" value="No" required>
                                    <label class="form-check-label" for="outsourcingMonitoringControlNo">No</label>
                                </div>

                        </div>
                    </div>
                </form>

            </div>

            <div class="modal-footer justify-content-end">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>

                <button id="loadingOutsourcingProvider" class="btn btn-primary" type="button" disabled>
                    <span class="spinner-border spinner-border-sm" aria-hidden="true"></span>
                    Loading...
                </button>
                <button class="btn solid royal-blue" type="submit" form="outsourcingProviderForm"
                    id="submitOutsourcingProvider">Save</button>
            </div>

        </div>
    </div>
</div>


<script type='text/javascript' src='/templates/substance/outsourcingproviders.precompiled.js'></script>
<script type='text/javascript' src="/views-js/partials/substance/modals/outsourcing-provider-modal.js"></script>
