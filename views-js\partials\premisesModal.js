
function addPremises() {
	$("#iframePremises").prop('src', '/substance/entry/'+window.location.pathname.split('/')[3]+'/'+urlPart+'/premises/add')
	
	$("#AddPremises").modal('show')
}

$(document).on('click', '#addPremisesBtn', function () {
	addPremises()
})

async function deletePremises(id) {
	$.ajax({
  		type: "DELETE",
  		url: "/substance/entry/"+window.location.pathname.split('/')[3]+"/premises",
  		data: {
			_id: id,
			field: urlPart
		},
  		success: function( data ) {
			  if (data.result) {
				  //close modal
				  refreshPremises();
			  }
              else{
                  Swal.fire('Error',  data.message ? data.message : "Error deleting premise", 'error');
              }
		  },
  		dataType: "json"
	});
}

function editPremises(id) {
	$("#iframePremises").prop('src', '/substance/entry/'+window.location.pathname.split('/')[3]+'/'+urlPart+'/premises/'+id+'/edit')
    $("#AddPremises").modal('show')
}


function closePremiseIFrame(){
    $("#AddPremises").modal("hide");
    toastr.success('Premises is saved.')
}


