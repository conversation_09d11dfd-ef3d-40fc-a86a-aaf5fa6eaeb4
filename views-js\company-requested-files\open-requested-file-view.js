
$('[data-toggle="tooltip"]').tooltip({
    container: 'body',
    boundary: 'window'
});


$("#save-requested-files-button").on('click', async function () {
    const mcc = $("#save-requested-files-button").data('mcc');
    Swal.fire({
        title: 'Do you want to save the changes?',
        showCancelButton: true,
        icon: 'info',
        backdrop: true,
        cancelButtonColor: "#6c757d",
        confirmButtonColor: "#0081B4",
        confirmButtonText: 'Save',
        reverseButtons: true,
        showLoaderOnConfirm: true,
        preConfirm() {
            let data = {
              comment: $("#comment").val() || '',
                status: 'saved'
            };
            
            return axios.post(window.location.href, 
                JSON.stringify(data),
                {
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                }
                ).then(response => {
                try {
                    return response.data
                } catch (e) {
                    throw new Error(response.statusText)
                }

            }).catch(error => {
                if (error?.response?.data) {
                    return error.response.data
                }
                return {status: 500, error: error}
            });
        },
        allowOutsideClick: () => !Swal.isLoading()
    }).then((result) => {
        if (result.value) {
            swal.showLoading();
            if (result.value.status === 200) {
                Swal.fire({
                    title: 'Success',
                    text: 'Information has been updated successfully.',
                    icon: 'success',
                    confirmButtonColor: "#0081B4",
                }).then(() => {
                    location.href = '/masterclients/' + mcc + '/company-files';
                });
            } else if (result.value.status === 400) {
                Swal.fire({
                    title: 'Error',
                    text: result.value.message,
                    icon: 'error',
                    confirmButtonColor: "#0081B4",
                });
            } else {
                Swal.fire({
                    title: 'Error',
                    text: result.value.message ?  result.value.message : 'There was an error updating the information.',
                    icon: 'error',
                    confirmButtonColor: "#0081B4",
                });
            }
        }

    })
});

$("#submit-requested-files-button").on('click', async function () {
    const mcc = $("#submit-requested-files-button").data('mcc');
    
    Swal.fire({
        title: 'Are you sure?',
        text: "You are about to submit the information.",
        showCancelButton: true,
        icon: 'info',
        backdrop: true,
        cancelButtonColor: "#6c757d",
        confirmButtonColor: "#0081B4",
        confirmButtonText: 'Submit',
        reverseButtons: true,
        showLoaderOnConfirm: true,
        preConfirm() {
            let data = {
                comment: $("#comment").val() || '',
                status: 'submitted'
            };
            return axios.post(window.location.href, 
                JSON.stringify(data),
                {
                    headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }}
            ).then(response => {
                try {
                    return response.data
                } catch (e) {
                    throw new Error(response.statusText)
                }

            }).catch(error => {
                if(error?.response?.data){
                    return error.response.data
                }
                return {status: error?.status || 500, error: error}
            });
        },
        allowOutsideClick: () => !Swal.isLoading()
    }).then((result) => {
        if (result.value) {
            swal.showLoading();
            if (result.value.status === 200) {
                Swal.fire({
                    title: 'Success',
                    text: 'Files have been submitted to Trident successfully.',
                    icon: 'success',
                    confirmButtonColor: "#0081B4",
                }).then(() => {
                    location.href = '/masterclients/' + mcc + '/company-files';
                });
            } else if (result.value.status === 400) {
                Swal.fire({
                    title: 'Error',
                    text: result.value.message,
                    icon: 'error',
                    confirmButtonColor: "#0081B4",
                });
            } else {
                Swal.fire({
                    title: 'Error',
                    text:  result.value.message ?  result.value.message : 'There was an error updating the information.',
                    icon: 'error',
                    confirmButtonColor: "#0081B4",
                });
            }
        }

    })
});
