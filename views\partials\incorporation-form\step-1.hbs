<div class="col-lg-12">
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-12 hide-element" id="missingFileRowStep1">
                    <div class="alert alert-warning" role="alert">
                        <i class="fa fa-warning mr-2"></i><b>Missing file!</b> Please upload a structure chart
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="entityNameControl">Name of
                            entity* 
                            {{#ifEquals incorporation.nameReservationStatus 'APPROVED'}}
                            <i class="fa fa-check fa-lg incorp-color-green" data-toggle="tooltip" data-placement="top" title="Name reservation has been approved"></i>
                            {{/ifEquals}}
                            {{#ifEquals incorporation.nameReservationStatus 'IN PROGRESS'}}
                            <i class="fa fa-clock-o fa-lg incorp-color-blue" data-placement="top" title="Name reservation is in progress"></i>
                            {{/ifEquals}}
                        </label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="row">
                        <div class="form-group mb-3 {{#ifEquals incorporation.nameReservationStatus 'IN PROGRESS'}}col-8{{/ifEquals}}{{#ifCond incorporation.nameReservationStatus '!=' 'IN PROGRESS'}}col-12{{/ifCond}}">
                            <input type="text" name="entityName" id="entityNameControl" class="form-control"
                                value="{{incorporation.name}}" {{#if cantEditName}} readonly {{/if}}

                                required>
                        </div>
                        {{#ifEquals incorporation.nameReservationStatus 'IN PROGRESS'}}
                        <div class="pl-1 col-4">
                            <button type="button" class="btn btn-primary waves-effect waves-light btn-block" id="enableNameChange">
                                Change
                            </button>
                        </div>
                        {{/ifEquals}}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="entityTypeControl">Type of
                            entity*</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <select name="entityType" id="entityTypeControl" class="form-control w-100" data-toggle="select2" required>
                            <option value="" hidden>Select an option</option>
                            <option {{#ifEquals incorporation.type "Standard MA of 50000 shares no par value" }}
                                selected {{/ifEquals}}>Standard MA of 50000 shares no par value</option>
                            <option {{#ifEquals incorporation.type "Standard MA of 50000 shares US$1.00 par value" }}
                                selected {{/ifEquals}}>Standard MA of 50000 shares US$1.00 par value</option>
                            <option {{#ifEquals incorporation.type "More than 50000 shares no par value" }} selected
                                {{/ifEquals}}>More than 50000 shares no par value</option>
                            <option {{#ifEquals incorporation.type "More than 50000 shares US$1.00 par value" }}
                                selected {{/ifEquals}}>More than 50000 shares US$1.00 par value</option>
                            <option {{#ifEquals incorporation.type "Special Instructions" }} selected {{/ifEquals}}>
                                Special Instructions</option>
                        </select>
                    </div>
                </div>
            </div>
            <div  id="specialInstructionsRow" class="{{#ifCond incorporation.type '!=' "Special Instructions" }} hide-element {{else}} row {{/ifCond}}" >
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="specialInstructionsControl" required>Please specify special
                            instructions</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <textarea class="form-control" name="specialInstructions" id="specialInstructionsControl"
                            rows="3" required>{{incorporation.typeSpecialInstructions}}</textarea>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="partOfGroupControl">Is the entity part of a group/structure?*</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="row">
                        <div class="col-6">
                            <div class="custom-control custom-radio">
                                <input class="custom-control-input" type="radio" id="partOfGroupYes"
                                    name="partOfGroupControl" value="Yes" required {{#ifEquals incorporation.partOfGroup
                                    true}} checked {{/ifEquals}}>
                                <label class="custom-control-label" for="partOfGroupYes">Yes</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="custom-control custom-radio">
                                <input class="custom-control-input" type="radio" id="partOfGroupNo"
                                    name="partOfGroupControl" value="No" required {{#ifEquals incorporation.partOfGroup
                                    false}} checked {{/ifEquals}}>
                                <label class="custom-control-label" for="partOfGroupNo">No</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="structureRow" class="{{#ifEquals incorporation.partOfGroup false}} hide-element {{/ifEquals}}">
                <div class="row w-100">
                    <div class="col-md-8">
                        <div class="form-group mb-3">
                            <label class="mb-2">Please provide a structure chart:</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="row">
                            <div class="col-6">
                                <button type="button"
                                    class="btn btn-primary waves-effect waves-light width-xl"
                                        data-toggle="modal"
                                        data-target="#upload-modal"
                                        id="uploadStructureChartBtn"
                                        data-mcc="{{masterclientcode}}"
                                        data-incorporation-id="{{ incorporationId }}"
                                        data-file-group="Structure chart files"
                                        data-field="structureChartFiles">
                                        {{#if incorporation.files.structureChartFiles}}Modify{{/if}}
                                        {{#unless incorporation.files.structureChartFiles}}Upload{{/unless}}
                                        </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-12">
                    <div class="progress">
                        <div class="progress-bar width-12" role="progressbar" aria-valuenow="1"
                            aria-valuemin="0" aria-valuemax="8">1 of 8
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="/views-js/partials/incorporate-form/step-1.js"></script>