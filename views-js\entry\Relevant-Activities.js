function SwitchActivity(field, init) {
    field.parent().parent().parent().find('input[type=radio]').prop('disabled', !field.prop('checked'));


    if (!init) {
        //clear radio button
        //clear dates
        field.parent().parent().parent().find("input[type=radio]").prop('checked', false);
        field.parent().parent().parent().find('input[type=text]').prop('disabled', true);
        field.parent().parent().parent().find("input[type=text]").val('');
    }

    if (field.attr("name") == "NoneCheckBox") {
        if (field.prop("checked")){
            $(".activity-checkbox").prop('checked', false);
            $(".activity-checkbox").prop('disabled', true);
            $("#NonePartYes").prop("disabled", true);
            $("#NonePartNo").prop("checked", true).trigger('change');
        }
    }
}

function SwitchPartFinancialPeriod(field, init) {
    field.parent().parent().parent().find('input[type=text]').prop('disabled', true);

    if ($("input:radio[name='"+field.prop("name")+"']:checked").val() == "Yes") {
            field.parent().parent().parent().find('input[type=text]').prop('disabled', false);
    }
    if (!init) {
        if ((field.prop("checked") && field.val() == "No")) {
            const date = $('.header-title').children('b').text()
            const intialDate = date.split('to')[0]
            const finalDate = date.split('to')[1]
            field.parent().parent().parent().find("input[name*='StartDate']").flatpickr({ defaultDate: intialDate.trim()})
            field.parent().parent().parent().find("input[name*='EndDate']").flatpickr({ defaultDate: finalDate.trim() })
        }
    }
}


$(function(){
    $("input[type=checkbox]").each(function(){
        SwitchActivity($(this), true);
    });
    $(".activity-checkbox").click(function() {
        SwitchActivity($(this), false);
    });
    $("input[type=radio]").each(function() {
        SwitchPartFinancialPeriod($(this), true);
    })
    $("input[type=radio]").click(function() {
        SwitchPartFinancialPeriod($(this), false);
    });
});


$("#NoneCheckBox").on('click', function (e) {
    const activeCheckbox = $(this).prop("checked");


    if (activeCheckbox){
        const activitiesChecked = $(".activity-checkbox:checked").length;
        if (activitiesChecked > 0){
            e.preventDefault();
            Swal.fire( {
                title: "Are you sure? ",
                text: 'Selecting "None" clears your input for other activities.',
                icon: 'warning',
                backdrop: true,
                showCancelButton: true,
                cancelButtonColor: "#6c757d",
                confirmButtonColor: "#0081B4",
                confirmButtonText: 'Confirm',
                reverseButtons: true,
            }).then(function (result) {
                if (result.isConfirmed) {
                    $("#NoneCheckBox").prop('checked', true).trigger('change');
                    $("input[type=checkbox]").each(function(){
                        SwitchActivity($(this), false);
                    });

                    $("input[type=radio]").each(function() {
                        SwitchPartFinancialPeriod($(this), false);
                    })
                }
            });
        }else{
            $("#NoneCheckBox").prop('checked', true).trigger('change');
            $("input[type=checkbox]").each(function(){
                SwitchActivity($(this), false);
            });

            $("input[type=radio]").each(function() {
                SwitchPartFinancialPeriod($(this), false);
            })
        }

    }else{
        SwitchActivity($(this), false);
        $("#NoneCheckBox").removeAttr('Checked');
        $(".activity-checkbox").prop('disabled', false);
    }

})
