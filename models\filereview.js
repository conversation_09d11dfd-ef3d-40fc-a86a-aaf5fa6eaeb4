const mongoose = require('mongoose');
const ObjectId = require('mongoose').Types.ObjectId;
const fileTypeSchema = require('./file');

const commentSchema = new mongoose.Schema({
  username: { type: String, required: true },
  role: { type: String, required: true },
  comment: { type: String, required: false },
  date: { type: Date, required: true },
  from: { type: String, required: false },
  to: { type: String, required: false },
});
const fileTypeValidationSchema = new mongoose.Schema({
  validated: { type: Boolean, required: false },
  referenceFile: { type: String, required: false },
});

const positionReviewSchema = new mongoose.Schema({
  referenceId: { type: ObjectId, ref: 'organizations.positions._id', required: true },
  files: [fileTypeValidationSchema],
  details: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  identification: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  residentialAddress: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  taxResidence: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  advisorDetails: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  principalAddress: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  mailingAddress: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  worldCheck: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  additional: {
    complete: { type: Boolean, required: false, default: false},
    validated: { type: Boolean, required: false, default: false },
  },
});

const naturalReviewSchema = new mongoose.Schema({
  referenceId: { type: ObjectId, ref: 'naturalpeople', required: true },
  files: [fileTypeValidationSchema],
  details: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  identification: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  residentialAddress: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  taxResidence: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  advisorDetails: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  principalAddress: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  mailingAddress: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  pepDetails: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  worldCheck: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  additional: {
    complete: { type: Boolean, required: false, default: false},
    validated: { type: Boolean, required: false, default: false },
  },
});

const organizationReviewSchema = new mongoose.Schema({
  referenceId: { type: ObjectId, ref: 'organizations', required: true },
  files: [fileTypeValidationSchema],
  details: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  principalAddress: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  mailingAddress: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  listedCompanyDetails: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  limitedCompanyDetails: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  mutualFundDetails: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  foundation: {
    complete: { type: Boolean, required: false, default: false },
    validated: { type: Boolean, required: false, default: false },
  },
  worldCheck: {
    complete: { type: Boolean, required: true, default: false },
    validated: { type: Boolean, required: true, default: false },
  },
  additional: {
    complete: { type: Boolean, required: false, default: false},
    validated: { type: Boolean, required: false, default: false },
  },
});

const pendingElectronicSchema = new mongoose.Schema({
  relationId: { type: ObjectId, required: false },
  uuid:  { type: String, required: false },
  complete: { type: Boolean, required: false },
});

// // File Review Schema
const fileReviewSchema = new mongoose.Schema({
  clientId: { type: ObjectId, ref: 'client', required: false },
  masterClientCode: { type: String, required: true},
  companyCode: { type: String, required: true },
  companyName: { type: String, required: true },
  companyType: { type: String, required: true },
  riskGroup: [{type: String, required: true}],
  status: {
    code: {type: String, required: true},
    statusDate: {type: Date, required: true},
  },
  correctType: { type: Boolean, required: true },
  remark: { type: String, required: false },
  approved: { type: Boolean, required: true, default: false },
  comments: [commentSchema],
  fileReview: {
    username: { type: String, required: false },
    name: { type: String, required: false },
    dateAssigned: { type: Date, required: false },
    validatedDate: { type: Date, required: false },
    assignedBy: {
      username: { type: String, required: false },
      role: { type: String, required: false },
    },
  },
  qualityAssurance: {
    username: { type: String, required: false },
    name: { type: String, required: false },
    dateAssigned: { type: Date, required: false },
    validatedDate: { type: Date, required: false },
  },
  compliance: {
    username: { type: String, required: false },
    name: { type: String, required: false },
    dateAssigned: { type: Date, required: false },
    validatedDate: { type: Date, required: false },
    assignedBy: { type: String, required: false}
  },
  companyActivityReview: {
    present: { type: Boolean, required: false, default: false},
    validated: { type: Boolean, required: false, default: false},
    explanation: { type: String, required: false, max: 100 },
  },
  beneficialOwners: {
    natural: [naturalReviewSchema],
    organization: [organizationReviewSchema],
  },
  shareholders:{
    natural: [naturalReviewSchema],
    organization: [organizationReviewSchema],
  },
  directors:{
    natural: [naturalReviewSchema],
    organization: [organizationReviewSchema],
  },
  files: [fileTypeSchema],
  positions: [positionReviewSchema],
  updatedAt: { type: Date, required: true},
  createdAt: { type: Date, required: true},
  pendingElectronicIds: [pendingElectronicSchema],
  partitionkey: { type: String, required: true },
});

//Export model
//module.exports = mongoose.model("filetypes", fileTypeSchema);
const filereview = mongoose.model('filereview', fileReviewSchema);
const naturalReview = mongoose.model('naturalReviewSchema', naturalReviewSchema);
const positionReview = mongoose.model('positionReviewSchema', positionReviewSchema);
const organizationReview = mongoose.model('organizationReviewSchema', organizationReviewSchema);
const fileTypeValidation = mongoose.model('fileTypeValidationSchema', fileTypeValidationSchema);


module.exports ={
  filereview: filereview,
  naturalReview: naturalReview,
  positionReview: positionReview,
  organizationReview: organizationReview,
  fileTypeValidation: fileTypeValidation,
};
