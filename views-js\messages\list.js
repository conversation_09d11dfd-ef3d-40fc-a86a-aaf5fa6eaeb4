$(document).ready(function () {
    const receivedAtColumnIndex = 3;
    $("#horizontal-datatable").DataTable({
        scrollX: true,
        order: [[receivedAtColumnIndex, "desc"]],
        language: {
            paginate: {
                previous: "<i class='mdi mdi-chevron-left'>",
                next: "<i class='mdi mdi-chevron-right'>",
            },
        },
        drawCallback: function () {
            $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
        },
    });
});

$(document).on('click', '.openMessage', async function () {
   await openMessage($(this).attr('data-message-id'), $(this).attr('data-ids'))
})

async function openMessage(messageId, ids) {
    $.ajax({
        type: 'POST',
        url: '/messages/' + messageId,
        data: {
            ids: ids
        },
        success: function (data) {
            if (data.success) {
                $('#messageSubject').html(data.message.subject.replace(/</g, '&lt;').replace(/>/g, '&gt;'));
                $('#messageContent').val(data.message.content);


                if (data.message.files && data.message.files.length > 0) {
                    for (let i = 0; i < data.message.files.length; i++) {
                        let file = data.message.files[i];
                        let fileRow = '<li>' +
                            '<a target="_blank" href="/messages/' + data.message._id + '/files/' +
                            file.fileId + '">' + file.originalName + '</a></li>';

                        $("#messageFilesBox").append(fileRow)
                    }
                }
                else {
                    $("#messageFilesBox").append('There are no attachments')
                }

                if (data.message.urls && data.message.urls.length > 0) {
                    for (const url of data.message.urls) {
                        let urlRow = '<li><a target="_blank" href="'+ url.url + '">' + url.name + '</a></li>';

                        $("#messageUrlsBox").append(urlRow)
                    }
                }
                else {
                    $("#messageUrlsBox").append('There are no url attachments')
                }
                $('#messageModal').modal();
            }else{
                Swal.fire(
                    'Error',
                    data.error ? data.error : 'There was an error while trying to get the message',
                    'error'
                ).then(() => {
                    $('#messageModal').modal('hide');
                });
            }
        },
        error: function () {
            Swal.fire(
                'Error',
                'There was an error while trying to fetch the message',
                'error'
            ).then(() => {
                $('#messageModal').modal('hide');
            });
        },
    });
}