const httpConstants = require('http2').constants;
const multer = require('multer');
const multerAzureStorage = require('../classes/MulterAzureStorage');
const EntryModel = require('../models/entry').EntryModel;
const { BlobServiceClient, StorageSharedKeyCredential } = require("@azure/storage-blob");
const { DefaultAzureCredential } = require("@azure/identity");
const verifyExtFile = require('../utils/verifyExtensionFile');

const fileFilter = (req, file, cb) => {
  // Limit file name length to 900 characters
  if (file.originalname.length > 900) {
    return cb(new Error('File name too long. Please rename and try again'));
  }
  // Limit files uploaded per session
  if (process.env.MAX_FILES_PER_SESSION && req.session.filesUploaded && req.session.filesUploaded + 1 >= process.env.MAX_FILES_PER_SESSION) {
    return cb(new Error('Too many files uploaded in the current session, please log in again.'));
  }
  req.session.filesUploaded ??= 0;
  req.session.filesUploaded += 1;
  cb(null, true);
}

function removeFileReference(files, id) {
  for (let idx = 0; idx < files.length; idx++) {
    if (files[idx]._id == id) {
      files.splice(idx, 1);
    }
  }
  return files;
}

exports.uploadErrorHandler = function (upload) {
  return (req, res, next) => {
    upload(req, res, function (err) {
      if (err) {
        return res.status(400).json({ error: err.message });
      } else {
        next();
      }
    });
  }
};

exports.uploadFile = multer({
  storage: new multerAzureStorage({
    containerName: process.env.AZURE_STORAGE_CONTAINER,
    accessKey: process.env.AZURE_STORAGE_ACCESS_KEY,
    accountName: process.env.AZURE_STORAGE_ACCOUNT,
  }),
  fileFilter,
});

exports.uploadIncorporationFile = multer({
  storage: new multerAzureStorage({
    containerName: process.env.AZURE_STORAGE_CONTAINER_CLIENT_INCORPORATION,
    accessKey: process.env.AZURE_STORAGE_ACCESS_KEY,
    accountName: process.env.AZURE_STORAGE_ACCOUNT,
  }),
  fileFilter,
});

exports.uploadFinancialReportsFile = multer({
  storage: new multerAzureStorage({
    containerName: process.env.AZURE_STORAGE_CONTAINER_FINANCIAL_REPORTS,
    accessKey: process.env.AZURE_STORAGE_ACCESS_KEY,
    accountName: process.env.AZURE_STORAGE_ACCOUNT,
  }),
  fileFilter,
});

exports.uploadRequestedFile = multer({
  storage: new multerAzureStorage({
    containerName: process.env.AZURE_STORAGE_CONTAINER_FILEREVIEW_REQUESTED_FILES,
    accessKey: process.env.AZURE_STORAGE_ACCESS_KEY,
    accountName: process.env.AZURE_STORAGE_ACCOUNT,
  }),
  fileFilter
});

async function doMoveIncorporationUpload(companyIncorporationId, file, fileType) {
  //moves the uploaded file in azure to a subfolder with the fileReview id
  if (file) {
    const newName = companyIncorporationId + '/' + file.blobName.replace('fileUploaded', fileType);
    await renameBlob(process.env.AZURE_STORAGE_CONTAINER_CLIENT_INCORPORATION, file.blobName, newName);
  }
}

exports.moveIncorporationUpload = doMoveIncorporationUpload;

async function doMoveFileUpload(id, file, fileType, container) {
  if (file) {
    const newName = id + '/' + file.blobName.replace('fileUploaded', fileType);
    await renameBlob(container, file.blobName, newName);
  }
}

exports.moveUploadFile = doMoveFileUpload;

function setUploadedFiles(businessObj, uploadedFiles, fileLocation) {
  if (businessObj[fileLocation]) {
    //append
    businessObj[fileLocation] = businessObj[fileLocation].concat(uploadedFiles)
  } else {
    businessObj[fileLocation] = uploadedFiles
  }
}

function doMoveUpload(entry, file, filetype, callback) {
  if (file) {
    const newName = entry.id + '/' + file.blobName.replace('SubmitEvidence', filetype);
    renameBlob(process.env.AZURE_STORAGE_CONTAINER, file.blobName, newName).then(callback);
  } else {
    callback();
  }
}

exports.saveUpload = [
  (req, res, next) => {
    EntryModel.findById(req.params.id, function (err, entry) {
      if (err) { return next(err); }
      if (entry == null) { // No results.
        err = new Error('Entry not found');
        err.status = 404;
        return next(err);
      }
      const uploadedFiles = req.files['SubmitEvidence'];

      if (uploadedFiles && uploadedFiles.length > 0) {
        const sessData = req.session;
        const itemsToProcess = uploadedFiles.length;
        let itemsProcessed = 0;
        uploadedFiles.forEach(function (item, index) {
          if (item.mimetype !== 'application/pdf' ||
            !verifyExtFile.isValidExtensionFile(item.originalname) ||
            !verifyExtFile.isValidExtensionFile(item.blob) ||
            !verifyExtFile.isValidExtensionFile(item.blobName)
          ) {
            console.log("Incorrect file type");
            const err = new Error('Incorrect file type');
            err.status = 400;
            return next(err);

          }
          doMoveUpload(entry, item, req.body.filetype, function () {
            uploadedFiles[index].fieldname = uploadedFiles[index].fieldname.replace(/submitevidence/i, req.body.filetype);
            uploadedFiles[index].blob = uploadedFiles[index].blob.replace(/submitevidence/i, req.body.filetype)
            uploadedFiles[index].blobName = uploadedFiles[index].blobName.replace(/submitevidence/i, req.body.filetype)
            uploadedFiles[index].url = uploadedFiles[index].url.replace(/submitevidence/i, entry.id + "/" + req.body.filetype)
            itemsProcessed++;
            if (itemsProcessed === itemsToProcess) {
              //proceed
              //tax residency may not exist yet when uploading first files. create empty object
              switch (req.body.filetype) {
                case "EvidenceNonResidency-tax-residency":
                  if (!entry.tax_residency) {
                    entry.tax_residency = {};
                  }
                  if (entry.tax_residency.evidence_non_residency) {
                    //append
                    entry.tax_residency.evidence_non_residency = entry.tax_residency.evidence_non_residency.concat(uploadedFiles)
                  } else {
                    entry.tax_residency.evidence_non_residency = uploadedFiles
                  }
                  break;
                case "EvidenceNoRelevantActivities-relevant-activities":
                  if (!entry.relevant_activities) {
                    entry.relevant_activities = {};
                  }
                  if (entry.relevant_activities.evidence_none_activities) {
                    //append
                    entry.relevant_activities.evidence_none_activities = entry.relevant_activities.evidence_none_activities.concat(uploadedFiles)
                  } else {
                    entry.relevant_activities.evidence_none_activities = uploadedFiles
                  }
                  break;
                case "EvidenceEquipment-banking-business":
                  if (!entry.banking_business) {
                    entry.banking_business = {};
                  }
                  setUploadedFiles(entry.banking_business, uploadedFiles, 'evidence_equipment');
                  break;
                case "EvidenceEquipment-insurance-business":
                  if (!entry.insurance_business) {
                    entry.insurance_business = {};
                  }
                  setUploadedFiles(entry.insurance_business, uploadedFiles, 'evidence_equipment');
                  break;
                case "EvidenceEquipment-fund-management-business":
                  if (!entry.fund_management_business) {
                    entry.fund_management_business = {};
                  }
                  setUploadedFiles(entry.fund_management_business, uploadedFiles, 'evidence_equipment');
                  break;
                case "EvidenceEquipment-finance-leasing-business":
                  if (!entry.finance_leasing_business) {
                    entry.finance_leasing_business = {};
                  }
                  setUploadedFiles(entry.finance_leasing_business, uploadedFiles, 'evidence_equipment');
                  break;
                case "EvidenceEquipment-headquarters-business":
                  if (!entry.headquarters_business) {
                    entry.headquarters_business = {};
                  }
                  setUploadedFiles(entry.headquarters_business, uploadedFiles, 'evidence_equipment');
                  break;
                case "EvidenceEquipment-shipping-business":
                  if (!entry.shipping_business) {
                    entry.shipping_business = {};
                  }
                  setUploadedFiles(entry.shipping_business, uploadedFiles, 'evidence_equipment');
                  break;
                case "EvidenceEquipment-intellectual-property-business":
                  if (!entry.intellectual_property_business) {
                    entry.intellectual_property_business = {};
                  }
                  setUploadedFiles(entry.intellectual_property_business, uploadedFiles, 'evidence_equipment');
                  break;
                case "EvidenceEquipment-holding-business":
                  if (!entry.holding_business) {
                    entry.holding_business = {};
                  }
                  setUploadedFiles(entry.holding_business, uploadedFiles, 'evidence_equipment');
                  break;
                case "EvidenceEquipment-service-centre-business":
                  if (!entry.service_centre_business) {
                    entry.service_centre_business = {};
                  }
                  setUploadedFiles(entry.service_centre_business, uploadedFiles, 'evidence_equipment');
                  break;

                case "EvidenceOutsourcing-banking-business":
                  if (!entry.banking_business) {
                    entry.banking_business = {};
                  }
                  setUploadedFiles(entry.banking_business, uploadedFiles, 'outsourcing_evidence');
                  break;
                case "EvidenceOutsourcing-insurance-business":
                  if (!entry.insurance_business) {
                    entry.insurance_business = {};
                  }
                  setUploadedFiles(entry.insurance_business, uploadedFiles, 'outsourcing_evidence');
                  break;
                case "EvidenceOutsourcing-fund-management-business":
                  if (!entry.fund_management_business) {
                    entry.fund_management_business = {};
                  }
                  setUploadedFiles(entry.fund_management_business, uploadedFiles, 'outsourcing_evidence');
                  break;
                case "EvidenceOutsourcing-finance-leasing-business":
                  if (!entry.finance_leasing_business) {
                    entry.finance_leasing_business = {};
                  }
                  setUploadedFiles(entry.finance_leasing_business, uploadedFiles, 'outsourcing_evidence');
                  break;
                case "EvidenceOutsourcing-headquarters-business":
                  if (!entry.headquarters_business) {
                    entry.headquarters_business = {};
                  }
                  setUploadedFiles(entry.headquarters_business, uploadedFiles, 'outsourcing_evidence');
                  break;
                case "EvidenceOutsourcing-shipping-business":
                  if (!entry.shipping_business) {
                    entry.shipping_business = {};
                  }
                  setUploadedFiles(entry.shipping_business, uploadedFiles, 'outsourcing_evidence');
                  break;
                case "EvidenceOutsourcing-intellectual-property-business":
                  if (!entry.intellectual_property_business) {
                    entry.intellectual_property_business = {};
                  }
                  setUploadedFiles(entry.intellectual_property_business, uploadedFiles, 'outsourcing_evidence');
                  break;
                case "EvidenceOutsourcing-holding-business":
                  if (!entry.holding_business) {
                    entry.holding_business = {};
                  }
                  setUploadedFiles(entry.holding_business, uploadedFiles, 'outsourcing_evidence');
                  break;
                case "EvidenceOutsourcing-service-centre-business":
                  if (!entry.service_centre_business) {
                    entry.service_centre_business = {};
                  }
                  setUploadedFiles(entry.service_centre_business, uploadedFiles, 'outsourcing_evidence');
                  break;

                case "SupportDocuments-banking-business":
                  if (!entry.banking_business) {
                    entry.banking_business = {};
                  }
                  setUploadedFiles(entry.banking_business, uploadedFiles, 'support_documents');
                  break;
                case "SupportDocuments-insurance-business":
                  if (!entry.insurance_business) {
                    entry.insurance_business = {};
                  }
                  setUploadedFiles(entry.insurance_business, uploadedFiles, 'support_documents');
                  break;
                case "SupportDocuments-fund-management-business":
                  if (!entry.fund_management_business) {
                    entry.fund_management_business = {};
                  }
                  setUploadedFiles(entry.fund_management_business, uploadedFiles, 'support_documents');
                  break;
                case "SupportDocuments-finance-leasing-business":
                  if (!entry.finance_leasing_business) {
                    entry.finance_leasing_business = {};
                  }
                  setUploadedFiles(entry.finance_leasing_business, uploadedFiles, 'support_documents');
                  break;
                case "SupportDocuments-headquarters-business":
                  if (!entry.headquarters_business) {
                    entry.headquarters_business = {};
                  }
                  setUploadedFiles(entry.headquarters_business, uploadedFiles, 'support_documents');
                  break;
                case "SupportDocuments-shipping-business":
                  if (!entry.shipping_business) {
                    entry.shipping_business = {};
                  }
                  setUploadedFiles(entry.shipping_business, uploadedFiles, 'support_documents');
                  break;
                case "SupportDocuments-intellectual-property-business":
                  if (!entry.intellectual_property_business) {
                    entry.intellectual_property_business = {};
                  }
                  setUploadedFiles(entry.intellectual_property_business, uploadedFiles, 'support_documents');
                  break;
                case "SupportDocuments-holding-business":
                  if (!entry.holding_business) {
                    entry.holding_business = {};
                  }
                  setUploadedFiles(entry.holding_business, uploadedFiles, 'support_documents');
                  break;
                case "SupportDocuments-service-centre-business":
                  if (!entry.service_centre_business) {
                    entry.service_centre_business = {};
                  }
                  setUploadedFiles(entry.service_centre_business, uploadedFiles, 'support_documents');
                  break;

                case "EvidenceHighRiskIp-intellectual-property-business":
                  if (!entry.intellectual_property_business) {
                    entry.intellectual_property_business = {};
                  }
                  setUploadedFiles(entry.intellectual_property_business, uploadedFiles, 'high_risk_ip_evidence');
                  break;
                case "EvidenceTangibleAssetsExplanation-intellectual-property-business":
                  if (!entry.intellectual_property_business) {
                    entry.intellectual_property_business = {};
                  }
                  setUploadedFiles(entry.intellectual_property_business, uploadedFiles, 'tangible_assets_explanation_files');
                  break;
                case "EvidenceIntangibleAssetsDecisions-intellectual-property-business":
                  if (!entry.intellectual_property_business) {
                    entry.intellectual_property_business = {};
                  }
                  setUploadedFiles(entry.intellectual_property_business, uploadedFiles, 'intangible_assets_decisions_files');
                  break;
                case "EvidenceIntangibleAssetsNature-intellectual-property-business":
                  if (!entry.intellectual_property_business) {
                    entry.intellectual_property_business = {};
                  }
                  setUploadedFiles(entry.intellectual_property_business, uploadedFiles, 'intangible_assets_nature_files');
                  break;
                case "EvidenceIntangibleAssetsTradingNature-intellectual-property-business":
                  if (!entry.intellectual_property_business) {
                    entry.intellectual_property_business = {};
                  }
                  setUploadedFiles(entry.intellectual_property_business, uploadedFiles, 'intangible_assets_trading_nature_files');
                  break;
                case "EvidenceOtherCigaBusiness-intellectual-property-business":
                  if (!entry.intellectual_property_business) {
                    entry.intellectual_property_business = {};
                  }
                  setUploadedFiles(entry.intellectual_property_business, uploadedFiles, 'other_ciga_business_files');
                  break;
                case "EvidenceOtherCigaDecisions-intellectual-property-business":
                  if (!entry.intellectual_property_business) {
                    entry.intellectual_property_business = {};
                  }
                  setUploadedFiles(entry.intellectual_property_business, uploadedFiles, 'other_ciga_decisions_files');
                  break;
                case "EvidenceOtherCigaEvidence-intellectual-property-business":
                  if (!entry.intellectual_property_business) {
                    entry.intellectual_property_business = {};
                  }
                  setUploadedFiles(entry.intellectual_property_business, uploadedFiles, 'other_ciga_evidence_files');
                  break;
                case "EvidenceOtherCigaFiles-intellectual-property-business":
                  if (!entry.intellectual_property_business) {
                    entry.intellectual_property_business = {};
                  }
                  setUploadedFiles(entry.intellectual_property_business, uploadedFiles, 'other_ciga_files');
                  break;
                case "SupportingDetailsAttachment-supporting-details":
                  if (!entry.supporting_details) {
                    entry.supporting_details = {};
                  }
                  if (entry.supporting_details.support_attachments) {
                    //append
                    entry.supporting_details.support_attachments = entry.supporting_details.support_attachments.concat(uploadedFiles)
                  } else {
                    entry.supporting_details.support_attachments = uploadedFiles
                  }
                  break;
                default:
                  break;
              }




              EntryModel.findOneAndUpdate({ _id: req.params.id, company: sessData.company.code }, entry, {}, function (err) {
                if (err) {
                  err = new Error('Unable to upload. Please try again...');
                  err.status = httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR;
                  res.send(err)
                } else {
                  // Successful - redirect to payment if necessary
                  res.sendStatus(httpConstants.HTTP_STATUS_OK)
                }
              });

            }

          });
        });
      }
    });
  },

  exports.deleteFile = function (req, res, next) {
    EntryModel.findById(req.params.id, function (err, entry) {
      if (err) { return next(err); }
      if (entry == null) { // No results.
        err = new Error('Entry not found');
        err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
        return next(err);
      }
      const sessData = req.session;

      switch (req.body.field) {
        case "EvidenceNonResidency-tax-residency":
          if (entry.tax_residency?.evidence_non_residency?.length > 0) {
            entry.tax_residency.evidence_non_residency = removeFileReference(entry.tax_residency.evidence_non_residency, req.body._id);
          }

          if (entry.tax_residency?.evidence_provisional_treatment_non_residency?.length > 0) {
            entry.tax_residency.evidence_provisional_treatment_non_residency = removeFileReference(entry.tax_residency.evidence_provisional_treatment_non_residency, req.body._id);
          }
          break;
        case "EvidenceNoRelevantActivities-relevant-activities":
          entry.relevant_activities.evidence_none_activities = removeFileReference(entry.relevant_activities.evidence_none_activities, req.body._id);
          break;
        case "EvidenceProvisionalTreatmentNonResident-tax-residency":
          entry.tax_residency.evidence_provisional_treatment_non_residency = removeFileReference(entry.tax_residency.evidence_provisional_treatment_non_residency, req.body._id);
          break;
        case "EvidenceEquipment-banking-business":
          entry.banking_business.evidence_equipment = removeFileReference(entry.banking_business.evidence_equipment, req.body._id);
          break;
        case "EvidenceEquipment-insurance-business":
          entry.insurance_business.evidence_equipment = removeFileReference(entry.insurance_business.evidence_equipment, req.body._id);
          break;
        case "EvidenceEquipment-fund-management-business":
          entry.fund_management_business.evidence_equipment = removeFileReference(entry.fund_management_business.evidence_equipment, req.body._id);
          break;
        case "EvidenceEquipment-finance-leasing-business":
          entry.finance_leasing_business.evidence_equipment = removeFileReference(entry.finance_leasing_business.evidence_equipment, req.body._id);
          break;
        case "EvidenceEquipment-headquarters-business":
          entry.headquarters_business.evidence_equipment = removeFileReference(entry.headquarters_business.evidence_equipment, req.body._id);
          break;
        case "EvidenceEquipment-shipping-business":
          entry.shipping_business.evidence_equipment = removeFileReference(entry.shipping_business.evidence_equipment, req.body._id);
          break;
        case "EvidenceEquipment-intellectual-property-business":
          entry.intellectual_property_business.evidence_equipment = removeFileReference(entry.intellectual_property_business.evidence_equipment, req.body._id);
          break;
        case "EvidenceEquipment-holding-business":
          entry.holding_business.evidence_equipment = removeFileReference(entry.holding_business.evidence_equipment, req.body._id);
          break;
        case "EvidenceEquipment-service-centre-business":
          entry.service_centre_business.evidence_equipment = removeFileReference(entry.service_centre_business.evidence_equipment, req.body._id);
          break;

        case "EvidenceOutsourcing-banking-business":
          entry.banking_business.outsourcing_evidence = removeFileReference(entry.banking_business.outsourcing_evidence, req.body._id);
          break;
        case "EvidenceOutsourcing-insurance-business":
          entry.insurance_business.outsourcing_evidence = removeFileReference(entry.insurance_business.outsourcing_evidence, req.body._id);
          break;
        case "EvidenceOutsourcing-fund-management-business":
          entry.fund_management_business.outsourcing_evidence = removeFileReference(entry.fund_management_business.outsourcing_evidence, req.body._id);
          break;
        case "EvidenceOutsourcing-finance-leasing-business":
          entry.finance_leasing_business.outsourcing_evidence = removeFileReference(entry.finance_leasing_business.outsourcing_evidence, req.body._id);
          break;
        case "EvidenceOutsourcing-headquarters-business":
          entry.headquarters_business.outsourcing_evidence = removeFileReference(entry.headquarters_business.outsourcing_evidence, req.body._id);
          break;
        case "EvidenceOutsourcing-shipping-business":
          entry.shipping_business.outsourcing_evidence = removeFileReference(entry.shipping_business.outsourcing_evidence, req.body._id);
          break;
        case "EvidenceOutsourcing-intellectual-property-business":
          entry.intellectual_property_business.outsourcing_evidence = removeFileReference(entry.intellectual_property_business.outsourcing_evidence, req.body._id);
          break;
        case "EvidenceOutsourcing-holding-business":
          entry.holding_business.outsourcing_evidence = removeFileReference(entry.holding_business.outsourcing_evidence, req.body._id);
          break;
        case "EvidenceOutsourcing-service-centre-business":
          entry.service_centre_business.outsourcing_evidence = removeFileReference(entry.service_centre_business.outsourcing_evidence, req.body._id);
          break;

        case "SupportDocuments-banking-business":
          entry.banking_business.support_documents = removeFileReference(entry.banking_business.support_documents, req.body._id);
          break;
        case "SupportDocuments-insurance-business":
          entry.insurance_business.support_documents = removeFileReference(entry.insurance_business.support_documents, req.body._id);
          break;
        case "SupportDocuments-fund-management-business":
          entry.fund_management_business.support_documents = removeFileReference(entry.fund_management_business.support_documents, req.body._id);
          break;
        case "SupportDocuments-finance-leasing-business":
          entry.finance_leasing_business.support_documents = removeFileReference(entry.finance_leasing_business.support_documents, req.body._id);
          break;
        case "SupportDocuments-headquarters-business":
          entry.headquarters_business.support_documents = removeFileReference(entry.headquarters_business.support_documents, req.body._id);
          break;
        case "SupportDocuments-shipping-business":
          entry.shipping_business.support_documents = removeFileReference(entry.shipping_business.support_documents, req.body._id);
          break;
        case "SupportDocuments-intellectual-property-business":
          entry.intellectual_property_business.support_documents = removeFileReference(entry.intellectual_property_business.support_documents, req.body._id);
          break;
        case "SupportDocuments-holding-business":
          entry.holding_business.support_documents = removeFileReference(entry.holding_business.support_documents, req.body._id);
          break;
        case "SupportDocuments-service-centre-business":
          entry.service_centre_business.support_documents = removeFileReference(entry.service_centre_business.support_documents, req.body._id);
          break;
        case "EvidenceHighRiskIp-intellectual-property-business":
          entry.intellectual_property_business.high_risk_ip_evidence = removeFileReference(entry.intellectual_property_business.high_risk_ip_evidence, req.body._id);
          break;
        case "EvidenceTangibleAssetsExplanation-intellectual-property-business":
          entry.intellectual_property_business.tangible_assets_explanation_files = removeFileReference(entry.intellectual_property_business.tangible_assets_explanation_files, req.body._id);
          break;
        case "EvidenceIntangibleAssetsDecisions-intellectual-property-business":
          entry.intellectual_property_business.intangible_assets_decisions_files = removeFileReference(entry.intellectual_property_business.intangible_assets_decisions_files, req.body._id);
          break;
        case "EvidenceIntangibleAssetsNature-intellectual-property-business":
          entry.intellectual_property_business.intangible_assets_nature_files = removeFileReference(entry.intellectual_property_business.intangible_assets_nature_files, req.body._id);
          break;
        case "EvidenceIntangibleAssetsTradingNature-intellectual-property-business":
          entry.intellectual_property_business.intangible_assets_trading_nature_files = removeFileReference(entry.intellectual_property_business.intangible_assets_trading_nature_files, req.body._id);
          break;
        case "EvidenceOtherCigaBusiness-intellectual-property-business":
          entry.intellectual_property_business.other_ciga_business_files = removeFileReference(entry.intellectual_property_business.other_ciga_business_files, req.body._id);
          break;
        case "EvidenceOtherCigaDecisions-intellectual-property-business":
          entry.intellectual_property_business.other_ciga_decisions_files = removeFileReference(entry.intellectual_property_business.other_ciga_decisions_files, req.body._id);
          break;
        case "EvidenceOtherCigaEvidence-intellectual-property-business":
          entry.intellectual_property_business.other_ciga_evidence_files = removeFileReference(entry.intellectual_property_business.other_ciga_evidence_files, req.body._id);
          break;
        case "EvidenceOtherCigaFiles-intellectual-property-business":
          entry.intellectual_property_business.other_ciga_files = removeFileReference(entry.intellectual_property_business.other_ciga_files, req.body._id);
          break;
        case "SupportingDetailsAttachment-supporting-details":
          entry.supporting_details.support_attachments = removeFileReference(entry.supporting_details.support_attachments, req.body._id);
          break;
        default:
          break;
      }


      EntryModel.findOneAndUpdate({ _id: req.params.id, company: sessData.company.code }, entry, {}, function (err) {
        if (err) {
          err = new Error('Unable to upload. Please try again...');
          err.status = httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR;
          res.send(err)
        } else {
          // Successful - redirect to payment if necessary
          res.json({ result: true });
        }
      });
    });
  }
]


exports.copyFilesFromContainerToOther = async function (directoryFrom, directoryTo, containerFrom, containerTo, blobNameList) {
  try {
    let accountName = process.env.AZURE_STORAGE_ACCOUNT;
    let accountKey = process.env.AZURE_STORAGE_ACCESS_KEY;

    let credentials;

    if (!accountKey) {
      credentials = new DefaultAzureCredential();
    } else {
      // Only for local development and testing
      credentials = new StorageSharedKeyCredential(accountName, accountKey);
    }
    const blobServiceClient = new BlobServiceClient(`https://${accountName}.blob.core.windows.net`, credentials);

    const containerClient = blobServiceClient.getContainerClient(containerFrom);
    const resultFiles = containerClient.listBlobsFlat({ prefix: directoryFrom });

    for await (const blob of resultFiles) {
      const fileName = blob.name;

      if (blobNameList.includes(fileName)) {
        const newFileName = fileName.replace(directoryFrom, directoryTo);
        const sourceBlobClient = containerClient.getBlobClient(fileName);
        const targetBlobClient = containerClient.getBlobClient(newFileName);

        await targetBlobClient.beginCopyFromURL(sourceBlobClient.url);
      }
    }
    return { status: httpConstants.HTTP_STATUS_OK, message: "Process of copying files finished" };

  } catch (e) {
    console.error("Error with the copy of the files: ", e);
    return { status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: "Error with the copy of the files" };
  }
};


async function renameBlob(container, oldName, newName) {
  try {
    let accountName = process.env.AZURE_STORAGE_ACCOUNT;
    let accountKey = process.env.AZURE_STORAGE_ACCESS_KEY;

    let credentials;

    if (!accountKey) {
      credentials = new DefaultAzureCredential();
    } else {
      // Only for local development and testing
      credentials = new StorageSharedKeyCredential(accountName, accountKey);
    }
    const blobServiceClient = new BlobServiceClient(`https://${accountName}.blob.core.windows.net`, credentials);
    const containerClient = blobServiceClient.getContainerClient(container);

    const newBlobClient = containerClient.getBlobClient(newName);
    const sourceBlobClient = containerClient.getBlobClient(oldName);

    const poller = await newBlobClient.beginCopyFromURL(sourceBlobClient.url);
    await poller.pollUntilDone();
    await sourceBlobClient.delete();

  } catch (error) {
    console.log("error: ", error);
  }
}