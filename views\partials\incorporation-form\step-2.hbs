<div class="col-lg-12">
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-12 hide-element" id="missingRelationRowStep2">
                    <div class="alert alert-warning" role="alert">
                        <i class="fa fa-warning mr-2"></i><b>Missing relation!</b> Please add at least one relation
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <button type="button" class="btn solid royal-blue" data-toggle="modal"
                        data-target="#newRelationModal">
                        <i class="fa fa-plus pr-2"></i>Add New Relation
                    </button>
                </div>
            </div>
            <div class="mt-3">
                <h5>RELATIONS</h5>
                {{#if incorrectShareholderPercentage}}
                <p><small>Please note: The total percentage of shares does not equal 100%</small></p>
                {{/if}}
                <div class="table-responsive" id="relationsTableDiv">
                    <table class="table table-striped" id="relationsTable">
                        <thead>
                            <tr>
                                <th class="header-20-percent">Name</th>
                                <th class="header-15-percent">Country</th>
                                <th class="header-15-percent">Percentage</th>
                                <th class="header-15-percent">Owned Type</th>
                                <th class="header-25-percent">Relation type</th>
                                <th class="header-5-percent"></th>
                                <th class="header-5-percent"></th>
                            </tr>
                        </thead>
                        <tbody >
                            {{#each incorporation.relations}}
                            <tr class="relation-row">
                                {{!-- MAY NEED REFACTORING --}}
                                {{#ifEquals type 'natural'}}
                                    <!-- NAME -->
                                    <td class="text-capitalize">
                                        {{details.firstName}} {{details.middleName}}  {{details.lastName}}

                                    </td>
                                    <!-- COUNTRY -->
                                    <td class="text-capitalize">
                                        {{details.countryBirth}}
                                    </td>
                                {{else}}
                                    <!-- NAME -->
                                    <td class="text-capitalize">
                                        {{details.organizationName}}
                                    </td>
                                    <!-- COUNTRY -->
                                    <td class="text-capitalize">
                                        {{details.incorporationCountry}}
                                    </td>
                                {{/ifEquals}}

                                <!-- PERCENTAGE -->
                                <td class="text-capitalize">
                                    {{#if percentage}}
                                    {{percentage}} %
                                    {{else}}
                                    0%
                                    {{/if}}
                                </td>
                                <td class="text-capitalize">{{ type }}</td>
                                <td class="text-capitalize">{{ groups }}</td>
                                <td>
                                    <a href="/masterclients/{{../masterclientcode}}/incorporate-company/{{../incorporationId}}/relations/{{_id}}/edit"
                                        class="btn btn-outline-secondary" id="edit-{{ _id }}">
                                        <i class="fa fa-pencil"></i>
                                    </a>
                                </td>
                                <td>
                                    <button type="button" class="delete btn btn-danger" id="delete-{{ _id }}"
                                        data-relation-id="{{ _id }}" data-incorporation-id="{{ ../incorporationId  }}"
                                        data-toggle="modal" data-target="#deleteRelationModal">
                                        <i class="fa fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {{else}}
                            <tr>
                                <td colspan="9" class="text-center font-italic">
                                    There are no relations
                                </td>
                            </tr>
                            {{/each}}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-md-12">
                    <div class="progress">
                        <div class="progress-bar width-25" role="progressbar" aria-valuenow="2"
                            aria-valuemin="0" aria-valuemax="8">2 of 8
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script type='text/javascript' src='/templates/incorporations/step2relationstable.precompiled.js'></script>
<script type="text/javascript" src="/views-js/partials/incorporate-form/step-2.js"></script>
