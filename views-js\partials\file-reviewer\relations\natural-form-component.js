  $('[data-toggle="tooltip"]').tooltip({container: 'body', trigger: 'hover'});
    $('#pep-confirmation-check-yes').change(function () {
        $('#pepInfo').toggle(200);
    });
    $('#pep-confirmation-check-no').change(function () {
        $('#pepInfo').hide();
    });


    $('#identification[identificationType]').val($('#identification[identificationType]').attr('data-value'));

    $('#add-news-check-yes').change(function () {
        $('#lbl-additionalComments').toggle(200);
        $('#pep-add-news').toggle(200);

    });
    $('#add-news-check-no').change(function () {
        $('#lbl-additionalComments').hide();
        $('#pep-add-news').hide();

    });

    $('input[name="taxResidence[confirmation]"]').on('change', function () {
        const value = $(this).val() === 'YES';
        if(value){
          $("#showAdvisorFields").show();
        }else{
            $("#showAdvisorFields").hide();
        }
    });

    $('input[name="natural[isSamePrincipalAddress]"]').on('change', function () {
        const value = $(this).val() === 'YES';
        if (value){
            $('#naturalForm #mailingAddressDetails').hide(200);
        }
        else{
            $('#naturalForm #mailingAddressDetails').show(200);
        }
    });

    $('input[name="electronicIdInfo[isElectronicId]"]').on('change', function () {
        const value = $(this).val() === 'YES';
        if (value){
            $('#naturalForm #electronic-id-files').show(200);
            $('#naturalForm #manual-id-info').hide();
        }
        else{
            $('#naturalForm #electronic-id-files').hide();
            $('#naturalForm #manual-id-info').show(200);
        }
    });