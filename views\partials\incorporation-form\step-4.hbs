<div class="col-lg-12">
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-12 hide-element" id="missingAssetRowStep4">
                    <div class="alert alert-warning" role="alert">
                        <i class="fa fa-warning mr-2"></i><b>Missing asset!</b> Please add at least one asset
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12 hide-element" id="missingFundRowStep4">
                    <div class="alert alert-warning" role="alert">
                        <i class="fa fa-warning mr-2"></i><b>Missing fund!</b> Please add at least one fund
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="companyOwnAssetsControl">Will the company own any assets legally or
                            beneficially?*</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="row">
                        <div class="col-6">
                            <div class="custom-control custom-radio">
                                <input class="custom-control-input" type="radio" id="companyOwnAssetsYes"
                                    name="companyOwnAssetsControl" value="Yes" required {{#ifEquals
                                    incorporation.ownAssets true}} checked {{/ifEquals}}>
                                <label class="custom-control-label" for="companyOwnAssetsYes">Yes</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="custom-control custom-radio">
                                <input class="custom-control-input" type="radio" id="companyOwnAssetsNo"
                                    name="companyOwnAssetsControl" value="No" required {{#ifEquals
                                    incorporation.ownAssets false}} checked {{/ifEquals}}>
                                <label class="custom-control-label" for="companyOwnAssetsNo">No</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="assetsAndFunds" {{#unless incorporation.ownAssets}} class="hide-element" {{/unless}}>
                <div class="row">
                    <div class="col-12">
                        <button type="button" class="btn solid royal-blue" id="openNewAssetModal">
                            <i class="fa fa-plus pr-2"></i>Add new asset
                        </button>
                    </div>
                </div>
                <div class="mt-3">
                    <h5>ASSETS</h5>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th class="header-20-percent">Asset Type</th>
                                    <th class="header-40-percent">Information</th>
                                    <th class="header-20-percent"></th>
                                    <th class="header-20-percent"></th>
                                </tr>
                            </thead>
                            <tbody id="assetsTableBody">
                                {{#each incorporation.assets}}
                                <tr id="asset-table-row-{{_id}}" class="asset-row">
                                    <td>{{type}}</td>
                                    {{#ifEquals type 'Aircraft'}}
                                    <td>
                                        Registration Number: {{registrationNumber}}<br>
                                        Jurisdiction of Registration: {{jurisdictionOfRegistration}}
                                    </td>
                                    {{/ifEquals}}
                                    {{#ifEquals type 'Vessel (ship/yacht)'}}
                                    <td>
                                        Registration Number: {{registrationNumber}}<br>
                                        Jurisdiction of Registration: {{jurisdictionOfRegistration}}
                                    </td>
                                    {{/ifEquals}}
                                    {{#ifEquals type 'Intellectual property rights'}}
                                    <td>
                                        Details: {{details}}
                                    </td>
                                    {{/ifEquals}}
                                    {{#ifEquals type 'Shares/equity participations'}}
                                    <td>
                                        Details: {{details}}
                                    </td>
                                    {{/ifEquals}}
                                    {{#ifEquals type 'Debt'}}
                                    <td>
                                        Details: {{details}}
                                    </td>
                                    {{/ifEquals}}
                                    {{#ifEquals type 'Other'}}
                                    <td>
                                        Details: {{details}}
                                    </td>
                                    {{/ifEquals}}
                                    {{#ifEquals type 'Investment portfolio'}}
                                    <td>
                                        Name of institution: {{nameOfInstitution}} <br>
                                        Address of institution: {{addressOfInstitution}}
                                    </td>
                                    {{/ifEquals}}
                                    {{#ifEquals type 'Bank account'}}
                                    <td>
                                        Name of bank: {{nameOfBank}} <br>
                                        Address of bank: {{addressOfBank}}
                                    </td>
                                    {{/ifEquals}}
                                    {{#ifEquals type 'Trust assets'}}
                                    <td>
                                        Name of trust: {{nameOfTrust}}
                                    </td>
                                    {{/ifEquals}}
                                    {{#ifEquals type 'Real estate'}}
                                    <td>
                                        Type: {{realEstateType}} <br>
                                        Location: {{location}}
                                    </td>
                                    {{/ifEquals}}
                                    <th class="text-right">
                                        <button type="button" class="btn btn-outline-secondary openEditAssetModal" 
                                                data-id="{{_id}}">
                                            <i class="fa fa-pencil mr-2"></i>Edit
                                        </button>
                                    </th>
                                    <th class="text-left">
                                        <button type="button" class="delete btn btn-danger deleteAsset"
                                                data-id="{{_id}}">
                                            <i class="fa fa-trash mr-2"></i>Delete
                                        </button>
                                    </th>
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                    </div>
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="EstimatedValueOfAssets">Estimated value of assets*</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <input type="text" class="form-control autonumber text-right" data-a-sep="," required
                                    data-m-dec="2" name="EstimatedValueOfAssets" id="EstimatedValueOfAssets" value="{{incorporation.estimated_value_of_assets}}">
                            </div>
                        </div>
                    </div>
                </div>

                
            </div>
            <div class="mt-3">
                <h5>SOURCE OF FUNDS</h5>
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group mb-3">
                            <label class="mb-2" for="bankAccountOwnerControl">Payment for Trident Services will be
                                made
                                from bank account owned by*</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="row">
                            <div class="col-6">
                                <div class="custom-control custom-radio">
                                    <input class="custom-control-input" type="radio" id="bankAccountOwnerEntity"
                                        name="bankAccountOwnerControl" value="Entity" required {{#ifEquals
                                        incorporation.bankAccountOwner "Entity" }} checked {{/ifEquals}}>
                                    <label class="custom-control-label" for="bankAccountOwnerEntity">Entity</label>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="custom-control custom-radio">
                                    <input class="custom-control-input" type="radio" id="bankAccountOwnerBO"
                                        name="bankAccountOwnerControl" value="Beneficial Owner" required
                                        {{#ifEquals incorporation.bankAccountOwner "Beneficial Owner" }} checked {{/ifEquals}}
                                        {{#ifEquals incorporation.bankAccountOwner "Benefitial Owner" }} checked {{/ifEquals}}
                                    >
                                    <label class="custom-control-label" for="bankAccountOwnerBO">Beneficial
                                        Owner</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group mb-3">
                            <label class="mb-2" for="bankNameControl">Bank name*</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <input type="text" name="bankName" id="bankNameControl" class="form-control" required
                                value="{{incorporation.bankName}}">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group mb-3">
                            <label class="mb-2" for="bankAddressControl">Bank address*</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group mb-3">
                            <input type="text" name="bankAddress" id="bankAddressControl" class="form-control"
                                required value="{{incorporation.bankAddress}}">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group mb-3">
                            <label class="mb-2">From what source or sources have the assets and funds to be
                                introduced into the entity been derived?</label>
                        </div>
                    </div>
                    <div class="col-4">
                        <button type="button" class="btn solid royal-blue" id="openNewFundModal">
                            <i class="fa fa-plus pr-2"></i>Add funds
                            </button>
                    </div>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th class="header-20-percent">Source Type</th>
                                <th class="header-40-percent">Information</th>
                                <th class="header-20-percent"></th>
                                <th class="header-20-percent"></th>
                            </tr>
                        </thead>
                        <tbody id="fundsTableBody">
                            {{#each incorporation.funds}}
                            <tr id="fund-table-row-{{_id}}" class="fund-row">
                                <td>{{type}}</td>
                                {{#ifEquals type 'Loan'}}
                                <td>
                                    Name of Financial Institution: {{nameOfFinancialInstitution}}
                                </td>
                                {{/ifEquals}}
                                {{#ifEquals type 'Sale of assets'}}
                                <td>
                                    Details: {{details}}
                                </td>
                                {{/ifEquals}}
                                {{#ifEquals type 'Other'}}
                                <td>
                                    Details: {{details}}
                                </td>
                                {{/ifEquals}}
                                {{#ifEquals type 'Business income'}}
                                <td>
                                    Average annual turnover/profit: {{profit}}
                                </td>
                                {{/ifEquals}}
                                {{#ifEquals type 'Dividend from subsidiary'}}
                                <td>
                                    Name of subsidiary: {{nameOfSubsidiary}} <br>
                                    Jurisdiction of operation: {{jurisdictionOfOperation}}
                                </td>
                                {{/ifEquals}}
                                {{#ifEquals type 'Share capital'}}
                                <td>
                                </td>
                                {{/ifEquals}}
                                <th class="text-right">
                                    <button type="button" class="btn btn-outline-secondary openEditFundModal"
                                            data-id="{{_id}}">
                                        <i class="fa fa-pencil mr-2"></i>Edit
                                    </button>
                                </th>
                                <th class="text-left">
                                    <button type="button" class="delete btn btn-danger deleteFund"
                                            data-id="{{_id}}">
                                        <i class="fa fa-trash mr-2"></i>Delete
                                    </button>
                                </th>
                            </tr>
                            {{/each}}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="progress">
                        <div class="progress-bar w-50" role="progressbar" aria-valuenow="4"
                            aria-valuemin="0" aria-valuemax="8">4 of 8
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{{> incorporation-form/create-asset-modal}}
{{> incorporation-form/create-fund-modal}}

<script type='text/javascript' src='/templates/incorporations/step4createassetrow.precompiled.js'></script>
<script type='text/javascript' src='/templates/incorporations/step4createfundrow.precompiled.js'></script>
<script type="text/javascript" src="/views-js/partials/incorporate-form/step-4.js"></script>

