const EntryModel = require('../models/entry').EntryModel;
const ArchivedEntryModel = require('../models/entry').ArchivedEntryModel;
const moment = require('moment');

exports.validate = async function (entry) {

  let errors = [];

  if (entry.entity_details.financial_period_changed === true) {
    if (!entry.entity_details.date_of_application_ITA) {
      errors.push({ msg: "Provide date of application to ITA", field: "DateOfApplicationToITA" })
    } else {
      const today = moment(new Date()).utc();
      const applicationITADate = moment(entry.entity_details.date_of_application_ITA).utc();

      if (applicationITADate.isAfter(today)) {
        errors.push({ msg: "Your ITA application date cannot be in the future", field: "DateOfApplicationToITA" })
      }
    }
  }

  //get all submissions for this company and check if there is any submission
  // - with a startdate between the start and end date of another submission
  // - with a enddate between the start and end date of another submission
  let dbEntries = await EntryModel.find({ 'company': entry.company }, ['_id', 'entity_details'], {});
  for (let dbEntry of dbEntries) {
    if (dbEntry._id.toString() != entry._id.toString() && dbEntry.entity_details) {
      if (entry.entity_details.financial_period_begins >= dbEntry.entity_details.financial_period_begins &&
        entry.entity_details.financial_period_begins < dbEntry.entity_details.financial_period_ends) {

        errors.push({ msg: 'This financial period is already submitted' });
        break;
      }
      if (entry.entity_details.financial_period_ends >= dbEntry.entity_details.financial_period_begins &&
        entry.entity_details.financial_period_ends <= dbEntry.entity_details.financial_period_ends) {

        errors.push({ msg: 'This financial period is already submitted' });
        break;
      }
    }
  }

  let dbArchivedEntries = await ArchivedEntryModel.find({ 'company': entry.company }, ['_id', 'entity_details'], {});
  for (let dbEntry of dbArchivedEntries) {
    if (dbEntry._id.toString() != entry._id.toString() && dbEntry.entity_details) {
      if (entry.entity_details.financial_period_begins >= dbEntry.entity_details.financial_period_begins &&
        entry.entity_details.financial_period_begins < dbEntry.entity_details.financial_period_ends) {

        errors.push({ msg: 'This financial period is already submitted' });
        break;
      }
      if (entry.entity_details.financial_period_ends >= dbEntry.entity_details.financial_period_begins &&
        entry.entity_details.financial_period_ends <= dbEntry.entity_details.financial_period_ends) {

        errors.push({ msg: 'This financial period is already submitted' });
        break;
      }
    }
  }

  return errors;
}
