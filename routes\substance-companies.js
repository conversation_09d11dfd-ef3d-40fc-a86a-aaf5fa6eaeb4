const express = require('express');
const router = express.Router({mergeParams: true});
const sessionUtils = require('../utils/sessionUtils');

// Require controller modules.
const company_controller = require('../controllers/companyController');


router.get('/', company_controller.ensureAuthenticated, company_controller.company_list);
router.post('/', company_controller.ensureAuthenticated, company_controller.company_list);
router.get('/select', company_controller.ensureAuthenticated, company_controller.select_company);
router.get('/:companycode/validate-submissions', company_controller.ensureAuthenticated, company_controller.open_company_submissions);
router.get('/:companyCode/forms', company_controller.ensureAuthenticated, company_controller.forms_list);
router.get('/:companyCode/forms/:entryId/select', company_controller.ensureAuthenticated, company_controller.select_form);
router.get('/:companyCode/forms/:entryId/submission.pdf', company_controller.ensureAuthenticated, company_controller.downloadPdf);
router.get('/:companycode/forms/create', company_controller.ensureAuthenticated, company_controller.create_form); // jquery / ajax
router.post('/:companyCode/forms/delete', company_controller.ensureAuthenticated, company_controller.delete_form);

router.get('/:companycode/forms/:entryId/invoice/invoice.pdf', ensureAuthenticated, company_controller.validateMasterClient, company_controller.downloadInvoicePdf);
router.get('/:companycode/forms/:entryId/reopened-info/:reopenedId', ensureAuthenticated, company_controller.validateMasterClient, company_controller.getReopenedInfo); // jquery / ajax

module.exports = router;

function ensureAuthenticated(req, res, next) {
  if ((req.user && req.session.id === req.user.sessionId) && req.session.auth2fa) {
    if (!req.params.companyCode) {
      next();
    } else {
      req.logout(function (err) {
        if (err) { return next(err) }
        req.session.destroy(function () {
          // cannot access session here
          sessionUtils.onSessionDestroyed(req, res);
        });
      });
    }
  } else if ((req.user && req.session.id === req.user.sessionId) && !req.session.auth2fa) {
    if (req.user.secret_2fa) {
      res.redirect('/users/2fa-code');
    } else {
      res.redirect('/users/2fa-setup');
    }
  } else {
    req.logout(function (err) {
      if (err) { return next(err) }
      req.session.destroy(function () {
        // cannot access session here
        sessionUtils.onSessionDestroyed(req, res);
      });
    });
  }
}
