const express = require('express');
const router = express.Router({mergeParams: true});

// Require controller modules.
const companyIncorporationController = require('../controllers/companyIncorporationController');
const downloadController = require('../controllers/downloadController');
const uploadController = require('../controllers/uploadController');

router.get('/', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient, companyIncorporationController.incorporate_company);
router.get('/:incorporationId/incorporation.pdf', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient, companyIncorporationController.downloadIncorporationPdf);
router.get('/:incorporationId/invoice.pdf', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient, companyIncorporationController.downloadIncorporationInvoicePdf);
router.get('/new', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient,
  companyIncorporationController.incorporate_company_form);
router.get('/:incorporationId', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient, companyIncorporationController.load_incorporate_company_form);
router.post('/:incorporationId/delete', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient, 
  companyIncorporationController.delete_incorporate_company); // jquery / ajax
router.post('/:incorporationId', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient,
  companyIncorporationController.save_incorporate_form); // jquery / ajax
router.get('/:incorporationId/files', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient,
  companyIncorporationController.get_incorporation_files 
); // jquery / ajax
router.get('/:incorporationId/name-reservation-info', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient,
  companyIncorporationController.get_name_reservation_info
); // jquery / ajax

router.post('/:incorporationId/name-reservation-info', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient,
  companyIncorporationController.update_name_reservation_info
); // jquery / ajax
router.get('/:incorporationId/declined-reason-info', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient,
  companyIncorporationController.get_decline_reason_info
); // jquery / ajax

router.post('/:incorporationId/upload-files', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient,
  uploadController.uploadErrorHandler(uploadController.uploadIncorporationFile.fields([{ name: 'fileUploaded', maxCount: 5 }])),
  companyIncorporationController.save_incorporation_files
);
router.delete('/:incorporationId/files', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient,
  companyIncorporationController.delete_incorporation_files
); // jquery / ajax
router.get('/:incorporationId/files/:fileId/download',
  companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient,
  downloadController.downloadIncorporationFiles
); 


// Assets and funds - Incorporation
router.post('/:incorporationId/assets/', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient, 
  companyIncorporationController.incorporation_add_asset); // jquery / ajax
router.put('/:incorporationId/assets/:id', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient, 
  companyIncorporationController.incorporation_edit_asset); // jquery / ajax
router.delete('/:incorporationId/assets/:id', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient, 
  companyIncorporationController.incorporation_delete_asset); // jquery / ajax
router.get('/:incorporationId/assets/:id', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient, 
  companyIncorporationController.incorporation_get_asset); // jquery / ajax

router.post('/:incorporationId/funds/', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient,
  companyIncorporationController.incorporation_add_fund); // jquery / ajax
router.put('/:incorporationId/funds/:id', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient, 
  companyIncorporationController.incorporation_edit_fund); // jquery / ajax
router.delete('/:incorporationId/funds/:id', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient, 
  companyIncorporationController.incorporation_delete_fund); // jquery / ajax
router.get('/:incorporationId/funds/:id', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient, 
  companyIncorporationController.incorporation_get_fund); // jquery / ajax

//<--- RELATIONS ROUTES --->

//GET RELATION INFO
/// MODIFY ASAP
//GET UPLOADED FILES
router.get('/:incorporationId/relations/files', companyIncorporationController.ensureAuthenticated, companyIncorporationController.get_relation_files); // jquery / ajax
router.get('/:incorporationId/relations/:relationId', companyIncorporationController.ensureAuthenticated, companyIncorporationController.get_relation); // jquery / ajax
router.get('/:incorporationId/relations/:relationId/edit', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient, 
  companyIncorporationController.get_edit_relation); // jquery / ajax
//POST SAVE RELATION
router.post('/:incorporationId/create-relation', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient,
  companyIncorporationController.create_relation); // jquery / ajax
router.post('/:incorporationId/relations/:relationId/edit', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient,
   companyIncorporationController.update_relation);  // jquery / ajax
//POST CLEAR TEMPORAL FILES
router.post('/:incorporationId/clear-relation-temporal-files', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient,
  companyIncorporationController.clear_relation_temporal_files); // jquery / ajax

// GET template files to file review
router.get('/relations/get-template-files', companyIncorporationController.ensureAuthenticated,
  companyIncorporationController.get_relation_template_files); // jquery / ajax

//POST SAVE RELATION FILES IN AZURE
router.post('/relations/upload-document', companyIncorporationController.ensureAuthenticated,
  uploadController.uploadErrorHandler(uploadController.uploadIncorporationFile.fields([{ name: 'fileUploaded', maxCount: 5 }])),
  companyIncorporationController.save_relation_files
);

//DELETE UPLOADED FILE
router.delete('/:incorporationId/relations/files', companyIncorporationController.ensureAuthenticated,
  companyIncorporationController.delete_relation_file); // jquery / ajax

//DELETE RELATION
/// MODIFY ASAP
router.delete('/:incorporationId/relations/:relationId', companyIncorporationController.ensureAuthenticated,
  companyIncorporationController.remove_incorporation_relation); // jquery / ajax

//<--- END RELATIONS ROUTES --->

// REQUEST VIEW
router.get('/:incorporationId/request-information', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient,
  companyIncorporationController.get_incorporation_request_view
);
router.post('/:incorporationId/request-information', companyIncorporationController.ensureAuthenticated, companyIncorporationController.validateMasterClient,
  companyIncorporationController.submit_incorporation_request 
);
router.post('/:incorporationId/request-information/upload-files', companyIncorporationController.ensureAuthenticated,
  uploadController.uploadErrorHandler(uploadController.uploadIncorporationFile.fields([{ name: 'fileUploaded', maxCount:5 }])), companyIncorporationController.save_request_files);
router.post('/:incorporationId/request-information/delete-files', companyIncorporationController.ensureAuthenticated, companyIncorporationController.delete_request_files);

router.get('/:incorporationId/approved-information', companyIncorporationController.ensureAuthenticated,
  companyIncorporationController.validateMasterClient,
  companyIncorporationController.get_incorporation_approved_view
);

module.exports = router;

