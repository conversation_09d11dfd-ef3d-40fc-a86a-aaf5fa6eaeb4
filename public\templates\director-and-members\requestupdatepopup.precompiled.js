(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['requestupdatepopup'] = template({"1":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                <option value=\"\" hidden>Select an option</option>\r\n\r\n"
    + ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"hasMissingData") : depth0),{"name":"if","hash":{},"fn":container.program(2, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":9,"column":16},"end":{"line":11,"column":23}}})) != null ? stack1 : "")
    + "                <option value=\"Change of Director(s)\" >Change of Director(s)</option>\r\n                <option value=\"Change of Director(s) Particulars\">Change of Director(s) Particulars</option>\r\n                <option value=\"Change of Director(s) Address\">Change of Director(s) Address</option>\r\n                <option value=\"Other update for Director(s)\">Other update for Director(s)</option>\r\n                <option value=\"Confirmation for Licensed Director to provide service\">Confirmation for Licensed Director to provide service</option>\r\n                <option value=\"Revert confirmation for Licensed Director to provide service\">Revert confirmation for Licensed Director to provide service</option>\r\n";
},"2":function(container,depth0,helpers,partials,data) {
    return "                    <option value=\"Missing Director(s) info\">Missing Director(s) info</option>\r\n";
},"4":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||container.hooks.helperMissing).call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"type") : depth0),"member",{"name":"ifEquals","hash":{},"fn":container.program(5, data, 0),"inverse":container.program(8, data, 0),"data":data,"loc":{"start":{"line":18,"column":12},"end":{"line":39,"column":12}}})) != null ? stack1 : "");
},"5":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                <option value=\"\" hidden>Select an option</option>\r\n\r\n"
    + ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"hasMissingData") : depth0),{"name":"if","hash":{},"fn":container.program(6, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":21,"column":16},"end":{"line":23,"column":23}}})) != null ? stack1 : "")
    + "                <option value=\"Change of Shareholder\" >Change of Shareholder</option>\r\n                <option value=\"Change of Shareholder Particulars\" >Change of Shareholder Particulars</option>\r\n                <option value=\"Change of Shareholder Address\" >Change of Shareholder Address</option>\r\n                <option value=\"Other update for Shareholder\">Other update for Shareholder</option>\r\n                <option value=\"Confirmation for Nominee Arrangement\">Confirmation for Nominee Arrangement</option>\r\n                <option value=\"Revert confirmation for Nominee Arrangement\">Revert confirmation for Nominee Arrangement</option>\r\n";
},"6":function(container,depth0,helpers,partials,data) {
    return "                    <option value=\"Missing Info\">Missing Info</option>\r\n";
},"8":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                <option value=\"\" hidden>Select an option</option>\r\n\r\n"
    + ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"hasMissingData") : depth0),{"name":"if","hash":{},"fn":container.program(9, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":33,"column":16},"end":{"line":35,"column":23}}})) != null ? stack1 : "")
    + "                <option value=\"Change of Particular(s)\" >Change of Particular(s)</option>\r\n                <option value=\"Cease Exemption\" >Cease Exemption</option>\r\n                <option value=\"Other changes\" >Other changes</option>\r\n            ";
},"9":function(container,depth0,helpers,partials,data) {
    return "                    <option value=\"Missing Company Info\">Missing Company Info</option>\r\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "<div class=\"text-left\">\r\n\r\n    <div class=\"form-group mb-2\" >\r\n        <label for=\"changeType\">Type of request:</label>\r\n        <select name=\"changeType\" id=\"changeType\" class=\"form-control w-100\" >\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||container.hooks.helperMissing).call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"type") : depth0),"director",{"name":"ifEquals","hash":{},"fn":container.program(1, data, 0),"inverse":container.program(4, data, 0),"data":data,"loc":{"start":{"line":6,"column":12},"end":{"line":39,"column":25}}})) != null ? stack1 : "")
    + "\r\n        </select>\r\n\r\n    </div>\r\n    <div  class=\"form-group w-100 \">\r\n        <label for=\"changeReason\">Additional information if necessary:</label>\r\n        <textarea name=\"changeReason\" class=\"form-control w-100\"\r\n                  id=\"changeReason\" rows=\"3\" placeholder=\"...\" maxlength=\"500\"></textarea>\r\n    </div>\r\n</div>\r\n";
},"useData":true});
})();