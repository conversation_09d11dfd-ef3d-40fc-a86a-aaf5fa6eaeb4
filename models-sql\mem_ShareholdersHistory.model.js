const moment = require('moment');

module.exports = (sequelize, Sequelize) => {
    const ShareholdersHistory = sequelize.define(
        'mem_ShareholdersHistory',
        {
            Id: {
                type: Sequelize.INTEGER,
                allowNull: false,
                primaryKey: true,
                autoIncrement: true
            },
            UniqueRelationID: Sequelize.STRING(42),
            ClientCode: {
                type: Sequelize.STRING(10),
                allowNull: true,
            },
            ClientName: {
                type: Sequelize.STRING(356),
                allowNull: true,
            },
            ClientUniqueNr: {
                type: Sequelize.INTEGER,
                allowNull: false,
            },
            EntityCode: {
                type: Sequelize.STRING(10),
                allowNull: false,
            },
            EntityName: {
                type: Sequelize.STRING(356),
                allowNull: true,
            },
            EntityUniqueNr: {
                type: Sequelize.INTEGER,
                allowNull: false,
            },
            EntityLegacyID: {
                type: Sequelize.STRING(30),
                allowNull: true,
            },
            RelationType: {
                type: Sequelize.STRING(20),
                allowNull: false,
            },
            SHCode: {
                type: Sequelize.STRING(10),
                allowNull: true,
            },
            SHName: {
                type: Sequelize.STRING(356),
                allowNull: true,
            },
            SHUniqueNr: {
                type: Sequelize.INTEGER,
                allowNull: true,
            },
            CombinedSHCode: {
                type: Sequelize.STRING(10),
                allowNull: true,
            },
            SHFileTypeCode: {
                type: Sequelize.STRING(50),
                allowNull: true,
            },
            SHFileType: {
                type: Sequelize.STRING(50),
                allowNull: true,
            },
            MemberID: {
                type: Sequelize.STRING(100),
                allowNull: true,
            },
            MemberTypeCode: {
                type: Sequelize.STRING(20),
                allowNull: false,
            },
            MemberType: {
                type: Sequelize.STRING(255),
                allowNull: false,
            },
            MemberCode: {
                type: Sequelize.STRING(10),
                allowNull: true,
            },
            MemberName: {
                type: Sequelize.STRING(356),
                allowNull: true,
            },
            MemberUniqueNr: {
                type: Sequelize.INTEGER,
                allowNull: true,
            },
            MemberFileTypeCode: {
                type: Sequelize.STRING(50),
                allowNull: true,
            },
            MemberFileType: {
                type: Sequelize.STRING(50),
                allowNull: true,
            },
            SHDateStart: {
                type: Sequelize.DATE,
                allowNull: true,
                get() {
                    const d = this.getDataValue('SHDateStart');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                },
            },
            MemberDateStart: {
                type: Sequelize.DATE,
                allowNull: true,
                get() {
                    const d = this.getDataValue('MemberDateStart');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                },
            },
            MemberDateEnd: {
                type: Sequelize.DATE,
                allowNull: true,
                get() {
                    const d = this.getDataValue('MemberDateEnd');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                },
            },
            ShareTypeCode: {
                type: Sequelize.STRING(2),
                allowNull: true,
            },
            ShareClassCode: {
                type: Sequelize.STRING(6),
                allowNull: true,
            },
            ShareClassName: {
                type: Sequelize.STRING(100),
                allowNull: true,
            },
            SHVotingRights: {
                type: Sequelize.STRING(16),
                allowNull: true,
            },
            ShareIssueDate: {
                type: Sequelize.DATE,
                allowNull: true,
                get() {
                const d = this.getDataValue('ShareIssueDate');
                return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                },
            },
            SHCertNr: {
                type: Sequelize.STRING(16),
                allowNull: false,
                primaryKey: true,
            },
            NrOfShares: {
                type: Sequelize.DECIMAL(38, 12),
                allowNull: true,
            },
            SHAddress: {
                type: Sequelize.TEXT,
                allowNull: false,
            },
            BenOwnerCode: {
                type: Sequelize.STRING(10),
                allowNull: true,
            },
            BenOwner: {
                type: Sequelize.STRING(356),
                allowNull: true,
            },
            BenOwnerUniqueNr: {
                type: Sequelize.INTEGER,
                allowNull: true,
            },
            BenOwnerCertNr: {
                type: Sequelize.STRING(10),
                allowNull: true,
            },
            ShareholderID: {
                type: Sequelize.STRING(100),
                allowNull: true,
            },
            UpdateRequestDate: {
                type: Sequelize.DATE,
                get() {
                    const d = this.getDataValue('UpdateRequestDate');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                }
            },
            ConfirmedDate: {
                type: Sequelize.DATE,
                get() {
                    const d = this.getDataValue('ConfirmedDate');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                }
            },
            Status: Sequelize.STRING(50),
            UserEmail: Sequelize.STRING(255),
            TypeOfUpdateRequest: Sequelize.STRING(255),
            UpdateRequestComments: Sequelize.STRING(2500),
            CreatedAt: {
                type: Sequelize.DATE,
                get() {
                    const d = this.getDataValue('CreatedAt');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                }
            },
            UpdatedAt: {
                type: Sequelize.DATE,
                get() {
                    const d = this.getDataValue('UpdatedAt');
                    return d ? moment.utc(d).format('YYYY-MM-DD') : null;
                }
            },
            hasNomineeArrangement: Sequelize.BOOLEAN
        },
        {
            sequelize,
            tableName: 'mem_ShareholdersHistory',
            schema: 'dbo',
            timestamps: true,
            createdAt: 'CreatedAt',
            updatedAt: 'UpdatedAt'
        }
    );

    ShareholdersHistory.associate = models => {
        ShareholdersHistory.belongsTo(models.mem_MemberProfiles, {
            foreignKey: 'MemberUniqueNr',
            targetKey: 'MFUniqueNr'
        });
    };

    return ShareholdersHistory;
};
