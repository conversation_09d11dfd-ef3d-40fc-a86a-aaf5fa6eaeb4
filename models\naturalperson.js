const fileSchema = require('./file');
const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');


const addressSchema = new mongoose.Schema({
  primaryAddress: { type: String, required: false },
  secondaryAddress: { type: String, required: false },
  country: { type: String, required: false },
  state: { type: String, required: false },
  postalCode: { type: String, required: false },
  city: { type: String, required: false },
});

const commentSchema = new mongoose.Schema({
  username: { type: String, required: false },
  comment: { type: String, required: false },
  date: { type: Date, required: false },
  status: { type: String, required: false },
});

const fileTypeSchema = new mongoose.Schema({
  id: { type: String, required: true, default: uuidv4 },
  internal: { type: String, required: true },
  external: { type: String, required: true },
  fileGroup:  { type: String, required: true },
  present:  { type: Boolean, required: true, default: false},
  explanation: { type: String, required: false, max: 100 },
  comments: { type: String, required: false },
  validated: { type: Boolean, required: true, default: false },
  uploadFiles: [ fileSchema]
});

const electronicIdInfoSchema = new mongoose.Schema({
  isElectronicId: { type: Boolean, required: false },
  details: { type: Object, required: false },
  emailType:  { type: String, required: false },
  electronicIdInvitationSent: { type: Boolean, required: false, default: false },
  idPalInformation: [Object],
  status: { type: String, required: false },
  uuid:  { type: String, required: false },
  invitationDate: {type:Date, required: false},
  email: { type: String, required: false },
  allowNewRequest: { type: Boolean, required: false, default: false },
  comments: [commentSchema],
  files: [fileTypeSchema],
});

const naturalPersonSchema = new mongoose.Schema({
  type: { type: String, required: true, default: 'natural' },
  percentage: { type: String, required: false },
  groups: [String],
  unavailableToPick: { type: Boolean, required: false },
  lockedByFileReview: { type: mongoose.Schema.Types.ObjectId, ref: 'filereview', required: false },
  pep: { type: Boolean, required: true, default: false },
  details: {
    fullName: { type: String, required: false },
    firstName: { type: String, required: false },
    middleName: { type: String, required: false },
    lastName: { type: String, required: false },
    occupation: { type: String, required: false },
    source_of_income: { type: String, required: false },
    birthDate: { type: Date, required: false },
    nationality: { type: String, required: false },
    countryBirth:{ type: String, required: false },
  },
  identification: {
    identificationType: { type: String, required: false },
    issueCountry: { type: String, required: false },
    expiryDate: { type: Date, required: false },
    valid: { type: Boolean, required: false },
    files: [fileTypeSchema],
  },
  electronicIdInfo: {type: electronicIdInfoSchema, required: false},
  residentialAddress: addressSchema,
  isSamePrincipalAddress: {type: Boolean, required: false, default: false},
  mailingAddress: addressSchema,
  taxResidence: {
    confirmation: { type: Boolean, required: false },
    taxResidence: { type: String, required: false },
  },
  advisorDetails:{
    firstName: { type: String, required: false },
    middleName: { type: String, required: false },
    lastName: { type: String, required: false },
    firmName: { type: String, required: false },
    phone: { type: String, required: false },
    email: { type: String, required: false },
    nationality: { type: String, required: false },
    incorporationCountry: { type: String, required: false },
  },
  principalAddress: addressSchema,
  pepDetails: {
    files: [fileTypeSchema],
    information: { type: String, required: false },
    additionalComments: {type: String, required: false},
    confirmAdditionalComments: {type: Boolean, required: false},
  },
  worldCheck: {
    files: [fileTypeSchema]
  },
  createdByImportedBo: { type: Boolean, required: false, default: false },
  partitionkey: { type: String, required: false, default: 'natural' },
  createdAt: { type: Date, required: false},
  updatedAt: { type: Date, required: false },
});

module.exports = mongoose.model('naturalperson', naturalPersonSchema);
module.exports.model = naturalPersonSchema;
