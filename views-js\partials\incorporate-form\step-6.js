$(document).ready(function () {
    $('#sibaLicenceTypeControl').hide(200);
    $('#otherServicesDetailsControl').hide(200);
    $('#trustLicenceTypeControl').hide(200);
})

$('#otherServices').change(function () {
    if ($(this).is(':checked')) {
        $('#otherServicesDetailsControl').show(200);
    } else {
        $('#otherServicesDetailsControl').hide(200);
    }
});

$('#trustLicence').change(function () {
    if ($(this).is(':checked')) {
        $('#trustLicenceTypeControl').show(200);
    } else {
        $('#trustLicenceTypeControl').hide(200);
    }
});

$('#sibaLicence').change(function () {
    if ($(this).is(':checked')) {
        $('#sibaLicenceTypeControl').show(200);
    } else {
        $('#sibaLicenceTypeControl').hide(200);
    }
});

$('input[name="additionalServicesControl"]').change(function () {
    if ($('input[name="additionalServicesControl"]:checked').val() === 'Yes') {
        $('#additionalServicesRow').show(200);
    } else {
        $('#additionalServicesRow').hide(200);
    }
});