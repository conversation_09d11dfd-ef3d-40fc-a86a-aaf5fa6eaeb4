const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');
const CompanyModel = require('./company').model;
const { FinancialReportSelfPrepareMapperSchema} = require('./mappings/financialReportMapping');
const { traverseSchema} = require('../utils/schemaUtils');
const { REPORT_STATUS } = require('../utils/financialReportConstants')


const liabilitySchema = new mongoose.Schema({
  isFirstYearOperation: { type: Boolean, required: false },
  accountsPayable: { type: Number, required: false },
  longTermDebts: { type: Number, required: false },
  valueOfOtherLiabilitiesStartPeriod: { type: Number, required: false },
  valueOfOtherLiabilitiesEndPeriod: { type: Number, required: false },
  accruedExpenses: { type: Number, required: false },
  anyCompanyAccPayable: { type: Boolean, required: false },
  accountsPayableBalance: { type: Number, required: false },
  invoicesIssued: { type: Boolean, required: false },
  compAdminFees: { type: Number, required: false },
  otherExpenses: { type: Number, required: false },
  didCompanyOweLongTermDebts: { type: Boolean, required: false },
  ltDebtsReceived: { type: Number, required: false },
  loanInterestAccrued: { type: Number, required: false },
  ltDebtsRepayments: { type: Number, required: false },
  ltDebtsClosingBalance: { type: Number, required: false },
  otherLiabilitiesOwed: { type: Boolean, required: false },
  totalInvoicesAndAccruedExpenses: { type: Number, required: false },
  totalLiabilities: { type: Number, required: false },
  costOfGoods: { type: Number, required: false },
})

const reopenedDetailsSchema = new mongoose.Schema({
  reopenedAt: { type: Date, required: true },
  reopenedBy: { type: String, required: true },
  resubmittedAt: { type: Date, required: false },
  resubmittedBy: { type: String, required: false },
  reason: { type: String, required: false },
  changeFinancialPeriodDates: { type: Boolean, required: false },
  oldStartDate: { type: Date, required: false },
  oldEndDate: { type: Date, required: false },
  newStartDate: { type: Date, required: false },
  newEndDate: { type: Date, required: false },
});


const reopenedSchema = new mongoose.Schema({
  details: [reopenedDetailsSchema]
});


const fileSchema = new mongoose.Schema({
  fileId: { type: String, required: true, default: uuidv4 },
  fileTypeId: { type: String, required: false },
  fieldName: { type: String, required: true },
  originalName: { type: String, required: true },
  encoding: { type: String, required: false },
  mimeType: { type: String, required: true },
  blobName: { type: String, required: true },
  container: { type: String, required: true },
  blob: { type: String, required: true },
  blobType: { type: String, required: true },
  size: { type: String, required: false },
  etag: { type: String, required: false },
  url: { type: String, required: true },
});

const exemptionApprovalSchema = new mongoose.Schema({
  email: { type: String, required: false },
  approvedAt: { type: Date, required: false }
})

const declarationSchema = new mongoose.Schema({
  information: { type: Boolean, required: false },
  assetsLiabilities: { type: Boolean, required: false },
  tridentService: { type: Boolean, required: false },
  clientPurpose: { type: Boolean, required: false },
  legalAdviseObtain: { type: Boolean, required: false },
  termsAndConditions: { type: Boolean, required: false },
  declarationAmount: { type: Boolean, required: false },
  name: { type: String, required: false },
  relation: { type: String, required: false },
  relationOther: { type: String, required: false },
  phone: { type: String, required: false },
  email: { type: String, required: false },
});

const paymentSchema = new mongoose.Schema({
  total: { type: Number, required: false },
  paidAt: { type: Date, required: false, index: true },
  type: { type: String, required: false },
  reference: { type: String, required: false },
  batchpaymentId: { type: String, required: false },
  batchpaymentTransactionId: { type: String, required: false },
  batchpaymentCode: { type: String, required: false },
});

const fileListSchema = new mongoose.Schema({
  // step 1
  exemptEvidenceFiles: [fileSchema],
  copyResolutionFiles: [fileSchema],
  accountingRecordFiles: [fileSchema],
  annualReturnFiles: [fileSchema],
});

const reportDetailsSchema = new mongoose.Schema({
  isExemptCompany: { type: Boolean, required: false },
  exemptCompanyType: { type: String, required: false },
  exemptCompanyExplanation: { type: String, required: false },
  serviceType: { type: String, required: false },
  isFirstYearOperation: { type: Boolean, required: false },
  isThereFinancialYearChange: { type: Boolean, required: false },
  assistanceRequired: { type: String, required: false },
})

const cashTransactionSchema = new mongoose.Schema({
  description: { type: String, required: false },
  accountType: { type: String, required: false },
  openingAmount: { type: Number, required: false },
  closingAmount: { type: Number, required: false },
  transactionCurrency: { type: String, required: false },
  foreingExchangeRate: { type: Number, required: false },
  closingBalancePerBank: { type: Number, required: false },
  cashAtBank: { type: Number, required: false },
  invalid: { type: Boolean, required: false, default: false },
  income: {
    dividendReceived: { type: Number, required: false },
    couponInterestReceived: { type: Number, required: false },
    loanInterestReceived: { type: Number, required: false },
    bankInterestReceived: { type: Number, required: false },
    otherIncome: { type: Number, required: false },
  },
  expenses: {
    compAdminFees: { type: Number, required: false },
    portMngmntFees: { type: Number, required: false },
    loanInterest: { type: Number, required: false },
    bankFees: { type: Number, required: false },
    incomeTax: { type: Number, required: false },
    otherExpenses: { type: Number, required: false },
  },
  assets: {
    bankTransfers: { type: Number, required: false },
    investmentsAcquisition: { type: Number, required: false },
    investmentsSale: { type: Number, required: false },
    tangibleAcquisition: { type: Number, required: false },
    tangibleSale: { type: Number, required: false },
    intangibleAcquisition: { type: Number, required: false },
    intangibleSale: { type: Number, required: false },
    otherAcquisition: { type: Number, required: false },
    otherSale: { type: Number, required: false },
    loanReceivablePaid: { type: Number, required: false },
    loanReceivableReceived: { type: Number, required: false },
    receivablesPaid: { type: Number, required: false },
    receivablesReceived: { type: Number, required: false },
  },
  liabilities: {
    accountsPayableReceived: { type: Number, required: false },
    accountsPayablePaid: { type: Number, required: false },
    loansPayableReceived: { type: Number, required: false },
    loansPayablePaid: { type: Number, required: false },
  },
  equity: {
    paymentToShareholder: { type: Number, required: false },
    receiptsFromShareholder: { type: Number, required: false },
    capitalContribution: { type: Number, required: false },
  },
})

const assetsSchema = new mongoose.Schema({
  isFirstYearReporting: { type: Boolean, required: false },  
  loansAndReceivables: { type: Number, required: false },   
  otherFinancialAssets: { type: Number, required: false },   
  tangibleFixedAssets: { type: Number, required: false },   
  intangibleAssets: { type: Number, required: false },
  valueOfOtherAssetsStartPeriod: { type: Number, required: false }, 
  isLoansAndReceivablesEndPeriod: { type: Boolean, required: false },   
  amountsOfLoansReceivables: { type: Number, required: false },  
  interestReceivableOnTheLoan: { type: Number, required: false },   
  loansReceivedByShareholder: { type: Number, required: false },   
  paymentsOfLoansByShareholder: { type: Number, required: false },   
  invoicesIssued: { type: Number, required: false },  
  declaredDividedIncome: { type: Number, required: false },  
  balanceOfLoansReceivables: { type: Number, required: false },   
  anyInvestments: { type: Boolean, required: false },   
  totalAmountOfInvestments: { type: Number, required: false },  
  investmentsTransferredByShareholder: { type: Number, required: false },   
  investmentTransferredToTheShareholder: { type: Number, required: false },  
  isTangibleFixAssets: { type: Boolean, required: false },   
  deprecationExpenses: { type: Number, required: false },  
  isFixedAssetsCotributed: { type: Boolean, required: false },   
  tangibleAssetsEndPeriod: { type: Number, required: false },  
  tangibleAssetsContributed: { type: Number, required: false },  
  isIntangibleAssets: { type: Boolean, required: false },  
  amortisationExpenses: { type: Number, required: false },  
  isIntagibleAssetsContributed: { type: Boolean, required: false },   
  intangibleAssetsEndReportPeriod: { type: Number, required: false },   
  intangibleAssetsContributed: { type: Number, required: false },  
  intangibleAssetsEndFinancialPeriod: { type: Number, required: false },   
  isOtherAssets: { type: Boolean, required: false },   
  balanceOfInvestment: { type: Number, required: false },   
  valueTangibleAssetsEndPeriod: { type: Number, required: false },  
  balanceOfTangibleAssets: { type: Number, required: false },  
  balanceOfIntangibleAssets: { type: Number, required: false },  
  valueOfOtherAssetsEndPeriod: { type: Number, required: false },   
})



// step 11 and 12
const completeDetailsSchema = new mongoose.Schema({
  income: {
    total: { type: Number, required: false }, 
    costOfSales: { type: Number, required: false }
  },
  expenses: {
    operatingExpenses: { type: Number, required: false },
    totalOtherExpenses: { type: Number, required: false },
    incomeTax: { type: Number, required: false }, 
    totalOfExpenses: { type: Number, required: false },
  },
  assets: {
    cashAmount: { type: Number, required: false },
    loansAndReceivables: { type: Number, required: false },
    investmentsAssetsAmount: { type: Number, required: false },
    fixedAssetsAmount: { type: Number, required: false },
    intangibleAssetsAmount: { type: Number, required: false },
    total: { type: Number, required: false },
    totalOtherAssets: { type: Number, required: false },
  },
  liabilities: {
    accountsPayable: { type: Number, required: false },
    longTermDebts: { type: Number, required: false },
    total: { type: Number, required: false },
    totalOtherLiabilities: { type: Number, required: false },
  },
  shareholderEquity: { type: Number, required: false },
  grossProfit: { type: Number, required: false },
  netIncome: { type: Number, required: false },
})

const officerHistorySchema = new mongoose.Schema({
  username: { type: String, required: false },
  assignedBy: { type: String, required: false },
  assignedAt: { type: Date, required: false },
});

const officerDetailsSchema = new mongoose.Schema({
  currentOfficer: { type: String, required: false },
  details: [officerHistorySchema]
})

const requestInformationDetailsSchema = new mongoose.Schema({
  id: { type: String, required: true, default: uuidv4 },
  username: { type: String, required: false },
  requestedAt: { type: Date, required: false },
  deadlineAt: { type: Date, required: false },
  comment: { type: String, required: false },
  status: { type: String, required: false, default: "REQUESTED" },
  files: [fileSchema]
});

const responseInformationDetailsSchema = new mongoose.Schema({
  requestId: { type: String, required: true }, // request information id
  username: { type: String, required: false },
  returnedAt: { type: Date, required: false },
  comment: { type: String, required: false },
  isCanceled: { type: Boolean, required: false, default: false },
  files: [fileSchema]
});

const requestInformationSchema = new mongoose.Schema({
  details: [requestInformationDetailsSchema]
});

const clientResponseInformationSchema = new mongoose.Schema({
  details: [responseInformationDetailsSchema]
});


const calculatedValuesSchema = new mongoose.Schema({
  totalAssets: { type: Number, required: false },
  shareholderEquality:  { type: Number, required: false },
  dividedIncome:  { type: Number, required: false },
  netGainInvestment: { type: Number, required: false },
  otherIncome: { type: Number, required: false },
  totalIncome: { type: Number, required: false },
  investmentAsset: { type: Number, required: false },
  netLossInvestment: { type: Number, required: false },
  portfolioFees: { type: Number, required: false },
  bankFees: { type: Number, required: false },
  incomeTaxExpenses: { type: Number, required: false },
  assetsTangible: { type: Number, required: false },
  assetsintangible: { type: Number, required: false },
  assetsOtherAssets: { type: Number, required: false },
  otherExpenses: { type: Number, required: false },
  totalExpenses: { type: Number, required: false },
  netProfit: { type: Number, required: false },
  _revenue: { type: Number, required: false },
  _costOfSales: { type: Number, required: false },
  _profit: { type: Number, required: false },
  _operationExpenses: { type: Number, required: false },
  _totalExpenses: { type: Number, required: false },
  _netIncome: { type: Number, required: false },
  _otherExpenses: { type: Number, required: false }
});

const penaltyDetailsSchema = new mongoose.Schema({
  penaltyStartedAt: { type: Date, required: false },
  penaltyCompletedAt: { type: Date, required: false },
  penaltyAmount: { type: Number, required: false },
  penaltyDays: { type: Number, required: false }
})

const FinancialReportBSPLSchema = new mongoose.Schema(
  {
    masterClientCode: { type: String, required: true, max: 100 },
    companyData: CompanyModel,
    status: { type: String, required: false, default: "NOT STARTED" },
    previousStatus: { type: String, required: false, max: 100 },
    createdBy: { type: String, required: true, max: 100 },
    initialSubmitDate: { type: Date, required: false },
    submittedAt: { type: Date, required: false },
    submittedBy: { type: String, required: false },
    deletedAt: { type: Date, required: false },
    deletedBy: { type: String, required: false },
    lockedByPeriodLimit: { type: Boolean, required: false },
    isPaid: { type: Boolean, required: false },
    // Step 1 
    reportDetails: reportDetailsSchema,
    financialPeriod: {
      start: { type: Date, required: false },
      end: { type: Date, required: false },
    },
    originalFinancialPeriod: { // This is used to store the original financial period dates when isThereFinancialYearChange is set to true
      start: { type: Date, required: false },
      end: { type: Date, required: false },
    },
    //step 2
    currency: { type: String, required: false },
    cashTransactions: {
      companyOwnsCashOrEquivalents: { type: Boolean, required: false },
      bankAccounts: [cashTransactionSchema],
      totalBankAccounts: { type: Number, required: false },
    },
    //step 3
    assets: { type: assetsSchema, required: false },
    //step 4
    liabilities: { type: liabilitySchema, required: false },
    //step 5
    finReportAccurate: { type: Boolean, required: false },
    comments: { type: String, required: false },
    // complete details flow step 6-7
    completeDetails: { type: completeDetailsSchema, required: false },
    // step 8
    declaration: { type: declarationSchema, required: false },
    reopened: reopenedSchema,
    payment: { type: paymentSchema, required: false },
    officerDetails: officerDetailsSchema,
    requestedInformation: requestInformationSchema,
    clientResponseInformation: clientResponseInformationSchema,
    invoiceNumber: { type: String, required: false },
    invoiceExportDate: { type: Date, required: false },
    partitionkey: { type: String, required: false, default: "accountingrecords"},
    submissionExportDate: { type: Date, required: false },
    files: { type: fileListSchema, required: false },
    version: { type: String, required: false, default: "2.0" },
    calculatedValues: {type: calculatedValuesSchema, required: false},
    exemptionApprovalValues : exemptionApprovalSchema,
    penaltyDetails: penaltyDetailsSchema
  }, {
  timestamps: { createdAt: 'createdAt', updatedAt: 'updatedAt', submittedAt: 'submittedAt' }
}
);

FinancialReportBSPLSchema.methods.getStatusLabel = function () {
  if (this.status === REPORT_STATUS.CONFIRMED && !this.isReportPaid() && !this.reportDetails.isExemptCompany) { 
    return 'IN PROCESS'
  } else if (((this.status === REPORT_STATUS.CONFIRMED || this.status === REPORT_STATUS.HELP_COMPLETED) && this.isReportPaid() && !this.reportDetails.isExemptCompany) || this.exemptionApprovalValues) {
      return 'COMPLETED';
  } else {
    return this.status
  }
}

FinancialReportBSPLSchema.methods.isReportPaid = function () {
  return !!this.payment;
}


FinancialReportBSPLSchema.methods.getMappedSchema = function () {
  const mappedSchema = traverseSchema(FinancialReportSelfPrepareMapperSchema, '', this);
  return mappedSchema
}

//Export model
module.exports = mongoose.model('financialreportsbspls', FinancialReportBSPLSchema);
