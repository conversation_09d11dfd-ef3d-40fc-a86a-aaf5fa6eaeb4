<main class="px-4">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <h4 class="mt-3">Overview of all companies related to Master Client Code :
                        <B>{{masterClientCode}}</B>
                    </h4>
                    <br>
                    <form method='GET'>
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="filter_company">Company Name</label>
                                <input class='form-control' type='text' name='filter_company' id='filter_company' />
                            </div>
                            <div class="col-md-3">
                                <label for="filter_company_code">Incorporation Code</label>
                                <input class='form-control' type='text' name='filter_company_code'
                                    id='filter_company_code' />
                            </div>
                            <div class="col-md-3 input-div">
                                <input type='SUBMIT' class='btn btn-light waves-effect' value='Search' />
                            </div>
                        </div>
                    </form>
                    <div class="table-responsive ">
                        <table id="companies-table" class="table table-striped  w-100 nowrap">
                            <thead>
                                <tr>
                                    <th class="header-30-percent">Entity Name</th>
                                    <th class="header-20-percent">Incorporation Date</th>
                                    <th class="header-20-percent">Incorporation Number</th>
                                    <th class="header-20-percent">Fiscal Period</th>
                                    <th class="header-15-percent"></th>
                                    <th class="header-15-percent"></th>
                                    <th class="header-10-percent"></th>
                                    <th class="header-15-percent"></th>

                                </tr>
                            </thead>
                            <tbody>
                                {{#each companies}}
                                <tr>
                                    <td>{{name}}</td>
                                    <td>{{formatDate incorporationdate "YYYY/MM/DD"}} </td>                                                                       
                                    <td>{{incorporationCode}} </td>                                                                       
                                    <td>
                                        {{#if hasFinancialReport}}
                                            {{formatDate latestReport.financialPeriod.start "YYYY/MM/DD"}} - {{formatDate latestReport.financialPeriod.end "YYYY/MM/DD"}}
                                        {{else}}
                                            <strong>
                                                Missing <i class="fa fa-exclamation-circle text-danger fa-lg" data-toggle="tooltip" data-placement="top"
                                                    title="Please submit your fiscal period"></i>
                                            </strong>

                                        {{/if}}
                                    </td>
                                    <td>
                                        {{#if hasFinancialReport}}
                                        <a href="/masterclients/{{masterClientCode}}/financial-reports/companies/{{code}}"
                                            class="btn solid royal-blue width-xl" data-toggle="tooltip"
                                            data-placement="left"
                                            title="Click here for all reports submitted and currently in process">Retrieve
                                            reports
                                        </a>
                                        {{/if}}
                                    </td>
                                    <td class="text-center">

                                        {{#if lockedByPenalty}}
                                            <strong data-toggle="tooltip" data-placement="top"
                                                title="The report cannot be edited, nine months have passed since the fiscal year end date.">
                                                Locked <i class="fa fa fa-lock  fa-lg"></i>
                                            </strong>

                                        {{else}}
                                            {{#if inProgressReport}}                              
                                                {{#ifEquals inProgressReport.status 'RE-OPEN'}}
                                                    <button data-mcc="{{masterClientCode}}" data-code="{{code}}" data-id="{{inProgressReport._id}}"
                                                        data-reopened-id="{{inProgressReport.reopenedId}}" class="btn solid royal-blue width-xl showReopenContinueModal">
                                                        Continue
                                                    </button>
                                                {{else}}
                                                    <a href="/masterclients/{{masterClientCode}}/financial-reports/companies/{{code}}/{{inProgressReport._id}}"
                                                        class="btn solid royal-blue width-xl">Continue</a>
                                                {{/ifEquals}}
                                            {{else}}
                                                <button type="button" class="btn btn-primary solid royal-blue width-xl new-financial-report"
                                                    data-has-reports="{{hasFinancialReport}}" data-mcc="{{masterClientCode}}" data-company="{{code}}"
                                                    data-last-end-period="{{latestReport.financialPeriod.end}}" data-incorporation="{{formatDate incorporationdate "
                                                    YYYY-MM-DD"}}">
                                                    {{#if hasFinancialReport}} New report {{else}} Confirm Period {{/if}}
                                                </button>
                                            {{/if}}

                                        {{/if}}



                                    </td>
                                    <td>
                                        {{#if helpInProgress}}
                                            {{#ifCond helpInProgress.status '===' 'HELP IN PROGRESS'}}
                                                    <i class="fa fa-hand-paper-o font-size-logo color-green" aria-hidden="true"  data-toggle="tooltip" placement="top" 
                                                                title="Your help request is in progress."></i>
                                            {{else}}
                                                {{#ifCond helpInProgress.status '===' 'INFORMATION REQUEST'}}
                                                    <i class="fa fa-hand-paper-o font-size-logo color-orange cursor-pointer" data-status="information-request" aria-hidden="true" data-toggle="tooltip" placement="top" 
                                                                title="A TridentTrust officer has sent a Request for Information."></i>
                                                {{else}}
                                                    {{#ifCond helpInProgress.status '===' 'HELP COMPLETED'}}
                                                        <i class="fa fa-hand-paper-o font-size-logo colo-gray" aria-hidden="true"  data-toggle="tooltip" placement="top" 
                                                                title="The request for assistance is complete. You may download your completed PDF file at this time"></i>
                                                    {{else}} 
                                                        {{#ifCond helpInProgress.status '===' 'HELP REQUEST'}}
                                                            <i class="fa fa-hand-paper-o font-size-logo color-orange" aria-hidden="true" data-toggle="tooltip" placement="top" 
                                                                title="You have requested assistance from Trident, a Trident Trust employee will be in contact with you."></i>
                                                        {{/ifCond}}
                                                    {{/ifCond}}
                                                {{/ifCond}}           
                                            {{/ifCond}}              
                                        {{/if}}

                                    </td>
                                </tr>
                                {{else}}
                                <tr>
                                    <td colspan="8" class="text-center font-italic">
                                        There are no companies available
                                    </td>
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                    </div> <!-- end .padding -->
                    <br>
                    <div class="row">
                        <div class="col-md-12 d-flex justify-content-between">
                            <a href="/masterclients/{{masterClientCode}}"
                                class="btn btn-secondary waves-effect waves-light width-xl">Back</a>

                        </div>
                    </div>

                </div> <!-- end card-box-->
            </div> <!-- end col -->
        </div> <!-- end table-responsive-->
    </div> <!-- end card-box -->
    {{>financial-reports/modals/create-first-report}}
</main>

<script type="text/javascript" src="/views-js/financial-reports/dashboard.js"></script>