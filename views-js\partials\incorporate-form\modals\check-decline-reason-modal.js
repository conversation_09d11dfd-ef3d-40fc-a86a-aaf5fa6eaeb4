$('#showDeclineReasonModal').on('show.bs.modal', function (event) {
    let button = $(event.relatedTarget); // Button that triggered the modal
    let incorpId = button.data('incorporation-id');
    let mcc = button.data('mcc');
    let name = button.data('name');
    $("#inc-name").text(name);
    $.ajax({
        type: "GET",
        url: "/masterclients/"+mcc+"/incorporate-company/"+ incorpId + "/declined-reason-info",
        success: (response) => {
            if (response.status === 200) {
                console.log(response.data);

                if (response.data.reason){
                    $("#declined-reason").val(response.data.reason);
                }
                if (response.data.declinedAt){
                    $("#declined-date").val(response.data.declinedAt);
                }

            } else {
                Swal.fire('Error', 'There was an error getting the information.', 'error').then(() => {
                    $('#showDeclineReasonModal').modal('hide');
                });
            }
        },
        error: (err) => {
            Swal.fire('Error', 'There was an error getting the information.', 'error').then(() => {
                $('#showDeclineReasonModal').modal('hide');
            });
        },
    });
});

$('#showDeclineReasonModal').on('hide.bs.modal', function (event) {
    $("#showDeclinedInfoForm").trigger("reset");
    $("#inc-name").text('');
});
