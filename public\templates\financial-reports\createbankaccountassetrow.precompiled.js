(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['createbankaccountassetrow'] = template({"1":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"assets") : depth0),{"name":"if","hash":{},"fn":container.program(2, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":2,"column":4},"end":{"line":24,"column":11}}})) != null ? stack1 : "");
},"2":function(container,depth0,helpers,partials,data) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, alias5=container.lambda, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <div id=\"bankAccount-"
    + alias4(((helper = (helper = lookupProperty(helpers,"index") || (data && lookupProperty(data,"index"))) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"index","hash":{},"data":data,"loc":{"start":{"line":3,"column":25},"end":{"line":3,"column":35}}}) : helper)))
    + "\">\r\n        <input value=\""
    + alias4(alias5(((stack1 = (depth0 != null ? lookupProperty(depth0,"assets") : depth0)) != null ? lookupProperty(stack1,"loanReceivablePaid") : stack1), depth0))
    + "\" id=\"loanReceivablePaid_banckAcc-"
    + alias4(((helper = (helper = lookupProperty(helpers,"index") || (data && lookupProperty(data,"index"))) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"index","hash":{},"data":data,"loc":{"start":{"line":4,"column":90},"end":{"line":4,"column":100}}}) : helper)))
    + "\" class=\"hidden\">\r\n        <input value=\""
    + alias4(alias5(((stack1 = (depth0 != null ? lookupProperty(depth0,"assets") : depth0)) != null ? lookupProperty(stack1,"loanReceivableReceived") : stack1), depth0))
    + "\" id=\"loanReceivableReceived_banckAcc-"
    + alias4(((helper = (helper = lookupProperty(helpers,"index") || (data && lookupProperty(data,"index"))) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"index","hash":{},"data":data,"loc":{"start":{"line":5,"column":98},"end":{"line":5,"column":108}}}) : helper)))
    + "\"\r\n            class=\"hidden\">\r\n        <input value=\""
    + alias4(alias5(((stack1 = (depth0 != null ? lookupProperty(depth0,"assets") : depth0)) != null ? lookupProperty(stack1,"receivablesPaid") : stack1), depth0))
    + "\" id=\"receivablesPaid_banckAcc-"
    + alias4(((helper = (helper = lookupProperty(helpers,"index") || (data && lookupProperty(data,"index"))) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"index","hash":{},"data":data,"loc":{"start":{"line":7,"column":84},"end":{"line":7,"column":94}}}) : helper)))
    + "\" class=\"hidden\">\r\n        <input value=\""
    + alias4(alias5(((stack1 = (depth0 != null ? lookupProperty(depth0,"assets") : depth0)) != null ? lookupProperty(stack1,"receivablesReceived") : stack1), depth0))
    + "\" id=\"receivablesReceived_banckAcc-"
    + alias4(((helper = (helper = lookupProperty(helpers,"index") || (data && lookupProperty(data,"index"))) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"index","hash":{},"data":data,"loc":{"start":{"line":8,"column":92},"end":{"line":8,"column":102}}}) : helper)))
    + "\" class=\"hidden\">\r\n        <input value=\""
    + alias4(alias5(((stack1 = (depth0 != null ? lookupProperty(depth0,"assets") : depth0)) != null ? lookupProperty(stack1,"investmentsAcquisition") : stack1), depth0))
    + "\" id=\"investmentsAcquisition_banckAcc-"
    + alias4(((helper = (helper = lookupProperty(helpers,"index") || (data && lookupProperty(data,"index"))) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"index","hash":{},"data":data,"loc":{"start":{"line":9,"column":98},"end":{"line":9,"column":108}}}) : helper)))
    + "\"\r\n            class=\"hidden\">\r\n        <input value=\""
    + alias4(alias5(((stack1 = (depth0 != null ? lookupProperty(depth0,"assets") : depth0)) != null ? lookupProperty(stack1,"investmentsSale") : stack1), depth0))
    + "\" id=\"investmentsSale_banckAcc-"
    + alias4(((helper = (helper = lookupProperty(helpers,"index") || (data && lookupProperty(data,"index"))) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"index","hash":{},"data":data,"loc":{"start":{"line":11,"column":84},"end":{"line":11,"column":94}}}) : helper)))
    + "\" class=\"hidden\">\r\n        <input value=\""
    + alias4(alias5(((stack1 = (depth0 != null ? lookupProperty(depth0,"assets") : depth0)) != null ? lookupProperty(stack1,"tangibleAcquisition") : stack1), depth0))
    + "\" id=\"tangibleAcquisition_banckAcc-"
    + alias4(((helper = (helper = lookupProperty(helpers,"index") || (data && lookupProperty(data,"index"))) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"index","hash":{},"data":data,"loc":{"start":{"line":12,"column":92},"end":{"line":12,"column":102}}}) : helper)))
    + "\" class=\"hidden\">\r\n        <input value=\""
    + alias4(alias5(((stack1 = (depth0 != null ? lookupProperty(depth0,"assets") : depth0)) != null ? lookupProperty(stack1,"tangibleSale") : stack1), depth0))
    + "\" id=\"tangibleSale_banckAcc-"
    + alias4(((helper = (helper = lookupProperty(helpers,"index") || (data && lookupProperty(data,"index"))) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"index","hash":{},"data":data,"loc":{"start":{"line":13,"column":78},"end":{"line":13,"column":88}}}) : helper)))
    + "\" class=\"hidden\">\r\n        <input value=\""
    + alias4(alias5(((stack1 = (depth0 != null ? lookupProperty(depth0,"assets") : depth0)) != null ? lookupProperty(stack1,"intangibleAcquisition") : stack1), depth0))
    + "\" id=\"intangibleAcquisition_banckAcc-"
    + alias4(((helper = (helper = lookupProperty(helpers,"index") || (data && lookupProperty(data,"index"))) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"index","hash":{},"data":data,"loc":{"start":{"line":14,"column":96},"end":{"line":14,"column":106}}}) : helper)))
    + "\" class=\"hidden\">\r\n        <input value=\""
    + alias4(alias5(((stack1 = (depth0 != null ? lookupProperty(depth0,"assets") : depth0)) != null ? lookupProperty(stack1,"intangibleSale") : stack1), depth0))
    + "\" id=\"intangibleSale_banckAcc-"
    + alias4(((helper = (helper = lookupProperty(helpers,"index") || (data && lookupProperty(data,"index"))) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"index","hash":{},"data":data,"loc":{"start":{"line":15,"column":82},"end":{"line":15,"column":92}}}) : helper)))
    + "\" class=\"hidden\">\r\n        <input value=\""
    + alias4(alias5(((stack1 = (depth0 != null ? lookupProperty(depth0,"assets") : depth0)) != null ? lookupProperty(stack1,"otherAcquisition") : stack1), depth0))
    + "\" id=\"otherAcquisition_banckAcc-"
    + alias4(((helper = (helper = lookupProperty(helpers,"index") || (data && lookupProperty(data,"index"))) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"index","hash":{},"data":data,"loc":{"start":{"line":16,"column":86},"end":{"line":16,"column":96}}}) : helper)))
    + "\" class=\"hidden\">\r\n        <input value=\""
    + alias4(alias5(((stack1 = (depth0 != null ? lookupProperty(depth0,"assets") : depth0)) != null ? lookupProperty(stack1,"otherSale") : stack1), depth0))
    + "\" id=\"otherSale_banckAcc-"
    + alias4(((helper = (helper = lookupProperty(helpers,"index") || (data && lookupProperty(data,"index"))) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"index","hash":{},"data":data,"loc":{"start":{"line":17,"column":72},"end":{"line":17,"column":82}}}) : helper)))
    + "\" class=\"hidden\">\r\n        <input value=\""
    + alias4(alias5((depth0 != null ? lookupProperty(depth0,"foreingExchangeRate") : depth0), depth0))
    + "\" id=\"exchangeRate_banckAcc-"
    + alias4(((helper = (helper = lookupProperty(helpers,"index") || (data && lookupProperty(data,"index"))) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"index","hash":{},"data":data,"loc":{"start":{"line":18,"column":78},"end":{"line":18,"column":88}}}) : helper)))
    + "\" class=\"hidden\">\r\n"
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,(depth0 != null ? lookupProperty(depth0,"liabilities") : depth0),{"name":"if","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":19,"column":8},"end":{"line":22,"column":15}}})) != null ? stack1 : "")
    + "    </div>\r\n";
},"3":function(container,depth0,helpers,partials,data) {
    var stack1, helper, alias1=container.lambda, alias2=container.escapeExpression, alias3=depth0 != null ? depth0 : (container.nullContext || {}), alias4=container.hooks.helperMissing, alias5="function", lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "        <input value=\""
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"liabilities") : depth0)) != null ? lookupProperty(stack1,"loansPayableReceived") : stack1), depth0))
    + "\" id=\"loansPayableReceived_banckAcc-"
    + alias2(((helper = (helper = lookupProperty(helpers,"index") || (data && lookupProperty(data,"index"))) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"index","hash":{},"data":data,"loc":{"start":{"line":20,"column":99},"end":{"line":20,"column":109}}}) : helper)))
    + "\" class=\"hidden\">\r\n        <input value=\""
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"liabilities") : depth0)) != null ? lookupProperty(stack1,"loansPayablePaid") : stack1), depth0))
    + "\" id=\"loansPayablePaid_banckAcc-"
    + alias2(((helper = (helper = lookupProperty(helpers,"index") || (data && lookupProperty(data,"index"))) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"index","hash":{},"data":data,"loc":{"start":{"line":21,"column":91},"end":{"line":21,"column":101}}}) : helper)))
    + "\" class=\"hidden\">\r\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"bankAccounts") : depth0),{"name":"each","hash":{},"fn":container.program(1, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":1,"column":0},"end":{"line":25,"column":9}}})) != null ? stack1 : "");
},"useData":true});
})();