const sqlDb = require('../../models-sql');
const mem_DirectorsModel = sqlDb.mem_Directors;
const mem_DirectorsHistoryModel = sqlDb.mem_DirectorsHistory;
const mem_MemberProfilesModel = sqlDb.mem_MemberProfiles;
const mem_MemberProfilesHistoryModel = sqlDb.mem_MemberProfilesHistory;
const mem_EntitiesModel = sqlDb.mem_Entities;
const VpDirectorInfoHistoryModel = sqlDb.VpDirectorInfoHistory;
const moment = require('moment');
const {
  TYPE_OF_DIRECTOR,
  DIRMEMBER_STATUS,
} = require('../../utils/directorAndMemberConstants');
const { getCompanyData, getListFieldsToValidate } = require('../../utils/companyInformationUtils');
const MailController = require('../../controllers/mailController');
const MailFormatter = require('../../controllers/mailFormatController');


exports.getDirectorEntries = async function (req, res, next) {
  try {
    const company = await getCompanyData(req.params.code, req.params.masterclientcode, req.user.email);

    if (company?.error) {
      const err = new Error(company.error);
      err.status = company.status;
      return next(err);
    }

    let directorsWithMissingValues = [];
    let entityName = company.name;
    let directors = [];

    // Get directors from mem_Directors table
    let memDirectors = await mem_DirectorsModel.findAll({
      where: {
        EntityLegacyID: company.code,
      },
      include: [{
        model: mem_MemberProfilesModel,
        as: 'directorProfile',
        required: false
      }
      ],
      raw: false
    });

    if (memDirectors && memDirectors.length > 0) {
      entityName = memDirectors[0].EntityName;
    }

    // Process records from mem_Directors
    if (memDirectors && memDirectors.length > 0) {
      // Filter out directors with populated DirToDate (Cessation Date)
      directors = memDirectors.filter(director => !director.DirToDate);
    }

    if (directors.length > 0) {
      // Get history from mem_DirectorsHistory table
      const directorIds = directors.map(d => d.UniqueRelationID);

      const historyLogs = await mem_DirectorsHistoryModel.findAll({
        where: {
          EntityLegacyID: company.code,
          UniqueRelationID: directorIds,
        },
        include: [{
          model: mem_MemberProfilesHistoryModel,
          as: 'directorProfileHistory',
          required: false
        }
        ],
        raw: true
      });

      // Get the history logs for these directors from old VpDirectorInfoHistory table
      let oldHistoryLogs = [];
      try {
        oldHistoryLogs = await VpDirectorInfoHistoryModel.findAll({
          where: {
            CompanyNumber: company.code,
            RelationType: "Director",
            UniqueRelationId: directors.map(d => d.PreviousUniqueRelationId).filter(id => id)
          },
          raw: true
        });
      } catch (error) {
        console.error("Error fetching old history logs:", error);
        oldHistoryLogs = [];
      }

      if (historyLogs && historyLogs.length > 0) {
        historyLogs.sort((a, b) => b.Id - a.Id);
      }

      if (oldHistoryLogs && oldHistoryLogs.length > 0) {
        oldHistoryLogs.sort((a, b) => b.Id - a.Id);
      }

      directors = directors.map((director) => {
        director.canUpdate = true;
        director.canConfirm = true;
        director.showHistory = false;
        director.showInfoQuestion = true;

        // Check if this director was confirmed in the old VP history logs
        // AND there are no records in the new history database
        const hasNewHistoryRecords = historyLogs.some(
          log => log.UniqueRelationID === director.UniqueRelationID
        );

        director.isConfirmedInHistory = !hasNewHistoryRecords && oldHistoryLogs && oldHistoryLogs.some(log =>
          log.UniqueRelationId === director.PreviousUniqueRelationId &&
          log.Status === DIRMEMBER_STATUS.CONFIRMED
        );

        // Check mem_DirectorsHistory logs
        const entryLogs = historyLogs.filter(
          (r) => r.UniqueRelationID === director.UniqueRelationID && r.RelationType === director.RelationType
        );

        let latestHistory = null;

        if (entryLogs.length > 0) {
          entryLogs.sort((a, b) => b.Id - a.Id);
          const lastLogCreated = entryLogs[0];
          latestHistory = lastLogCreated;

          if (lastLogCreated.Status !== DIRMEMBER_STATUS.RECEIVED &&
            lastLogCreated.Status !== DIRMEMBER_STATUS.INITIAL &&
            lastLogCreated.Status !== DIRMEMBER_STATUS.REFRESHED) {
            director.showInfoQuestion = false;
            director.canConfirm = false;
          } else if (lastLogCreated.Status == DIRMEMBER_STATUS.RECEIVED ||
            lastLogCreated.Status == DIRMEMBER_STATUS.REFRESHED) {
            const confirmedOrUpdateRequestLogs = entryLogs.filter(
              (r) => r.Status == DIRMEMBER_STATUS.CONFIRMED ||
                r.Status == DIRMEMBER_STATUS.PENDING ||
                r.Status == DIRMEMBER_STATUS.INITIAL
            );
            if (confirmedOrUpdateRequestLogs && confirmedOrUpdateRequestLogs.length > 0) {
              confirmedOrUpdateRequestLogs.sort((a, b) => b.Id - a.Id);
              const lastConfirmedOrUpdateRequestLog = confirmedOrUpdateRequestLogs[0];
              director.oldData = lastConfirmedOrUpdateRequestLog;
            }
          }

          director.showHistory = lastLogCreated.Status === DIRMEMBER_STATUS.PENDING;
          director.canUpdate = lastLogCreated.Status !== DIRMEMBER_STATUS.PENDING;

          if (lastLogCreated.Status === DIRMEMBER_STATUS.PENDING && lastLogCreated.UpdateRequestDate === null) {
            const userRequestUpdateLogs = entryLogs.find(r => r.UpdateRequestDate !== null);

            director.lastChange = {
              changeType: userRequestUpdateLogs ? userRequestUpdateLogs.TypeOfUpdateRequest : "",
              changeReason: userRequestUpdateLogs ? userRequestUpdateLogs.UpdateRequestComments : "",
              status: lastLogCreated.Status
            };
          } else {
            director.lastChange = {
              changeType: lastLogCreated.TypeOfUpdateRequest,
              changeReason: lastLogCreated.UpdateRequestComments,
              status: lastLogCreated.Status
            };
          }
        }

        if (director.canUpdate) {
          const listOfRequiredFields = getListFieldsToValidate(TYPE_OF_DIRECTOR, director.DirFileType?.toLowerCase() === 'individual' ? 'Individual' : 'Corporate');

          // Map profile fields to Director object for validation
          if (director.directorProfile) {
            // Common fields
            director.ServiceAddress = director.directorProfile.MFRSAddress;
            director.ResidentialOrRegisteredAddress = director.directorProfile.MFRAAddress;

            // For individual directors
            if (director.DirFileType?.toLowerCase() === "individual") {
              director.DateOfBirthOrIncorp = director.directorProfile.MFDateOfBirth;
              director.PlaceOfBirthOrIncorp = director.directorProfile.MFBirthCountry;
              director.Nationality = director.directorProfile.MFNationality;
            }
            // For corporate directors - use the actual field names from the profile
            else {
              director.MFIncropNr = director.directorProfile.MFIncropNr;
              director.MFROAddress = director.directorProfile.MFROAddress;
              director.MFIncorpDate = director.directorProfile.MFIncorpDate;
              director.MFIncorpCountry = director.directorProfile.MFIncorpCountry;
            }
          }

          const missingValues = listOfRequiredFields.filter((item) => {
            const fieldValue = director[item.field];
            const isMissing = fieldValue === null || fieldValue === undefined || fieldValue === "";
            return isMissing;
          }).map((item) => item.name);

          if (missingValues.length > 0) {
            director.hasMissingValues = true;
            director.showInfoQuestion = false;
            director.canConfirm = false;
            directorsWithMissingValues.push({
              id: director.UniqueRelationID,
              name: director.DirName,
              missingValues: missingValues.join(', ')
            });
          }
        }

        // Compare fields between current director and latest history record to check for new data from VP
        let hasVPDataReceived = false;
        if (latestHistory && director) {
          // Common fields from mem_Directors to compare
          const directorFieldsToCompare = [
            'DirName', 'DirCode', 'RelationType', 'DirFileType',
            'DirStatus', 'DirFromDate', 'DirToDate',
            'LicenseeEntityName', 'DirCapacity'
          ];

          // Compare main director fields
          const directorsChanged = directorFieldsToCompare.some(field => {
            const currentValue = director.dataValues?.[field];
            const historyValue = latestHistory[field];

            // Skip if both values are empty/null
            if ((currentValue === null || currentValue === undefined || currentValue === '') &&
              (historyValue === null || historyValue === undefined || historyValue === '')) {
              return false;
            }

            // If one value exists and the other doesn't, consider it changed
            if ((currentValue && !historyValue) || (!currentValue && historyValue)) {
              return true;
            }

            // Special handling for dates
            if (field === 'DirFromDate' || field === 'DirToDate') {
              if (!currentValue || !historyValue) return false;

              // Format dates as YYYY-MM-DD for comparison
              const currentDate = new Date(currentValue).toISOString().split('T')[0];
              const historyDate = new Date(historyValue).toISOString().split('T')[0];
              return currentDate !== historyDate;
            }

            // Regular string comparison (case insensitive)
            if (typeof currentValue === 'string' && typeof historyValue === 'string') {
              return currentValue.toLowerCase() !== historyValue.toLowerCase();
            }

            return currentValue !== historyValue;
          });

          // Check profile fields if profile exists
          let profileChanged = false;

          if (director.directorProfile?.dataValues) {
            // Get profile fields to compare based on director type
            const profileFields = director.DirFileType?.toLowerCase() === 'individual'
              ? ['MFFormerName', 'MFDateOfBirth', 'MFBirthCountry', 'MFNationality', 'MFRAAddress', 'MFRSAddress']
              : ['MFIncropNr', 'MFIncorpCountry', 'MFIncorpDate', 'MFROAddress'];

            profileChanged = profileFields.some(field => {
              const currentProfileValue = director.directorProfile.dataValues[field];

              // Handle dates
              if (field === 'MFDateOfBirth' || field === 'MFIncorpDate') {
                if (!currentProfileValue || !latestHistory[`directorProfileHistory.${field}`]) return false;

                // Format dates as YYYY-MM-DD for comparison
                const currentDate = new Date(currentProfileValue).toISOString().split('T')[0];
                const historyDate = new Date(latestHistory[`directorProfileHistory.${field}`]).toISOString().split('T')[0];
                return currentDate !== historyDate;
              }

              // If profile field exists in current data but not in history
              // or has changed from what was previously there
              return currentProfileValue &&
                (currentProfileValue !== latestHistory[`directorProfileHistory.${field}`]);
            });
          }

          hasVPDataReceived = directorsChanged || profileChanged;
        }

        director.confirmedAndUpdated = hasVPDataReceived && entryLogs.some(log => log.Status === DIRMEMBER_STATUS.CONFIRMED);
        return director;
      });
    }

    let individualEntries = directors.filter((e) => e.DirFileType?.toLowerCase() === "individual");
    let corporateEntries = directors.filter((e) => e.DirFileType?.toLowerCase() !== "individual");

    return res.render('director-and-members/directors-forms', {
      masterClientCode: req.params.masterclientcode,
      company: company,
      entityName,
      type: "directors",
      individualEntries: individualEntries,
      corporateEntries: corporateEntries,
      hasCorporateEntries: corporateEntries.length > 0,
      hasDirectors: individualEntries.length > 0 || corporateEntries.length > 0,
      user: req.user,
      messages: req.session.messages,
      directorsWithMissingValues
    });
  } catch (e) {
    console.error("Error getting director entries: ", e);
    const err = new Error('Internal server error');
    err.status = 500;
    return next(err);
  }
};

exports.getDirectorDetails = async function (req, res, next) {
  try {
    const company = await getCompanyData(req.params.code, req.params.masterclientcode, req.user.email);

    if (company?.error) {
      const err = new Error(company.error);
      err.status = company.status;
      return next(err);
    }

    const type = TYPE_OF_DIRECTOR;

    // Get the director by ID from mem_Directors table with associated profile
    let director = await mem_DirectorsModel.findOne({
      where: {
        EntityLegacyID: company.code,
        UniqueRelationID: req.params.id,
      },
      include: [{
        model: mem_MemberProfilesModel,
        as: 'directorProfile',
        required: false
      }],
      raw: false
    });

    if (!director) {
      const err = new Error('Director not found');
      err.status = 404;
      return next(err);
    }

    // Get director history records
    let directorHistoryRecords = await mem_DirectorsHistoryModel.findAll({
      where: {
        EntityLegacyID: company.code,
        UniqueRelationID: director.UniqueRelationID
      },
      include: [{
        model: mem_MemberProfilesHistoryModel,
        as: 'directorProfileHistory',
        required: false
      }],
      raw: true,
      order: [['Id', 'DESC']],
    });

    // Check if director has confirmed
    const isConfirmedNew = directorHistoryRecords.some(record => record.Status === DIRMEMBER_STATUS.CONFIRMED);

    // Get the latest history record if any exist
    const latestHistory = directorHistoryRecords.length > 0 ? directorHistoryRecords[0] : null;

    // Compare fields between current director and latest history record to check for new data from VP
    let hasVPDataReceived = false;
    if (latestHistory && director) {
      // Common fields from mem_Directors to compare
      const directorFieldsToCompare = [
        'DirName', 'DirCode', 'RelationType', 'DirFileType',
        'DirStatus', 'DirFromDate', 'DirToDate',
        'LicenseeEntityName', 'DirCapacity'
      ];

      // Compare main director fields
      const directorsChanged = directorFieldsToCompare.some(field => {
        const currentValue = director.dataValues?.[field];
        const historyValue = latestHistory[field];

        // Skip if both values are empty/null
        if ((currentValue === null || currentValue === undefined || currentValue === '') &&
          (historyValue === null || historyValue === undefined || historyValue === '')) {
          return false;
        }

        // If one value exists and the other doesn't, consider it changed
        if ((currentValue && !historyValue) || (!currentValue && historyValue)) {
          return true;
        }

        // Special handling for dates
        if (field === 'DirFromDate' || field === 'DirToDate') {
          if (!currentValue || !historyValue) return false;

          // Format dates as YYYY-MM-DD for comparison
          const currentDate = new Date(currentValue).toISOString().split('T')[0];
          const historyDate = new Date(historyValue).toISOString().split('T')[0];
          return currentDate !== historyDate;
        }

        // Regular string comparison (case insensitive)
        if (typeof currentValue === 'string' && typeof historyValue === 'string') {
          return currentValue.toLowerCase() !== historyValue.toLowerCase();
        }

        return currentValue !== historyValue;
      });

      // Check profile fields if profile exists
      let profileChanged = false;

      if (director.directorProfile?.dataValues) {
        // Get profile fields to compare based on director type
        const profileFields = director.DirFileType?.toLowerCase() === 'individual'
          ? ['MFFormerName', 'MFDateOfBirth', 'MFBirthCountry', 'MFNationality', 'MFRAAddress', 'MFRSAddress']
          : ['MFIncropNr', 'MFIncorpCountry', 'MFIncorpDate', 'MFROAddress'];

        profileChanged = profileFields.some(field => {
          const currentProfileValue = director.directorProfile.dataValues[field];

          // Handle dates
          if (field === 'MFDateOfBirth' || field === 'MFIncorpDate') {
            if (!currentProfileValue || !latestHistory[`directorProfileHistory.${field}`]) return false;

            // Format dates as YYYY-MM-DD for comparison
            const currentDate = new Date(currentProfileValue).toISOString().split('T')[0];
            const historyDate = new Date(latestHistory[`directorProfileHistory.${field}`]).toISOString().split('T')[0];
            return currentDate !== historyDate;
          }

          // If profile field exists in current data but not in history
          // or has changed from what was previously there
          return currentProfileValue &&
            (currentProfileValue !== latestHistory[`directorProfileHistory.${field}`]);
        });
      }

      hasVPDataReceived = directorsChanged || profileChanged;
    }

    // Get the history logs for this director from old VpDirectorInfoHistory table
    let historyLogs = [];
    try {
      historyLogs = await VpDirectorInfoHistoryModel.findAll({
        where: {
          CompanyNumber: company.code,
          RelationType: type,
          UniqueRelationId: director.PreviousUniqueRelationId
        },
        raw: true
      });
    } catch (error) {
      historyLogs = [];
    }

    if (historyLogs && historyLogs.length > 0) {
      historyLogs.sort((a, b) => b.Id - a.Id);
    }

    // Check if confirmed in old VP history logs - this drives the blue highlighting
    // Only apply if there are no new history records
    const hasNewHistoryRecords = directorHistoryRecords?.length > 0
    const isConfirmedInHistory = !hasNewHistoryRecords && historyLogs && historyLogs.length > 0 &&
      historyLogs.some(log => log.Status === DIRMEMBER_STATUS.CONFIRMED);

    // Create a properly formatted directorForView with all necessary fields
    let directorForView;
    let isConfirmed = latestHistory?.Status === DIRMEMBER_STATUS.CONFIRMED;

    // Check the actual value of isLicensed in the database
    const isLicensed = latestHistory?.isLicensed;


    if (director) {
      // Base fields for both types
      directorForView = {
        // Core fields required for functionality
        UniqueRelationID: director.UniqueRelationID,

        // Fields displayed in both views
        Name: director.DirName,
        Code: director.DirCode,
        OfficerType: director.DirOfficerType,
        DirectorCapacity: director.DirCapacity || '',
        LicenseeEntity: director.LicenseeEntityName || '',
        LicenseeEntityName: director.LicenseeEntityName || '',
        isLicensed: isLicensed,
      };

      // FileType determines if individual or corporate
      if (director.DirFileType?.toLowerCase() === 'individual') {
        // Fields specific to Individual directors
        directorForView.FileType = director.DirFileType;
        directorForView.FromDate = director.DirFromDate;
        directorForView.ToDate = director.DirToDate;
        directorForView.FormerName = director.directorProfile?.MFFormerName || '';
        directorForView.ServiceAddress = director.directorProfile?.MFRSAddress || '';
        directorForView.ResidentialOrRegisteredAddress = director.directorProfile?.MFRAAddress || '';
        directorForView.DateOfBirthOrIncorp = director.directorProfile?.MFDateOfBirth || '';
        directorForView.PlaceOfBirthOrIncorp = director.directorProfile?.MFBirthCountry || '';
        directorForView.Nationality = director.directorProfile?.MFNationality || '';
      } else {
        // Fields specific to Corporate directors
        directorForView.FileType = director.DirFileType;
        directorForView.FromDate = director.DirFromDate;
        directorForView.ToDate = director.DirToDate;
        directorForView.BoDirIncorporationNumber = director.directorProfile?.MFIncropNr || '';
        directorForView.PlaceOfBirthOrIncorp = director.directorProfile?.MFIncorpCountry || '';
        directorForView.DateOfBirthOrIncorp = director.directorProfile?.MFIncorpDate || '';
        directorForView.ResidentialOrRegisteredAddress = director.directorProfile?.MFROAddress || '';
      }
    }

    const listOfRequiredFields = getListFieldsToValidate(TYPE_OF_DIRECTOR, director.DirFileType?.toLowerCase() === 'individual' ? 'Individual' : 'Corporate');

    if (director.directorProfile) {
      // Common fields
      director.ServiceAddress = director.directorProfile.MFRSAddress;
      director.ResidentialOrRegisteredAddress = director.directorProfile.MFRAAddress;

      // For individual directors
      if (director.DirFileType?.toLowerCase() === "individual") {
        director.DateOfBirthOrIncorp = director.directorProfile.MFDateOfBirth;
        director.PlaceOfBirthOrIncorp = director.directorProfile.MFBirthCountry;
        director.Nationality = director.directorProfile.MFNationality;
      }
      // For corporate directors - use the actual field names from the profile
      else {
        director.MFIncropNr = director.directorProfile.MFIncropNr;
        director.MFROAddress = director.directorProfile.MFROAddress;
        director.MFIncorpDate = director.directorProfile.MFIncorpDate;
        director.MFIncorpCountry = director.directorProfile.MFIncorpCountry;
      }
    }

    const missingValues = listOfRequiredFields.filter((item) => {
      const fieldValue = director[item.field];
      const isMissing = fieldValue === null || fieldValue === undefined || fieldValue === "";
      return isMissing;
    }).map((item) => item.field);

    return res.render('director-and-members/director-details-forms', {
      masterClientCode: req.params.masterclientcode,
      company: company,
      director: directorForView,
      isIndividual: directorForView.FileType?.toLowerCase() === 'individual',
      isCorporate: directorForView.FileType?.toLowerCase() !== 'individual',
      hasDirectorCapacity: !!directorForView.DirectorCapacity,
      hasLicenseeEntity: !!directorForView.LicenseeEntityName || !!directorForView.LicenseeEntity,
      showConfirmButton: !isConfirmed && missingValues.length === 0,
      missingValues,
      isConfirmedInHistory: isConfirmedInHistory, // This is what controls the blue highlighting
      isConfirmedNew,
      user: req.user,
      messages: req.session.messages,
      isDirectorLicensed: isLicensed,
      hasVPDataReceived: hasVPDataReceived
    });
  } catch (e) {
    console.error("Error getting director details: ", e);
    const err = new Error('Internal server error');
    err.status = 500;
    return next(err);
  }
};

exports.createDirectorConfirmationLog = async function (req, res) {
  try {
    const company = await getCompanyData(req.params.code, req.params.masterclientcode, req.user.email);
    const data = req.body;

    if (company?.error) {
      return res.status(company.status).json({
        "status": company.status,
        "error": company.error
      });
    }

    // Only allow directors and members as type
    if (req.originalUrl.includes('/director-and-members/')) {
      if (!req.originalUrl.includes('/directors/') && !req.originalUrl.includes('/members/')) {
        return res.status(404).json({
          "status": 404,
          "error": 'Director information not found'
        });
      }
    }
    // Get director info from mem_Directors table
    let director = await mem_DirectorsModel.findOne({
      where: {
        EntityLegacyID: company.code,
        UniqueRelationID: req.params.id,
      },
      include: [{
        model: mem_MemberProfilesModel,
        as: 'directorProfile',
        required: false
      }],
      raw: false
    });

    if (!director) {
      return res.status(404).json({
        "status": 404,
        "error": 'Director information not found'
      });
    }

    // Set licensee entity code based on user input (radio button)
    const isDirectorLicensed = data.isDirectorLicensed === 'yes';

    // Create log in mem_DirectorsHistory table
    let confirmDirectorDataLog;
    try {
      // Create a deep copy of director data to avoid reference issues
      const directorJSON = director.toJSON();

      confirmDirectorDataLog = await mem_DirectorsHistoryModel.create({
        ...directorJSON,
        ConfirmedDate: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
        Status: DIRMEMBER_STATUS.CONFIRMED,
        UserEmail: req.user.username,
        TypeOfUpdateRequest: null,
        UpdateRequestComments: null,
        isLicensed: isDirectorLicensed
      });
      confirmDirectorDataLog = confirmDirectorDataLog.toJSON();

      // Create corresponding record in mem_MemberProfilesHistory if a profile exists
      if (director.directorProfile && confirmDirectorDataLog.Id) {
        try {
          const profileData = {
            DirectorHistoryId: confirmDirectorDataLog.Id,
            MFCode: director.directorProfile.MFCode,
            MFUniqueNr: director.directorProfile.MFUniqueNr,
            MFName: director.directorProfile.MFName,
            MFTypeCode: director.directorProfile.MFTypeCode,
            MFLegacyID: director.directorProfile.MFLegacyID,
            MFType: director.directorProfile.MFType,
            MFIncropNr: director.directorProfile.MFIncropNr,
            MFIncorpDate: director.directorProfile.MFIncorpDate,
            MFIncorpCountry: director.directorProfile.MFIncorpCountry,
            MFDateOfBirth: director.directorProfile.MFDateOfBirth,
            MFFormerName: director.directorProfile.MFFormerName,
            MFBirthCountry: director.directorProfile.MFBirthCountry,
            MFNationality: director.directorProfile.MFNationality,
            MFRAAddress: director.directorProfile.MFRAAddress,
            MFROAddress: director.directorProfile.MFROAddress,
            MFRSAddress: director.directorProfile.MFRSAddress,
            MFStatusCode: director.directorProfile.MFStatusCode,
            MFStatus: director.directorProfile.MFStatus,
            MFProductionOffice: director.directorProfile.MFProductionOffice
          };

          await mem_MemberProfilesHistoryModel.create(profileData);
        } catch (error) {
          console.error("Error creating MemberProfilesHistory record:", error);
          // Continue execution even if this fails
        }
      }

    } catch (error) {
      console.error("Error creating record in mem_DirectorsHistory:", error);
    }

    // Determine message based on the URL path and type
    let successMessage;
    if (req.originalUrl.includes('/director-and-bo/')) {
      successMessage = `${req.params.type === "directors" ? "Director" : "BO"} information has been confirmed successfully`;
    } else if (req.originalUrl.includes('/directors/')) {
      successMessage = "We have received your confirmation to director information and will process the data to BVI Registry in due course if we haven't submit before.";
    } else if (req.originalUrl.includes('/members/')) {
      successMessage = "Member information has been confirmed successfully";
    } else {
      successMessage = `${req.params.type === "directors" ? "Director" : "Member"} information has been confirmed successfully`;
    }

    // Return success if confirmation log was created successfully
    if (confirmDirectorDataLog?.Id) {
      return res.status(200).json({ "status": 200, "message": successMessage });
    } else {
      return res.status(500).json({ "status": 500, "message": "There was an error in the process to confirm the information" });
    }

  } catch (e) {
    console.error("Error in process to confirm director data: ", e);
    return res.status(500).json({
      "status": 500,
      "error": 'Internal server error'
    });
  }
}

exports.requestToUpdate = async function (req, res) {
  try {
    const company = await getCompanyData(req.params.code, req.params.masterclientcode, req.user.email);
    const data = req.body;

    if (company?.error) {
      return res.status(company.status).json({
        "status": company.status,
        "error": company.error
      });
    }

    if (!data.changeType) {
      return res.status(400).json({
        "status": 400,
        "error": 'Please select a valid option'
      });
    }

    //Get director from mem_Directors table
    let type = TYPE_OF_DIRECTOR;
    let director = await mem_DirectorsModel.findOne({
      where: {
        EntityLegacyID: company.code,
        UniqueRelationID: req.params.id,
      },
      include: [{
        model: mem_MemberProfilesModel,
        as: 'directorProfile',
        required: false
      }, {
        model: mem_EntitiesModel,
        as: 'entity',
        required: false
      }],
      raw: false
    });

    if (!director) {
      return res.status(404).json({
        "status": 404,
        "error": 'Director information not found'
      });
    }

    let emailTo;

    if (director.entity?.ProductionOffice === 'TBVI') {
      emailTo = process.env.REQUEST_UPDATE_EMAIL_TBVI
    } else if (director.entity?.ProductionOffice === 'THKO') {
      emailTo = process.env.REQUEST_UPDATE_EMAIL_THKO
    } else if (director.entity?.ProductionOffice === 'TCYP') {
      emailTo = process.env.REQUEST_UPDATE_EMAIL_TCYP
    } else if (director.entity?.ProductionOffice === 'TPANVG') {
      emailTo = process.env.REQUEST_UPDATE_EMAIL_TPANVG
    } else {
      emailTo = process.env.REQUEST_UPDATE_EMAIL_THKO
    }

    const noProductionOffice = director.entity?.ProductionOffice !== 'THKO' && director.entity?.ProductionOffice !== 'TBVI' && director.entity?.ProductionOffice !== 'TCYP' && director.entity?.ProductionOffice !== 'TPANVG' ? true : false;

    // Determine if the director is licensed based on user input
    const isDirectorLicensed = data.isDirectorLicensed;
    // ! mem_DirectorsHistory table has a limit of 10 characters for EntityCode
    const entityCode = director.EntityCode ? director.EntityCode.substring(0, 10) : '';
    const directorData = {
      UniqueRelationID: director.UniqueRelationID,
      ClientCode: director.ClientCode,
      ClientName: director.ClientName,
      ClientUniqueNr: director?.ClientUniqueNr,
      EntityCode: entityCode,
      EntityName: director.EntityName,
      EntityUniqueNr: director?.EntityUniqueNr,
      EntityLegacyID: director.EntityLegacyID,
      DirCode: director.DirCode,
      DirName: director.DirName,
      DirUniqueNr: director.DirUniqueNr,
      DirFileType: director.DirFileType,
      RelationType: director.RelationType,
      DirOfficerType: director.DirOfficerType,
      DirFromDate: director.DirFromDate,
      DirToDate: director.DirToDate,
      DirStatus: director.DirStatus || '',
      LicenseeEntityCode: director.LicenseeEntityCode,
      LicenseeEntityName: director.LicenseeEntityName,
      DirCapacityCode: director.DirCapacityCode,
      DirCapacity: director.DirCapacity,
      DirID: director.DirID,
      PreviousUniqueRelationId: director.PreviousUniqueRelationId,
      UpdateRequestDate: moment.utc().format('YYYY-MM-DD HH:mm:ss'),
      ConfirmedDate: null,
      Status: DIRMEMBER_STATUS.PENDING,
      UserEmail: req.user.username,
      TypeOfUpdateRequest: data.changeType,
      UpdateRequestComments: data.changeReason || "",
      isLicensed: isDirectorLicensed
    };

    // Create request in mem_DirectorsHistory
    let requestUpdateLog = await mem_DirectorsHistoryModel.create(directorData);

    // Create corresponding record in mem_MemberProfilesHistory if a profile exists
    if (director.directorProfile && requestUpdateLog.Id) {
      try {
        const profileData = {
          DirectorHistoryId: requestUpdateLog.Id,
          MFCode: director.directorProfile.MFCode,
          MFUniqueNr: director.directorProfile.MFUniqueNr,
          MFName: director.directorProfile.MFName,
          MFTypeCode: director.directorProfile.MFTypeCode,
          MFLegacyID: director.directorProfile.MFLegacyID,
          MFType: director.directorProfile.MFType,
          MFIncropNr: director.directorProfile.MFIncropNr,
          MFIncorpDate: director.directorProfile.MFIncorpDate,
          MFIncorpCountry: director.directorProfile.MFIncorpCountry,
          MFDateOfBirth: director.directorProfile.MFDateOfBirth,
          MFFormerName: director.directorProfile.MFFormerName,
          MFBirthCountry: director.directorProfile.MFBirthCountry,
          MFNationality: director.directorProfile.MFNationality,
          MFRAAddress: director.directorProfile.MFRAAddress,
          MFROAddress: director.directorProfile.MFROAddress,
          MFRSAddress: director.directorProfile.MFRSAddress,
          MFStatusCode: director.directorProfile.MFStatusCode,
          MFStatus: director.directorProfile.MFStatus,
          MFProductionOffice: director.directorProfile.MFProductionOffice
        };

        await mem_MemberProfilesHistoryModel.create(profileData);
      } catch (error) {
        console.error("Error creating MemberProfilesHistory record:", error);
      }
    }

    const subject = `${process.env.EMAIL_SUBJECT_PREFIX || ''} ${company.name} – Request update for Portal`;

    if (requestUpdateLog.Id) {
      // Get missing information
      let missingInformation = '';
      const listOfRequiredFields = getListFieldsToValidate(TYPE_OF_DIRECTOR, director.DirFileType?.toLowerCase() === 'individual' ? 'Individual' : 'Corporate');

      const directorValues = director.toJSON();

      if (directorValues.directorProfile) {
        // Common fields
        directorValues.ServiceAddress = director.directorProfile.MFRSAddress;
        directorValues.ResidentialOrRegisteredAddress = director.directorProfile.MFRAAddress;

        // For individual directors
        if (directorValues.DirFileType?.toLowerCase() === "individual") {
          directorValues.DateOfBirthOrIncorp = director.directorProfile.MFDateOfBirth;
          directorValues.PlaceOfBirthOrIncorp = director.directorProfile.MFBirthCountry;
          directorValues.Nationality = director.directorProfile.MFNationality;
        }
        // For corporate directors - use the actual field names from the profile
        else {
          directorValues.MFIncropNr = director.directorProfile.MFIncropNr;
          directorValues.MFROAddress = director.directorProfile.MFROAddress;
          directorValues.MFIncorpDate = director.directorProfile.MFIncorpDate;
          directorValues.MFIncorpCountry = director.directorProfile.MFIncorpCountry;
        }
      }

      missingInformation = listOfRequiredFields
        .filter((item) => directorValues[item.field] == null || directorValues[item.field] == undefined || directorValues[item.field] === "")
        .map((item) => item.name)
        .join(', ');

      let email = MailFormatter.generateDirRequestUpdateEmail({
        "companyCode": director.EntityCode + " (" + company.code + ")",
        "companyName": director.EntityName + " (" + company.name + ")",
        "mcc": director.ClientCode + " (" + company.masterclientcode + ")",
        "directorCode": director.DirCode || 'N/A',
        "requestor": requestUpdateLog.UserEmail,
        "relationType": directorValues.DirFileType?.toLowerCase() === "individual" ? 'Individual' : 'Corporate',
        "requestType": requestUpdateLog.TypeOfUpdateRequest,
        "comment": requestUpdateLog.UpdateRequestComments || 'N/A',
        "position": type,
        "licensedDirector": (isDirectorLicensed || director.LicenseeEntityName || director.DirCapacity) ? 'Yes' : 'No',
        "missingInformation": missingInformation || 'N/A'
      });

      let emailResponse = await MailController.asyncSend(
        emailTo,
        noProductionOffice ? '(!Production office unknown) ' + subject : subject,
        email.textString,
        email.htmlString
      );

      if (emailResponse.error) {
        console.error("Send director email error: ", emailResponse);
      }

      return res.status(200).json({ "status": 200, "message": "Request an update has been created successfully" });
    } else {
      return res.status(500).json({ "status": 500, "message": "There was an error creating the request" });
    }

  } catch (e) {
    console.error("Error creating request an update: ", e);
    return res.status(500).json({
      "status": 500,
      "error": 'Internal server error'
    });
  }
}
