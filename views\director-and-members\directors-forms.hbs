<main class="">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="form-group">
                        {{#unless individualEntries}}
                            {{#unless hasCorporateEntries}}
                                <div class="alert alert-warning py-3 px-4">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="fa fa-warning fa-lg mr-2"></i>
                                        <strong>Actions</strong>
                                    </div>
                                    <ul class="mb-0 pl-4 list-disc">
                                        <li class="list-item">If you are unable to locate the entity’s Director below, Please click the “Request Assistance” and your usual Trident representative shall be in touch to assist.</li>
                                    </ul>
                                </div>
                            {{/unless}}
                        {{/unless}}
                        {{#if hasDirectors}}
                            <div class="alert alert-blue py-3 px-4">
                                <div class="d-flex align-items-center mb-2">
                                    <i class="fa fa-info-circle fa-lg mr-2"></i>
                                    <strong>Actions</strong>
                                </div>
                                <ul class="mb-0 pl-4 list-disc">
                                    <li class="list-item">
                                        Please verify the company's director information by clicking the "show more to confirm" button which will show all details. You will be required to confirm if an individual or corporate director licensed by the BVI Financial Services Commission is acting for the entity.
                                    </li>
                                    <li class="mt-2" class="list-item">
                                        By confirming the information here, you are confirming consent for Trident Trust to process your data and file it with the BVI Registry pursuant to legal requirements.
                                    </li>
                                </ul>
                            </div>
                        {{/if}}
                    </div>
                    <br>
                    {{#if directorsWithMissingValues}}
                        <input id="missingDirMemberData" type="text" readonly hidden value="true">
                        {{> director-and-members/missing-data-modal dataRecords=directorsWithMissingValues }}
                    {{/if}}

                    {{#if individualEntries}}
                    <h4 class="mb-3"><strong>Individual Directors</strong></h4>
                    <div class="table-responsive">
                        <table id="individual-director-table" class="table table-sm table-striped mb-0 table-equal-width">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Appointment Date</th>
                                    <th>Cessation Date</th>
                                    <th>Status</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each individualEntries}}
                                <tr>
                                    <td>{{DirName}}</td>
                                    <td>{{formatDate DirFromDate "YYYY-MM-DD"}}</td>
                                    <td>{{formatDate DirToDate "YYYY-MM-DD"}}</td>
                                    <td>
                                        {{#if hasMissingValues}}
                                            MISSING INFORMATION
                                        {{else if isConfirmedInHistory}}
                                            CONFIRMED
                                        {{else if confirmedAndUpdated}}
                                            CONFIRMED
                                        {{else if lastChange.status}}
                                            {{lastChange.status}}
                                        {{else}}
                                            CONFIRMATION REQUIRED
                                        {{/if}}
                                    </td>
                                    <td class="text-right">
                                        {{#if lastChange.status}}
                                            {{#ifCond lastChange.status '===' 'CONFIRMED'}}
                                                <a href="#" class="btn solid royal-blue width-xl show-more-btn" data-id="{{UniqueRelationID}}">Show More</a>
                                            {{else}}
                                                {{#if isConfirmedInHistory}}
                                                    <a href="#" class="btn solid royal-blue width-xl show-more-btn" data-id="{{UniqueRelationID}}">Show More</a>
                                                {{else if hasMissingValues}}
                                                    <a href="#" class="btn solid royal-blue width-xl show-more-btn" data-id="{{UniqueRelationID}}">Show More</a>
                                                {{else}}
                                                    {{#ifCond lastChange.status '===' 'PENDING UPDATE REQUEST'}}
                                                        <a href="#" class="btn solid royal-blue width-xl show-more-btn" data-id="{{UniqueRelationID}}">Show More</a>
                                                    {{else}}
                                                        <a href="#" class="btn solid royal-blue width-xl show-more-btn" data-id="{{UniqueRelationID}}">Show More to Confirm</a>
                                                    {{/ifCond}}
                                                {{/if}}
                                            {{/ifCond}}
                                        {{else}}
                                            {{#if isConfirmedInHistory}}
                                                <a href="#" class="btn solid royal-blue width-xl show-more-btn" data-id="{{UniqueRelationID}}">Show More</a>
                                            {{else}}
                                                <a href="#" class="btn solid royal-blue width-xl show-more-btn" data-id="{{UniqueRelationID}}">Show More to Confirm</a>
                                            {{/if}}
                                        {{/if}}
                                        
                                        {{#if showHistory}}
                                        <button 
                                            data-type="{{lastChange.changeType}}" 
                                            data-reason="{{lastChange.changeReason}}"
                                            class="btn btn-sm solid width-md btn-secondary ml-2 showLastChange">
                                            <small>View History</small>
                                        </button>
                                        {{/if}}
                                        {{#if isConfirmedInHistory}}
                                            {{#ifCond lastChange.status '!==' 'CONFIRMED'}}
                                                <div class="text-danger mb-2 w-100">
                                                    <small><strong>Notice:</strong> Due to updated BVI regulatory reporting requirements, please review and confirm the highlighted sections. Click 'Show more to confirm' to proceed.</small>
                                                </div>
                                            {{/ifCond}}
                                        {{/if}}
                                    </td>
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                    </div>
                    <br>
                    {{/if}}

                    {{#if hasCorporateEntries}}
                    <h4 class="mb-3"><strong>Corporate Directors</strong></h4>
                    <div class="table-responsive">
                        <table id="corporate-director-table" class="table table-sm table-striped mb-0 table-equal-width">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Appointment Date</th>
                                    <th>Cessation Date</th>
                                    <th>Status</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each corporateEntries}}
                                <tr>
                                    <td>{{DirName}}</td>
                                    <td>{{formatDate DirFromDate "YYYY-MM-DD"}}</td>
                                    <td>{{formatDate DirToDate "YYYY-MM-DD"}}</td>
                                    <td>
                                        {{#if hasMissingValues}}
                                            MISSING INFORMATION
                                        {{else if isConfirmedInHistory}}
                                            CONFIRMED
                                        {{else if confirmedAndUpdated}}
                                            CONFIRMED
                                        {{else if lastChange.status}}
                                            {{lastChange.status}}
                                        {{else}}
                                            CONFIRMATION REQUIRED
                                        {{/if}}
                                    </td>
                                    <td class="text-right">
                                        {{#if lastChange.status}}
                                            {{#ifCond lastChange.status '===' 'CONFIRMED'}}
                                                <a href="#" class="btn solid royal-blue width-xl show-more-btn" data-id="{{UniqueRelationID}}">Show More</a>
                                            {{else}}
                                                {{#if isConfirmedInHistory}}
                                                    <a href="#" class="btn solid royal-blue width-xl show-more-btn" data-id="{{UniqueRelationID}}">Show More</a>
                                                {{else if hasMissingValues}}
                                                    <a href="#" class="btn solid royal-blue width-xl show-more-btn" data-id="{{UniqueRelationID}}">Show More</a>
                                                {{else}}
                                                    {{#ifCond lastChange.status '===' 'PENDING UPDATE REQUEST'}}
                                                        <a href="#" class="btn solid royal-blue width-xl show-more-btn" data-id="{{UniqueRelationID}}">Show More</a>
                                                    {{else}}
                                                        <a href="#" class="btn solid royal-blue width-xl show-more-btn" data-id="{{UniqueRelationID}}">Show More to Confirm</a>
                                                    {{/ifCond}}
                                                {{/if}}
                                            {{/ifCond}}
                                        {{else}}
                                            {{#if isConfirmedInHistory}}
                                                <a href="#" class="btn solid royal-blue width-xl show-more-btn" data-id="{{UniqueRelationID}}">Show More</a>
                                            {{else}}
                                                <a href="#" class="btn solid royal-blue width-xl show-more-btn" data-id="{{UniqueRelationID}}">Show More to Confirm</a>
                                            {{/if}}
                                        {{/if}}
                                        
                                        {{#if showHistory}}
                                        <button 
                                            data-type="{{lastChange.changeType}}" 
                                            data-reason="{{lastChange.changeReason}}"
                                            class="btn btn-sm solid width-md btn-secondary ml-2 showLastChange">
                                            <small>View History</small>
                                        </button>
                                        {{/if}}
                                        {{#if isConfirmedInHistory}}
                                            {{#ifCond lastChange.status '!==' 'CONFIRMED'}}
                                                <div class="text-danger mb-2 w-100">
                                                    <small><strong>Notice:</strong> Due to updated BVI regulatory reporting requirements, please review and confirm the highlighted sections. Click 'Show more to confirm' to proceed.</small>
                                                </div>
                                            {{/ifCond}}
                                        {{/if}}
                                    </td>
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                    </div>
                    <br>
                    {{/if}}

                    <br>

                    <div>
                        <a href="/masterclients/{{masterClientCode}}/director-and-members"
                            class="btn btn-secondary waves-effect waves-light width-xl">
                            Back
                        </a>

                        {{#unless individualEntries}}
                            {{#unless hasCorporateEntries}}
                            <button class="btn solid  btn-primary" id="requestAssistanceBtn">
                                Request Assistance
                            </button>
                            {{/unless}}
                        {{/unless}}
                    </div>
                </div> <!-- end card-box-->
            </div> <!-- end col -->
        </div> <!-- end row -->
    </div> <!-- end container-fluid -->
</main>

<script type="text/javascript" src="/templates/director-and-members/requestupdatelog.precompiled.js"></script>
<script src="/views-js/director-and-members/directors-forms.js"></script> 