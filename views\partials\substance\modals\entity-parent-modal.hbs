<div id="entityParentModal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="entitParentModalLbl"
    aria-hidden="true">
    <div class="modal-dialog modal-lg contour container-fluid">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="entitParentModalLbl">Add Entity Parent</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form class="form no-border p-1" novalidate id="parentEntityForm">
                    <input hidden type="text" name="parentEntityType" id="parentEntityType" value="" />

                    <div class="row">
                        <div class="col-md-6">
                            <label id="parentNameLbl" for="parentName" class="form-label">
                                i. Name:
                            </label>
                        </div>

                        <div class="col-md-6">
                            <input type="text" name="parentName" id="parentName" class="form-control" maxlength="100"
                                required value="" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label id="parentAlternativeNameLbl" for="parentAlternativeName" class="form-label">
                                ii. Alternative Name:
                            </label>
                        </div>

                        <div class="col-md-6">
                            <input type="text" name="parentAlternativeName" id="parentAlternativeName"
                                class="form-control" maxlength="100" value="" />
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label id="parentJurisdictionLbl" for="parentJurisdiction" maxlength="100"
                                class="form-label">
                                iii. Jurisdiction of formation:
                            </label>
                        </div>

                        <div class="col-md-6">
                            <select class="form-control w-100" id="parentJurisdiction" required name="parentJurisdiction" data-toggle="select2" data-value="">
                                <option value="">Select country</option>
                                {{#each countries}}
                                <option value="{{alpha_3_code}}">
                                    {{name}}
                                </option>
                                {{/each}}
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label id="parentIncorporationNumberLbl" for="parentIncorporationNumber" class="form-label">
                                iv. Incorporation Number or its equivalent:
                            </label>
                        </div>

                        <div class="col-md-6">
                            <input type="text" name="parentIncorporationNumber" required id="parentIncorporationNumber"
                                class="form-control" maxlength="100" value="" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label id="parentTINLbl" for="parentTIN" class="form-label">
                                v. Taxpayer Identification Number(TIN#) or other Identification
                                reference number:
                            </label>
                        </div>

                        <div class="col-md-6">
                            <input type="text" name="parentTIN" id="parentTIN" class="form-control" required
                                maxlength="100" value="" />
                        </div>
                    </div>
                </form>

            </div>

            <div class="modal-footer justify-content-end">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>

                <button id="loadingParentEntity" class="btn btn-primary" type="button" disabled>
                    <span class="spinner-border spinner-border-sm" aria-hidden="true"></span>
                    Loading...
                </button>
                <button class="btn solid royal-blue" type="submit" form="parentEntityForm"
                    id="submitParentEntity">Save</button>
            </div>

        </div>
    </div>
</div>
<script type='text/javascript' src='/templates/substance/parententities.precompiled.js'></script>
<script type='text/javascript' src="/views-js/partials/substance/modals/entity-parent-modal.js"></script>