async function setCaptchaToken(recaptcha_site) {
    return new Promise(async (resolve) => {
        try {
            await grecaptcha.ready(async () => {
                try {
                    const token = await grecaptcha.execute(recaptcha_site, { action: 'submit' });
                    $('#g-recaptcha-response').val(token);
                    resolve(true);
                } catch (err) {
                    resolve(false);
                }
            });
        } catch (error) {
            console.log("error ", error);
            resolve(false);
        }
    });
}


