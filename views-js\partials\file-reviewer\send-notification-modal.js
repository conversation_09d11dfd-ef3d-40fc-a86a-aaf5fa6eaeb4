let companyCode = '';
let companyName = '';
let nameToNotify = '';
let emailToNotify = '';
let emailText = '';
$('.notifyButton').on('click', function () {
  companyCode = $(this).data('companycode');
  companyName = $(this).data('companyname');
  nameToNotify = $(this).data('frname');
  emailToNotify = $(this).data('fremail');
  $('#userToNotify').text(emailToNotify);
  $('#companyName').text(companyName);
});

$('#notificationMessage').on('input', function () {
  if ($('#notificationMessage').val()) {
    $('#notificationMessage').removeClass('is-invalid');
    $('#sendNotificationButton').prop('disabled', false);
  } else {
    $('#sendNotificationButton').prop('disabled', true);
  }
});

$('#notifyFileReviewerForm').on('submit', async function (event) {
  event.preventDefault();
  emailText = $('#notificationMessage').val();
  if (emailText) {
    $('#sendNotificationButton').prop('disabled', true);
    $.ajax({
      type: 'POST',
      url: '/file-reviewer/send-file-review-notification',
      data: {
        companyCode: companyCode,
        companyName: companyName,
        nameToNotify: nameToNotify,
        emailToNotify: emailToNotify,
        emailText: emailText,
      },
      success: function (res) {
        Swal.fire('Email sent', 'An email was sent to your colleague', 'success');
        location.href = '/file-reviewer/file-review-list';
      },
      error: function (res) {
        Swal.fire('Email not sent', 'There was an error sending the email', 'error');
      },
    });
  } else {
    $('#notificationMessage').addClass('is-invalid');
  }
});