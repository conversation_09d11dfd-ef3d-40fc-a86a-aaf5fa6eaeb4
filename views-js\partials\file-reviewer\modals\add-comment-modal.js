let complianceReviewId;
let commentOfficer;

$('#commentsModal').on('show.bs.modal', function (event) {
    let button = $(event.relatedTarget); // Button that triggered the modal
    complianceReviewId = button.data('id');
    commentOfficer = button.data('officer');
});


$("#sendCommentBtn").on('click', async function () {
    const submitComment = $('#newComment').val();
    $.ajax({
        type: 'POST',
        url: '/file-reviewer/reviews/' + complianceReviewId + '/add-comment',
        data: {
            officer: commentOfficer,
            comment: submitComment
        },
        success: () => {
            Swal.fire('Success', 'The comment has been submitted successfully.', 'success').then(() => {
                $('#commentsModal').modal('hide');
            });
        },
        error: (err) => {
            $('#commentsModal').modal('hide');
            Swal.fire('Error', 'There was an error submitting your comment.', 'error');
        },
    });
});