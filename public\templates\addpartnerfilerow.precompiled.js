(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['addpartnerfilerow'] = template({"1":function(container,depth0,helpers,partials,data) {
    return "present-file-btn";
},"3":function(container,depth0,helpers,partials,data) {
    return "Modify";
},"5":function(container,depth0,helpers,partials,data) {
    return "Upload";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data) {
    var stack1, helper, alias1=container.lambda, alias2=container.escapeExpression, alias3=depth0 != null ? depth0 : (container.nullContext || {}), alias4=container.hooks.helperMissing, alias5="function", lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "\r\n<tr>\r\n    <td>"
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"file") : depth0)) != null ? lookupProperty(stack1,"external") : stack1), depth0))
    + "</td>\r\n    <td class=\"text-center\">\r\n        <button\r\n                type=\"button\"\r\n                class=\"btn solid royal-blue "
    + ((stack1 = lookupProperty(helpers,"if").call(alias3,((stack1 = (depth0 != null ? lookupProperty(depth0,"file") : depth0)) != null ? lookupProperty(stack1,"present") : stack1),{"name":"if","hash":{},"fn":container.program(1, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":7,"column":44},"end":{"line":7,"column":87}}})) != null ? stack1 : "")
    + "\"\r\n                data-toggle=\"modal\"\r\n                data-target=\"#upload-temp-modal\"\r\n                id=\"btn-detailsPartner-"
    + alias2(((helper = (helper = lookupProperty(helpers,"row") || (depth0 != null ? lookupProperty(depth0,"row") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"row","hash":{},"data":data,"loc":{"start":{"line":10,"column":39},"end":{"line":10,"column":46}}}) : helper)))
    + "\"\r\n                data-incorporation-id=\""
    + alias2(((helper = (helper = lookupProperty(helpers,"incorporationId") || (depth0 != null ? lookupProperty(depth0,"incorporationId") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"incorporationId","hash":{},"data":data,"loc":{"start":{"line":11,"column":39},"end":{"line":11,"column":58}}}) : helper)))
    + "\"\r\n                data-mcc=\""
    + alias2(((helper = (helper = lookupProperty(helpers,"mcc") || (depth0 != null ? lookupProperty(depth0,"mcc") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"mcc","hash":{},"data":data,"loc":{"start":{"line":12,"column":26},"end":{"line":12,"column":33}}}) : helper)))
    + "\"\r\n                data-id=\""
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"file") : depth0)) != null ? lookupProperty(stack1,"id") : stack1), depth0))
    + "\"\r\n                data-review-id=\""
    + alias2(((helper = (helper = lookupProperty(helpers,"reviewId") || (depth0 != null ? lookupProperty(depth0,"reviewId") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"reviewId","hash":{},"data":data,"loc":{"start":{"line":14,"column":32},"end":{"line":14,"column":45}}}) : helper)))
    + "\"\r\n                data-relation-id=\""
    + alias2(((helper = (helper = lookupProperty(helpers,"relationId") || (depth0 != null ? lookupProperty(depth0,"relationId") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"relationId","hash":{},"data":data,"loc":{"start":{"line":15,"column":34},"end":{"line":15,"column":49}}}) : helper)))
    + "\"\r\n                data-row=\""
    + alias2(((helper = (helper = lookupProperty(helpers,"row") || (depth0 != null ? lookupProperty(depth0,"row") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"row","hash":{},"data":data,"loc":{"start":{"line":16,"column":26},"end":{"line":16,"column":33}}}) : helper)))
    + "\"\r\n                data-file-type=\""
    + alias2(((helper = (helper = lookupProperty(helpers,"group") || (depth0 != null ? lookupProperty(depth0,"group") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"group","hash":{},"data":data,"loc":{"start":{"line":17,"column":32},"end":{"line":17,"column":41}}}) : helper)))
    + "\"\r\n                data-field=\""
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"file") : depth0)) != null ? lookupProperty(stack1,"internal") : stack1), depth0))
    + "\"\r\n                data-file-group=\"detailsPartner\"\r\n        >\r\n            "
    + ((stack1 = lookupProperty(helpers,"if").call(alias3,((stack1 = (depth0 != null ? lookupProperty(depth0,"file") : depth0)) != null ? lookupProperty(stack1,"present") : stack1),{"name":"if","hash":{},"fn":container.program(3, data, 0),"inverse":container.program(5, data, 0),"data":data,"loc":{"start":{"line":21,"column":12},"end":{"line":21,"column":59}}})) != null ? stack1 : "")
    + "\r\n        </button>\r\n    </td>\r\n    <td>\r\n            <textarea\r\n                    class=\"form-control\"\r\n                    name=\"detailsPartner[files]["
    + alias2(((helper = (helper = lookupProperty(helpers,"row") || (depth0 != null ? lookupProperty(depth0,"row") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"row","hash":{},"data":data,"loc":{"start":{"line":27,"column":48},"end":{"line":27,"column":55}}}) : helper)))
    + "][explanation]\"\r\n                    id=\""
    + alias2(((helper = (helper = lookupProperty(helpers,"group") || (depth0 != null ? lookupProperty(depth0,"group") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"group","hash":{},"data":data,"loc":{"start":{"line":28,"column":24},"end":{"line":28,"column":33}}}) : helper)))
    + "-detailsPartner-"
    + alias2(((helper = (helper = lookupProperty(helpers,"row") || (depth0 != null ? lookupProperty(depth0,"row") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"row","hash":{},"data":data,"loc":{"start":{"line":28,"column":49},"end":{"line":28,"column":56}}}) : helper)))
    + "-explanation-file\"\r\n                    rows=\"1\"\r\n                    data-value=\""
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"file") : depth0)) != null ? lookupProperty(stack1,"explanation") : stack1), depth0))
    + "\"\r\n            ></textarea>\r\n        <label for=\""
    + alias2(((helper = (helper = lookupProperty(helpers,"group") || (depth0 != null ? lookupProperty(depth0,"group") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"group","hash":{},"data":data,"loc":{"start":{"line":32,"column":20},"end":{"line":32,"column":29}}}) : helper)))
    + "-detailsPartner-"
    + alias2(((helper = (helper = lookupProperty(helpers,"row") || (depth0 != null ? lookupProperty(depth0,"row") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"row","hash":{},"data":data,"loc":{"start":{"line":32,"column":45},"end":{"line":32,"column":52}}}) : helper)))
    + "-explanation-file\" hidden></label>\r\n    </td>\r\n</tr>";
},"useData":true});
})();