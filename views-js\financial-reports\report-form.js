
let reportId = ""
const url = location.pathname.split('/')
reportId = url[url.length - 1]

const stepsFlow = {
    default: {
        "details": {previous: null, next: "cash-transactions" },
        "cash-transactions": { previous: "details", next: "assets" },
        "assets": { previous: "cash-transactions", next: "liabilities" },
        "liabilities": { previous: "assets", next: "summary" },
        "summary": { previous: "liabilities", next: "declaration" },
        "declaration": { previous: "summary", next: "confirmation" },
        "confirmation": { previous: "declaration", next: null },
    },
    exempt: {
        "details": { previous: null, next: "declaration" },
        "declaration": { previous: "details", next: "confirmation" },
        "confirmation": { previous: "declaration", next: null },
    },
    complete: {
        "details": { previous: null, next: "complete-income-expenses" },
        "complete-income-expenses": { previous: "details", next: "complete-assets-lbt" },
        "complete-assets-lbt": { previous: "complete-income-expenses", next: "declaration" },
        "declaration": { previous: "complete-assets-lbt", next: "confirmation" },
        "confirmation": { previous: "declaration", next: null },
    }
};

function changeStep(currentStep, action, id) {
    reportId = id
    const serviceType = $("input[name='serviceType']:checked").val();
    let stepValues;

    if ($('#isExemptCompanyYes').is(':checked') === true) {
        stepValues = stepsFlow.exempt[currentStep];
    }else{
        if (serviceType === "self-service-prepare") {
            stepValues = stepsFlow.default[currentStep];
        } 
        else if(serviceType === "self-service-complete") {
            stepValues = stepsFlow.complete[currentStep];

        }else{
            stepValues = stepsFlow.exempt[currentStep];
        }
    }



    if (action === 'NEXT') {
        saveReportForm(currentStep, stepValues.next, false, false);
    } else if (action === 'BACK') {
        if (currentStep === "declaration" && $('#isExemptCompanyYes').is(':checked')) {
            $('#step-details').show();
        } else {
            $('#step-' + stepValues.previous).show();
        }

        $('#step-' + currentStep).hide();
        $("html, body").animate({
            scrollTop: $("#financialReportTitle").offset().top
        }, 200);
    } 
    else if(action === "SAVE-AND-CLOSE"){
        saveReportForm(currentStep, stepValues.next, true, false);
    }
    else if (action === 'COMPLETE') {
        saveReportForm(currentStep, stepValues.next, true, true);
    } 
}
$('.nextBtnAssets').click(function () {
    var emptyVisibleInputs = false;
    $('.assetInput:visible').each(function() {
        if ($(this).css('display') !== 'none' && $(this).val() === '') {
            emptyVisibleInputs = true;
            return false;
        }
    });
    if (emptyVisibleInputs) {
        toastr['warning']('All the fields left blank have been populated with 0', 'Error');
    }
    $('.assetInput').each(function() {
        if ($(this).css('display') !== 'none' && $(this).val() === '') {
                $(this).val('0.0')
        }
    });
});

$('.nextBtnLiabilities').click(function () {
    var emptyVisibleInputs = false;
    $('.liabilityInput:visible').each(function() {
        if ($(this).css('display') !== 'none' && $(this).val() === '') {
            emptyVisibleInputs = true;
            return false;
        }
    });
    if (emptyVisibleInputs) {
        toastr['warning']('All the fields left blank have been populated with 0', 'Error');
    }
    $('.liabilityInput').each(function() {
        if ($(this).css('display') !== 'none' && $(this).val() === '') {
                $(this).val('0.0')
        }
    });
});

$('.nextBtn').click(function () {
    changeStep($(this).attr('data-step'), $(this).attr('data-action'), $(this).attr('data-id'))
})

$('.backBtn').click(function () {
    changeStep($(this).attr('data-step'), $(this).attr('data-action'), $(this).attr('data-id'))
})

$('input[required]').on('keyup', function () {
    const empty = $(this).val() === "";
    $(this).toggleClass("is-invalid", empty);
});

$('textarea[required]').on('keyup', function () {
    const empty = $(this).val() === "";
    $(this).toggleClass("is-invalid", empty);
});

$('select[required]').on('change', function () {
    const empty = $(this).val() === "";
    if ($(this).attr('id') === '-records[operationCountry]') {
        checkBlacklistedStep5($(this).val());
    }
    // Used for select2 selectors (country select)
    $(`span[aria-labelledby='select2-${$(this).attr('id').replace('[', '').replace(']', '')}-container']`).toggleClass("is-invalid", empty);
    $(this).toggleClass("is-invalid", empty);
});

$("input[type='radio'][required]").on('change', function () {
    const empty = $('input[name="' + this.name + '"]:checked').val() === "";
    $('input[name="' + this.name + '"]').toggleClass("is-invalid", empty);
});

$("input[type='checkbox'][required]").on('change', function () {
    const empty = !($('input[name="' + this.name + '"]:checked').val());
    $('input[name="' + this.name + '"]').toggleClass("is-invalid", empty);
});

$('#currency').on('change', function () {
    const val = $(this).val();
    $("#valuePerShareLabel").html('What is the par value per share in ' + val + ' currency?*');
    $(".prepend-currency").text(val);
});

async function saveReportForm(currentStep, nextStep, isLastStep, isCompleted) {
    let invalidRadios = false;
    let invalidFiles = false;

    $('#reportForm input[required]:visible').trigger('keyup');
    $('#reportForm textarea[required]:visible').trigger('keyup');
    $('#reportForm select[required]:visible').trigger('change');
    $("#reportForm input[type='checkbox'][required]:visible").each(function () {
        const val = $('input[name="' + this.name + '"]:checked').val();
        if (val === undefined) {
            $('input[name="' + this.name + '"]').toggleClass("is-invalid", true);
            invalidRadios = true;
        } else {
            $('input[name="' + this.name + '"]').toggleClass("is-invalid", false);
        }
    });
    let missingAssets = false;
    if ($('#assetsAndFunds').is(':visible')) {
        missingAssets = true;
        $('.asset-row').each(function () {
            missingAssets = false;
        });
        $('.intangible-asset-row').each(function () {
            missingAssets = false;
        });
        if (missingAssets) {
            $('#missingAssetRowStep5').show(200);
        } else {
            $('#missingAssetRowStep5').hide(200);
        }
    }

    $("#reportForm input[type='radio'][required]:visible").each(function () {
        const val = $('input[name="' + this.name + '"]:checked').val();
        if (val === undefined) {
            $('input[name="' + this.name + '"]').toggleClass("is-invalid", true);
            invalidRadios = true;
        } else {
            $('input[name="' + this.name + '"]').toggleClass("is-invalid", false);
        }
    });



    if ($(".is-invalid:visible").length === 0 && !invalidRadios && !invalidFiles && !missingAssets) {
        const form = $("#reportForm").serializeJSON();
        form.isCompleted = isCompleted;
        form.additionalServices = [];
        form.currentStep = currentStep;
        form.isFixedAssetsCotributed = $('input[name="isFixedAssetsCotributed"]:checked').val() ? $('input[name="isFixedAssetsCotributed"]:checked').val() : ''

        $('input[name="additionalServices"]:checked').each(function () {
            form.additionalServices.push($(this).val());
        });

        $.ajax({
            type: "POST",
            url: "./" + reportId,
            data: JSON.stringify(form),
            contentType: "application/json; charset=utf-8",
            success: function (data) {
                if (data.status === 200) {
                    setFinancialPeriodText(data.response.newFinancialPeriod?.start, data.response.newFinancialPeriod?.end);
            
                    if (currentStep === 6) {
                        calcBCTotal();
                    }
                    if (!isLastStep ){
                        if (data.response.invalidSubmitPeriod === true) {
                            $("#submitAccountingFormBtn").prop('disabled', true);
                            $("#submitAccountingFormBtn").hide();
                            $("#saveAndCloseAccountingFormBtn").prop('disabled', false);
                            $("#invalidSubmitInfo").show();
                            $("#invalidSubmitInfoText").show();

                        } else {
                            $("#submitAccountingFormBtn").prop('disabled', false);
                            $("#submitAccountingFormBtn").show();
                            $("#saveAndCloseAccountingFormBtn").prop('disabled', true);
                            $("#invalidSubmitInfo").hide();
                            $("#invalidSubmitInfoText").hide();
                        }
                    }

                    if(isLastStep && !isCompleted){
                        
                        Swal.fire('Success', "Report information saved successfully", 'success').then(() => {
                            document.location = location.pathname.substring(0, location.pathname.lastIndexOf('/'));
                        });
                        
                        
                        return;
                    }
            
                    $("#step-" + nextStep).show();
                    $('#step-' + currentStep).hide();

                    if (currentStep === 1 || isCompleted) {
                        $(".isNotExemptCompany").hide()
                        $(".exemptCompany").hide()
                        if (data.response.noGenerateInvoice === true) {
                            $(".exemptCompany").show()
                        } else {
                            $(".isNotExemptCompany").show()
                            
                        }

                        if (isCompleted && data.response.companyInPenalty === true){
                            showCompanyPenaltyAlert()
                        }
                    }


            
                    $("html, body").animate({
                        scrollTop: $("#financialReportTitle").offset().top
                    }, 200); 
                } else if (data.status === 400 && data.errors && data.errors.length) {
                    for (let error of data.errors) {
                        toastr["warning"](error, 'Error!');
                    }
                } else {
                    toastr["warning"](data.message, 'Error!');
                }
            },
            
            error: function (err) {
                toastr["warning"]('Submission could not be saved, please try again later.', 'Error!');
            }
        });

    } else {
        return true;
    }
}

function showDecimalValue(dVal, minimumDigits=2) {
    if (dVal) {
        return Number(dVal).toLocaleString("en", { minimumFractionDigits: minimumDigits });
    } else {
        return 0.00;
    }
};

function calcBCTotal() {
    $.get("./" + reportId + "/cash-balance-details", function (data) {
        if (data.status === 200) {
            $("#equityTotal").val(showDecimalValue(data.balanceDetails.cashSetUp));
            $("#incomeTotal").val(showDecimalValue(data.balanceDetails.cashReceivedFromIncome));
            $("#expensesTotal").val(showDecimalValue(data.balanceDetails.cashPaidForExpenses));
            $("#loanLiabilityTotal").val(showDecimalValue(data.balanceDetails.cashReceivedFromLoans));
            $("#cashForInvestmentsSold").val(showDecimalValue(data.balanceDetails.cashReceivedForInvestmentsSold));
            $("#purchaseCostTotal").val(showDecimalValue(data.balanceDetails.cashPaidForInvestmentPurchases));
            $("#nonCurrentAssetTotal").val(showDecimalValue(data.balanceDetails.cashReceivedFromNonCurrentAssets));
            $("#bankReconciliationOtherCashTrans").val(showDecimalValue(data.balanceDetails.otherCashTransactions));
            $("#cashBalanceTotal").val(showDecimalValue(data.balanceDetails.cashBankBalance));

            if (data.balanceDetails.otherCashTransactions) {
                $(".bankReconciliationOtherCashTransValue").text(showDecimalValue(data.balanceDetails.otherCashTransactions));
                $('.ReconcileText').show(200);
            } else {
                $(".bankReconciliationOtherCashTransValue").text('');
                $('.ReconcileText').hide(200);
            }

        } else {
            $("#equityTotal").val('0.00');
            $("#incomeTotal").val('0.00');
            $("#expensesTotal").val('0.00');
            $("#loanLiabilityTotal").val('0.00');
            $("#cashForInvestmentsSold").val('0.00');
            $("#purchaseCostTotal").val('0.00');
            $("#nonCurrentAssetTotal").val('0.00');
            $("#bankReconciliationOtherCashTrans").val('0.00');
            $("#cashBalanceTotal").val('0.00');
        }
    });

}


function calculateCompleteTotalValues(items, totalField) {
    let total = 0;
    items.each(function () {
        const item = $(this)
        const itemValue = item.val()
        const value = parseFloat(itemValue.replace(/,/g, '')) || 0;
        total += value;

    });

    totalField.val(showDecimalValue(total)).trigger('keyup');
}

function createOtherValueRow(type, typeClass, newPosition) {
    let template = Handlebars.templates.createothervaluerow;
    let currency = $("#completeReportCurrency").val();

    let newOtherRow = template({
        type: type,
        typeClass: typeClass,
        currency: currency,
        position: newPosition,
        positionLbl: newPosition + 1
    });


    $(`#completeDetailsOther${type}Row${newPosition - 1}`).after(newOtherRow);
    $(`#completeDetailsOther${type + newPosition}`).autoNumeric('init');
}


function showCompanyPenaltyAlert(){
    Swal.fire({
        icon: 'warning',
        title: 'COMPANY IN PENALTY',
        html: `<h5>Your company has already incurred a governement penalty for the late submission of the Financial Return. The submission will be processed upon settlement of the applicable cost.<br><br>
            You will soon be contacted by a Trident representative with the relevant invoice for settlement. For any questions or queries please contact
            <a href="#"><EMAIL></a>. </h5>
        `,
    });

}