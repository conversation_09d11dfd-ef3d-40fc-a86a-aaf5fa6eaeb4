(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['createotherdescriptionrow'] = template({"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data) {
    var helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "<div class=\"row\" id=\"other"
    + alias4(((helper = (helper = lookupProperty(helpers,"type") || (depth0 != null ? lookupProperty(depth0,"type") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"type","hash":{},"data":data,"loc":{"start":{"line":1,"column":26},"end":{"line":1,"column":34}}}) : helper)))
    + "Row"
    + alias4(((helper = (helper = lookupProperty(helpers,"position") || (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"position","hash":{},"data":data,"loc":{"start":{"line":1,"column":37},"end":{"line":1,"column":49}}}) : helper)))
    + "\">\r\n    <div class=\"col-12 col-md-8\">\r\n        <div class=\"form-group ml-5\">\r\n            <label class=\"lbl-read-only\" for=\"other"
    + alias4(((helper = (helper = lookupProperty(helpers,"type") || (depth0 != null ? lookupProperty(depth0,"type") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"type","hash":{},"data":data,"loc":{"start":{"line":4,"column":51},"end":{"line":4,"column":59}}}) : helper)))
    + alias4(((helper = (helper = lookupProperty(helpers,"position") || (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"position","hash":{},"data":data,"loc":{"start":{"line":4,"column":59},"end":{"line":4,"column":71}}}) : helper)))
    + "\">Other "
    + alias4(((helper = (helper = lookupProperty(helpers,"typeLbl") || (depth0 != null ? lookupProperty(depth0,"typeLbl") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"typeLbl","hash":{},"data":data,"loc":{"start":{"line":4,"column":79},"end":{"line":4,"column":90}}}) : helper)))
    + "\r\n                ("
    + alias4(((helper = (helper = lookupProperty(helpers,"positionLbl") || (depth0 != null ? lookupProperty(depth0,"positionLbl") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"positionLbl","hash":{},"data":data,"loc":{"start":{"line":5,"column":17},"end":{"line":5,"column":32}}}) : helper)))
    + ")</label>\r\n        </div>\r\n    </div>\r\n    <div class=\"col-12 col-md-4\">\r\n        <div class=\"form-group input-group mb-1\">\r\n            <input type=\"text\" id=\"other"
    + alias4(((helper = (helper = lookupProperty(helpers,"type") || (depth0 != null ? lookupProperty(depth0,"type") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"type","hash":{},"data":data,"loc":{"start":{"line":10,"column":40},"end":{"line":10,"column":48}}}) : helper)))
    + alias4(((helper = (helper = lookupProperty(helpers,"position") || (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"position","hash":{},"data":data,"loc":{"start":{"line":10,"column":48},"end":{"line":10,"column":60}}}) : helper)))
    + "\"\r\n                class=\"form-control\"  placeholder=\"Description\"\r\n                name=\"other"
    + alias4(((helper = (helper = lookupProperty(helpers,"type") || (depth0 != null ? lookupProperty(depth0,"type") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"type","hash":{},"data":data,"loc":{"start":{"line":12,"column":27},"end":{"line":12,"column":35}}}) : helper)))
    + "[]\">\r\n        </div>\r\n    </div>\r\n</div>";
},"useData":true});
})();