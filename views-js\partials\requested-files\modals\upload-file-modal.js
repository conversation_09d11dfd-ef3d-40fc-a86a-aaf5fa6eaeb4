$("#uploadedRequestedFiles").html('');


let mcc = '';
let requestFileId = '';
let company = '';
Dropzone.autoDiscover = false;

let button;
$(async function () {
    let field = '';
    let id = '';
    let fieldName;
    
    const csrfToken = $("input[name='csrf-token']").val()
    const myDropZone = new Dropzone('#uploadModalForm', {
        url: '/',
        acceptedFiles: 'application/pdf',
        autoProcessQueue: true,
        parallelUploads: 3,
        maxFiles: 3,
        maxFilesize: 5,
        paramName: function () {
            return 'fileUploaded';
        },
        headers: {
            'x-csrf-token': csrfToken
        },
        uploadMultiple: true,
        init: function () {
            this.on('processing', function () {
                this.options.url = '/masterclients/' + mcc + '/company-files/' + requestFileId + '/upload-files';
            });
            this.on('success', function () {
                refreshUploadedFiles(requestFileId, id);

            });
            this.on('sending', function (file, xhr, formData) {
                if (!formData.has('fieldName')) {
                    formData.append('fieldName', field);
                }
                if (!formData.has('fileTypeId')) {
                    formData.append('fileTypeId', id);
                }
            });

            this.on('errormultiple', function (files, response) {
            });

            this.on('maxfilesexceeded', function (file) {
            });

            this.on('resetFiles', function () {
                if (this.files.length !== 0) {
                    for (i = 0; i < this.files.length; i++) {
                        this.files[i].previewElement.remove();
                    }
                    this.files.length = 0;
                }
                $('#maxUpload').text(this.options.maxFiles);
            });
        },
    });

    $('#upload-request-file-modal').on('show.bs.modal', function (event) {
        button = $(event.relatedTarget); // Button that triggered the modal
        fieldName = button.data('field'); //name of the file
        mcc = button.data('mcc');
        company = button.data('company');
        requestFileId = button.data('request-id');
        field = fieldName.replace(/[\s\’\'\/\(\)]/g, '');
        id = button.data('file-type-id');
        $('#upload-modal-file-label').text(field);
        // GET FILES UPLOADED PREVIOUSLY
        refreshUploadedFiles(requestFileId, id);
        const objDZ = Dropzone.forElement('#uploadModalForm');
        objDZ.emit('resetFiles');
    });
});

async function deleteRequestedFile(requestId, fileTypeId, fileId) {
    
    $.ajax({
        type: 'DELETE',
        url: '/masterclients/' + mcc + '/company-files/' + requestId + '/files/',
        data: {
            fileTypeId: fileTypeId,
            fileId: fileId
        },
        success: function (res) {
            if (res.status === 200) {
                refreshUploadedFiles(requestId, fileTypeId);
            }
            else{
                toastr["warning"]('Error deleting the file... Please try again later.', 'Error!');
            }
        },
        dataType: 'json',
    });
    return false;
}

$(document).on('click', '.downloadRequestedFile', function () {
    downloadRequestedFile($(this).attr('data-id'), $(this).attr('data-type'), $(this).attr('data-field-id'))
})

$(document).on('click', '.deleteRequestedFile', async function () {
    await deleteRequestedFile($(this).attr('data-id'), $(this).attr('data-type'), $(this).attr('data-field-id'))
})

function downloadRequestedFile(requestId, fileTypeId, fileId) {
    const url = '/masterclients/' + mcc + '/company-files/' + requestId + '/files/' + fileTypeId + "/" + fileId;
    window.open(url, "_blank");
    return false;
}

function refreshUploadedFiles(requestId, fileTypeId) {
    $.ajax({
        type: 'GET',
        url: '/masterclients/' + mcc + '/company-files/' + requestId + '/search-files',
        data: {
            fileTypeId: fileTypeId,
        },
        timeout: 5000,
        success: function (data) {
            if ( data.result === true){
                let template = Handlebars.templates.uploadrequestedfiles;
                let d = {
                    id: requestId,
                    files: data.files ? data.files : [],
                    fileTypeId: fileTypeId,
                };
                let html = template(d);
                $('#uploadedRequestedFiles').html(html);
                if (data.files && data.files.length > 0) {
                    button.text('Modify');
                    button[0].style.setProperty('background-color', '#0AC293', 'important');
                    button[0].style.setProperty('border-color', '#0AC292', 'important');
                } else {
                    button.text('Upload');
                    button[0].style.setProperty('background-color', '#0081b4', 'important');
                    button[0].style.setProperty('border-color', '#0081b4', 'important');
                }
            }else{
                Swal.fire('Error', data.error ? data.error : 'There was an error getting files', 'error');
            }
        },
        error: function (res) {
            console.log(res);
            Swal.fire('Error', 'There was an error getting files', 'error');
        },
    });

}


$("#upload-request-file-modal").on("hidden.bs.modal", function () {
    $("#uploadedRequestedFiles").html('');
});