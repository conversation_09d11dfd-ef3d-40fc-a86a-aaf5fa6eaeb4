$('#fundFinancialInstitutionNameRow').hide();
$('#fundDetailsRow').hide();
$('#fundProfitRow').hide();
$('#fundNameOfSubsidiaryRow').hide();
$('#fundNameOfSubsidiaryRow').hide();
$('#fundJurisdictionOfOperationRow').hide();


$('#fundTypeControl').change(function () {
    $('#fundFinancialInstitutionNameRow').hide();
    $('#fundDetailsRow').hide();
    $('#fundProfitRow').hide();
    $('#fundNameOfSubsidiaryRow').hide();
    $('#fundJurisdictionOfOperationRow').hide();
    if ($(this).val() === 'Loan') {
        $('#fundFinancialInstitutionNameRow').show();
    } else if ($(this).val() === 'Sale of assets') {
        $('#fundDetailsRow').show();
    } else if ($(this).val() === 'Business income') {
        $('#fundProfitRow').show();
    } else if ($(this).val() === 'Dividend from subsidiary') {
        $('#fundNameOfSubsidiaryRow').show();
        $('#fundJurisdictionOfOperationRow').show();
    } else if ($(this).val() === 'Other') {
        $('#fundDetailsRow').show();
    }
});

$('#-fundJurisdictionOfOperation').change(function () {
    checkBlacklistedFund();
});

function checkBlacklistedFund() {
    let isBlacklisted = false;
    if (blacklist.find((country) => country === $('#-fundJurisdictionOfOperation').val())) {
        isBlacklisted = true;
    }
    if (isBlacklisted && $('#-fundJurisdictionOfOperation').is(':visible')) {
        $('#blacklistedCountryRowFund').show(200);
        $('#submitFundBtn').hide();
    } else {
        $('#blacklistedCountryRowFund').hide(200);
        $('#submitFundBtn').show();
    }
}