$(document).ready(function () {
    $("#searchText").on("keyup", function () {
        var value = $(this).val().toLowerCase();
        $(".container .col-lg-4").filter(function () {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
        });
    });
});

$(document).on('click', '.CreateSubmission', function (event) {
    event.stopPropagation();
    CreateSubmission($(this).attr('data-code'), event)
})


$(document).on('click', '.openSubmission', function (event) {
    event.stopPropagation();
    openSubmission($(this).attr('data-code'), $(this).attr('data-id'))
})


$(document).on('click', '.showReopenContinueModal', function (event) {
    event.stopPropagation();
    showReopenContinueModal($(this).attr('data-code'), $(this).attr('data-id'), $(this).attr('data-reopened-id'))
})



function CreateSubmission(companyCode, e) {
    const masterclientcode = window.location.pathname
    $(".submission-btn").prop('disabled', true);
    $('[data-toggle="tooltip"]').tooltip('hide');
    $.ajax({
        type: 'GET',
        url: `/masterclients/${masterclientcode.split('/')[2]}/substance/companies/${companyCode}/forms/create`,
        timeout: 5000,
        success: function (data) {
            if (data.status === 200) {
                document.location = '/substance/entry/' + data.entryId + '/financial-period';
            }
            else if (data.status !== 200 && data.error) {
                Swal.fire('Error', data.error, 'error').then(() => {
                    $(".submission-btn").prop('disabled', false);
                });

            } else {
                Swal.fire('Error', 'There was an error creating a new submission... Please try again later.', 'error').then(() => {
                    $(".submission-btn").prop('disabled', false);
                });
            }

        },
        error: function (err) {
            if (err?.responseJSON && err.responseJSON.error) {
                Swal.fire('Error', err.responseJSON.error, 'error').then(() => {
                    $(".submission-btn").prop('disabled', false);
                });
            } else {
                Swal.fire('Error', 'There was an error creating a new submission... Please try again later.', 'error').then(() => {
                    $(".submission-btn").prop('disabled', false);
                });
            }

        },
    });

}

function openSubmission(companyCode, entryId) {
    window.location.href = `./companies/select?companyCode=${companyCode}&entryId=${entryId}`;
}


function showReopenContinueModal(company, entryId, reopenedId) {
    $(".submission-btn").prop('disabled', true);
    $.ajax({
        type: 'GET',
        url: `./companies/${company}/forms/${entryId}/reopened-info/${reopenedId}`,
        timeout: 5000,
        success: function (data) {
            if (data.status === 200) {
                Swal.fire({
                    title: "Re-opened submission",
                    icon: "warning",
                    backdrop: true,
                    showCancelButton: true,
                    confirmButtonColor: "#005C81",
                    confirmButtonText: 'Continue',
                    reverseButtons: true,
                    allowOutsideClick: false,
                    html: "<div class='text-justify'> Please note the following message from your Trident Trust Officer: " + data.reopenedData.reason + "</div> " +
                            (data.reopenedData.change_financial_period_dates === true ?
                                    "<hr> " +
                                    "<div class='text-justify'> <h5>  Note that your Financial Period has been changed by a " +
                                    "Trident Officer according to the ITA guidelines. Please make sure the information on " +
                                    "the remaining pages of this submission is correct and in accordance with the amended dates.  </h5> </div>" :
                                    ""),
                }).then((result) => {

                    if (result.isConfirmed){
                        window.location.href = `./companies/select?companyCode=${company}&entryId=${entryId}`;
                    }else{
                        $(".submission-btn").prop('disabled', false);
                    }
                });
            } else {
                Swal.fire('Error', 'There was an error opening the re-open submission... Please try again later.', 'error').then(() => {
                    $(".submission-btn").prop('disabled', false);
                });
            }

        },
        error: function (err) {
            console.log(err);
            Swal.fire('Error', 'There was an error opening the re-open submission... Please try again later.', 'error').then(() => {
                $(".submission-btn").prop('disabled', false);
            });
        },
    });


}
