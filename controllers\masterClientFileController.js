const httpConstants = require('http2').constants;
const MasterClientModel = require('../models/masterClientCode');
const sessionUtils = require('../utils/sessionUtils');
const { ShareClient, StorageSharedKeyCredential } = require('@azure/storage-file-share');
const { DefaultAzureCredential } = require('@azure/identity');

exports.getFiles = async function (req, res) {
  try {
    await MasterClientModel.findOneAndUpdate({ code: req.params.masterclientcode }, { openedFiles: true });
    let currentDirectory = (req.query.directory ? req.query.directory : '');
    if (currentDirectory.includes('..')) {
      currentDirectory = currentDirectory.replace(/\.\./g, '');
    }

    const shareClient = getShareClient();
    const shareDirectoryClient = shareClient.getDirectoryClient(`${req.params.masterclientcode}${currentDirectory ? `/${currentDirectory}` : ''}`);

    const filesAndDirectories = shareDirectoryClient.listFilesAndDirectories();

    const files = [];
    const directories = [];

    for await (const item of filesAndDirectories) {
      if (item.kind === 'file') {
        files.push({
          fileName: item.name,
          isDirectory: false,
          directory: currentDirectory ? '/' + currentDirectory : ''
        });
      } else if (item.kind === 'directory') {
        directories.push({
          directoryName: item.name,
          directoryRoute: (currentDirectory ? currentDirectory + '/' : '') + item.name,
          isDirectory: true
        });
      }
    }

    const lastSlash = currentDirectory.lastIndexOf('/');

    res.render("master-client-files/list", {
      user: req.user,
      title: "Files for " + req.params.masterclientcode,
      masterClientCode: req.params.masterclientcode,
      files: [...files, ...directories],
      messages: req.session.messages,
      lastDirectory: currentDirectory ? currentDirectory.slice(0, (lastSlash === -1 ? 0 : lastSlash)) : null
    });
  } catch (e) {
    console.log(e);
    res.render("master-client-files/list", {
      user: req.user,
      title: "Files for " + req.params.masterclientcode,
      masterClientCode: req.params.masterclientcode,
      files: [],
      messages: req.session.messages,
      lastDirectory: null
    });
  }
};

exports.downloadFile = function (req, res, next) {
  try {
    res.setHeader('Content-Disposition', 'inline; filename=' + req.query.file);
    let currentDirectory = (req.query.directory ? req.query.directory : '');
    let fileName = (req.query.file ? req.query.file : '');
    if (currentDirectory.includes('..')) {
      currentDirectory = currentDirectory.replace(/\.\./g, '');
    }

    if (fileName.includes('..')) {
      fileName = fileName.replace(/\.\./g, '');
    }

    const shareClient = getShareClient();
    const directoryClient = shareClient.getDirectoryClient(`${req.params.masterclientcode}${currentDirectory}`);
    const fileClient = directoryClient.getFileClient(fileName);

    fileClient.download().then((downloadResponse) => {
      if (downloadResponse.contentLength === 0) {
        return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).send({ "message": "File not found" });
      }
      downloadResponse.readableStreamBody.pipe(res);
    }).catch((error) => {
      console.error("Error downloading file:", error);
      return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).send({ "message": "Error downloading file" });
    });

  } catch (e) {
    console.log(e);
    let err = new Error('Internal Server Error');
    err.status = httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR;
    return next(err);
  }
};

exports.ensureAuthenticated = async function (req, res, next) {

  if ((req.user && req.session.id == req.user.sessionId) && req.session.auth2fa) {
    let masterclient = await MasterClientModel.findOne({ 'code': req.params.masterclientcode });
    if (masterclient == null || masterclient.owners.indexOf(req.user.email.toLowerCase()) == -1) {
      let err = new Error('Masterclient not found');
      err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
      return next(err);
    }
    next();
  } else if ((req.user && req.session.id == req.user.sessionId) && !req.session.auth2fa) {
    if (req.user.secret_2fa) {
      res.redirect('/users/2fa-code');
    } else {
      res.redirect('/users/2fa-setup');
    }
  } else {
    req.logout(function (err) {
      if (err) { return next(err) }
      req.session.destroy(function () {
        // cannot access session here
        sessionUtils.onSessionDestroyed(req, res);
      });
    });
  }
}

function getShareClient() {
  const accountName = process.env.AZURE_STORAGE_ACCOUNT_MASTERCLIENT_FILES;
  const accountKey = process.env.AZURE_STORAGE_ACCOUNT_MASTERCLIENT_FILES_ACCESS_KEY;
  const masterClientShare = process.env.AZURE_STORAGE_ACCOUNT_MASTERCLIENT_FILES_MASTERCLIENTS_SHARE;

  let credentials;

  if (!accountKey) {
    credentials = new DefaultAzureCredential();
  } else {
    // Only for local development and testing
    credentials = new StorageSharedKeyCredential(accountName, accountKey);
  }
  return new ShareClient(`https://${accountName}.file.core.windows.net/${masterClientShare}`, credentials, {fileRequestIntent: 'backup'});
}
