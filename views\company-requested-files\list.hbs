<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <h4 class="mt-3">{{title}}</h4>
                    <br>
                    <div class="row">
                        <div class="col-md-12">
                            <form method='GET'>
                                <div class="row mb-3">
                                    <div class="col-md-3">
                                        <label for="filter_company">Company Name</label>
                                        <input class='form-control' type='text' name='filter_company' id='filter_company'/>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="filter_company_code">Incorporation Code</label>
                                        <input class='form-control' type='text' name='filter_company_code'
                                               id='filter_company_code'/>
                                    </div>
                                    <div class="col-md-3 input-div">
                                        <input type='SUBMIT' class='btn btn-light waves-effect' value='Search'/>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <br>
                    <h4 class="mt-3">Company Information Request</h4>
                    <div class="table-responsive">
                        <table id="horizontal-datatable-company" class="table table-striped mb-0 w-100 nowrap">
                            <thead>
                                <tr>
                                    <th class="header-25-percent">Entity Name</th>
                                    <th class="header-25-percent">Entity Code</th>
                                    <th class="header-25-percent">Created at</th>
                                    <th class="header-25-percent">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each results}}
                                    <tr>
                                        <td >
                                            {{companyName}}
                                        </td>
                                        <td>{{companyCode}} </td>
                                        <td> {{formatDate createdAt "MM/DD/YYYY"}}</td>
                                        <td class="text-center">
                                            <a href="/masterclients/{{masterClientCode}}/company-files/{{_id}}"
                                                class="btn btn-primary waves-effect waves-light width-xl">
                                                Update my file
                                            </a>
                                        </td>
                                    </tr>
                                {{else}}
                                <tr>
                                    <td colspan="4" class="text-center font-italic">
                                        There are no requested files
                                    </td>
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                    </div>
                    <h4 class="mt-3">Substance Information Request</h4>
                    <div class="table-responsive">
                        <table id="horizontal-datatable-substance" class="table table-striped mb-0 w-100 nowrap">
                            <thead>
                                <tr>
                                    <th class="header-25-percent">Entity Name</th>
                                    <th class="header-25-percent">Financial period</th>
                                    <th class="header-25-percent">Request deadline</th>
                                    <th class="header-25-percent">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each substanceResults}}
                                    <tr>
                                        <td >
                                            {{company_data.name}}
                                        </td>
                                        <td> {{formatDate entity_details.financial_period_begins "MM/DD/YYYY"}} - {{formatDate entity_details.financial_period_ends "MM/DD/YYYY"}}</td>
                                        <td> {{formatDate deadline_at "MM/DD/YYYY"}} </td>
                                        <td class="text-center">
                                            <a href="/masterclients/{{company_data.masterclientcode}}/company-files/substance/{{company}}/{{_id}}"
                                                class="btn btn-primary waves-effect waves-light width-xl">
                                                Provide information
                                            </a>
                                        </td>
                                    </tr>
                                {{else}}
                                <tr>
                                    <td colspan="4" class="text-center font-italic">
                                        There are no requested files
                                    </td>
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                    </div>
                    <h4 class="mt-3">Financial Report Information Request</h4>
                    <div class="table-responsive">
                        <table id="horizontal-datatable-financial-reports" class="table table-striped mb-0 w-100 nowrap">
                            <thead>
                                <tr>
                                    <th class="header-25-percent">Entity Name</th>
                                    <th class="header-25-percent">Fiscal period</th>
                                    <th class="header-25-percent">Request deadline</th>
                                    <th class="header-25-percent">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each financialReportResults}}
                                    <tr>
                                        <td >
                                            {{companyData.name}}
                                        </td>
                                        <td> {{formatDate financialPeriod.start "MM/DD/YYYY"}} - {{formatDate financialPeriod.end "MM/DD/YYYY"}}</td>
                                        <td> {{formatDate requestedInformation.details.[0].deadlineAt "MM/DD/YYYY"}} </td>
                                        <td class="text-center">
                                            <a href="/masterclients/{{companyData.masterclientcode}}/company-files/financial-reports/{{companyData.code}}/{{_id}}"
                                                class="btn btn-primary waves-effect waves-light width-xl">
                                                Provide information
                                            </a>
                                        </td>
                                    </tr>
                                {{else}}
                                <tr>
                                    <td colspan="4" class="text-center font-italic">
                                        There are no requested files
                                    </td>
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                    </div>
                    <br>
                    <a href="/masterclients/{{masterClientCode}}"
                        class="btn btn-secondary waves-effect waves-light width-xl mt-3 mr-2">Back</a>
                </div>
            </div>
        </div>
    </div>
</main>
{{>messages/message-modal }}
<script type="text/javascript" src="/views-js/company-requested-files/list.js"></script>
