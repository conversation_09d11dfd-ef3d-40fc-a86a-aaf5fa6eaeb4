exports.validate = async function (entry) {

    let errors = [];
    const entityDetails = entry.entity_details;

    if(parseFloat(entry.version) < 5){
        return errors;
    }


    if (entityDetails.isSameBusinessAddress === undefined) {
        errors.push({ msg: "Select YES/NO if business address is same as registered address.", field: "isSameBusinessAddress" })
    }

    if (entityDetails.hasUltimateParents === undefined) {
        errors.push({ msg: "Select YES/NO if entity have an ultimate parent.", field: "hasUltimateParents" })
    }

    if (entityDetails.hasImmediateParents === undefined) {
        errors.push({ msg: "Select YES/NO if entity have an immediate parent.", field: "hasImmediateParents" })
    }


    if (!entityDetails.totalAnnualGrossCurrency) {
        errors.push({ msg: "Provide total annual gross currency", field: "totalAnnualGross" })
    }

    if (entityDetails.totalAnnualGross && entityDetails.totalAnnualGross < 0){
        errors.push({ msg: "If you enter a value for Gross total annual income, please make sure it's a non-negative value", field: "totalAnnualGross" })
    }

    if (entityDetails.isSameBusinessAddress === false && 
        (!entityDetails.businessAddress.address_line1 || !entityDetails.businessAddress.country)){
        errors.push({ msg: "Provide business address details", field: "businessAddress" })
    }

    if (entityDetails.isSameMNEName === true && !entityDetails.nameOfMNEGroup ){
        errors.push({ msg: "Provide name of MNE group", field: "nameOfMNEGroup" })
    }

    if (entityDetails.hasUltimateParents === true && entityDetails.ultimateParents.length === 0){
        errors.push({ msg: "Please add at least one ultimate parents", field: "hasUltimateParents" })
    }

    if (entityDetails.hasImmediateParents === true && entityDetails.immediateParents.length === 0) {
        errors.push({ msg: "Please add at least one  immediate parents", field: "CoreIncomeGeneratingOutsourced" })
    }


    return errors;
}
