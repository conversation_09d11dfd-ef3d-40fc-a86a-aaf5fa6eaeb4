<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <link type="text/css" rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" />
    <script src="https://code.jquery.com/jquery-3.1.1.min.js"></script>
    <script src="https://code.jquery.com/jquery-migrate-git.min.js"></script>

    <!-- emojis 😁! -->
    <meta http-equiv="X-UA-Compatible" content="IE=Edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="svg4everybody.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/emojione/2.2.7/lib/js/emojione.min.js"></script>

    <link type="text/css" rel="stylesheet" href="./password.min.css" />
    <script src="./password.min.js"></script>
    <script type="text/javascript">
        jQuery(document).ready(function($) {
          // emojis 😁! See #password5 for more details
          emojione.imageType = 'svg';
          emojione.sprints = true;
          emojione.imagePathSVGSprites = 'https://github.com/Ranks/emojione/raw/master/assets/sprites/emojione.sprites.svg';

          // Showing the progress bar since the first moment.
          $('#password').password({
            animate: false
            // Check out the readme or directly jquery.password.js
            // for a detailed list of properties.
          });

          // Default behavior
          $('#default').password();

          // Linked to username input
          $('#linked').password({
            username: '#username',
            showPercent: true
          });

          // Custom events (enables button on certain score)
          // Check the readme for a detailed list of events
          $('#submit').attr('disabled', true);
          $('#events').password().on('password.score', function (e, score) {
            if (score > 75) {
              $('#submit').removeAttr('disabled');
            } else {
              $('#submit').attr('disabled', true);
            }
          });

          // Change translations
          $('#translations').password({
            animate: false,
            minimumLength: 6,
            enterPass: emojione.unicodeToImage('Type your password 🔜'),
            shortPass: emojione.unicodeToImage('You can do it better, dude! 🤕'),
            badPass: emojione.unicodeToImage('Still needs improvement! 😷'),
            goodPass: emojione.unicodeToImage('Yeah! That\'s better! 👍'),
            strongPass: emojione.unicodeToImage('Yup, you made it 🙃'),
          });
        });
    </script>

    <style type="text/css">
      label {
        display: block;
      }
      input {
        width: 300px;
      }
      .container {
        margin-top: 30px;
      }
      @media (min-width: 600px) {
        #main {
          width: 60%;
        }
      }
      .emojione {
        width: 20px;
        height: 20px;
      }

      /* better progress bar styles for the bootstrap demo */
      .pass-strength-visible input.form-control,
      input.form-control:focus {
        border-bottom-right-radius: 0;
        border-bottom-left-radius: 0;
      }

      .pass-strength-visible .pass-graybar,
      .pass-strength-visible .pass-colorbar,
      .form-control:focus + .pass-wrapper .pass-graybar,
      .form-control:focus + .pass-wrapper .pass-colorbar {
        border-bottom-right-radius: 4px;
        border-bottom-left-radius: 4px;
      }
    </style>
</head>
<body>
    <div class="container">
        <h1>
            <a href="https://github.com/elboletaire/password-strength-meter">
                Password Strength Meter - jQuery plugin
            </a>
        </h1>
        <p>
            <a href="https://github.com/elboletaire/password-strength-meter">Password Strength Meter</a>
            is a jQuery plugin that allows you to easily bind a progress bar showing the strength of a password to
            your inputs with multiple options.
        </p>
        <p>
            <a href="https://github.com/elboletaire/password-strength-meter">Check the docs for more info.</a>
        </p>
        <form action="#">
            <fieldset>
                <legend>Just the password input, with the progress shown since the very first moment</legend>
                <div class="form-group">
                    <label for="password">
                        Password
                    </label>
                    <input id="password" type="password" class="form-control" />
                </div>
            </fieldset>
            <fieldset>
                <legend>Default behavior (progress bar animated on focus/blur)</legend>
                <div class="form-group">
                    <label for="default">
                        Password
                    </label>
                    <input id="default" type="password" class="form-control" />
                </div>
            </fieldset>
            <fieldset>
                <legend>Linked to username input (password cannot contain username)</legend>
                <div class="form-group">
                    <label for="username">
                        Username
                    </label>
                    <input id="username" type="text" placeholder="John Doe" class="form-control" />
                </div>
                <div class="form-group">
                    <label for="linked">
                        Password
                    </label>
                    <input id="linked" type="password" class="form-control" />
                </div>
            </fieldset>
            <fieldset>
                <legend>Change translations</legend>
                <div class="form-group">
                    <label for="translations">
                        Password
                    </label>
                    <input id="translations" type="password" class="form-control" />
                </div>
            </fieldset>
            <fieldset>
                <legend>Custom events (button enabled when score > 75%)</legend>
                <div class="form-group">
                    <label for="events">
                        Password
                    </label>
                    <input id="events" type="password" class="form-control" />
                </div>
                <button id="submit" onclick="return false" class="btn btn-info">Send</button>
            </fieldset>
        </form>
    </div>
    <script>
      (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
      (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
      m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
      })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

      ga('create', 'UA-70920203-4', 'auto');
      ga('send', 'pageview');
    </script>
</body>
</html>
