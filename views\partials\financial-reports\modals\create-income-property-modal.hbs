<div class="modal fade" id="newIncomePropertyModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable " role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 id="modal-asset-title" class="modal-title">New Property</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div id="modal-asset-body" class="modal-body p-3">
                <form action="" class="form" id="newIncomeExpensePropertyForm no-border p-1">
                   
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="description-property">Description*</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <input type="text" name="description" id="description-property" class="form-control"
                                       required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="value-amount-property">Amount*</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text prepend-currency"> {{report.currency}}</span>
                                </div>
                                <input type="text" id="value-amount-property" class="form-control autonumber text-right" data-a-sep="," required
                                    data-m-dec="2" name="currentAmount">
                            </div>
                        </div>
                    </div>

                </form>
            </div>
            <div class="modal-footer justify-content-end">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>

                <button class="btn solid royal-blue" type="button" id="submitIncomePropertyBtn">Save</button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="/views-js/partials/financial-reports/modals/create-income-property-modal.js"></script>
