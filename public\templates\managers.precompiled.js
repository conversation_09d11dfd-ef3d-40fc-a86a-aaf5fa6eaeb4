(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['managers'] = template({"1":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "\r\n"
    + ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"managers") : stack1),{"name":"each","hash":{},"fn":container.program(2, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":3,"column":4},"end":{"line":13,"column":13}}})) != null ? stack1 : "");
},"2":function(container,depth0,helpers,partials,data) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "        <tr>\r\n            <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"first_name") || (depth0 != null ? lookupProperty(depth0,"first_name") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"first_name","hash":{},"data":data,"loc":{"start":{"line":5,"column":16},"end":{"line":5,"column":30}}}) : helper)))
    + " "
    + alias4(((helper = (helper = lookupProperty(helpers,"middle_name") || (depth0 != null ? lookupProperty(depth0,"middle_name") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"middle_name","hash":{},"data":data,"loc":{"start":{"line":5,"column":31},"end":{"line":5,"column":46}}}) : helper)))
    + " "
    + alias4(((helper = (helper = lookupProperty(helpers,"last_name") || (depth0 != null ? lookupProperty(depth0,"last_name") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"last_name","hash":{},"data":data,"loc":{"start":{"line":5,"column":47},"end":{"line":5,"column":60}}}) : helper)))
    + "</td>\r\n            <td>"
    + alias4((lookupProperty(helpers,"formatDate")||(depth0 && lookupProperty(depth0,"formatDate"))||alias2).call(alias1,(depth0 != null ? lookupProperty(depth0,"date_of_birth") : depth0),{"name":"formatDate","hash":{},"data":data,"loc":{"start":{"line":6,"column":16},"end":{"line":6,"column":44}}}))
    + "</td>\r\n            <td>"
    + alias4((lookupProperty(helpers,"formatAddress")||(depth0 && lookupProperty(depth0,"formatAddress"))||alias2).call(alias1,depth0,{"name":"formatAddress","hash":{},"data":data,"loc":{"start":{"line":7,"column":16},"end":{"line":7,"column":38}}}))
    + "</td>\r\n            <td>"
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,(depth0 != null ? lookupProperty(depth0,"resident_in_bvi") : depth0),{"name":"if","hash":{},"fn":container.program(3, data, 0),"inverse":container.program(5, data, 0),"data":data,"loc":{"start":{"line":8,"column":16},"end":{"line":8,"column":59}}})) != null ? stack1 : "")
    + "</td>\r\n            <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"position_held") || (depth0 != null ? lookupProperty(depth0,"position_held") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"position_held","hash":{},"data":data,"loc":{"start":{"line":9,"column":16},"end":{"line":9,"column":33}}}) : helper)))
    + "</td>\r\n            <td class=\"text-center\"><i class=\"fa fa-pencil  btn btn-sm royal-blue solid  editmanager\" data-id=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":10,"column":111},"end":{"line":10,"column":118}}}) : helper)))
    + "\"></i></td>\r\n            <td class=\"text-center\"><i class=\"fa fa-times  btn btn-sm btn-danger deletemanager\"  data-id=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":11,"column":106},"end":{"line":11,"column":113}}}) : helper)))
    + "\"></i></td>\r\n        </tr>\r\n";
},"3":function(container,depth0,helpers,partials,data) {
    return "Yes";
},"5":function(container,depth0,helpers,partials,data) {
    return "No";
},"7":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"managers") : stack1),{"name":"each","hash":{},"fn":container.program(8, data, 0),"inverse":container.program(13, data, 0),"data":data,"loc":{"start":{"line":15,"column":4},"end":{"line":36,"column":13}}})) != null ? stack1 : "");
},"8":function(container,depth0,helpers,partials,data) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <tr>\r\n        <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"first_name") || (depth0 != null ? lookupProperty(depth0,"first_name") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"first_name","hash":{},"data":data,"loc":{"start":{"line":17,"column":12},"end":{"line":17,"column":26}}}) : helper)))
    + " "
    + alias4(((helper = (helper = lookupProperty(helpers,"middle_name") || (depth0 != null ? lookupProperty(depth0,"middle_name") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"middle_name","hash":{},"data":data,"loc":{"start":{"line":17,"column":27},"end":{"line":17,"column":42}}}) : helper)))
    + " "
    + alias4(((helper = (helper = lookupProperty(helpers,"last_name") || (depth0 != null ? lookupProperty(depth0,"last_name") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"last_name","hash":{},"data":data,"loc":{"start":{"line":17,"column":43},"end":{"line":17,"column":56}}}) : helper)))
    + "</td>\r\n        <td>"
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,(depth0 != null ? lookupProperty(depth0,"is_corporate_director") : depth0),{"name":"if","hash":{},"fn":container.program(9, data, 0),"inverse":container.program(11, data, 0),"data":data,"loc":{"start":{"line":18,"column":12},"end":{"line":18,"column":64}}})) != null ? stack1 : "")
    + " </td>\r\n        <td>"
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,(depth0 != null ? lookupProperty(depth0,"resident_in_bvi") : depth0),{"name":"if","hash":{},"fn":container.program(3, data, 0),"inverse":container.program(5, data, 0),"data":data,"loc":{"start":{"line":19,"column":12},"end":{"line":19,"column":55}}})) != null ? stack1 : "")
    + "</td>\r\n        <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"position_held") || (depth0 != null ? lookupProperty(depth0,"position_held") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"position_held","hash":{},"data":data,"loc":{"start":{"line":20,"column":12},"end":{"line":20,"column":29}}}) : helper)))
    + "</td>\r\n        <td class=\"justify-content-center d-flex d-flex-inline\">\r\n            <button type=\"button\" class=\"btn btn-sm royal-blue solid mr-1 editmanager\" data-id=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":22,"column":96},"end":{"line":22,"column":103}}}) : helper)))
    + "\">\r\n                <i class=\"fa fa-pencil\"></i>\r\n            </button>\r\n            <button type=\"button\" class=\"btn btn-sm btn-danger deletemanager\"  data-id=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":25,"column":88},"end":{"line":25,"column":95}}}) : helper)))
    + "\">\r\n                <i class=\"fa fa-times\"></i>\r\n            </button>\r\n        </td>\r\n    </tr>\r\n";
},"9":function(container,depth0,helpers,partials,data) {
    return "Yes ";
},"11":function(container,depth0,helpers,partials,data) {
    return " No ";
},"13":function(container,depth0,helpers,partials,data) {
    return "        <tr>\r\n            <td colspan=\"5\">\r\n                No directors found\r\n            </td>\r\n        </tr>>\r\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||container.hooks.helperMissing).call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"version") : depth0),"<",5,{"name":"ifCond","hash":{},"fn":container.program(1, data, 0),"inverse":container.program(7, data, 0),"data":data,"loc":{"start":{"line":1,"column":0},"end":{"line":37,"column":11}}})) != null ? stack1 : "");
},"useData":true});
})();