Dropzone.autoDiscover = false;

let rowindex;
let button = '';
let fileType = '';
let row;
let fileGroup = '';

let fileTypeId = '';
let incorpId = '';
let mcc2 = '';
let relationId = '';
$(async function () {
    let field = '';
    let name = '';
    const csrfToken = $("input[name='csrf-token']").val()
    
    let tempDropzone = new Dropzone('#uploadModalTempForm', {
        url: '/upload-files',
        acceptedFiles: 'application/pdf',
        autoProcessQueue: true,
        parallelUploads: 3,
        maxFiles: 3,
        maxFilesize: 5,
        paramName: function () {
            return 'fileUploaded';
        },
        uploadMultiple: true,
        headers: {
            'x-csrf-token': csrfToken
        },
        init: function () {
            this.on('processing', function () {
                this.options.url = '/masterclients/' + mcc2 + '/incorporate-company/relations/upload-document';
            });
            this.on('success', function () {
                refreshTempUploadedFiles(mcc2, incorpId, relationId, fileGroup, fileTypeId, fileType);

                let $fileUpload = $("#" + button[0].id);
                $fileUpload.text('Modify');
                $fileUpload.css({
                    'background-color': '#0AC292',
                    'border-color': '#0AC292',
                });
            });
            this.on('sending', function (file, xhr, formData) {
                if (!formData.has('fileName')) {
                    formData.append('fileName', field);
                }
                if (!formData.has('relationId')) {
                    formData.append('relationId', relationId);
                }
                if (!formData.has('fileTypeId')) {
                    formData.append('fileTypeId', fileTypeId);
                }
                if (!formData.has('fileGroup')) {
                    formData.append('fileGroup', fileGroup);
                }
                if (!formData.has('row')) {
                    formData.append('row', row);
                }
            });

            this.on('errormultiple', function (files, response) {
            });

            this.on('maxfilesexceeded', function (file) {
            });

            this.on('resetFiles', function () {
                if (this.files.length !== 0) {
                    for (i = 0; i < this.files.length; i++) {
                        this.files[i].previewElement.remove();
                    }
                }
                $('#maxUploadTemp').text(this.options.maxFiles);
            });
        },
    });

    $('#upload-temp-modal').on('show.bs.modal', function (event) {
        button = $(event.relatedTarget); // Button that triggered the modal
        name = button.data('field'); //name of the file
        field = name.replace(/[\s\’\'\/\(\)]/g, ''); //formatted name with no special chars
        fileTypeId = button.data('id'); // _id
        incorpId = button.data('incorporation-id'); // _id
        mcc2 = button.data('mcc');

        relationId = button.data('relation-id');
        row = button.data('row'); // row index
        fileGroup = button.data('file-group');
        fileType = button.data('file-type');
        $('#upload-modal-temp-label').text(name);
        refreshTempUploadedFiles(mcc2, incorpId, relationId, fileGroup, fileTypeId, fileType);
        var modal = $(this);
        const objDZ = Dropzone.forElement('#uploadModalTempForm');
        objDZ.emit('resetFiles');
    });

    $('#upload-temp-modal').on('hide.bs.modal', function (event) {
        var objDZ = Dropzone.forElement('#uploadModalTempForm');
        objDZ.removeAllFiles(true);
        $('#uploadedTempFiles').html('');
    });
});

$(document).on('click', '.deleteRelationFile', async function () {
    await deleteRelationFile($(this).attr('data-mcc'), $(this).attr('data-incorporation-id'), $(this).attr('data-id'), $(this).attr('data-group'), $(this).attr('data-field'), $(this).attr('data-field-id'), $(this).attr('data-blobName'))
})

async function deleteRelationFile(mcc, incorporationId, relationId, fileGroup, rowId, fileId, blobName) {
    $.ajax({
        type: 'DELETE',
        url: '/masterclients/' + mcc + '/incorporate-company/' + incorporationId + '/relations/files',
        data: {
            relationId: relationId,
            group: fileGroup,
            row: row,
            rowId: rowId,
            fileId: fileId,
            blobName: blobName,
            type: fileType,
            deleteTempFiles: true
        },
        success: function (res) {
            if (res.result) {
                refreshTempUploadedFiles(mcc, incorporationId, relationId, fileGroup, rowId, fileType);
                const objDZ = Dropzone.forElement('#uploadModalTempForm');
                const index = objDZ.files.findIndex((file) => file.blobName === blobName);
                if (index > -1) {
                    objDZ.files[index].pop();
                }

            }
        },
        dataType: 'json',
    });
    return false;
}

function refreshTempUploadedFiles(mcc, incorporationId, relationId, fileGroup, fileTypeId, fileType) {
   
    $.get(
        '/masterclients/' + mcc + '/incorporate-company/' + incorporationId + '/relations/files',
        { relationId: relationId, fileGroup: fileGroup, getTempFiles: true, type: fileType, fileTypeId: fileTypeId, row: row },
        function (data) {
            let template = Handlebars.templates.uploadrelationfiles;
            let d = {
                id: relationId,
                incorporationId: incorporationId,
                masterClientCode: mcc,
                files: data.files ? data.files : [],
                group: fileGroup,
                row: row,
                fileTypeId: fileTypeId,
            };
            let html = template(d);
            $('#uploadedTempFiles').html(html);

            if (data.length === 0) {
                let $fileUpload = $("#" + button[0].id);
                $fileUpload.text('Upload');
                $fileUpload.css({
                    'background-color': '#0081b4',
                    'border-color': '#0081b4',
                });
            }
        }
    );
}