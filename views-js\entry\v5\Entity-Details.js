let showWarningFinancialPeriod = true;

$(document).ready(function () {
    showUltimateParentFields();
    showImmediateParentFields();
});

$('#hasImmediateParentsYes').click(function () {
    showImmediateParentFields()
})

$('#hasImmediateParentsNo').click(function () {
    showImmediateParentFields()
})


$('#hasUltimateParentsNo').click(function () {
    showUltimateParentFields()
})

$('#hasUltimateParentsYes').click(function () {
    showUltimateParentFields()
})

$('#isSameBusinessAddressYes').click(function () {
    showBusinessAddressFields()
})

$('#isSameBusinessAddressNo').click(function () {
    showBusinessAddressFields()
})


function showBusinessAddressFields() {
    const isSameBusinessAddress = $('input[name=isSameBusinessAddress]:checked').val();
    
    if (isSameBusinessAddress === 'Yes' || isSameBusinessAddress === 'No' ) {
        const countrySelect2 = $('#businessAddressCountry').select2();
        if(isSameBusinessAddress === 'Yes'){
            $("#businessAddress1").val('Trident Chambers, Wickhams Cay 1').prop('disabled', true);
            $("#businessAddress2").val('').prop('disabled', true);
            countrySelect2.prop('disabled', true);
            countrySelect2.val('VGB').trigger('change');

        }
        else{
            $("#businessAddress1").val('').prop('disabled', false);;
            $("#businessAddress2").val('').prop('disabled', false);
            countrySelect2.prop('disabled', false);
            countrySelect2.val('').trigger('change');
        }
        $("#showBusinessAddressRows").show(200);
    } else {
        $("#showBusinessAddressRows").hide();
    }

}

function showUltimateParentFields() {
    const hasUltimateParents = $('input[name=hasUltimateParents]:checked').val()
    if (hasUltimateParents === 'Yes') {
        $("#showUltimateParentRows").show(200);
    } else {
        $("#showUltimateParentRows").hide();
    }

}

function showImmediateParentFields() {
    const hasImmediateParents = $('input[name=hasImmediateParents]:checked').val()
    if (hasImmediateParents === 'Yes') {
        $("#showImmediateParentRows").show(200);
    } else {
        $("#showImmediateParentRows").hide();
    }

}