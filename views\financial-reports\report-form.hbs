<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class='contour'>
                    <div class="row">
                        <div class="col-md-12 mb-1">
                            <h2 class="mb-0" id="financialReportTitle">
                                {{title}}
                            </h2>
                            {{#if report.financialPeriod.start}}
                            <span class="mb-2">
                                <strong>
                                    Financial Period from <span class="finPrdStartText">{{formatDate report.financialPeriod.start "DD MMMM YYYY"}}</span>
                                    to <span class="finPrdEndText">{{formatDate report.financialPeriod.end "DD MMMM YYYY"}}</span>
                                </strong>
                            </span>
                            {{/if}}
                        </div>
                    </div>

                    <form method="POST" class='enquiry px-1' autocomplete="off" id="reportForm">
                        <input type="text" hidden name="csrf-token" value="{{csrfToken}}">
                        <div class="container-fluid" >

                            <div id="step-{{ACCOUNTING_FORM_STEPS.DETAILS}}">
                                <div class="row">
                                    {{> financial-reports/report-details report=report currencies=currencies}}
                                </div>

                                <div class="row">
                                    <div class="col-md-2">
                                        <div class="form-group mb-2">
                                            <a type="button"
                                                href="/masterclients/{{masterClientCode}}/financial-reports/companies/{{companyCode}}"
                                                class="btn btn-secondary waves-effect waves-light width-xl">Go
                                                back</a>
                                        </div>
                                    </div>

                                    <div class="col-md-10 text-right">
                                        <button type="button" data-step="{{ACCOUNTING_FORM_STEPS.DETAILS}}" data-action="NEXT"
                                            data-id="{{ report._id }}" data-save="true"
                                            class="btn btn-primary waves-effect waves-light width-xl nextBtn">
                                            Continue
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!--SERVICE TYPE 2 - CASH TRANSACTIONS -->
                            <div class="hide-element" id="step-{{ACCOUNTING_FORM_STEPS.CASH}}">
                                <div class="row">
                                    {{> financial-reports/cash-transactions report=report currencies=currencies}}
                                </div>

                                <div class="row">
                                    <div class="col-md-2">
                                        <div class="form-group mb-2">
                                            <button type="button"
                                                class="btn btn-secondary waves-effect waves-light width-xl backBtn"
                                                data-step="{{ACCOUNTING_FORM_STEPS.CASH}}" data-action="BACK" data-id="{{ report._id }}">Go
                                                back
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-10 text-right">

                                        <button type="button" data-step="{{ACCOUNTING_FORM_STEPS.CASH}}" data-action="NEXT"
                                            data-id="{{ report._id }}"
                                            class="btn btn-primary waves-effect waves-light width-xl nextBtn">
                                            Continue
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!--SERVICE TYPE 2 - ASSETS -->
                            <div class="hide-element" id="step-{{ACCOUNTING_FORM_STEPS.ASSETS}}">
                                <div class="row">
                                    {{> financial-reports/assets report=report}}
                                </div>
                            
                                <div class="row">
                                    <div class="col-md-2">
                                        <div class="form-group mb-2">
                                            <button type="button" class="btn btn-secondary waves-effect waves-light width-xl backBtn" data-step="{{ACCOUNTING_FORM_STEPS.ASSETS}}"
                                                data-action="BACK" data-id="{{ report._id }}">Go
                                                back
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-10 text-right">
                            
                                        <button type="button" data-step="{{ACCOUNTING_FORM_STEPS.ASSETS}}" data-action="NEXT" data-id="{{ report._id }}"
                                            class="btn btn-primary waves-effect waves-light width-xl nextBtn nextBtnAssets">
                                            Continue
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <!--SERVICE TYPE 2 - LIABILITIES -->
                            <div class="hide-element" id="step-{{ACCOUNTING_FORM_STEPS.LIABILITIES}}">
                                <div class="row">
                                    {{> financial-reports/liabilities report=report}}
                                </div>
                            
                                <div class="row">
                                    <div class="col-md-2">
                                        <div class="form-group mb-2">
                                            <button type="button" class="btn btn-secondary waves-effect waves-light width-xl backBtn" data-step="{{ACCOUNTING_FORM_STEPS.LIABILITIES}}"
                                                data-action="BACK" data-id="{{ report._id }}">Go
                                                back
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-10 text-right">
                            
                                        <button type="button" data-step="{{ACCOUNTING_FORM_STEPS.LIABILITIES}}" data-action="NEXT" data-id="{{ report._id }}"
                                            class="btn btn-primary waves-effect waves-light width-xl nextBtn nextBtnLiabilities">
                                            Continue
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- SERVICE TYPE 2 -SUMMARY -->
                            <div class="hide-element" id="step-{{ACCOUNTING_FORM_STEPS.SUMMARY}}">
                                <div class="row">
                                    {{> financial-reports/report-summary report=report}}
                                </div>
                            
                                <div class="row">
                                    <div class="col-md-2">
                                        <div class="form-group mb-2">
                                            <button type="button" class="btn btn-secondary waves-effect waves-light width-xl backBtn" data-step="{{ACCOUNTING_FORM_STEPS.SUMMARY}}"
                                                data-action="BACK" data-id="{{ report._id }}">Go
                                                back
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-10 text-right">
                            
                                        <button type="button" data-step="{{ACCOUNTING_FORM_STEPS.SUMMARY}}" data-action="NEXT" data-id="{{ report._id }}"
                                            class="btn btn-primary waves-effect waves-light width-xl nextBtn">
                                            Continue
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- COMPLETE ANNUAL RETURN FLOW STEPS START-->
                            <div class="hide-element" id="step-{{ACCOUNTING_FORM_STEPS.COMP_INCOME_EXPENSES}}">
                                <div class="row">
                                    {{> financial-reports/complete-income-expenses report=report}}
                                </div>
                            
                                <div class="row">
                                    <div class="col-md-2">
                                        <div class="form-group mb-2">
                                            <button type="button" class="btn btn-secondary waves-effect waves-light width-xl backBtn" data-step="{{ACCOUNTING_FORM_STEPS.COMP_INCOME_EXPENSES}}"
                                                data-step-flow="complete-annual"
                                                data-action="BACK" data-id="{{ report._id }}">Go
                                                back
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-10 text-right">
                            
                                        <button type="button" data-step="{{ACCOUNTING_FORM_STEPS.COMP_INCOME_EXPENSES}}" data-action="NEXT" data-id="{{ report._id }}"
                                            data-step-flow="complete-annual"
                                            class="btn btn-primary waves-effect waves-light width-xl nextBtn">
                                            Continue
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="hide-element" id="step-{{ACCOUNTING_FORM_STEPS.COMP_ASSETS_LBT}}">
                                <div class="row">
                                    {{> financial-reports/complete-assets-liabilities report=report}}
                                </div>
                            
                                <div class="row">
                                    <div class="col-md-2">
                                        <div class="form-group mb-2">
                                            <button type="button" class="btn btn-secondary waves-effect waves-light width-xl backBtn" data-step="{{ACCOUNTING_FORM_STEPS.COMP_ASSETS_LBT}}"
                                                data-step-flow="complete-annual" data-action="BACK" data-id="{{ report._id }}">Go
                                                back
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-10 text-right">
                            
                                        <button type="button" data-step="{{ACCOUNTING_FORM_STEPS.COMP_ASSETS_LBT}}" data-action="NEXT" data-id="{{ report._id }}"
                                            data-step-flow="complete-annual" class="btn btn-primary waves-effect waves-light width-xl nextBtn">
                                            Continue
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <!-- COMPLETE ANNUAL RETURN FLOW STEPS END -->

                            <div class="hide-element" id="step-{{ACCOUNTING_FORM_STEPS.DECLARATION}}">
                                <div class="row">
                                    {{> financial-reports/declaration report=report}}
                                </div>

                                <div class="row">
                                    <div class="col-md-2">
                                        <div class="form-group mb-2">
                                            <button type="button"
                                                class="btn btn-secondary waves-effect waves-light width-xl backBtn"
                                                data-step="{{ACCOUNTING_FORM_STEPS.DECLARATION}}" data-action="BACK" data-id="{{ report._id }}">Go
                                                back
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-10 text-right">

                                        <button type="button" data-step="{{ACCOUNTING_FORM_STEPS.DECLARATION}}" data-action="COMPLETE"
                                            data-id="{{ report._id }}" id="submitAccountingFormBtn" 
                                            class="btn btn-primary waves-effect waves-light width-xl nextBtn  {{#if invalidSubmitPeriod}} d-custom-none  {{/if}}" 
                                            {{#if invalidSubmitPeriod}} disabled  {{/if}}
                                            >
                                            Submit
                                        </button>
                                        <div id="invalidSubmitInfo" class="flex inline-flex  {{#unless invalidSubmitPeriod}} d-custom-none {{/unless}}">
                                            <button type="button" data-step="{{ACCOUNTING_FORM_STEPS.DECLARATION}}" data-action="SAVE-AND-CLOSE"
                                                data-id="{{ report._id }}" id="saveAndCloseAccountingFormBtn" 
                                                class="btn btn-primary waves-effect waves-light width-xl nextBtn  {{#unless invalidSubmitPeriod}} d-custom-none  {{/unless}}"
                                                {{#unless invalidSubmitPeriod}} disabled {{/unless}}>
                                                Save and close
                                            </button>
                                        </div>

                                    </div>
                                </div>

                                <div id="invalidSubmitInfoText" class="row mt-2  {{#unless invalidSubmitPeriod}} d-custom-none {{/unless}}" >
                                    <div class="col-12 justify-content-center">
                                        <p class="text-center">
                                            <b>The financial period has not yet concluded, as the end date is forthcoming. Submission is currently not possible.</b>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            <div class="hide-element" id="step-{{ACCOUNTING_FORM_STEPS.CONFIRMATION}}">
                                <div class="row">
                                    {{> financial-reports/confirmation report=report}}
                                </div>
                                <div class="col-12">
                                    <div class="row justify-content-between">
                                        <div class="col-md-2">
                                            <div class="form-group mb-2">
                                                <a type="button"
                                                    class="btn btn-secondary waves-effect waves-light width-xl"
                                                    href="/masterclients/{{masterClientCode}}/financial-reports">Back
                                                    to
                                                    dashboard
                                                </a>
                                            </div>
                                        </div>
                                        <div class="col-md-8">
                                            <div class="row justify-content-end">
                                                <a href="/masterclients/{{masterClientCode}}/financial-reports/companies/{{companyCode}}/{{report._id}}/report.pdf"
                                                    class="btn solid royal-blue width-xl mr-2 downloadPDFStep10"
                                                    target="_blank">Download PDF</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</main>
{{>financial-reports/modals/upload-file-modal}}
{{>financial-reports/modals/tooltip-modal}}
{{>financial-reports/modals/create-cash-transaction-modal currencies=currencies}}
{{>financial-reports/modals/create-asset-modal report=report}}
{{> financial-reports/modals/create-liability-modal}}
{{> financial-reports/modals/pdf-downloads-modal}}

<script src="/javascripts/libs/jquery-mask-plugin/jquery.mask.min.js"></script>
<script src="/javascripts/libs/autonumeric/autoNumeric-min.js"></script>

<!-- Init js-->
<script src="/javascripts/form-masks.init.js"></script>
<script type='text/javascript' src='/templates/financial-reports/createincomeexpenserow.precompiled.js'></script>
<script type='text/javascript' src='/templates/financial-reports/createpropertyrow.precompiled.js'></script>
<script type='text/javascript' src='/templates/financial-reports/createassetrow.precompiled.js'></script>
<script type='text/javascript' src='/templates/financial-reports/createbankaccountrow.precompiled.js'></script>
<script type='text/javascript' src='/templates/financial-reports/createbankaccountassetrow.precompiled.js'></script>
<script type='text/javascript' src='/templates/financial-reports/createothervaluerow.precompiled.js'></script>
<script type='text/javascript' src='/templates/financial-reports/createotherdescriptionrow.precompiled.js'></script>
<script type='text/javascript' src='/templates/financial-reports/createotherassetrow.precompiled.js'></script>
<script type='text/javascript' src='/templates/financial-reports/createotherliabilityrow.precompiled.js'></script>
<script type='text/javascript' src='/views-js/financial-reports/report-form.js'></script>
<script type='text/javascript' src="/views-js/helpers/parseNumber.js"></script>