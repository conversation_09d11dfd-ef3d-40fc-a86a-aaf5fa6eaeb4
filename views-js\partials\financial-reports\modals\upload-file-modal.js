$("#uploadedFiles").html('');


let reportMcc = '';
let reportCompany = '';
Dropzone.autoDiscover = false;
let button;
$(async function () {
    let field = '';
    let id = '';
    let fieldName;
    let maxFileAllowed;

    const csrfToken = $("input[name='csrf-token']").val()
    const myDropZone = new Dropzone('#uploadModalForm', {
        url: '/',
        acceptedFiles: 'application/pdf',
        autoProcessQueue: true,
        parallelUploads: 3,
        maxFiles: 3,
        maxFilesize: 5,
        paramName: function () {
            return 'fileUploaded';
        },
        uploadMultiple: true,
        headers: {
            'x-csrf-token': csrfToken
        },
        init: function () {
            this.on('processing', function () {
                this.options.url = '/masterclients/' + reportMcc + '/financial-reports/companies/' + reportCompany + "/" + id + '/upload-files';
            });
            this.on('success', function () {
                refreshUploadedFiles(id, field);
            });
            this.on('sending', function (file, xhr, formData) {
                if (!formData.has('fieldName')) {
                    formData.append('fieldName', field);
                }
                formData.append('maxFileAllowed', maxFileAllowed);
            });

            this.on("error", function (file, error, xhr) {
                if (error.message != undefined) {
                    toastr["warning"](error.message, 'Error!');
                    this.defaultOptions.error(file, error.message);
                }
            });

            this.on('errormultiple', function (files, response) {
            });

            this.on('maxfilesexceeded', function (file) {
            });

            this.on('resetFiles', function () {
                if (this.files.length !== 0) {
                    for (i = 0; i < this.files.length; i++) {
                        this.files[i].previewElement.remove();
                    }
                    this.files.length = 0;
                }
                $('#maxUpload').text(this.options.maxFiles);
            });
        },
    });

    $('#upload-modal').on('show.bs.modal', function (event) {
        button = $(event.relatedTarget); // Button that triggered the modal
        fieldName = button.data('field'); //name of the file
        reportMcc = button.data('mcc');
        reportCompany = button.data('company-code');
        field = fieldName.replace(/[\s\’\'\/\(\)]/g, ''); //formatted name with no special chars
        id = button.data('report-id'); // _id
        const fileGroup = button.data('file-group');
        $('#upload-modal-file-label').text(fileGroup);
        // GET FILES UPLOADED PREVIOUSLY
        refreshUploadedFiles(id, field);
        const modal = $(this);
        const objDZ = Dropzone.forElement('#uploadModalForm');
        objDZ.emit('resetFiles');

        maxFileAllowed = button.data('file-max');
        objDZ.options.maxFiles = maxFileAllowed;
        $('#maxUpload').text(maxFileAllowed);
    });
});

async function deleteFinancialReportFile(reportId, fieldName, fileId) {
    $.ajax({
        type: 'DELETE',
        url: '/masterclients/' + reportMcc + '/financial-reports/companies/' + reportCompany + "/" + reportId + '/files',
        data: {
            fieldName: fieldName,
            fileId: fileId
        },
        success: function (res) {
            if (res.result) {
                refreshUploadedFiles(reportId, fieldName);
            }
        },
        dataType: 'json',
    });
    return false;
}

function downloadFinancialReportFile(reportId, fieldName, fileId) {
    const url = '/masterclients/' + reportMcc + '/financial-reports/companies/' + reportCompany + "/" + reportId + '/files/' + fileId + '/download';
    window.open(url, "_blank");
    return false;
}

function refreshUploadedFiles(id, field) {
    $.ajax({
        type: 'GET',
        url: '/masterclients/' + reportMcc + '/financial-reports/companies/' + reportCompany + '/' + id + '/search-files',
        data: {
            fieldName: field,
        },
        timeout: 5000,
        success: function (data) {
            let template = Handlebars.templates.uploadfinancialreportfiles;
            let d = {
                id: id,
                files: data.files ? data.files : [],
                group: field,
            };
            let html = template(d);
            $('#uploadedFiles').html(html);
            if (data.files && data.files.length > 0) {
                button.text('Modify');
                button.css({
                    'background-color': '#0AC292',
                    'border-color': '#0AC292',
                });

            }
            else {
                button.text('Upload');
                button.css({
                    'background-color': '#0081b4',
                    'border-color': '#0081b4',
                });
            }
        },
        error: function (res) {
            Swal.fire('Error', 'There was an error getting files', 'error');
        },
    });

}

$(document).on('click', '.downloadFinancialReportFile', function () {
    downloadFinancialReportFile($(this).attr('data-id'), $(this).attr('data-group'), $(this).attr('data-field'))
})

$(document).on('click', '.deleteFinancialReportFile', async function () {
    await deleteFinancialReportFile($(this).attr('data-id'), $(this).attr('data-group'), $(this).attr('data-field'))
})

$("#upload-modal").on("hidden.bs.modal", function () {
    $("#uploadedFiles").html('');
});