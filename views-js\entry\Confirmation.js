let financialPeriodEnd;
let fullNumber;
$(document).ready(function () {
    let completed = $('#submitForm');
    let pre2022 = $('label.mb-2[for="confirmed_payment"]');
    let prePaid= $('p:contains("Thank you for submitting the declaration. Click Download for the summary of the submission. You have no outstanding payment for this submission.")');
    let isReopened= $('#isReopened').val();
    let isScheduled= $('p:contains("Your submission is successfully scheduled and will be sent to Trident on")');;
    financialPeriodEnd = $('#financialPeriodEnds').val()
    realtionToEntity = $('#relation_to_entity').attr('value')
    $("#relation_to_entity").val(realtionToEntity);
    $('option[value="'+realtionToEntity+'"]').prop('selected', true)
    $("#select2-relation_to_entity-container").text(realtionToEntity);
    $("#select2-relation_to_entity-container").attr('title', realtionToEntity);
    toggleSpecifyEntityRelation();
    if (completed.length > 0) {

        $("#btnSubmitForm").on('click', function (event) {
            event.preventDefault();

            if (isScheduled.length == 0 && isReopened === "false" &&  prePaid.length > 0){

                let htmlBody = "";

                if (financialPeriodEnd && moment.utc(financialPeriodEnd).toDate() > moment.utc()){
                    htmlBody = "<p   class='sweet-alert-sm-text'> Note that this submission is prepaid therefore kindly ensure that " +
                            "information provided accurately reflects all activities for the reporting period. <br>" +
                            "Any amendment following submission will incur additional costs.<hr>" +
                            "The financial period is in the future, therefore we currently cannot submit your application.<br>" +
                            `Would you like us to automatically submit your submission on ${moment.utc(financialPeriodEnd).add(1, 'd').format('YYYY-MM-DD')}.` +
                            "</p>";
                }else{
                    htmlBody = "<span>Please note that this submission is prepaid therefore kindly ensure that " +
                            "information provided accurately reflects all activities for the reporting period. <br>" +
                            "Any amendment following submission will incur additional costs. </span>";
                }

                Swal.fire({
                    title: 'Warning!',
                    html:  htmlBody,
                    icon: 'warning',
                    backdrop: true,
                    showCancelButton: true,
                    confirmButtonColor: "#005C81",
                    confirmButtonText: 'Yes, submit!',
                    reverseButtons: true,
                }).then((result) => {
                    if (result.isConfirmed){
                        $("#submitForm").submit();
                    }
                })
            }else if(financialPeriodEnd && moment.utc(financialPeriodEnd).toDate() > moment.utc()){
                Swal.fire(
                        {
                            title: "Financial period is in the future.",
                            html: `<span  class='sweet-alert-md-text'> The financial period is in the future, therefore we currently cannot submit your application. <br>
                       Would you like us to automatically submit your submission on ${moment.utc(financialPeriodEnd).add(1, 'd').format('YYYY-MM-DD')}.  </span>`,
                            icon: "warning",
                            showCancelButton: true,
                            confirmButtonColor: "#3085d6",
                            cancelButtonText: "Cancel",
                            confirmButtonText: "Ok",
                            reverseButtons: true,
                        }).then(function (t) {
                    if (t.value) {
                        $("#submitForm").submit();
                    }
                });

            }else{
                $("#submitForm").submit();
            }

        });

    }


    if (completed.length === 0 && pre2022.length > 0 && prePaid.length == 0){
        toastr["info"]("Please contact the ES support team to settle the payment for your submission.", 'Info!');
    }
    
});


$("#relation_to_entity").on('change', ()=>{
    toggleSpecifyEntityRelation();
});

function toggleSpecifyEntityRelation() {
    if ($("#relation_to_entity").val() === "Other (please specify)")
        $("#relation_to_entity_other").show();
    else
        $("#relation_to_entity_other").hide();
}

$(document).on('keyup', '#user_phonenumber', function() {
    let currentValue = $(this).val();
    
    let validValue = currentValue.replace(/[^0-9+\s]/g, '');
    
    if (currentValue !== validValue) {
        toastr["error"]('Only numbers and plus sign are allowed', 'Error');
        $(this).val(validValue);
    }
})