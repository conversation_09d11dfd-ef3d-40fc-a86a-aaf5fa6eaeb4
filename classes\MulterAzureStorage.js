const { BlobServiceClient, StorageSharedKeyCredential } = require('@azure/storage-blob');
const { DefaultAzureCredential } = require('@azure/identity');
const path = require('path');
const {
  v4: uuidv4
} = require('uuid');

let _requestsQueue = []

const blobName = (file) => {
  let name = file.fieldname + '-' + uuidv4() + path.extname(file.originalname)
  file.blobName = name
  return name
}


class MulterAzureStorage {

  constructor(opts) {
    this.containerCreated = false;
    this.containerError = false;

    let useDefaultCredentials = !opts.accessKey;

    let missingParameters = [];

    if (!opts.accountName) {
      missingParameters.push("accountName");
    }

    if (!opts.containerName) {
      missingParameters.push("containerName");
    }

    if (missingParameters.length > 0) {
      throw new Error('Missing required parameter' + (missingParameters.length > 1 ? 's' : '') + ' from the options of MulterAzureStorage: ' + missingParameters.join(', '))
    }

    this.containerName = opts.containerName

    this.fileName = opts.fileName


    let credentials;
    if (useDefaultCredentials) {
      credentials = new DefaultAzureCredential();
    } else {
      // Only for local development and testing
      credentials = new StorageSharedKeyCredential(opts.accountName, opts.accessKey);
    }

    this.blobService = new BlobServiceClient(`https://${opts.accountName}.blob.core.windows.net`, credentials);

    this.containerClient = this.blobService.getContainerClient(this.containerName);

    this.containerClient.createIfNotExists().then(() => {
      this.containerCreated = true;
      _requestsQueue.forEach(i => this._removeFile(i.req, i.file, i.cb));
      _requestsQueue = [];
    }).catch((err) => {
      console.log(err);
      this.containerError = true;
      throw new Error('Cannot use container. Check if provided options are correct.');
    });
  }

  _handleFile(req, file, cb) {
    if (this.containerError) {
      cb(new Error('Cannot use container. Check if provided options are correct.'))
    }

    if (!this.containerCreated) {
      _requestsQueue.push({ req: req, file: file, cb: cb })
      return
    }

    const blob = (typeof this.fileName !== 'function') ? blobName(file) : this.fileName(file);


    const blockBlobClient = this.containerClient.getBlockBlobClient(blob);
    blockBlobClient.uploadStream(file.stream).then(async () => {
      try {
        const blobProperties = await blockBlobClient.getProperties();
        cb(null, {
          container: this.containerName,
          blob: blob,
          blobType: blobProperties.blobType,
          size: blobProperties.contentLength,
          etag: blobProperties.etag,
          metadata: blobProperties.metadata,
          url: blockBlobClient.url
        })
      } catch (error) {
        return cb(error);
      }
    }).catch((err) => {
      return cb(err)
    });

  }

  _removeFile(req, file, cb) {
    if (this.containerError) {
      cb(new Error('Cannot use container. Check if provided options are correct.'))
    }

    if (file.blobName) {
      this.containerClient.getBlockBlobClient(file.blobName).deleteIfExists().then(cb);
    } else {
      cb(null)
    }
  }

}

/**
 * @param {object}      [opts]
 * @param {string}      [opts.accessKey]
 * @param {string}      [opts.accountName]
 * @param {string}      [opts.containerName]
 * @param {function}    [opts.fileName]     function that given a file will return the name to be used as the file's name
 */
module.exports = function (opts) {
  return new MulterAzureStorage(opts)
}
