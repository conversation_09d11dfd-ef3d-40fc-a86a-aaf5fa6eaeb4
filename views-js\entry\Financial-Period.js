let showWarningFinancialPeriod = true;

$(document).ready(function () {
  showHideDiv();
});

function showHideDiv() {
  const HasFinancialPeriodChangedYes = document.getElementById("HasFinancialPeriodChangedYes");
  let showRest = document.getElementById("Showrest");

  showRest.style.display = HasFinancialPeriodChangedYes.checked ? "block" : "none";
  $("#FinancialPeriodBegins").css({ 'pointer-events': 'none', 'background-color': "#dddddd" }).prop('disabled', true);
  $("#FinancialPeriodEnds").css({ 'pointer-events': 'none', 'background-color': "#dddddd" }).prop('disabled', true);
  $("#PreviousEndDate").css({ 'pointer-events': 'none', 'background-color': "#dddddd" }).prop('disabled', true);
  $("#PreviousEndDateLbl").addClass('disable-click');
}
