<div class="table-responsive">
  <table class="table table-sm table-striped table-equal-width mb-0">
    <thead>
      <tr>
        <th>Name</th>
        <th>Beneficial Owner Interest</th>
        <th>Status</th>
        <th class="text-right"></th>
      </tr>
    </thead>
    <tbody>
      {{#each boList}}
      <tr>
        <td>{{BOName}}</td>
        <td>
          {{#ifEquals BONatureOfInterestCode "VGNI10"}}
          Interest by Ownership
          {{else ifEquals BONatureOfInterestCode "VGNI11"}}
          Interest by Ownership
          {{else ifEquals BONatureOfInterestCode "VGNI12"}}
          Interest by Control
          {{/ifEquals}}
        </td>
        <td>
          {{#if hasMissingValues}}
          MISSING INFORMATION
          {{else if isConfirmed}}
          CONFIRMED
          {{else if lastChange.status}}
          {{lastChange.status}}
          {{else}}
          CONFIRMATION REQUIRED
          {{/if}}
        </td>
        <td class="text-right">
          <button class="btn solid royal-blue width-xl show-more-btn" data-type="{{../type}}" data-id="{{BOCode}}"
            data-joint-key="{{jointKey}}">
            {{#if hasMissingValues}}
            Show More
            {{else}}
            Show More to Confirm
            {{/if}}
          </button>
          {{#if showHistory}}
          <button data-type="{{lastChange.changeType}}" data-reason="{{lastChange.changeReason}}"
            class="btn btn-sm solid width-md btn-secondary ml-2 showLastChange">
            <small>View History</small>
          </button>
          {{/if}}
        </td>
      </tr>
      {{/each}}
    </tbody>
  </table>
</div>
