const { CURRENCIES} = require('../utils/constants');

exports.parseStringNumberToFloat = function(value) {
    return value ? parseFloat(value.replace(/,/g, '')) : 0;
}

exports.isInvalidNumber = function(value){
    if (typeof value === "string" && value !== "") {
        value = parseFloat(value.replace(/,/g, ''))
    }
    return (value === "" || value === null || isNaN(value)) ? true : false
}

exports.formatYesNoBoolean = function(value) {
    if (value === undefined || value === "" || value === null) {
        return null;
    }
    return value.toUpperCase() === "YES" ? true : false

}

exports.validateEmail = function(email) {
    const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
}

exports.getCurrencyByCode = function(code){
    return CURRENCIES.find((c) => c.cc === code);
}