const moment = require('moment');

function validateActivity(activity, dbEntry, activityTitle, errors) {
    let activitySelected = false;
    if (activity.selected) {
        activitySelected = true;
        if (activity.part_of_financial_period == null) {
            errors.push({msg: "Provide carried on for part of financial periods for " + activityTitle + " activity"})
        }
        if (!activity.financial_periods || activity.financial_periods.length == 0) {
            errors.push({msg: "Provide financial periods for " + activityTitle + " activity"})
        } else {
            activity.financial_periods.forEach(function(item) {
                if (!item.financial_period_begins || !moment(item.financial_period_begins).isValid() || !item.financial_period_ends || !moment(item.financial_period_ends).isValid()) {
                    errors.push({msg: "Provide financial periods for " + activityTitle + " activity"})
                }else{
                    //period must be within the financial period
                    if (item.financial_period_begins < dbEntry.entity_details.financial_period_begins ||
                        item.financial_period_begins > dbEntry.entity_details.financial_period_ends ||
                        item.financial_period_ends > dbEntry.entity_details.financial_period_ends ||
                        item.financial_period_ends < dbEntry.entity_details.financial_period_begins ||
                        item.financial_period_ends < item.financial_period_begins) {
                        errors.push({ msg: "Periods for " + activityTitle + " activity must be between the financial period" })
                    }
                }

            })
        }
    }
    return activitySelected;
}

exports.validate = function(entry, dbEntry) {
    let errors = [];
    let atLeastOneSelected = false;
    if (validateActivity(entry.relevant_activities.banking_business, dbEntry, "Banking Business", errors)) {
        atLeastOneSelected = true;
    }
    if (validateActivity(entry.relevant_activities.insurance_business, dbEntry, "Insurance Business", errors)) {
        atLeastOneSelected = true;
    }
    if (validateActivity(entry.relevant_activities.fund_management_business, dbEntry, "Fund Management Business", errors)) {
        atLeastOneSelected = true;
    }
    if (validateActivity(entry.relevant_activities.finance_leasing_business, dbEntry, "Finance and Leasing Business", errors)) {
        atLeastOneSelected = true;
    }
    if (validateActivity(entry.relevant_activities.headquarters_business, dbEntry, "Headquarters Business", errors)) {
        atLeastOneSelected = true;
    }
    if (validateActivity(entry.relevant_activities.shipping_business, dbEntry, "Shipping Business", errors)) {
        atLeastOneSelected = true;
    }
    if (validateActivity(entry.relevant_activities.holding_business, dbEntry, "Holding Business (Pure Equity Holding entities)", errors)) {
        atLeastOneSelected = true;
    }
    if (validateActivity(entry.relevant_activities.intellectual_property_business, dbEntry, "Intellectual Property Business", errors)) {
        atLeastOneSelected = true;
    }
    if (validateActivity(entry.relevant_activities.service_centre_business, dbEntry, "Distribution and Service Centre Business", errors) ){
        atLeastOneSelected = true;
    }
    if (validateActivity(entry.relevant_activities.none, dbEntry, "None", errors)) {
        atLeastOneSelected = true;
    }
    
    if (!atLeastOneSelected) {
        errors.push({msg: "Select at least one activity"})
    }

    return errors;
};

