
module.exports = (sequelize, Sequelize) => {
    const VPDirectorInfoStatus = sequelize.define('VPDirectorInfoStatus', {
        UniqueRelationId: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false,
        },
        CompanyNumber: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false,
        },
        EntityCode: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false,
        },
        RelationType: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false,
        },
        Status: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false,
        }
    }, {
        tableName: 'VPDirectorInfoStatusv2',
        timestamps: false,
    });
    
    VPDirectorInfoStatus.removeAttribute('id');
    return VPDirectorInfoStatus
}

