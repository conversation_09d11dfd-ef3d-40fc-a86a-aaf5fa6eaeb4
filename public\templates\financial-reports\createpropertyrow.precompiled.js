(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['createpropertyrow'] = template({"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, alias5=container.lambda, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "<tr id=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"tblID") || (depth0 != null ? lookupProperty(depth0,"tblID") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"tblID","hash":{},"data":data,"loc":{"start":{"line":1,"column":8},"end":{"line":1,"column":17}}}) : helper)))
    + "-"
    + alias4(alias5(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"property") : stack1)) != null ? lookupProperty(stack1,"_id") : stack1), depth0))
    + "\" class=\"property-row\">\r\n    <td>\r\n        "
    + alias4(alias5(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"property") : stack1)) != null ? lookupProperty(stack1,"propertyType") : stack1), depth0))
    + "\r\n    </td>\r\n    <td class=\"text-right\">\r\n        "
    + alias4(((helper = (helper = lookupProperty(helpers,"currencySym") || (depth0 != null ? lookupProperty(depth0,"currencySym") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"currencySym","hash":{},"data":data,"loc":{"start":{"line":6,"column":8},"end":{"line":6,"column":23}}}) : helper)))
    + " "
    + alias4((lookupProperty(helpers,"decimalValue")||(depth0 && lookupProperty(depth0,"decimalValue"))||alias2).call(alias1,((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"property") : stack1)) != null ? lookupProperty(stack1,"propertyValue") : stack1),{"name":"decimalValue","hash":{},"data":data,"loc":{"start":{"line":6,"column":24},"end":{"line":6,"column":68}}}))
    + "\r\n    </td>\r\n    <td class=\"text-right\">\r\n        <button type=\"button\" class=\"btn btn-outline-secondary openEditSetupPropertyModal\"\r\n                data-id=\""
    + alias4(alias5(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"property") : stack1)) != null ? lookupProperty(stack1,"_id") : stack1), depth0))
    + "\">\r\n            <i class=\"fa fa-pencil\"></i>\r\n        </button>\r\n    </td>\r\n    <td class=\"text-left\">\r\n        <button type=\"button\" class=\"delete btn btn-danger deleteProperty\"\r\n                data-id=\""
    + alias4(alias5(((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"property") : stack1)) != null ? lookupProperty(stack1,"_id") : stack1), depth0))
    + "\">\r\n            <i class=\"fa fa-trash\"></i>\r\n        </button>\r\n    </td>\r\n</tr>\r\n\r\n\r\n";
},"useData":true});
})();