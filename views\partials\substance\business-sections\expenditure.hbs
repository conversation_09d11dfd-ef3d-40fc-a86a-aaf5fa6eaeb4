<div id="expenditureContent">
    <!-- 4C.a -->
    <div class="row hidden-holding-business">
        <div class="col-md-8">
            <div class="form-group mb-3">
                <label class="mb-2" for="TotalExpenditureIncured">
                    Total expenditure incurred in the operations of the relevant activity during the financial period
                    (including outsourcing, if applicable):
                </label>
            </div>
        </div>
        <div class="col-md-4">
            <input type="text" name="totalExpenditureCurrency" id="totalExpenditureCurrency" class="form-control" 
                {{#if selectedCurrency}} 
                    value="{{selectedCurrency.cc}} - {{selectedCurrency.name}}" 
                {{else}}
                    value="{{defaultCurrency.cc}} - {{defaultCurrency.name}}" 
                {{/if}} 
                disabled 
            />


            <input type="text" id="TotalExpenditureIncured" class="form-control mt-2 autonumber" data-a-sep=","
                data-min="0" data-max="1000000000" placeholder="0.0"
                data-m-dec="2" name="TotalExpenditureIncured" aria-describedby="basic-addon1"
                value="{{data.total_expenditure}}" />

        </div>
    </div>
    
    <!-- 4C.b -->
    <div class="row hidden-holding-business">
        <div class="col-md-8">
            <div class="form-group mb-3">
                <label class="mb-2" for="TotalExpenditureIncurredInBVI">
                    Total expenditure incurred in the Virgin Islands in the operations of the relevant activity during
                    the financial period
                    (including outsourcing, if applicable):
                </label>
            </div>
        </div>
        <div class="col-md-4">

            <input type="text" name="totalExpenditureInBVICurrency" id="totalExpenditureInBVICurrency" class="form-control"
                {{#if selectedCurrency}}
                    value="{{selectedCurrency.cc}} - {{selectedCurrency.name}}" 
                {{else}}
                    value="{{defaultCurrency.cc}} - {{defaultCurrency.name}}" 
                {{/if}}
                disabled 
            />

            <input type="text" id="totalExpenditureIncurredInBVI" class="form-control mt-2 autonumber" data-a-sep="," 
                data-min="0" data-max="1000000000" placeholder="0.0"
                data-m-dec="2" name="totalExpenditureIncurredInBVI" aria-describedby="basic-addon1"
                value="{{data.total_expenditure_bvi}}" />

        </div>
    </div>
</div>