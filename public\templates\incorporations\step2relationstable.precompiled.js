(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['step2relationstable'] = template({"1":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, alias5=container.lambda, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "            <tr class=\"relation-row\">\r\n                <td class=\"text-capitalize\"> "
    + alias4(((helper = (helper = lookupProperty(helpers,"name") || (depth0 != null ? lookupProperty(depth0,"name") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"name","hash":{},"data":data,"loc":{"start":{"line":16,"column":45},"end":{"line":16,"column":53}}}) : helper)))
    + " </td>\r\n                <td class=\"text-capitalize\"> "
    + alias4(((helper = (helper = lookupProperty(helpers,"country") || (depth0 != null ? lookupProperty(depth0,"country") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"country","hash":{},"data":data,"loc":{"start":{"line":17,"column":45},"end":{"line":17,"column":56}}}) : helper)))
    + " </td>\r\n                <td class=\"text-capitalize\"> "
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,(depth0 != null ? lookupProperty(depth0,"percentage") : depth0),{"name":"if","hash":{},"fn":container.program(2, data, 0, blockParams, depths),"inverse":container.program(4, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":18,"column":45},"end":{"line":18,"column":99}}})) != null ? stack1 : "")
    + " </td>\r\n                <td class=\"text-capitalize\"> "
    + alias4(((helper = (helper = lookupProperty(helpers,"type") || (depth0 != null ? lookupProperty(depth0,"type") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"type","hash":{},"data":data,"loc":{"start":{"line":19,"column":45},"end":{"line":19,"column":53}}}) : helper)))
    + " </td>\r\n                <td class=\"text-capitalize\"> "
    + alias4(((helper = (helper = lookupProperty(helpers,"groups") || (depth0 != null ? lookupProperty(depth0,"groups") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"groups","hash":{},"data":data,"loc":{"start":{"line":20,"column":45},"end":{"line":20,"column":55}}}) : helper)))
    + " </td>\r\n                <td>\r\n                    <a href=\"/masterclients/"
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"mcc") : depths[1]), depth0))
    + "/incorporate-company/"
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"incorporationId") : depths[1]), depth0))
    + "/relations/"
    + alias4(((helper = (helper = lookupProperty(helpers,"relationId") || (depth0 != null ? lookupProperty(depth0,"relationId") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"relationId","hash":{},"data":data,"loc":{"start":{"line":22,"column":108},"end":{"line":22,"column":122}}}) : helper)))
    + "/edit\"\r\n                       class=\"btn btn-outline-secondary\" id=\"edit-"
    + alias4(((helper = (helper = lookupProperty(helpers,"relationId") || (depth0 != null ? lookupProperty(depth0,"relationId") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"relationId","hash":{},"data":data,"loc":{"start":{"line":23,"column":66},"end":{"line":23,"column":82}}}) : helper)))
    + "\">\r\n                        <i class=\"fa fa-pencil\"></i>\r\n                    </a>\r\n                </td>\r\n                <td>\r\n                    <button type=\"button\" class=\"delete btn btn-danger\" id=\"delete-"
    + alias4(((helper = (helper = lookupProperty(helpers,"relationId") || (depth0 != null ? lookupProperty(depth0,"relationId") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"relationId","hash":{},"data":data,"loc":{"start":{"line":28,"column":83},"end":{"line":28,"column":99}}}) : helper)))
    + "\"\r\n                            data-relation-id=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"relationId") || (depth0 != null ? lookupProperty(depth0,"relationId") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"relationId","hash":{},"data":data,"loc":{"start":{"line":29,"column":46},"end":{"line":29,"column":62}}}) : helper)))
    + "\" data-incorporation-id=\""
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"incorporationId") : depths[1]), depth0))
    + "\"\r\n                            data-toggle=\"modal\" data-target=\"#deleteRelationModal\">\r\n                        <i class=\"fa fa-trash\"></i>\r\n                    </button>\r\n                </td>\r\n            </tr>\r\n\r\n";
},"2":function(container,depth0,helpers,partials,data) {
    var helper, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return " "
    + container.escapeExpression(((helper = (helper = lookupProperty(helpers,"percentage") || (depth0 != null ? lookupProperty(depth0,"percentage") : depth0)) != null ? helper : container.hooks.helperMissing),(typeof helper === "function" ? helper.call(depth0 != null ? depth0 : (container.nullContext || {}),{"name":"percentage","hash":{},"data":data,"loc":{"start":{"line":18,"column":64},"end":{"line":18,"column":78}}}) : helper)))
    + "% ";
},"4":function(container,depth0,helpers,partials,data) {
    return " 0% ";
},"6":function(container,depth0,helpers,partials,data) {
    return "            <tr>\r\n                <td colspan=\"9\" class=\"text-center font-italic\">\r\n                    There are no relations\r\n                </td>\r\n            </tr>\r\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "<table class=\"table table-striped\" id=\"relationsTable\">\r\n    <thead>\r\n        <tr>\r\n            <th class=\"header-20-percent\">Name</th>\r\n            <th class=\"header-15-percent\">Country</th>\r\n            <th class=\"header-15-percent\">Percentage</th>\r\n            <th class=\"header-15-percent\">Owned Type</th>\r\n            <th class=\"header-25-percent\">Relation type</th>\r\n            <th class=\"header-5-percent\"></th>\r\n            <th class=\"header-5-percent\"></th>\r\n        </tr>\r\n    </thead>\r\n    <tbody>\r\n"
    + ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"relations") : depth0),{"name":"each","hash":{},"fn":container.program(1, data, 0, blockParams, depths),"inverse":container.program(6, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":14,"column":8},"end":{"line":42,"column":17}}})) != null ? stack1 : "")
    + "    </tbody>\r\n</table>\r\n\r\n";
},"useData":true,"useDepths":true});
})();