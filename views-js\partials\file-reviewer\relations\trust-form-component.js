
$('input[name="trust[isSamePrincipalAddress]"]').on('change', function () {
    const value = $(this).val() === 'YES';
    if (value){
        $('#trustForm #mailingAddressDetails').hide(200);
    }
    else{
        $('#trustForm #mailingAddressDetails').show(200);
    }
});


$('input[name="trust[isTridentClient]"]').on('change', function () {
    const value = $(this).val() === 'YES';
    if (value){
        $('#trustForm .isTridentClient').hide(200);
        $('#shareholderAdditionalForm').hide();
    }
    else{
        $('#trustForm .isTridentClient').show(200);

        if ($("#shareholderType").is(':checked')){
            $('#shareholderAdditionalForm').show(200);
        }
    }
});