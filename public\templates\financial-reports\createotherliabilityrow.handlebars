<tr id="liability-table-row-{{liability._id}}-{{liability.type}}" class="liability-row">
    <td>
        {{liability.description}}
    </td>
    <td {{#ifEquals liability.type 'LiabilityEndPeriod'}} class="otherLiabilityValue" {{/ifEquals}}>
        {{decimalValue liability.value}}
    </td>
    <td class="text-right">
        <button type="button" class="btn btn-outline-secondary openEditLiability"
                data-id="{{liability._id}}" data-type="{{liability.type}}">
            <i class="fa fa-pencil"></i>
        </button>
    </td>
    <td class="text-left">
        <button type="button" class="delete btn btn-danger deleteLiability"
                data-id="{{liability._id}}" data-type="{{liability.type}}">
            <i class="fa fa-trash"></i>
        </button>
    </td>
</tr>