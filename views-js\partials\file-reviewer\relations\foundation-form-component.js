
$('input[name="foundation[isSamePrincipalAddress]"]').on('change', function () {
    const value = $(this).val() === 'YES';
    if (value){
        $('#foundationForm #mailingAddressDetails').hide(200);
    }
    else{
        $('#foundationForm #mailingAddressDetails').show(200);
    }
});


$('input[name="foundation[isTridentClient]"]').on('change', function () {
    const value = $(this).val() === 'YES';
    if (value){
        $('#foundationForm .isTridentClient').hide(200);
        $('#shareholderAdditionalForm').hide();
    }
    else{
        $('#foundationForm .isTridentClient').show(200);

        if ($("#shareholderType").is(':checked')){
            $('#shareholderAdditionalForm').show(200);
        }
    }
});