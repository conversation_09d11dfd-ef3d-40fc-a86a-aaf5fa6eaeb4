const express = require('express');
const router = express.Router({ mergeParams: true });
const sessionUtils = require('../utils/sessionUtils');

// Require controller modules.
const commonController = require('../controllers/company-information/commonController');
const memberController = require('../controllers/company-information/memberController');
const directorController = require('../controllers/company-information/directorController');
const beneficialOwnerController = require('../controllers/company-information/beneficialOwnerController');

// Director specific routes
router.get('/', ensureAuthenticated, commonController.listCompanies);
router.get('/:code/directors', ensureAuthenticated, directorController.getDirectorEntries);
router.post('/:code/directors/:id/confirm', ensureAuthenticated, directorController.createDirectorConfirmationLog);
router.post('/:code/directors/:id/request-update', ensureAuthenticated, directorController.requestToUpdate);
router.post('/:code/directors/request-assistance', ensureAuthenticated, commonController.requestToAssistance);
router.get('/:code/directors/:id/details', ensureAuthenticated, directorController.getDirectorDetails);

// Member specific routes
router.get('/:code/members', ensureAuthenticated, memberController.getMemberEntries);
router.post('/:code/members/request-assistance', ensureAuthenticated, commonController.requestToAssistance);
router.post('/:code/members/:id/request-update', ensureAuthenticated, memberController.requestMemberToUpdate);
router.post('/:code/members/:id/confirm', ensureAuthenticated, memberController.createMemberConfirmationLog);
router.get('/:code/members/joint/:certNr/details', ensureAuthenticated, memberController.getJointMemberDetails);
router.post('/:code/members/joint/:certNr/request-update', ensureAuthenticated, memberController.requestJointMemberToUpdate);
router.post('/:code/members/joint/:certNr/confirm', ensureAuthenticated, memberController.createJointMemberConfirmationLog);
router.get('/:code/members/:id/details', ensureAuthenticated, memberController.getMemberDetails);

// Stock and fund specific routes
router.post('/:code/request-stock-update', ensureAuthenticated, memberController.requestStockOrFundToUpdate);
router.post('/:code/request-mutual-fund-update', ensureAuthenticated, memberController.requestStockOrFundToUpdate);
router.post('/:code/confirm-stock', ensureAuthenticated, memberController.createStockOrFundConfirmationLog);
router.post('/:code/confirm-fund', ensureAuthenticated, memberController.createStockOrFundConfirmationLog);

// Beneficial Owner specific routes
router.get('/:code/beneficial-owners', ensureAuthenticated, beneficialOwnerController.getBeneficialOwnerEntries);
router.post('/:code/beneficial-owners/request-assistance', ensureAuthenticated, commonController.requestToAssistance);
router.post('/:code/beneficial-owners/:id/request-update', ensureAuthenticated, beneficialOwnerController.requestBeneficialOwnerToUpdate);
router.post('/:code/beneficial-owners/:id/confirm', ensureAuthenticated, beneficialOwnerController.createBeneficialOwnerConfirmationLog);
router.get('/:code/beneficial-owners/:id/details', ensureAuthenticated, beneficialOwnerController.getBeneficialOwnerDetails);
router.get('/:code/beneficial-owners/joint/:jointCode/details', ensureAuthenticated, beneficialOwnerController.getJointBeneficialOwnerDetails);
router.post('/:code/beneficial-owners/joint/:jointCode/request-update', ensureAuthenticated, beneficialOwnerController.requestJointBeneficialOwnerToUpdate);
router.post('/:code/beneficial-owners/joint/:jointCode/confirm', ensureAuthenticated, beneficialOwnerController.createJointBeneficialOwnerConfirmationLog);
router.post('/:code/beneficial-owners/:entityId/request-update', ensureAuthenticated, beneficialOwnerController.requestEntityExemptionToUpdate);
router.post('/:code/beneficial-owners/:entityId/confirm', ensureAuthenticated, beneficialOwnerController.confirmEntityExemption);

module.exports = router;

function ensureAuthenticated(req, res, next) {
  if ((req.user && req.session.id === req.user.sessionId) && req.session.auth2fa) {
    next();
  } else if ((req.user && req.session.id === req.user.sessionId) && !req.session.auth2fa) {
    if (req.user.secret_2fa) {
      res.redirect('/users/2fa-code');
    } else {
      res.redirect('/users/2fa-setup');
    }
  } else {
    req.logout(function (err) {
      if (err) { return next(err) }

      req.session.destroy(function () {
        // cannot access session here
        sessionUtils.onSessionDestroyed(req, res);
      });
    });
  }
}