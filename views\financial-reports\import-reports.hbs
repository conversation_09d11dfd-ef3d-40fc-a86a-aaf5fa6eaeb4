<main class="">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-12">
                <div class="card">
                    <form method="POST" id="submitForm">
                        <input type="text" hidden name="csrf-token" value="{{csrfToken}}">
                        <div class="card-body">

                            <div class="row">
                                <div class="col-md-8">
                                    <h1>{{title}}</h1>
                                </div>
                                <div class="col-md-4 text-right">
                                    <a href="/import-financial-reports-template.xlsx" target="_blank" download
                                       class="btn btn-secondary waves-effect waves-light width-xl">Download template</a>
                                </div>
                            </div>

                            <div id="importTable">
                                <table class="table w-100 nowrap" id="scroll-horizontal-datatable">
                                </table>
                            </div>

                            <div class="row" id="uploadRow">
                                <div class="col-md-12">
                                    <p>
                                        Maximum of 1 file, XLSX only. File must not
                                        be password
                                        protected.
                                    </p>
                                    <div id="uploadFile" class="dropzone">
                                        <div class="fallback">
                                            <input name="fileUploaded" type="file" multiple/>
                                        </div>
                                        <div class="dz-message needsclick">
                                            <i class="h1 text-muted dripicons-cloud-upload"></i>
                                            <h3>Drop files here or click to upload.</h3>
                                            <span class="text-muted">Files will be automatically uploaded</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="row">
                                <div class="mb-3 ml-3">
                                    <a href='/masterclients/{{masterClientCode}}/financial-reports/'
                                       class="btn btn-secondary width-lg waves-effect waves-light">Back</a>
                                    <button type="button" class="btn btn-primary my-1 hide-element" id="loadDataBtn"
                                            id="loadTable" >Load Data
                                    </button>
                                    <button type="button" class="btn btn-primary my-1 hide-element" id="saveDataBtn"
                                            id="saveData" >Save Data
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</main>

<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script type='text/javascript' src='/templates/financial-reports/importdetailstable.precompiled.js'></script>
<script type="text/javascript" src="/views-js/financial-reports/import-reports.js"></script>
