$(document).ready(function () {
    $("input[type=date]").flatpickr({
        dateFormat: "Y-m-d",
        minDate: "2023-01-01"
    });

    $('#progressBarDiv').hide()

    $('[data-toggle="tooltip"]').tooltip({
        container: 'body',
        boundary: 'window',
        
    });

    $('#pdfDownloads').on('click', function () {
        $('#pdfModal').modal()
    })
    const isExemptCompany = $("input[name='isExemptCompany']:checked").val();

    const exemptCompanyType = $("#exemptCompanyType").val();
    const serviceType = $("input[name='serviceType']:checked").val();
    

    if (isExemptCompany === "YES"){
        calculateProgressBar('')
        resetServiceQuestions();
        showFinancialPeriodQuestion(false);
        $("#isExemptCompanyYesRows").show(200);
        if (exemptCompanyType !== "") {
            setLabelsForExemptService();
            $("#exemptCompanyTypeRows").show(200);

        }

    }

    if (isExemptCompany === "NO"){
        $("#isExemptCompanyNoRows").show(200);
        if (serviceType && serviceType !== "") {
            calculateProgressBar(serviceType)
            showFinancialPeriodQuestion(true);
        } else {
            showFinancialPeriodQuestion(false);
        }

    }

});

$('#serviceTypeSelfComplete').click(function () {
    $('#previewExceptCompany').css('display', 'block')
})

$('#serviceTypeSelfPrepare, #serviceTypeTridentComplete, #serviceTypeTridentDrop').click(function () {
    $('#previewExceptCompany').css('display', 'none')
})

$("input[name='isExemptCompany']").on("change", function () {
    const val = $(this).val()

    if (val === "YES"){
        calculateProgressBar('')
        $('#previewExceptCompany').css('display', 'block')
        $("#isExemptCompanyYesRows").show(200);
        $("#exemptCompanyTypeRow").show(200);
        $("#declarationAssetsLiabilities").hide();
        $("#paymentAmountText").text('190')
        resetServiceQuestions();
    }
    else if (val === "NO"){
        $('#progressBarDiv').hide()
        $('#previewExceptCompany').css('display', 'none')
        $("#exemptCompanyTypeRow").hide();
        $("#isExemptCompanyNoRows").show(200);
        $("#isExemptCompanyYesRows").hide();
        $("#exemptCompanyType").val('').trigger('change');
        $("#declarationAssetsLiabilities").show();
        $('#isFirstYearReportingYes').prop('checked', false)
        $('#isFirstYearReportingNo').prop('checked', true)
        $('#companyFirstOperationLiabilityNo').prop('checked', true)
        $('#companyFirstOperationLiabilityYes').prop('checked', false)
        $("#companyFirstOperationYesRows").show(200);
        $("#AssetsFirstRowsNo").show(200);
    }else{
        resetServiceQuestions();
        $("#isExemptCompanyYesRows").hide();
    }
});

$("#exemptCompanyType").on('change', function(e){
    const val = $(this).val();

    if (val === ''){
        $("#exemptCompanyTypeRows").hide();
        $("exemptCompanyTypeRows textarea").val('');

    }else{
        setLabelsForExemptService();
        $("#exemptCompanyTypeRows").show();
    }
})


$("input[name='serviceType']").on("change", function () {
    const val = $(this).val();

    if(val !== ""){
        calculateProgressBar(val)
        if (val === "self-service-complete") {
            $("#paymentAmountText").text($('#serviceTypeSelfCompleteAmount').val())
        }
        else if (val === "self-service-prepare") {
            $("#paymentAmountText").text($('#serviceTypeSelfPrepareAmount').val())
        }
        else if (val === "trident-service-complete") {
            $("#paymentAmountText").text($('#serviceTypeTridentCompleteAmount').val())
        }
        else if (val === "trident-service-drop") {
            $("#paymentAmountText").text($('#serviceTypeDropAmount').val())
        }else{
            $("#paymentAmountText").text('190')
        }
    }
    showFinancialPeriodQuestion(val !== "");
});

function calculateProgressBar (serviceType) {
    const progressBar =  $('#progressBarDiv')
    const confirmationBar = $('#confirmationProgressBar')
    const declarationBar = $("#declarationProgressBar")
    $(progressBar).show()
    switch (serviceType) {
        case 'self-service-complete':
            $(progressBar).text('1 of 5')
            $(progressBar).prop('aria-valuemax', 5)
            $(declarationBar).text('4 of 5')
            $(declarationBar).prop('aria-valuemax', 4)
            $(declarationBar).prop('aria-valuenow', 5)
            $(confirmationBar).text('5 of 5')
            $(confirmationBar).prop('aria-valuemax', 5)
            $(confirmationBar).prop('aria-valuenow', 5)
            break;
        case 'self-service-prepare':
            $(progressBar).text('1 of 7')
            $(progressBar).prop('aria-valuemax', 7)
            $(declarationBar).text('6 of 7')
            $(declarationBar).prop('aria-valuemax', 6)
            $(declarationBar).prop('aria-valuenow', 7)
            $(confirmationBar).text('7 of 7')
            $(confirmationBar).prop('aria-valuemax', 7)
            $(confirmationBar).prop('aria-valuenow', 7)
            break;
        case 'trident-service-complete':
        case 'trident-service-drop':
            $(progressBar).text('1 of 3')
            $(progressBar).prop('aria-valuemax', 3)
            $(declarationBar).text('2 of 3')
            $(declarationBar).prop('aria-valuemax', 2)
            $(declarationBar).prop('aria-valuenow', 3)
            $(confirmationBar).text('3 of 3')
            $(confirmationBar).prop('aria-valuemax', 3)
            $(confirmationBar).prop('aria-valuenow', 3)
            break;
        default: 
            $(progressBar).text('1 of 3')
            $(progressBar).prop('aria-valuemax', 3)
            $(declarationBar).text('2 of 3')
            $(declarationBar).prop('aria-valuemax', 2)
            $(declarationBar).prop('aria-valuenow', 3)
            $(confirmationBar).text('3 of 3')
            $(confirmationBar).prop('aria-valuemax', 3)
            $(confirmationBar).prop('aria-valuenow', 3)
            break;
    }
    
}

$("input[name='isFirstYearOperation']").on("change", function () {
    const val = $(this).val();
    $("input[name='isThereFinancialYearChange']").prop('disabled', true);
    $("input[name='isThereFinancialYearChange']").prop('checked', false);
    if(val === "YES"){
        
        

        //set default option first year for assets/liabilities
        $('#isFirstYearReportingYes').prop('checked', true)
        $('#isFirstYearReportingNo').prop('checked', false)
        $('#companyFirstOperationLiabilityNo').prop('checked', false)
        $('#companyFirstOperationLiabilityYes').prop('checked', true)
        $("#AssetsFirstRowsNo").hide(200);
        $("#companyFirstOperationYesRows").hide(200);
        $('input[name="isTangibleFixAssets"] input[type="radio"]').each(function () {
            $(this).prop('required', true)
        })
        $('input[name="isLoansAndReceivablesEndPeriod"] input[type="radio"]').each(function () {
            $(this).prop('required', true)
        })
        $("#companyFirstOperationAssetsYes").prop('checked', true)
        $("#companyFirstOperationAssetsNo").prop('checked', false)

    }
    else if (val === "NO"){

        //set default option first year for assets/liabilities
        $("#companyFirstOperationYesRows").show(200);
        $("#AssetsFirstRowsNo").show(200);
        $('#isFirstYearReportingYes').prop('checked', false)
        $('#isFirstYearReportingNo').prop('checked', true)
        $('#companyFirstOperationLiabilityNo').prop('checked', true)
        $('#companyFirstOperationLiabilityYes').prop('checked', false)

        $("#companyFirstOperationAssetsYes").prop('checked', false)
        $("#companyFirstOperationAssetsNo").prop('checked', true)
        $('input[name="isTangibleFixAssets"] input[type="radio"]').each(function () {
            $(this).prop('required', false)
        })
        $('input[name="isLoansAndReceivablesEndPeriod"] input[type="radio"]').each(function () {
            $(this).prop('required', false)

        })
        // If not first report, enable again
        if ($("input[name='isThereFinancialYearChange'][data-is-first-report='true']").length === 0) {
            $("input[name='isThereFinancialYearChange']").prop('disabled', false);
        }

        $("#changeFinancialYearRow").show(200);
    } else  {
        $('#isFirstYearReportingYes').prop('checked', true)
        $('#isFirstYearReportingNo').prop('checked', false)
        $('#companyFirstOperationLiabilityNo').prop('checked', false)
        $('#companyFirstOperationLiabilityYes').prop('checked', true)
        $("#AssetsFirstRowsNo").hide(200);
        $("#companyFirstOperationYesRows").hide(200);
        $('input[name="isTangibleFixAssets"] input[type="radio"]').each(function () {
            $(this).prop('required', true)
        }) 
        $('input[name="isLoansAndReceivablesEndPeriod"] input[type="radio"]').each(function () {
            $(this).prop('required', true)
        })
    }
    showFinancialPeriodQuestion(true);
});


$("input[name='isThereFinancialYearChange']").on("change", function () {
    const val = $("input[name='isThereFinancialYearChange']:checked").val();
    if (val === "YES") {
        $("#uploadCopyResolutionRow").show(200);
        $("#isFirstYearOperationYes").prop('disabled', true);
    } else {
        $("#uploadCopyResolutionRow").hide();
        $("#isFirstYearOperationYes").prop('disabled', false);
    }
    showFinancialPeriodQuestion(true);
});


function setLabelsForExemptService(){
    const val = $("#exemptCompanyType").val();

    const exemptTypes = {
        "in-liquidation": {
            "motivationLabel": "Additional Remarks",
            "fileLabel": "Provide supporting documentation (Registry stamped letter of appointment of the Liquidator)"
        },
        "stock-exchange": {
            "motivationLabel": "Confirm the name and jurisdiction of the stock exchange and any additional remarks",
            "fileLabel": "Provide proof of listing"
        },
        "is-regulated": {
            "motivationLabel": "Confirm that the Company submits financial statements, the deadline for submission and the latest reporting period",
            "fileLabel": "Provide copy of the licence and a recent receipt <a target='_blank' href='/TrustLicenceAndReceipt.pdf'>(Sample)</a>"
        },
        "tax-return-filed": {
            "motivationLabel": "Confirm registration date, registration number and any additional remarks",
            "fileLabel": "Provide a copy of the email acknowledgement from BVI Inland Revenue"
        }
    }
    const selectedType = exemptTypes[val];

    $("#exemptEvidenceFileLabel").html(selectedType.fileLabel);
    $("#exemptMotivationLabel").html(selectedType.motivationLabel);
}

function resetServiceQuestions(){
    $("#isExemptCompanyNoRows").hide();
    $("input[name='serviceType']").prop('checked', false);
    $("input[name='isFirstYearOperation']").prop('disabled', false);
    $("#uploadCopyResolutionRow").hide();
    showFinancialPeriodQuestion(false);
}

function showFinancialPeriodQuestion(show) {
    const serviceType = $("input[name='serviceType']:checked").val();
    const isFirstYear = $("input[name='isFirstYearOperation']:checked").val();
    const isThereFinancialYearChange = $("input[name='isThereFinancialYearChange']:checked").val();


    const guideTitles = {
        "default": "Please note that this can be any twelve (12) consecutive month period (e.g, 1 January - 31 December; 1 April - 31 March)",
        "titleFirstPeriod": "Please note because it is the Company's FIRST financial period, it can be any six (6) to eighteen (18) consecutive month period. The chosen end of this financial period would dictate the next financial year of the company.",
        "titlePeriodChangeYes": "Please note that this cannot be more than twelve (12) months from the current start date."
    }

    const financialPeriodInfoPopup = $("#financialPeriodInfo");

    if (show === true) {
        // Popup text for cases 1.4.B, 1.4.D
        financialPeriodInfoPopup.attr('data-original-title', guideTitles['default']);        

        switch (serviceType) {
            case "self-service-complete":
                // 1.4.c
                if (isFirstYear === 'YES') {
                    financialPeriodInfoPopup.attr('data-original-title', guideTitles["titleFirstPeriod"]);
                    $("#startFinReportlbl").text("Let's start completing your Annual Return");
                    $("#goToFinancialBtn").show(200);
                }else{
                    // 1.4.E
                    if (isThereFinancialYearChange === "YES") {
                        financialPeriodInfoPopup.attr('data-original-title', guideTitles["titlePeriodChangeYes"]);
                        $("#startFinReportlbl").text("Let's start preparing your Annual Return.");
                        $("#goToFinancialBtn").show(200);
                    } else {
                        // 1.4.D
                        $("#startFinReportlbl").text("Let's start completing your Annual Return.");
                        $("#goToFinancialBtn").hide();
                        
                    }
                }
                $("#startFinReportRow").show(200);
                $("#startFinReportRow2").show(200);
                $("#uploadAnnualRow").hide();
                $("#uploadAccountingRow").hide();
                $("#assistanceTextRow").hide();
                break;
            case "self-service-prepare":
                if (isFirstYear === 'YES') {
                    financialPeriodInfoPopup.attr('data-original-title', guideTitles["titleFirstPeriod"]);
                    $("#startFinReportlbl").text("Let's start completing your Annual Return");
                }else{
                    
                    $("#startFinReportlbl").text("Let's start preparing your Annual Return.");
                    // 1.4.E
                    if (isThereFinancialYearChange === "YES") {
                        financialPeriodInfoPopup.attr('data-original-title', guideTitles["titlePeriodChangeYes"]);
                    }
                    
                }

                $("#startFinReportRow").show(200);
                $("#startFinReportRow2").show(200);
                $("#goToFinancialBtn").show(200);
                $("#uploadAnnualRow").hide();
                $("#uploadAccountingRow").hide();
                $("#assistanceTextRow").hide();
                break;
            case "trident-service-complete":
                // 1.4.a
                if (isFirstYear === "YES") {
                    financialPeriodInfoPopup.attr('data-original-title', guideTitles["titleFirstPeriod"]);
                } else {
                    //1.4.G
                    if (isFirstYear === 'YES') {
                        financialPeriodInfoPopup.attr('data-original-title', guideTitles["titlePeriodChangeYes"]);
                    }
                }
                $("#uploadAnnualRow").show(200);
                $("#startFinReportRow").hide();
                $("#startFinReportRow2").hide();
                $("#goToFinancialBtn").hide();
                $("#uploadAccountingRow").hide();
                $("#assistanceTextRow").hide();
                break;
            case "trident-service-drop":
                // 1.4.I
                if (isFirstYear === 'YES') {
                    financialPeriodInfoPopup.attr('data-original-title', guideTitles["titleFirstPeriod"]);
                }else{
                    // 1.4.J
                    if (isThereFinancialYearChange === "YES") {
                        financialPeriodInfoPopup.attr('data-original-title', guideTitles["titlePeriodChangeYes"]);
                    }
                }
                $("#uploadAccountingRow").show(200);
                $("#assistanceTextRow").show(200);

                $("#startFinReportRow").hide();
                $("#startFinReportRow2").hide();
                $("#goToFinancialBtn").hide();
                $("#uploadAnnualRow").hide();
                break;
            default:
                $("#startFinReportRow").hide();
                $("#startFinReportRow2").hide();
                $("#goToFinancialBtn").hide();
                $("#uploadAccountingRow").hide();
                $("#uploadAnnualRow").hide();
                $("#assistanceTextRow").hide();
        }
            
        if (isThereFinancialYearChange === "YES"){
            $(".fiscalYearDiv").show(200);
        }else{
            $(".fiscalYearDiv").hide();
        }
  
    } else {
        $(".fiscalYearDiv").hide();
        $("#startFinReportRow").hide();
        $("#startFinReportRow2").hide();
        $("#goToFinancialBtn").hide();
        $("#uploadAccountingRow").hide();
        $("#uploadAnnualRow").hide();
        $("#assistanceTextRow").hide();
    }
}

function showInfoMessage(message){
    Swal.fire('Info', message, 'info');
}

function parseStringNumberToFloat(value) {
    return value ? parseFloat(value.replace(/,/g, '')) : 0;
}





$(".startPeriodDate").on('change', function () {
    const val = $(this).val();

    $(".finPrdStartText").val(val);

});

$(".endPeriodDate").on('change', function () {
    const val = $(this).val();

    $(".endPeriodDate").val(val);
    $(".finPrdEndText").text(moment.utc(val).format("DD MMMM YYYY"));
    $(".finPrdEnd12Text").text(moment(val, 'YYYY-MM-DD').add(1, 'years').format('YYYY-MM-DD'));

});

function setFinancialPeriodText(start, end) {
    $(".finPrdEndText").text(moment.utc(end).format("DD MMMM YYYY"));
    $(".finPrdStartText").text(moment.utc(start).format("DD MMMM YYYY"));
    $(".finPrdEnd12Text").text(moment(end, 'YYYY-MM-DD').add(1, 'years').format('YYYY-MM-DD'));
}