<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class='contour'>
                    <h2>3. Tax residency</h2>
                    {{#if validationErrors }}
                    {{#each validationErrors }}
                    {{renderValidationMessage this.msg this.field}}
                    {{/each}}
                    {{/if}}
                    <form method="POST" class='enquiry' autocomplete="off" id="submitForm">
                        <input type="text" hidden name="csrf-token" value="{{csrfToken}}">
                        <div class="container-fluid">
                            <h4 class="page-title"></h4>
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="card">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <div class="form-group mb-3">
                                                        <label class="mb-2" for="ResidencyOutsideBVIYesOrNo">
                                                            Does the entity intend to make a claim of tax residency outside the Virgin Islands under rule 2?</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4" align="right">
                                                    <div class="radio form-check-inline">
                                                        <input type="radio" id="ResidencyOutsideBVIYes"
                                                            name="ResidencyOutsideBVIYesOrNo" value="Yes"
                                                            {{#unless entry.pending_tax_resident}}
                                                                {{#ifEquals entry.tax_residency.resident_in_BVI false}}
                                                                   checked
                                                                {{/ifEquals}}
                                                            {{/unless}}
                                                            data-toggle="tooltip" data-placement="top"
                                                               title="If yes, please provide evidence of tax residency in another jurisdiction which may include a certification by the relevant tax authority or copy of the annual tax return payment receipt.">
                                                        <label for="ResidencyOutsideBVIYes">Yes</label>
                                                    </div>
                                                    <div class="radio form-check-inline">
                                                        <input type="radio" id="ResidencyOutsideBVINo"
                                                            name="ResidencyOutsideBVIYesOrNo" value="No"
                                                            {{#unless entry.pending_tax_resident}}
                                                                {{#ifEquals  entry.tax_residency.resident_in_BVI true}}
                                                               checked
                                                                {{/ifEquals}}
                                                            {{/unless}}

                                                            data-toggle="tooltip" data-placement="top"
                                                            title="ES Act deems all companies tax resident in the BVI. Only those companies that are actually tax resident in another jurisdiction will be required to submit evidence thereto.">
                                                        <label for="ResidencyOutsideBVINo">No</label>
                                                    </div>
                                                </div>
                                            </div>
                                            <div id="Showrest" class="hide-element">
                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <label class="mb-2" for="EntityJurisdiction">Select the entity’s
                                                            jurisdiction of tax residency: {{entry.tax_residency.entity_jurisdiction}}</label>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <select class="form-control w-100 pb-1" id="EntityJurisdiction"
                                                            name="EntityJurisdiction" data-toggle="select2"
                                                            >
                                                            <option value="" {{#ifEquals entry.tax_residency.entity_jurisdiction "" }} selected {{/ifEquals}}></option>
                                                            <option value="Afghanistan" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Afghanistan'}} selected {{/ifEquals}}>Afghanistan</option>
                                                            <option value="Åland Islands" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Åland Islands'}} selected {{/ifEquals}}>Aland Islands</option>
                                                            <option value="Albania" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Albania'}} selected {{/ifEquals}}>Albania</option>
                                                            <option value="Algeria" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Algeria'}} selected {{/ifEquals}}>Algeria</option>
                                                            <option value="American Samoa" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'American Samoa'}} selected {{/ifEquals}}>American Samoa</option>
                                                            <option value="Andorra" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Andorra'}} selected {{/ifEquals}}>Andorra</option>
                                                            <option value="Angola" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Angola'}} selected {{/ifEquals}}>Angola</option>
                                                            <option value="Anguilla" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Anguilla'}} selected {{/ifEquals}}>Anguilla</option>
                                                            <option value="Antarctica" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Antarctica'}} selected {{/ifEquals}}>Antarctica</option>
                                                            <option value="Antigua and Barbuda" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Antigua and Barbuda'}} selected {{/ifEquals}}>Antigua and Barbuda
                                                            </option>
                                                            <option value="Argentina" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Argentina'}} selected {{/ifEquals}}>Argentina</option>
                                                            <option value="Armenia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Armenia'}} selected {{/ifEquals}}>Armenia</option>
                                                            <option value="Aruba" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Aruba'}} selected {{/ifEquals}}>Aruba</option>
                                                            <option value="Australia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Australia'}} selected {{/ifEquals}}>Australia</option>
                                                            <option value="Austria" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Austria'}} selected {{/ifEquals}}>Austria</option>
                                                            <option value="Azerbaijan" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Azerbaijan'}} selected {{/ifEquals}}>Azerbaijan</option>
                                                            <option value="Bahamas" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Bahamas'}} selected {{/ifEquals}}>Bahamas</option>
                                                            <option value="Bahrain" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Bahrain'}} selected {{/ifEquals}}>Bahrain</option>
                                                            <option value="Bangladesh" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Bangladesh'}} selected {{/ifEquals}}>Bangladesh</option>
                                                            <option value="Barbados" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Barbados'}} selected {{/ifEquals}}>Barbados</option>
                                                            <option value="Belize" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Belize'}} selected {{/ifEquals}}>Belize</option>
                                                            <option value="Belarus" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Belarus'}} selected {{/ifEquals}}>Belarus</option>
                                                            <option value="Belgium" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Belgium'}} selected {{/ifEquals}}>Belgium</option>
                                                            <option value="Benin" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Benin'}} selected {{/ifEquals}}>Benin</option>
                                                            <option value="Bermuda" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Bermuda'}} selected {{/ifEquals}}>Bermuda</option>
                                                            <option value="Bhutan" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Bhutan'}} selected {{/ifEquals}}>Bhutan</option>
                                                            <option value="Bolivia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Bolivia'}} selected {{/ifEquals}}>Bolivia</option>
                                                            <option value="Bonaire" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Bonaire'}} selected {{/ifEquals}}>Bonaire</option>
                                                            <option value="Bosnia and Herzegovina" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Bosnia and Herzegovina'}} selected {{/ifEquals}}>Bosnia and
                                                                Herzegovina
                                                            </option>
                                                            <option value="Botswana" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Botswana'}} selected {{/ifEquals}}>Botswana</option>
                                                            <option value="Bouvet Island" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Bouvet Island'}} selected {{/ifEquals}}>Bouvet Island</option>
                                                            <option value="Brazil" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Brazil'}} selected {{/ifEquals}}>Brazil</option>
                                                            <option value="British Indian Ocean Territory" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'British Indian Ocean Territory'}} selected {{/ifEquals}}>British
                                                                Indian Ocean Territory
                                                            </option>
                                                            <option value="Brunei Darussalam" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Brunei Darussalam'}} selected {{/ifEquals}}>Brunei Darussalam</option>
                                                            <option value="Bulgaria" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Bulgaria'}} selected {{/ifEquals}}>Bulgaria</option>
                                                            <option value="Burkina Faso" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Burkina Faso'}} selected {{/ifEquals}}>Burkina Faso</option>
                                                            <option value="Burundi" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Burundi'}} selected {{/ifEquals}}>Burundi</option>
                                                            <option value="Cambodia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Cambodia'}} selected {{/ifEquals}}>Cambodia</option>
                                                            <option value="Cameroon" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Cameroon'}} selected {{/ifEquals}}>Cameroon</option>
                                                            <option value="Canada" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Canada'}} selected {{/ifEquals}}>Canada</option>
                                                            <option value="Cape Verde" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Cape Verde'}} selected {{/ifEquals}}>Cape Verde</option>
                                                            <option value="Cayman Islands" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Cayman Islands'}} selected {{/ifEquals}}>Cayman Islands</option>
                                                            <option value="Central African Republic" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Central African Republic'}} selected {{/ifEquals}}>Central African
                                                                Republic
                                                            </option>
                                                            <option value="Chad" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Chad'}} selected {{/ifEquals}}>Chad</option>
                                                            <option value="Chile" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Chile'}} selected {{/ifEquals}}>Chile</option>
                                                            <option value="China" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'China'}} selected {{/ifEquals}}>China</option>
                                                            <option value="Christmas Island" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Christmas Island'}} selected {{/ifEquals}}>Christmas Island</option>
                                                            <option value="Cocos (Keeling) Islands" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Cocos (Keeling) Islands'}} selected {{/ifEquals}}>Cocos (Keeling)
                                                                Islands
                                                            </option>
                                                            <option value="Colombia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Colombia'}} selected {{/ifEquals}}>Colombia</option>
                                                            <option value="Comoros" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Comoros'}} selected {{/ifEquals}}>Comoros</option>
                                                            <option value="Congo" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Congo'}} selected {{/ifEquals}}>Congo</option>
                                                            <option value="Congo, The Democratic Republic of The" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Congo, The Democratic Republic of The'}} selected {{/ifEquals}}>Congo,
                                                                The Democratic Republic of The
                                                            </option>
                                                            <option value="Cook Islands" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Cook Islands'}} selected {{/ifEquals}}>Cook Islands</option>
                                                            <option value="Costa Rica" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Costa Rica'}} selected {{/ifEquals}}>Costa Rica</option>
                                                            <option value="Cote D'ivoire" {{#ifEquals  entry.tax_residency.entity_jurisdiction "Cote D'ivoire"}} selected {{/ifEquals}}>Cote D'ivoire</option>
                                                            <option value="Croatia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Croatia'}} selected {{/ifEquals}}>Croatia</option>
                                                            <option value="Cuba" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Cuba'}} selected {{/ifEquals}}>Cuba</option>
                                                            <option value="Curacao" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Curacao'}} selected {{/ifEquals}}>Curacao</option>
                                                            <option value="Cyprus" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Cyprus'}} selected {{/ifEquals}}>Cyprus</option>
                                                            <option value="Czech Republic" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Czech Republic'}} selected {{/ifEquals}}>Czech Republic</option>
                                                            <option value="Denmark" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Denmark'}} selected {{/ifEquals}}>Denmark</option>
                                                            <option value="Djibouti" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Djibouti'}} selected {{/ifEquals}}>Djibouti</option>
                                                            <option value="Dominica" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Dominica'}} selected {{/ifEquals}}>Dominica</option>
                                                            <option value="Dominican Republic" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Dominican Republic'}} selected {{/ifEquals}}>Dominican Republic
                                                            </option>
                                                            <option value="Ecuador" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Ecuador'}} selected {{/ifEquals}}>Ecuador</option>
                                                            <option value="Egypt" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Egypt'}} selected {{/ifEquals}}>Egypt</option>
                                                            <option value="El Salvador" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'El Salvador'}} selected {{/ifEquals}}>El Salvador</option>
                                                            <option value="Equatorial Guinea" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Equatorial Guinea'}} selected {{/ifEquals}}>Equatorial Guinea</option>
                                                            <option value="Eritrea" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Eritrea'}} selected {{/ifEquals}}>Eritrea</option>
                                                            <option value="Estonia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Estonia'}} selected {{/ifEquals}}>Estonia</option>
                                                            <option value="Ethiopia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Ethiopia'}} selected {{/ifEquals}}>Ethiopia</option>
                                                            <option value="Falkland Islands (Malvinas)" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Falkland Islands (Malvinas)'}} selected {{/ifEquals}}>Falkland Islands
                                                                (Malvinas)
                                                            </option>
                                                            <option value="Faroe Islands" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Faroe Islands'}} selected {{/ifEquals}}>Faroe Islands</option>
                                                            <option value="Fiji" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Fiji'}} selected {{/ifEquals}}>Fiji</option>
                                                            <option value="Finland" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Finland'}} selected {{/ifEquals}}>Finland</option>
                                                            <option value="France" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'France'}} selected {{/ifEquals}}>France</option>
                                                            <option value="French Guiana" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'French Guiana'}} selected {{/ifEquals}}>French Guiana</option>
                                                            <option value="French Polynesia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'French Polynesia'}} selected {{/ifEquals}}>French Polynesia</option>
                                                            <option value="French Southern Territories" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'French Southern Territories'}} selected {{/ifEquals}}>French Southern
                                                                Territories
                                                            </option>
                                                            <option value="Gabon" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Gabon'}} selected {{/ifEquals}}>Gabon</option>
                                                            <option value="Gambia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Gambia'}} selected {{/ifEquals}}>Gambia</option>
                                                            <option value="Georgia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Georgia'}} selected {{/ifEquals}}>Georgia</option>
                                                            <option value="Germany" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Germany'}} selected {{/ifEquals}}>Germany</option>
                                                            <option value="Ghana" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Ghana'}} selected {{/ifEquals}}>Ghana</option>
                                                            <option value="Gibraltar {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Gibraltar'}} selected {{/ifEquals}}">Gibraltar</option>
                                                            <option value="Greece" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Greece'}} selected {{/ifEquals}}>Greece</option>
                                                            <option value="Greenland" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Greenland'}} selected {{/ifEquals}}>Greenland</option>
                                                            <option value="Grenada" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Grenada'}} selected {{/ifEquals}}>Grenada</option>
                                                            <option value="Guadeloupe" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Guadeloupe'}} selected {{/ifEquals}}>Guadeloupe</option>
                                                            <option value="Guam" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Guam'}} selected {{/ifEquals}}>Guam</option>
                                                            <option value="Guatemala" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Guatemala'}} selected {{/ifEquals}}>Guatemala</option>
                                                            <option value="Guernsey" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Guernsey'}} selected {{/ifEquals}}>Guernsey</option>
                                                            <option value="Guinea" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Guinea'}} selected {{/ifEquals}}>Guinea</option>
                                                            <option value="Guinea-bissau" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Guinea-bissau'}} selected {{/ifEquals}}>Guinea-bissau</option>
                                                            <option value="Guyana" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Guyana'}} selected {{/ifEquals}}>Guyana</option>
                                                            <option value="Haiti" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Haiti'}} selected {{/ifEquals}}>Haiti</option>
                                                            <option value="Heard Island and Mcdonald Islands" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Heard Island and Mcdonald Islands'}} selected {{/ifEquals}}>Heard
                                                                Island and Mcdonald Islands
                                                            </option>
                                                            <option value="Holy See (Vatican City State)" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Holy See (Vatican City State)'}} selected {{/ifEquals}}>Holy See
                                                                (Vatican City State)
                                                            </option>
                                                            <option value="Honduras" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Honduras'}} selected {{/ifEquals}}>Honduras</option>
                                                            <option value="Hong Kong" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Hong Kong'}} selected {{/ifEquals}}>Hong Kong</option>
                                                            <option value="Hungary" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Hungary'}} selected {{/ifEquals}}>Hungary</option>
                                                            <option value="Iceland" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Iceland'}} selected {{/ifEquals}}>Iceland</option>
                                                            <option value="India" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'India'}} selected {{/ifEquals}}>India</option>
                                                            <option value="Indonesia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Indonesia'}} selected {{/ifEquals}}>Indonesia</option>
                                                            <option value="Iran, Islamic Republic of" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Iran, Islamic Republic of'}} selected {{/ifEquals}}>Iran, Islamic
                                                                Republic of
                                                            </option>
                                                            <option value="Iraq" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Iraq'}} selected {{/ifEquals}}>Iraq</option>
                                                            <option value="Ireland" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Ireland'}} selected {{/ifEquals}}>Ireland</option>
                                                            <option value="Isle of Man" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Isle of Man'}} selected {{/ifEquals}}>Isle of Man</option>
                                                            <option value="Israel" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Israel'}} selected {{/ifEquals}}>Israel</option>
                                                            <option value="Italy" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Italy'}} selected {{/ifEquals}}>Italy</option>
                                                            <option value="Jamaica" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Jamaica'}} selected {{/ifEquals}}>Jamaica</option>
                                                            <option value="Japan" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Japan'}} selected {{/ifEquals}}>Japan</option>
                                                            <option value="Jersey" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Jersey'}} selected {{/ifEquals}}>Jersey</option>
                                                            <option value="Jordan" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Jordan'}} selected {{/ifEquals}}>Jordan</option>
                                                            <option value="Kazakhstan" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Kazakhstan'}} selected {{/ifEquals}}>Kazakhstan</option>
                                                            <option value="Kenya" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Kenya'}} selected {{/ifEquals}}>Kenya</option>
                                                            <option value="Kiribati" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Kiribati'}} selected {{/ifEquals}}>Kiribati</option>
                                                            <option value="Korea, Democratic People's Republic of" {{#ifEquals  entry.tax_residency.entity_jurisdiction "Korea, Democratic People's Republic of"}} selected {{/ifEquals}}>
                                                                Korea, Democratic People's Republic of
                                                            </option>
                                                            <option value="Korea, Republic of" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Korea, Republic of'}} selected {{/ifEquals}}>Korea, Republic of
                                                            </option>
                                                            <option value="Kuwait" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Kuwait'}} selected {{/ifEquals}}>Kuwait</option>
                                                            <option value="Kyrgyzstan" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Kyrgyzstan'}} selected {{/ifEquals}}>Kyrgyzstan</option>
                                                            <option value="Lao People's Democratic Republic" {{#ifEquals  entry.tax_residency.entity_jurisdiction "Lao People's Democratic Republic"}} selected {{/ifEquals}}>Lao
                                                                People's Democratic Republic
                                                            </option>
                                                            <option value="Latvia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Latvia'}} selected {{/ifEquals}}>Latvia</option>
                                                            <option value="Lebanon" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Lebanon'}} selected {{/ifEquals}}>Lebanon</option>
                                                            <option value="Lesotho" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Lesotho'}} selected {{/ifEquals}}>Lesotho</option>
                                                            <option value="Liberia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Liberia'}} selected {{/ifEquals}}>Liberia</option>
                                                            <option value="Libyan Arab Jamahiriya" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Libyan Arab Jamahiriya'}} selected {{/ifEquals}}>Libyan Arab
                                                                Jamahiriya
                                                            </option>
                                                            <option value="Liechtenstein" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Liechtenstein'}} selected {{/ifEquals}}>Liechtenstein</option>
                                                            <option value="Lithuania" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Lithuania'}} selected {{/ifEquals}}>Lithuania</option>
                                                            <option value="Luxembourg" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Luxembourg'}} selected {{/ifEquals}}>Luxembourg</option>
                                                            <option value="Macao" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Macao'}} selected {{/ifEquals}}>Macao</option>
                                                            <option value="Macedonia, The Former Yugoslav Republic of" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Macedonia, The Former Yugoslav Republic of'}} selected {{/ifEquals}}>
                                                                Macedonia, The Former Yugoslav Republic of
                                                            </option>
                                                            <option value="Madagascar" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Madagascar'}} selected {{/ifEquals}}>Madagascar</option>
                                                            <option value="Malawi" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Malawi'}} selected {{/ifEquals}}>Malawi</option>
                                                            <option value="Malaysia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Malaysia'}} selected {{/ifEquals}}>Malaysia</option>
                                                            <option value="Maldives" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Maldives'}} selected {{/ifEquals}}>Maldives</option>
                                                            <option value="Mali" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Mali'}} selected {{/ifEquals}}>Mali</option>
                                                            <option value="Malta" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Malta'}} selected {{/ifEquals}}>Malta</option>
                                                            <option value="Marshall Islands" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Marshall Islands'}} selected {{/ifEquals}}>Marshall Islands</option>
                                                            <option value="Martinique" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Martinique'}} selected {{/ifEquals}}>Martinique</option>
                                                            <option value="Mauritania" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Mauritania'}} selected {{/ifEquals}}>Mauritania</option>
                                                            <option value="Mauritius" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Mauritius'}} selected {{/ifEquals}}>Mauritius</option>
                                                            <option value="Mayotte" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Mayotte'}} selected {{/ifEquals}}>Mayotte</option>
                                                            <option value="Mexico" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Mexico'}} selected {{/ifEquals}}>Mexico</option>
                                                            <option value="Micronesia, Federated States of" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Micronesia, Federated States of'}} selected {{/ifEquals}}>Micronesia,
                                                                Federated States of
                                                            </option>
                                                            <option value="Moldova, Republic of" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Moldova, Republic of'}} selected {{/ifEquals}}>Moldova, Republic of
                                                            </option>
                                                            <option value="Monaco" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Monaco'}} selected {{/ifEquals}}>Monaco</option>
                                                            <option value="Mongolia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Mongolia'}} selected {{/ifEquals}}>Mongolia</option>
                                                            <option value="Montenegro" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Montenegro'}} selected {{/ifEquals}}>Montenegro</option>
                                                            <option value="Montserrat" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Montserrat'}} selected {{/ifEquals}}>Montserrat</option>
                                                            <option value="Morocco" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Morocco'}} selected {{/ifEquals}}>Morocco</option>
                                                            <option value="Mozambique" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Mozambique'}} selected {{/ifEquals}}>Mozambique</option>
                                                            <option value="Myanmar" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Myanmar'}} selected {{/ifEquals}}>Myanmar</option>
                                                            <option value="Namibia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Namibia'}} selected {{/ifEquals}}>Namibia</option>
                                                            <option value="Nauru" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Nauru'}} selected {{/ifEquals}}>Nauru</option>
                                                            <option value="Nepal" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Nepal'}} selected {{/ifEquals}}>Nepal</option>
                                                            <option value="Netherlands" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Netherlands'}} selected {{/ifEquals}}>Netherlands</option>
                                                            <option value="Netherlands Antilles" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Netherlands Antilles'}} selected {{/ifEquals}}>Netherlands Antilles
                                                            </option>
                                                            <option value="New Caledonia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'New Caledonia'}} selected {{/ifEquals}}>New Caledonia</option>
                                                            <option value="New Zealand" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'New Zealand'}} selected {{/ifEquals}}>New Zealand</option>
                                                            <option value="Nicaragua" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Nicaragua'}} selected {{/ifEquals}}>Nicaragua</option>
                                                            <option value="Niger" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Niger'}} selected {{/ifEquals}}>Niger</option>
                                                            <option value="Nigeria" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Nigeria'}} selected {{/ifEquals}}>Nigeria</option>
                                                            <option value="Niue" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Niue'}} selected {{/ifEquals}}>Niue</option>
                                                            <option value="Norfolk Island" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Norfolk Island'}} selected {{/ifEquals}}>Norfolk Island</option>
                                                            <option value="Northern Mariana Islands" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Northern Mariana Islands'}} selected {{/ifEquals}}>Northern Mariana
                                                                Islands
                                                            </option>
                                                            <option value="Norway" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Norway'}} selected {{/ifEquals}}>Norway</option>
                                                            <option value="Oman" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Oman'}} selected {{/ifEquals}}>Oman</option>
                                                            <option value="Pakistan" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Pakistan'}} selected {{/ifEquals}}>Pakistan</option>
                                                            <option value="Palau" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Palau'}} selected {{/ifEquals}}>Palau</option>
                                                            <option value="Palestinian Territory, Occupied" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Palestinian Territory, Occupied'}} selected {{/ifEquals}}>Palestinian
                                                                Territory, Occupied
                                                            </option>
                                                            <option value="Panama" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Panama'}} selected {{/ifEquals}}>Panama</option>
                                                            <option value="Papua New Guinea" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Papua New Guinea'}} selected {{/ifEquals}}>Papua New Guinea</option>
                                                            <option value="Paraguay" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Paraguay'}} selected {{/ifEquals}}>Paraguay</option>
                                                            <option value="Peru" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Peru'}} selected {{/ifEquals}}>Peru</option>
                                                            <option value="Philippines" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Philippines'}} selected {{/ifEquals}}>Philippines</option>
                                                            <option value="Pitcairn" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Pitcairn'}} selected {{/ifEquals}}>Pitcairn</option>
                                                            <option value="Poland" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Poland'}} selected {{/ifEquals}}>Poland</option>
                                                            <option value="Portugal" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Portugal'}} selected {{/ifEquals}}>Portugal</option>
                                                            <option value="Puerto Rico" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Puerto Rico'}} selected {{/ifEquals}}>Puerto Rico</option>
                                                            <option value="Qatar" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Qatar'}} selected {{/ifEquals}}>Qatar</option>
                                                            <option value="Reunion" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Reunion'}} selected {{/ifEquals}}>Reunion</option>
                                                            <option value="Romania" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Romania'}} selected {{/ifEquals}}>Romania</option>
                                                            <option value="Russian Federation" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Russian Federation'}} selected {{/ifEquals}}>Russian Federation
                                                            </option>
                                                            <option value="Rwanda" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Rwanda'}} selected {{/ifEquals}}>Rwanda</option>
                                                            <option value="Saint Helena" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Saint Helena'}} selected {{/ifEquals}}>Saint Helena</option>
                                                            <option value="Saint Kitts and Nevis" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Saint Kitts and Nevis'}} selected {{/ifEquals}}>Saint Kitts and
                                                                Nevis
                                                            </option>
                                                            <option value="Saint Lucia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Saint Lucia'}} selected {{/ifEquals}}>Saint Lucia</option>
                                                            <option value="Saint Pierre and Miquelon" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Saint Pierre and Miquelon'}} selected {{/ifEquals}}>Saint Pierre and
                                                                Miquelon
                                                            </option>
                                                            <option value="Saint Vincent and The Grenadines" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Saint Vincent and The Grenadines'}} selected {{/ifEquals}}>Saint
                                                                Vincent and The Grenadines
                                                            </option>
                                                            <option value="Samoa" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Samoa'}} selected {{/ifEquals}}>Samoa</option>
                                                            <option value="San Marino" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'San Marino'}} selected {{/ifEquals}}>San Marino</option>
                                                            <option value="Sao Tome and Principe" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Sao Tome and Principe'}} selected {{/ifEquals}}>Sao Tome and
                                                                Principe
                                                            </option>
                                                            <option value="Saudi Arabia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Saudi Arabia'}} selected {{/ifEquals}}>Saudi Arabia</option>
                                                            <option value="Senegal" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Senegal'}} selected {{/ifEquals}}>Senegal</option>
                                                            <option value="Serbia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Serbia'}} selected {{/ifEquals}}>Serbia</option>
                                                            <option value="Seychelles" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Seychelles'}} selected {{/ifEquals}}>Seychelles</option>
                                                            <option value="Sierra Leone" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Sierra Leone'}} selected {{/ifEquals}}>Sierra Leone</option>
                                                            <option value="Singapore" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Singapore'}} selected {{/ifEquals}}>Singapore</option>
                                                            <option value="Slovakia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Slovakia'}} selected {{/ifEquals}}>Slovakia</option>
                                                            <option value="Slovenia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Slovenia'}} selected {{/ifEquals}}>Slovenia</option>
                                                            <option value="Solomon Islands" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Solomon Islands'}} selected {{/ifEquals}}>Solomon Islands</option>
                                                            <option value="Somalia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Somalia'}} selected {{/ifEquals}}>Somalia</option>
                                                            <option value="South Africa" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'South Africa'}} selected {{/ifEquals}}>South Africa</option>
                                                            <option
                                                                value="South Georgia and The South Sandwich Islands" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'South Georgia and The South Sandwich Islands'}} selected {{/ifEquals}}>
                                                                South Georgia and The South Sandwich Islands
                                                            </option>
                                                            <option value="Spain" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Spain'}} selected {{/ifEquals}}>Spain</option>
                                                            <option value="Sri Lanka" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Sri Lanka'}} selected {{/ifEquals}}>Sri Lanka</option>
                                                            <option value="Sudan" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Sudan'}} selected {{/ifEquals}}>Sudan</option>
                                                            <option value="Suriname" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Suriname'}} selected {{/ifEquals}}>Suriname</option>
                                                            <option value="Svalbard and Jan Mayen" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Svalbard and Jan Mayen'}} selected {{/ifEquals}}>Svalbard and Jan
                                                                Mayen
                                                            </option>
                                                            <option value="Swaziland" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Swaziland'}} selected {{/ifEquals}}>Swaziland</option>
                                                            <option value="Sweden" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Sweden'}} selected {{/ifEquals}}>Sweden</option>
                                                            <option value="Switzerland" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Switzerland'}} selected {{/ifEquals}}>Switzerland</option>
                                                            <option value="Syrian Arab Republic" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Syrian Arab Republic'}} selected {{/ifEquals}}>Syrian Arab Republic
                                                            </option>
                                                            <option value="Taiwan, Province of China" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Taiwan, Province of China'}} selected {{/ifEquals}}>Taiwan, Province
                                                                of China
                                                            </option>
                                                            <option value="Tajikistan" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Tajikistan'}} selected {{/ifEquals}}>Tajikistan</option>
                                                            <option value="Tanzania, United Republic of" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Tanzania, United Republic of'}} selected {{/ifEquals}}>Tanzania,
                                                                United Republic of
                                                            </option>
                                                            <option value="Thailand" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Thailand'}} selected {{/ifEquals}}>Thailand</option>
                                                            <option value="Timor-leste" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Timor-leste'}} selected {{/ifEquals}}>Timor-leste</option>
                                                            <option value="Togo" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Togo'}} selected {{/ifEquals}}>Togo</option>
                                                            <option value="Tokelau" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Tokelau'}} selected {{/ifEquals}}>Tokelau</option>
                                                            <option value="Tonga" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Tonga'}} selected {{/ifEquals}}>Tonga</option>
                                                            <option value="Trinidad and Tobago" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Trinidad and Tobago'}} selected {{/ifEquals}}>Trinidad and Tobago
                                                            </option>
                                                            <option value="Tunisia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Tunisia'}} selected {{/ifEquals}}>Tunisia</option>
                                                            <option value="Turkey" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Turkey'}} selected {{/ifEquals}}>Turkey</option>
                                                            <option value="Turkmenistan" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Turkmenistan'}} selected {{/ifEquals}}>Turkmenistan</option>
                                                            <option value="Turks and Caicos Islands" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Turks and Caicos Islands'}} selected {{/ifEquals}}>Turks and Caicos
                                                                Islands
                                                            </option>
                                                            <option value="Tuvalu" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Tuvalu'}} selected {{/ifEquals}}>Tuvalu</option>
                                                            <option value="Uganda" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Uganda'}} selected {{/ifEquals}}>Uganda</option>
                                                            <option value="Ukraine" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Ukraine'}} selected {{/ifEquals}}>Ukraine</option>
                                                            <option value="United Arab Emirates" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'United Arab Emirates'}} selected {{/ifEquals}}>United Arab Emirates
                                                            </option>
                                                            <option value="United Kingdom" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'United Kingdom'}} selected {{/ifEquals}}>United Kingdom</option>
                                                            <option value="United States" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'United States'}} selected {{/ifEquals}}>United States</option>
                                                            <option value="United States Minor Outlying Islands" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'United States Minor Outlying Islands'}} selected {{/ifEquals}}>United
                                                                States Minor Outlying Islands
                                                            </option>
                                                            <option value="Uruguay" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Uruguay'}} selected {{/ifEquals}}>Uruguay</option>
                                                            <option value="Uzbekistan" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Uzbekistan'}} selected {{/ifEquals}}>Uzbekistan</option>
                                                            <option value="Venezuela" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Venezuela'}} selected {{/ifEquals}}>Venezuela</option>
                                                            <option value="Viet Nam" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Viet Nam'}} selected {{/ifEquals}}>Viet Nam</option>
                                                            <option value="Virgin Islands, American" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Virgin Islands, American'}} selected {{/ifEquals}}>Virgin Islands,
                                                                American
                                                            </option>
                                                            <option value="Virgin Islands, British" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Virgin Islands, British'}} selected {{/ifEquals}}>Virgin Islands,
                                                                British
                                                            </option>
                                                            <option value="Wallis and Futuna" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Wallis and Futuna'}} selected {{/ifEquals}}>Wallis and Futuna</option>
                                                            <option value="Vanuatu" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Vanuatu'}} selected {{/ifEquals}}>Vanuatu</option>
                                                            <option value="Western Sahara" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Western Sahara'}} selected {{/ifEquals}}>Western Sahara</option>
                                                            <option value="Yemen" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Yemen'}} selected {{/ifEquals}}>Yemen</option>
                                                            <option value="Zambia" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Zambia'}} selected {{/ifEquals}}>Zambia</option>
                                                            <option value="Zimbabwe" {{#ifEquals  entry.tax_residency.entity_jurisdiction 'Zimbabwe'}} selected {{/ifEquals}}>Zimbabwe</option>
                                                        </select>
                                                        <br>
                                                        <br>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <div id="ShowAlert" class="hide-element">
                                                            <p class="alert alert-danger">This country is blacklisted
                                                                and cannot be accepted.</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <div class="form-group mb-3">
                                                            <label class="mb-2" for="foreign_tax_id_number">Taxpayer identification number (“TIN”) or other identification reference number:</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group mb-3">
                                                            <input type="text" id="foreign_tax_id_number"
                                                                class="form-control" name="foreign_tax_id_number"
                                                                value="{{entry.tax_residency.foreign_tax_id_number}}">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <div class="form-group mb-3">
                                                            <label class="mb-2" for="MNE_group_name">Name of MNE group (if different):</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group mb-3">
                                                            <input type="text" id="MNE_group_name"
                                                                   class="form-control" name="MNE_group_name"
                                                                   value="{{entry.tax_residency.MNE_group_name}}">
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- DOES THE ENTITY HAVE A PARENT ENTITY? -->
                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <div class="form-group mb-3">
                                                            <label class="mb-2" for="haveParentEntityYesOrNo">Does the
                                                                entity have a parent entity?</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4 text-right">
                                                        <div class="radio form-check-inline">
                                                            <input type="radio" id="haveParentEntityYes" {{#unless
                                                                    entry.tax_residency.have_parent_entity_not_answered }}
                                                                {{#ifEquals entry.tax_residency.have_parent_entity
                                                                            true}}checked class="checkOn" {{/ifEquals}} {{/unless}}
                                                                   name="haveParentEntityYesOrNo" value="Yes">
                                                            <label for="haveParentEntityYes">Yes</label>
                                                        </div>
                                                        <div class="radio form-check-inline">
                                                            <input type="radio" id="haveParentEntityNo" {{#unless
                                                                    entry.tax_residency.have_parent_entity_not_answered }}
                                                                {{#ifEquals entry.tax_residency.have_parent_entity
                                                                            false}}checked class="checkOn" {{/ifEquals}} {{/unless}}
                                                                   name="haveParentEntityYesOrNo" value="No">
                                                            <label for="haveParentEntityNo">No</label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div id="ShowParentEntityYes" class="pl-3 hide-element">
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <div class="form-group mb-3">
                                                                <label class="mb-2" for="parent_entity_name">Parent
                                                                    entity name</label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="form-group mb-3">
                                                                <input type="text" id="parent_entity_name"
                                                                       class="form-control" name="parent_entity_name"
                                                                       value="{{entry.tax_residency.parent_entity_name}}"
                                                                       data-toggle="tooltip" data-placement="top"
                                                                       title="A parent entity of a corporate or legal entity is the entity which holds, directly or indirectly, either the beneficial interest in 75% or more of the shares or voting rights of the corporate or legal entity ">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <div class="form-group mb-3">
                                                                <label class="mb-2"
                                                                       for="parent_entity_alternative_name">Parent entity
                                                                    alternative name</label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="form-group mb-3">
                                                                <input type="text" id="parent_entity_alternative_name"
                                                                       class="form-control"
                                                                       name="parent_entity_alternative_name"
                                                                       value="{{entry.tax_residency.parent_entity_alternative_name}}">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <div class="form-group mb-3">
                                                                <label class="mb-2"
                                                                       for="parent_entity_jurisdiction">Parent Entity's
                                                                    jurisdiction
                                                                    of formation</label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="form-group mb-3">
                                                                <select class="form-control w-100 pb-1"
                                                                        id="parent_entity_jurisdiction"
                                                                        name="parent_entity_jurisdiction"
                                                                        data-toggle="select2">
                                                                    <option value="" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction ""}} selected {{/ifEquals}}
                                                                    ></option>
                                                                    <option value="Afghanistan" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Afghanistan"}} selected {{/ifEquals}}
                                                                    >Afghanistan</option>
                                                                    <option value="Åland Islands" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Åland Islands"}} selected {{/ifEquals}}
                                                                    >Aland Islands</option>
                                                                    <option value="Albania" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Albania"}} selected {{/ifEquals}}
                                                                    >Albania</option>
                                                                    <option value="Algeria" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Algeria"}} selected {{/ifEquals}}
                                                                    >Algeria</option>
                                                                    <option value="American Samoa" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "American Samoa"}} selected {{/ifEquals}}
                                                                    >American Samoa
                                                                    </option>
                                                                    <option value="Andorra" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Andorra"}} selected {{/ifEquals}}
                                                                    >Andorra</option>
                                                                    <option value="Angola" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Angola"}} selected {{/ifEquals}}
                                                                    >Angola</option>
                                                                    <option value="Anguilla" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Anguilla"}} selected {{/ifEquals}}
                                                                    >Anguilla</option>
                                                                    <option value="Antarctica" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Antarctica"}} selected {{/ifEquals}}
                                                                    >Antarctica</option>
                                                                    <option value="Antigua and Barbuda" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Antigua and Barbuda"}} selected {{/ifEquals}}
                                                                    >Antigua and
                                                                        Barbuda
                                                                    </option>
                                                                    <option value="Argentina" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Argentina"}} selected {{/ifEquals}}
                                                                    >Argentina</option>
                                                                    <option value="Armenia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Armenia"}} selected {{/ifEquals}}
                                                                    >Armenia</option>
                                                                    <option value="Aruba" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Aruba"}} selected {{/ifEquals}}
                                                                    >Aruba</option>
                                                                    <option value="Australia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Australia"}} selected {{/ifEquals}}
                                                                    >Australia</option>
                                                                    <option value="Austria" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Austria"}} selected {{/ifEquals}}
                                                                    >Austria</option>
                                                                    <option value="Azerbaijan" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Azerbaijan"}} selected {{/ifEquals}}
                                                                    >Azerbaijan</option>
                                                                    <option value="Bahamas" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Bahamas"}} selected {{/ifEquals}}
                                                                    >Bahamas</option>
                                                                    <option value="Bahrain" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Bahrain"}} selected {{/ifEquals}}
                                                                    >Bahrain</option>
                                                                    <option value="Bangladesh" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Bangladesh"}} selected {{/ifEquals}}
                                                                    >Bangladesh</option>
                                                                    <option value="Barbados" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Barbados"}} selected {{/ifEquals}}
                                                                    >Barbados</option>
                                                                    <option value="Belize" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Belize"}} selected {{/ifEquals}}
                                                                    >Belize</option>
                                                                    <option value="Belarus" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Belarus"}} selected {{/ifEquals}}
                                                                    >Belarus</option>
                                                                    <option value="Belgium" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Belgium"}} selected {{/ifEquals}}
                                                                    >Belgium</option>
                                                                    <option value="Benin" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Benin"}} selected {{/ifEquals}}
                                                                    >Benin</option>
                                                                    <option value="Bermuda" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Bermuda"}} selected {{/ifEquals}}
                                                                    >Bermuda</option>
                                                                    <option value="Bhutan" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Bhutan"}} selected {{/ifEquals}}
                                                                    >Bhutan</option>
                                                                    <option value="Bolivia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Bolivia"}} selected {{/ifEquals}}
                                                                    >Bolivia</option>
                                                                    <option value="Bonaire" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Bonaire"}} selected {{/ifEquals}}
                                                                    >Bonaire</option>
                                                                    <option value="Bosnia and Herzegovina" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Bosnia and Herzegovina"}} selected {{/ifEquals}}
                                                                    >Bosnia and
                                                                        Herzegovina
                                                                    </option>
                                                                    <option value="Botswana" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Botswana"}} selected {{/ifEquals}}
                                                                    >Botswana</option>
                                                                    <option value="Bouvet Island" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Bouvet Island"}} selected {{/ifEquals}}
                                                                    >Bouvet Island</option>
                                                                    <option value="Brazil" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Brazil"}} selected {{/ifEquals}}
                                                                    >Brazil</option>
                                                                    <option value="British Indian Ocean Territory" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "British Indian Ocean Territory"}} selected {{/ifEquals}}
                                                                    >
                                                                        British
                                                                        Indian
                                                                        Ocean Territory
                                                                    </option>
                                                                    <option value="Brunei Darussalam" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Brunei Darussalam"}} selected {{/ifEquals}}
                                                                    >Brunei Darussalam
                                                                    </option>
                                                                    <option value="Bulgaria" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Bulgaria"}} selected {{/ifEquals}}
                                                                    >Bulgaria</option>
                                                                    <option value="Burkina Faso" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Burkina Faso"}} selected {{/ifEquals}}
                                                                    >Burkina Faso</option>
                                                                    <option value="Burundi" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Burundi"}} selected {{/ifEquals}}
                                                                    >Burundi</option>
                                                                    <option value="Cambodia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Cambodia"}} selected {{/ifEquals}}
                                                                    >Cambodia</option>
                                                                    <option value="Cameroon" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Cameroon"}} selected {{/ifEquals}}
                                                                    >Cameroon</option>
                                                                    <option value="Canada" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Canada"}} selected {{/ifEquals}}
                                                                    >Canada</option>
                                                                    <option value="Cape Verde" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Cape Verde"}} selected {{/ifEquals}}
                                                                    >Cape Verde</option>
                                                                    <option value="Cayman Islands" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Cayman Islands"}} selected {{/ifEquals}}
                                                                    >Cayman Islands
                                                                    </option>
                                                                    <option value="Central African Republic" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Central African Republic"}} selected {{/ifEquals}}
                                                                    >Central
                                                                        African
                                                                        Republic
                                                                    </option>
                                                                    <option value="Chad" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Chad"}} selected {{/ifEquals}}
                                                                    >Chad</option>
                                                                    <option value="Chile" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Chile"}} selected {{/ifEquals}}
                                                                    >Chile</option>
                                                                    <option value="China" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "China"}} selected {{/ifEquals}}
                                                                    >China</option>
                                                                    <option value="Christmas Island" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Christmas Island"}} selected {{/ifEquals}}
                                                                    >Christmas Island
                                                                    </option>
                                                                    <option value="Cocos (Keeling) Islands" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Cocos (Keeling) Islands"}} selected {{/ifEquals}}
                                                                    >Cocos
                                                                        (Keeling)
                                                                        Islands
                                                                    </option>
                                                                    <option value="Colombia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Colombia"}} selected {{/ifEquals}}
                                                                    >Colombia</option>
                                                                    <option value="Comoros" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Comoros"}} selected {{/ifEquals}}
                                                                    >Comoros</option>
                                                                    <option value="Congo" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Congo"}} selected {{/ifEquals}}
                                                                    >Congo</option>
                                                                    <option value="Congo, The Democratic Republic of The" 
                                                                    {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Congo, The Democratic Republic of The"}} selected {{/ifEquals}}>
                                                                        Congo,
                                                                        The
                                                                        Democratic Republic of The
                                                                    </option>
                                                                    <option value="Cook Islands" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Cook Islands"}} selected {{/ifEquals}}
                                                                    >Cook Islands</option>
                                                                    <option value="Costa Rica" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Costa Rica"}} selected {{/ifEquals}}
                                                                    >Costa Rica</option>
                                                                    <option value="Cote D'ivoire" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Cote D'ivoire"}} selected {{/ifEquals}}
                                                                    >Cote D'ivoire</option>
                                                                    <option value="Croatia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Croatia"}} selected {{/ifEquals}}
                                                                    >Croatia</option>
                                                                    <option value="Cuba" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Cuba"}} selected {{/ifEquals}}  
                                                                    >Cuba</option>
                                                                    <option value="Curacao" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Curacao"}} selected {{/ifEquals}}
                                                                    >Curacao</option>
                                                                    <option value="Cyprus" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Cyprus"}} selected {{/ifEquals}}
                                                                    >Cyprus</option>
                                                                    <option value="Czech Republic" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Czech Republic"}} selected {{/ifEquals}}
                                                                    >Czech Republic
                                                                    </option>
                                                                    <option value="Denmark" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Denmark"}} selected {{/ifEquals}}
                                                                    >Denmark</option>
                                                                    <option value="Djibouti" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Djibouti"}} selected {{/ifEquals}}
                                                                    >Djibouti</option>
                                                                    <option value="Dominica" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Dominica"}} selected {{/ifEquals}}
                                                                    >Dominica</option>
                                                                    <option value="Dominican Republic" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Dominican Republic"}} selected {{/ifEquals}}
                                                                    >Dominican
                                                                        Republic
                                                                    </option>
                                                                    <option value="Ecuador" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Ecuador"}} selected {{/ifEquals}}
                                                                    >Ecuador</option>
                                                                    <option value="Egypt" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Egypt"}} selected {{/ifEquals}}
                                                                    >Egypt</option>
                                                                    <option value="El Salvador" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "El Salvador"}} selected {{/ifEquals}}
                                                                    >El Salvador</option>
                                                                    <option value="Equatorial Guinea" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Equatorial Guinea"}} selected {{/ifEquals}}>
                                                                        Equatorial Guinea
                                                                    </option>
                                                                    <option value="Eritrea" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Eritrea"}} selected {{/ifEquals}}>
                                                                        Eritrea
                                                                    </option>
                                                                    <option value="Estonia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Estonia"}} selected {{/ifEquals}}>
                                                                        Estonia
                                                                    </option>
                                                                    <option value="Ethiopia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Ethiopia"}} selected {{/ifEquals}}>
                                                                        Ethiopia
                                                                    </option>
                                                                    <option value="Falkland Islands (Malvinas)" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Falkland Islands (Malvinas)"}} selected {{/ifEquals}}>
                                                                        Falkland Islands (Malvinas)
                                                                    </option>
                                                                    <option value="Faroe Islands" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Faroe Islands"}} selected {{/ifEquals}}>
                                                                        Faroe Islands
                                                                    </option>
                                                                    <option value="Fiji" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Fiji"}} selected {{/ifEquals}}>
                                                                        Fiji
                                                                    </option>
                                                                    <option value="Finland" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Finland"}} selected {{/ifEquals}}>
                                                                        Finland
                                                                    </option>
                                                                    <option value="France" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "France"}} selected {{/ifEquals}}>
                                                                        France
                                                                    </option>
                                                                    <option value="French Guiana" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "French Guiana"}} selected {{/ifEquals}}>
                                                                        French Guiana
                                                                    </option>
                                                                    <option value="French Polynesia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "French Polynesia"}} selected {{/ifEquals}}>
                                                                        French Polynesia
                                                                    </option>
                                                                    <option value="French Southern Territories" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "French Southern Territories"}} selected {{/ifEquals}}>
                                                                        French Southern Territories
                                                                    </option>
                                                                    <option value="Gabon" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Gabon"}} selected {{/ifEquals}}>
                                                                        Gabon
                                                                    </option>
                                                                    <option value="Gambia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Gambia"}} selected {{/ifEquals}}>
                                                                        Gambia
                                                                    </option>
                                                                    <option value="Equatorial Guinea" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Equatorial Guinea"}} selected {{/ifEquals}}>
                                                                        Equatorial Guinea
                                                                    </option>
                                                                    <option value="Eritrea" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Eritrea"}} selected {{/ifEquals}}>
                                                                        Eritrea
                                                                    </option>
                                                                    <option value="Estonia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Estonia"}} selected {{/ifEquals}}>
                                                                        Estonia
                                                                    </option>
                                                                    <option value="Ethiopia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Ethiopia"}} selected {{/ifEquals}}>
                                                                        Ethiopia
                                                                    </option>
                                                                    <option value="Falkland Islands (Malvinas)" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Falkland Islands (Malvinas)"}} selected {{/ifEquals}}>
                                                                        Falkland Islands (Malvinas)
                                                                    </option>
                                                                    <option value="Faroe Islands" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Faroe Islands"}} selected {{/ifEquals}}>
                                                                        Faroe Islands
                                                                    </option>
                                                                    <option value="Fiji" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Fiji"}} selected {{/ifEquals}}>
                                                                        Fiji
                                                                    </option>
                                                                    <option value="Finland" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Finland"}} selected {{/ifEquals}}>
                                                                        Finland
                                                                    </option>
                                                                    <option value="France" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "France"}} selected {{/ifEquals}}>
                                                                        France
                                                                    </option>
                                                                    <option value="French Guiana" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "French Guiana"}} selected {{/ifEquals}}>
                                                                        French Guiana
                                                                    </option>
                                                                    <option value="French Polynesia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "French Polynesia"}} selected {{/ifEquals}}>
                                                                        French Polynesia
                                                                    </option>
                                                                    <option value="French Southern Territories" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "French Southern Territories"}} selected {{/ifEquals}}>
                                                                        French Southern Territories
                                                                    </option>
                                                                    <option value="Gabon" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Gabon"}} selected {{/ifEquals}}>
                                                                        Gabon
                                                                    </option>
                                                                    <option value="Gambia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Gambia"}} selected {{/ifEquals}}>
                                                                        Gambia
                                                                    </option>
                                                                    <option value="Georgia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Georgia"}} selected {{/ifEquals}}>
                                                                        Georgia
                                                                    </option>
                                                                    <option value="Germany" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Germany"}} selected {{/ifEquals}}>
                                                                        Germany
                                                                    </option>
                                                                    <option value="Ghana" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Ghana"}} selected {{/ifEquals}}>
                                                                        Ghana
                                                                    </option>
                                                                    <option value="Gibraltar" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Gibraltar"}} selected {{/ifEquals}}>
                                                                        Gibraltar
                                                                    </option>
                                                                    <option value="Greece" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Greece"}} selected {{/ifEquals}}>
                                                                        Greece
                                                                    </option>
                                                                    <option value="Greenland" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Greenland"}} selected {{/ifEquals}}>
                                                                        Greenland
                                                                    </option>
                                                                    <option value="Grenada" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Grenada"}} selected {{/ifEquals}}>
                                                                        Grenada
                                                                    </option>
                                                                    <option value="Guadeloupe" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Guadeloupe"}} selected {{/ifEquals}}>
                                                                        Guadeloupe
                                                                    </option>
                                                                    <option value="Guam" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Guam"}} selected {{/ifEquals}}>
                                                                        Guam
                                                                    </option>
                                                                    <option value="Guatemala" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Guatemala"}} selected {{/ifEquals}}>
                                                                        Guatemala
                                                                    </option>
                                                                    <option value="Guernsey" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Guernsey"}} selected {{/ifEquals}}>
                                                                        Guernsey
                                                                    </option>
                                                                    <option value="Guinea" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Guinea"}} selected {{/ifEquals}}>
                                                                        Guinea
                                                                    </option>
                                                                    <option value="Guinea-bissau" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Guinea-bissau"}} selected {{/ifEquals}}>
                                                                        Guinea-bissau
                                                                    </option>
                                                                    <option value="Guyana" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Guyana"}} selected {{/ifEquals}}>
                                                                        Guyana
                                                                    </option>
                                                                    <option value="Haiti" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Haiti"}} selected {{/ifEquals}}>
                                                                        Haiti
                                                                    </option>
                                                                    <option value="Heard Island and Mcdonald Islands" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Heard Island and Mcdonald Islands"}} selected {{/ifEquals}}>
                                                                        Heard Island and Mcdonald Islands
                                                                    </option>
                                                                    <option value="Holy See (Vatican City State)" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Holy See (Vatican City State)"}} selected {{/ifEquals}}>
                                                                        Holy See (Vatican City State)
                                                                    </option>
                                                                    <option value="Honduras" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Honduras"}} selected {{/ifEquals}}>
                                                                        Honduras
                                                                    </option>
                                                                    <option value="Hong Kong" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Hong Kong"}} selected {{/ifEquals}}>
                                                                        Hong Kong
                                                                    </option>
                                                                    <option value="Hungary" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Hungary"}} selected {{/ifEquals}}>
                                                                        Hungary
                                                                    </option>
                                                                    <option value="Iceland" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Iceland"}} selected {{/ifEquals}}>
                                                                        Iceland
                                                                    </option>
                                                                    <option value="India" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "India"}} selected {{/ifEquals}}>
                                                                        India
                                                                    </option>
                                                                    <option value="Indonesia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Indonesia"}} selected {{/ifEquals}}>
                                                                        Indonesia
                                                                    </option>
                                                                    <option value="Iran, Islamic Republic of" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Iran, Islamic Republic of"}} selected {{/ifEquals}}>
                                                                        Iran, Islamic Republic of
                                                                    </option>
                                                                    <option value="Iraq" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Iraq"}} selected {{/ifEquals}}>
                                                                        Iraq
                                                                    </option>
                                                                    <option value="Ireland" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Ireland"}} selected {{/ifEquals}}>
                                                                        Ireland
                                                                    </option>
                                                                    <option value="Isle of Man" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Isle of Man"}} selected {{/ifEquals}}>
                                                                        Isle of Man
                                                                    </option>
                                                                    <option value="Israel" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Israel"}} selected {{/ifEquals}}>
                                                                        Israel
                                                                    </option>
                                                                    <option value="Italy" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Italy"}} selected {{/ifEquals}}>
                                                                        Italy
                                                                    </option>
                                                                    <option value="Jamaica" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Jamaica"}} selected {{/ifEquals}}>
                                                                        Jamaica
                                                                    </option>
                                                                    <option value="Japan" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Japan"}} selected {{/ifEquals}}>
                                                                        Japan
                                                                    </option>
                                                                    <option value="Jersey" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Jersey"}} selected {{/ifEquals}}>
                                                                        Jersey
                                                                    </option>
                                                                    <option value="Jordan" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Jordan"}} selected {{/ifEquals}}>
                                                                        Jordan
                                                                    </option>
                                                                    <option value="Kazakhstan" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Kazakhstan"}} selected {{/ifEquals}}>
                                                                        Kazakhstan
                                                                    </option>
                                                                    <option value="Kenya" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Kenya"}} selected {{/ifEquals}}>
                                                                        Kenya
                                                                    </option>
                                                                    <option value="Kiribati" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Kiribati"}} selected {{/ifEquals}}>
                                                                        Kiribati
                                                                    </option>
                                                                    <option value="Korea, Democratic People's Republic of" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Korea, Democratic People's Republic of"}} selected {{/ifEquals}}>
                                                                        Korea, Democratic People's Republic of
                                                                    </option>
                                                                    <option value="Korea, Republic of" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Korea, Republic of"}} selected {{/ifEquals}}>
                                                                        Korea, Republic of
                                                                    </option>
                                                                    <option value="Kuwait" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Kuwait"}} selected {{/ifEquals}}>
                                                                        Kuwait
                                                                    </option>
                                                                    <option value="Kyrgyzstan" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Kyrgyzstan"}} selected {{/ifEquals}}>
                                                                        Kyrgyzstan
                                                                    </option>
                                                                    <option value="Lao People's Democratic Republic" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Lao People's Democratic Republic"}} selected {{/ifEquals}}>
                                                                        Lao People's Democratic Republic
                                                                    </option>
                                                                    <option value="Latvia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Latvia"}} selected {{/ifEquals}}>
                                                                        Latvia
                                                                    </option>
                                                                    <option value="Lebanon" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Lebanon"}} selected {{/ifEquals}}>
                                                                        Lebanon
                                                                    </option>
                                                                    <option value="Lesotho" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Lesotho"}} selected {{/ifEquals}}>
                                                                        Lesotho
                                                                    </option>
                                                                    <option value="Liberia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Liberia"}} selected {{/ifEquals}}>
                                                                        Liberia
                                                                    </option>
                                                                    <option value="Libyan Arab Jamahiriya" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Libyan Arab Jamahiriya"}} selected {{/ifEquals}}>
                                                                        Libyan Arab Jamahiriya
                                                                    </option>
                                                                    <option value="Liechtenstein" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Liechtenstein"}} selected {{/ifEquals}}>
                                                                        Liechtenstein
                                                                    </option>
                                                                    <option value="Lithuania" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Lithuania"}} selected {{/ifEquals}}>
                                                                        Lithuania
                                                                    </option>
                                                                    <option value="Luxembourg" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Luxembourg"}} selected {{/ifEquals}}>
                                                                        Luxembourg
                                                                    </option>
                                                                    <option value="Macao" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Macao"}} selected {{/ifEquals}}>Macao</option>
                                                                    <option
                                                                            value="Macedonia, The Former Yugoslav Republic of">
                                                                        Macedonia, The Former Yugoslav Republic of
                                                                    </option>
                                                                    <option value="Madagascar" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Madagascar"}} selected {{/ifEquals}}>Madagascar</option>
                                                                    <option value="Malawi" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Malawi"}} selected {{/ifEquals}}>Malawi</option>
                                                                    <option value="Malaysia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Malaysia"}} selected {{/ifEquals}}>Malaysia</option>
                                                                    <option value="Maldives" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Maldives"}} selected {{/ifEquals}}>Maldives</option>
                                                                    <option value="Mali" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Mali"}} selected {{/ifEquals}}>Mali</option>
                                                                    <option value="Malta" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Malta"}} selected {{/ifEquals}}>
                                                                        Malta
                                                                    </option>
                                                                    <option value="Marshall Islands" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Marshall Islands"}} selected {{/ifEquals}}>
                                                                        Marshall Islands
                                                                    </option>
                                                                    <option value="Martinique" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Martinique"}} selected {{/ifEquals}}>
                                                                        Martinique
                                                                    </option>
                                                                    <option value="Mauritania" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Mauritania"}} selected {{/ifEquals}}>
                                                                        Mauritania
                                                                    </option>
                                                                    <option value="Mauritius" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Mauritius"}} selected {{/ifEquals}}>
                                                                        Mauritius
                                                                    </option>
                                                                    <option value="Mayotte" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Mayotte"}} selected {{/ifEquals}}>
                                                                        Mayotte
                                                                    </option>
                                                                    <option value="Mexico" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Mexico"}} selected {{/ifEquals}}>
                                                                        Mexico
                                                                    </option>
                                                                    <option value="Micronesia, Federated States of" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Micronesia, Federated States of"}} selected {{/ifEquals}}>
                                                                        Micronesia, Federated States of
                                                                    </option>
                                                                    <option value="Moldova, Republic of" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Moldova, Republic of"}} selected {{/ifEquals}}>
                                                                        Moldova, Republic of
                                                                    </option>
                                                                    <option value="Monaco" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Monaco"}} selected {{/ifEquals}}>
                                                                        Monaco
                                                                    </option>
                                                                    <option value="Mongolia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Mongolia"}} selected {{/ifEquals}}>
                                                                        Mongolia
                                                                    </option>
                                                                    <option value="Montenegro" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Montenegro"}} selected {{/ifEquals}}>
                                                                        Montenegro
                                                                    </option>
                                                                    <option value="Montserrat" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Montserrat"}} selected {{/ifEquals}}>
                                                                        Montserrat
                                                                    </option>
                                                                    <option value="Morocco" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Morocco"}} selected {{/ifEquals}}>
                                                                        Morocco
                                                                    </option>
                                                                    <option value="Mozambique" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Mozambique"}} selected {{/ifEquals}}>
                                                                        Mozambique
                                                                    </option>
                                                                    <option value="Myanmar" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Myanmar"}} selected {{/ifEquals}}>
                                                                        Myanmar
                                                                    </option>
                                                                    <option value="Namibia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Namibia"}} selected {{/ifEquals}}>
                                                                        Namibia
                                                                    </option>
                                                                    <option value="Nauru" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Nauru"}} selected {{/ifEquals}}>
                                                                        Nauru
                                                                    </option>
                                                                    <option value="Nepal" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Nepal"}} selected {{/ifEquals}}>
                                                                        Nepal
                                                                    </option>
                                                                    <option value="Netherlands" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Netherlands"}} selected {{/ifEquals}}>
                                                                        Netherlands
                                                                    </option>
                                                                    <option value="Netherlands Antilles" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Netherlands Antilles"}} selected {{/ifEquals}}>
                                                                        Netherlands Antilles
                                                                    </option>
                                                                    <option value="New Caledonia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "New Caledonia"}} selected {{/ifEquals}}>
                                                                        New Caledonia
                                                                    </option>
                                                                    <option value="New Zealand" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "New Zealand"}} selected {{/ifEquals}}>
                                                                        New Zealand
                                                                    </option>
                                                                    <option value="Nicaragua" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Nicaragua"}} selected{{/ifEquals}}>Nicaragua</option>
                                                                    <option value="Niger" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Niger"}} selected {{/ifEquals}}>
                                                                        Niger
                                                                    </option>
                                                                    <option value="Nigeria" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Nigeria"}} selected {{/ifEquals}}>
                                                                        Nigeria
                                                                    </option>
                                                                    <option value="Niue" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Niue"}} selected {{/ifEquals}}>
                                                                        Niue
                                                                    </option>
                                                                    <option value="Norfolk Island" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Norfolk Island"}} selected {{/ifEquals}}>
                                                                        Norfolk Island
                                                                    </option>
                                                                    <option value="Northern Mariana Islands" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Northern Mariana Islands"}} selected {{/ifEquals}}>
                                                                        Northern Mariana Islands
                                                                    </option>
                                                                    <option value="Norway" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Norway"}} selected {{/ifEquals}}>
                                                                        Norway
                                                                    </option>
                                                                    <option value="Oman" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Oman"}} selected {{/ifEquals}}>
                                                                        Oman
                                                                    </option>
                                                                    <option value="Pakistan" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Pakistan"}} selected {{/ifEquals}}>
                                                                        Pakistan
                                                                    </option>
                                                                    <option value="Palau" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Palau"}} selected {{/ifEquals}}>
                                                                        Palau
                                                                    </option>
                                                                    <option value="Palestinian Territory, Occupied" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Palestinian Territory, Occupied"}} selected {{/ifEquals}}>
                                                                        Palestinian Territory, Occupied
                                                                    </option>
                                                                    <option value="Panama" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Panama"}} selected {{/ifEquals}}>
                                                                        Panama
                                                                    </option>
                                                                    <option value="Papua New Guinea" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Papua New Guinea"}} selected {{/ifEquals}}>
                                                                        Papua New Guinea
                                                                    </option>
                                                                    <option value="Paraguay" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Paraguay"}} selected {{/ifEquals}}>
                                                                        Paraguay
                                                                    </option>
                                                                    <option value="Peru" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Peru"}} selected {{/ifEquals}}>
                                                                        Peru
                                                                    </option>
                                                                    <option value="Philippines" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Philippines"}} selected {{/ifEquals}}>
                                                                        Philippines
                                                                    </option>
                                                                    <option value="Pitcairn" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Pitcairn"}} selected {{/ifEquals}}>
                                                                        Pitcairn
                                                                    </option>
                                                                    <option value="Poland" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Poland"}} selected {{/ifEquals}}>
                                                                        Poland
                                                                    </option>
                                                                    <option value="Portugal" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Portugal"}} selected {{/ifEquals}}>
                                                                        Portugal
                                                                    </option>
                                                                    <option value="Puerto Rico" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Puerto Rico"}} selected {{/ifEquals}}>
                                                                        Puerto Rico
                                                                    </option>
                                                                    <option value="Qatar" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Qatar"}} selected {{/ifEquals}}>
                                                                        Qatar
                                                                    </option>
                                                                    <option value="Reunion" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Reunion"}} selected {{/ifEquals}}>
                                                                        Reunion
                                                                    </option>
                                                                    <option value="Romania" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Romania"}} selected {{/ifEquals}}>
                                                                        Romania
                                                                    </option>
                                                                    <option value="Russian Federation" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Russian Federation"}} selected {{/ifEquals}}>
                                                                        Russian Federation
                                                                    </option>
                                                                    <option value="Rwanda" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Rwanda"}} selected {{/ifEquals}}>
                                                                        Rwanda
                                                                    </option>
                                                                    <option value="Saint Helena" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Saint Helena"}} selected {{/ifEquals}}>
                                                                        Saint Helena
                                                                    </option>
                                                                    <option value="Saint Kitts and Nevis" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Saint Kitts and Nevis"}} selected {{/ifEquals}}>
                                                                        Saint Kitts and Nevis
                                                                    </option>
                                                                    <option value="Saint Lucia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Saint Lucia"}} selected {{/ifEquals}}>
                                                                        Saint Lucia
                                                                    </option>
                                                                    <option value="Saint Pierre and Miquelon" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Saint Pierre and Miquelon"}} selected {{/ifEquals}}>
                                                                        Saint Pierre and Miquelon
                                                                    </option>
                                                                    <option value="Saint Vincent and The Grenadines" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Saint Vincent and The Grenadines"}} selected {{/ifEquals}}>
                                                                        Saint Vincent and The Grenadines
                                                                    </option>
                                                                    <option value="Samoa" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Samoa"}} selected {{/ifEquals}}>
                                                                        Samoa
                                                                    </option>
                                                                    <option value="San Marino" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "San Marino"}} selected {{/ifEquals}}>
                                                                        San Marino
                                                                    </option>
                                                                    <option value="Sao Tome and Principe" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Sao Tome and Principe"}} selected {{/ifEquals}}>
                                                                        Sao Tome and Principe
                                                                    </option>
                                                                    <option value="Saudi Arabia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Saudi Arabia"}} selected {{/ifEquals}}>
                                                                        Saudi Arabia
                                                                    </option>
                                                                    <option value="Senegal" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Senegal"}} selected {{/ifEquals}}>
                                                                        Senegal
                                                                    </option>
                                                                    <option value="Serbia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Serbia"}} selected {{/ifEquals}}>
                                                                        Serbia
                                                                    </option>
                                                                    <option value="Seychelles" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Seychelles"}} selected {{/ifEquals}}>
                                                                        Seychelles
                                                                    </option>
                                                                    <option value="Sierra Leone" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Sierra Leone"}} selected {{/ifEquals}}>
                                                                        Sierra Leone
                                                                    </option>
                                                                    <option value="Singapore" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Singapore"}} selected {{/ifEquals}}>
                                                                        Singapore
                                                                    </option>
                                                                    <option value="Slovakia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Slovakia"}} selected {{/ifEquals}}>
                                                                        Slovakia
                                                                    </option>
                                                                    <option value="Slovenia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Slovenia"}} selected {{/ifEquals}}>
                                                                        Slovenia
                                                                    </option>
                                                                    <option value="Solomon Islands" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Solomon Islands"}} selected {{/ifEquals}}>
                                                                        Solomon Islands
                                                                    </option>
                                                                    <option value="Somalia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Somalia"}} selected {{/ifEquals}}>
                                                                        Somalia
                                                                    </option>
                                                                    <option value="South Africa" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "South Africa"}} selected {{/ifEquals}}>
                                                                        South Africa
                                                                    </option>
                                                                    <option value="South Georgia and The South Sandwich Islands" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "South Georgia and The South Sandwich Islands"}} selected {{/ifEquals}}>
                                                                        South Georgia and The South Sandwich Islands
                                                                    </option>
                                                                    <option value="Spain" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Spain"}} selected {{/ifEquals}}>
                                                                        Spain
                                                                    </option>
                                                                    <option value="Sri Lanka" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Sri Lanka"}} selected {{/ifEquals}}>
                                                                        Sri Lanka
                                                                    </option>
                                                                    <option value="Sudan" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Sudan"}} selected {{/ifEquals}}>
                                                                        Sudan
                                                                    </option>
                                                                    <option value="South Sudan" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "South Sudan"}} selected {{/ifEquals}}>
                                                                        South Sudan
                                                                    </option>
                                                                    <option value="Suriname" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Suriname"}} selected {{/ifEquals}}>
                                                                        Suriname
                                                                    </option>
                                                                    <option value="Svalbard and Jan Mayen" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Svalbard and Jan Mayen"}} selected {{/ifEquals}}>
                                                                        Svalbard and Jan Mayen
                                                                    </option>
                                                                    <option value="Swaziland" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Swaziland"}} selected {{/ifEquals}}>
                                                                        Swaziland
                                                                    </option>
                                                                    <option value="Sweden" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Sweden"}} selected {{/ifEquals}}>
                                                                        Sweden
                                                                    </option>
                                                                    <option value="Switzerland" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Switzerland"}} selected {{/ifEquals}}>
                                                                        Switzerland
                                                                    </option>
                                                                    <option value="Syrian Arab Republic" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Syrian Arab Republic"}} selected {{/ifEquals}}>
                                                                        Syrian Arab Republic
                                                                    </option>
                                                                    <option value="Taiwan, Province of China" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Taiwan, Province of China"}} selected {{/ifEquals}}>
                                                                        Taiwan, Province of China
                                                                    </option>
                                                                    <option value="Tajikistan" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Tajikistan"}} selected {{/ifEquals}}>
                                                                        Tajikistan
                                                                    </option>
                                                                    <option value="Tanzania, United Republic of" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Tanzania, United Republic of"}} selected {{/ifEquals}}>
                                                                        Tanzania, United Republic of
                                                                    </option>
                                                                    <option value="Thailand" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Thailand"}} selected {{/ifEquals}}>
                                                                        Thailand
                                                                    </option>
                                                                    <option value="Timor-leste" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Timor-leste"}} selected {{/ifEquals}}>
                                                                        Timor-leste
                                                                    </option>
                                                                    <option value="Togo" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Togo"}} selected {{/ifEquals}}>
                                                                        Togo
                                                                    </option>
                                                                    <option value="Tokelau" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Tokelau"}} selected {{/ifEquals}}>
                                                                        Tokelau
                                                                    </option>
                                                                    <option value="Tonga" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Tonga"}} selected {{/ifEquals}}>
                                                                        Tonga
                                                                    </option>
                                                                    <option value="Trinidad and Tobago" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Trinidad and Tobago"}} selected {{/ifEquals}}>
                                                                        Trinidad and Tobago
                                                                    </option>
                                                                    <option value="Tunisia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Tunisia"}} selected {{/ifEquals}}>
                                                                        Tunisia
                                                                    </option>
                                                                    <option value="Turkey" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Turkey"}} selected {{/ifEquals}}>Turkey</option>
                                                                    <option value="Turkmenistan" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Turkmenistan"}} selected {{/ifEquals}}>Turkmenistan</option>
                                                                    <option value="Turks and Caicos Islands" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Turks and Caicos Islands"}} selected {{/ifEquals}}>Turks and Caicos Islands</option>
                                                                    <option value="Tuvalu" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Tuvalu"}} selected {{/ifEquals}}>Tuvalu</option>
                                                                    <option value="Uganda" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Uganda"}} selected {{/ifEquals}}>Uganda</option>
                                                                    <option value="Ukraine" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Ukraine"}} selected {{/ifEquals}}>Ukraine</option>
                                                                    <option value="United Arab Emirates" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "United Arab Emirates"}} selected {{/ifEquals}}>United Arab Emirates</option>
                                                                    <option value="United Kingdom" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "United Kingdom"}} selected {{/ifEquals}}>United Kingdom</option>
                                                                    <option value="United States" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "United States"}} selected {{/ifEquals}}>United States</option>
                                                                    <option value="United States Minor Outlying Islands" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "United States Minor Outlying Islands"}} selected {{/ifEquals}}>United States Minor Outlying Islands</option>
                                                                    <option value="Uruguay" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Uruguay"}} selected {{/ifEquals}}>Uruguay</option>
                                                                    <option value="Uzbekistan" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Uzbekistan"}} selected {{/ifEquals}}>Uzbekistan</option>
                                                                    <option value="Venezuela" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Venezuela"}} selected {{/ifEquals}}>Venezuela</option>
                                                                    <option value="Viet Nam" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Viet Nam"}} selected {{/ifEquals}}>Viet Nam</option>
                                                                    <option value="Virgin Islands, American" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Virgin Islands, American"}} selected {{/ifEquals}}>Virgin Islands, American</option>
                                                                    <option value="Virgin Islands, British" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Virgin Islands, British"}} selected {{/ifEquals}}>Virgin Islands, British</option>
                                                                    <option value="Wallis and Futuna" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Wallis and Futuna"}} selected {{/ifEquals}}>Wallis and Futuna</option>
                                                                    <option value="Vanuatu" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Vanuatu"}} selected {{/ifEquals}}>Vanuatu</option>
                                                                    <option value="Western Sahara" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Western Sahara"}} selected {{/ifEquals}}>Western Sahara</option>
                                                                    <option value="Yemen" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Yemen"}} selected {{/ifEquals}}>Yemen</option>
                                                                    <option value="Zambia" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Zambia"}} selected {{/ifEquals}}>Zambia</option>
                                                                    <option value="Zimbabwe" {{#ifEquals entry.tax_residency.parent_entity_jurisdiction "Zimbabwe"}} selected {{/ifEquals}}>Zimbabwe</option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <div class="form-group mb-3">
                                                                <label class="mb-2"
                                                                       for="parent_entity_incorporation_number">
                                                                    Parent Entity’s Incorporation/ Formation Number
                                                                </label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="form-group mb-3">
                                                                <input type="text"
                                                                       id="parent_entity_incorporation_number"
                                                                       class="form-control"
                                                                       name="parent_entity_incorporation_number"
                                                                       value="{{entry.tax_residency.parent_entity_incorporation_number}}">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- SUBMIT EVIDENCE -->
                                                <div class="row">
                                                    <div class="col-md-12">

                                                        <div class="form-group mb-3">
                                                            <label for="evidenceType">
                                                                Evidence of non-residency or provisional treatment:
                                                            </label>
                                                        </div>
                                                    </div>

                                                </div>
                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <div class="form-group mb-3">

                                                            <div class=" radio form-check">
                                                                <input class="form-check-input" type="radio" id="evidenceTypeNonResidency"
                                                                       name="evidenceType" value="non residency"
                                                                    {{#ifEquals  entry.tax_residency.evidence_type 'non residency'}}
                                                                       checked
                                                                    {{/ifEquals}}>
                                                                <label  class="form-check-label" for="evidenceTypeNonResidency">Upload evidence of Tax Residency in another jurisdiction which meets ITA Rule 3</label>
                                                            </div>

                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="text-right">
                                                            <button type="button" class="btn px-4 solid royal-blue"
                                                                data-toggle="modal" data-target="#upload-modal" id="nonResidencyUploadBtn"
                                                                name="SubmitEvidenceNonResidency"
                                                                data-field="EvidenceNonResidency-"
                                                                    data-entry="{{entry._id}}" disabled> Upload document(s)</button>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <div class="form-group mb-3">
                                                            <div class="radio form-check">
                                                                <input class="form-check-input"  type="radio" id="evidenceTypeProvisionalTreatment"
                                                                       name="evidenceType" value="provisional treatment"
                                                                    {{#ifEquals  entry.tax_residency.evidence_type 'provisional treatment'}}
                                                                       checked
                                                                    {{/ifEquals}}>
                                                                <label  class="form-check-label" for="evidenceTypeProvisionalTreatment">
                                                                    Upload an application for provisional treatment as non-resident under ITA Rule 6 which meets conditions in Rule 10
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="text-right">
                                                            <button type="button" class="btn px-4 solid royal-blue"
                                                                data-toggle="modal" data-target="#upload-modal"
                                                                id="provisionalTreatmentUploadBtn"
                                                                name="EvidenceProvisionalTreatmentNonResident"
                                                                data-field="EvidenceNonResidency-" disabled
                                                                data-entry="{{entry._id}}">Upload document(s) </button>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div id="uploaded_files_evidence_tax_residency" class="my-2" >
                                                </div>


                                            </div>

                                            <!-- PAGE-->
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <div class="progress">
                                                        <div class="progress-bar w-50" role="progressbar"
                                                            aria-valuenow="3" aria-valuemin="0" aria-valuemax="6">3 of
                                                            6
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="form-group mb-2">
                                                <input type="submit" name="submit" value="Previous page"
                                                    class="btn btn-secondary waves-effect waves-light width-xl" />
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group mb-2" align="right">
                                                <input type="submit" name="submit" value="Save & next page"
                                                    class="btn btn-primary waves-effect waves-light width-xl" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    {{> uploadModal entryId=entry._id}}


</main>

<script type='text/javascript' src='/templates/uploadedfiles.precompiled.js'></script>
<script type='text/javascript' src="/views-js/entry/Tax-Residency.js"></script>

