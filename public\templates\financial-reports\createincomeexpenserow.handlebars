{{#if isIncome}}
    <tr id="{{type}}-table-row-{{rowId}}" class="income-row">
        <td>
            {{propertyType}}
        </td>
        <td class="text-right">
            $ {{decimalValue propertyValue}}
        </td>
        <td class="text-right">
            <button type="button" class="btn btn-outline-secondary openEditIncomePropertyModal"
                    data-id="{{rowId}}" data-type="{{type}}">
                <i class="fa fa-pencil"></i>
            </button>
        </td>
        <td class="text-left">
            <button type="button" class="delete btn btn-danger deleteIncomeProperty"
                    data-id="{{rowId}}" data-type="{{type}}">
                <i class="fa fa-trash"></i>
            </button>
        </td>
    </tr>
{{else}}
    <tr id="{{type}}-table-row-{{rowId}}" class="expense-row">
        <td>
            {{propertyType}}
        </td>
        <td class="text-right">
            $ {{decimalValue propertyValue}}
        </td>
        <td class="text-right">
            <button type="button" class="btn btn-outline-secondary openEditExpensePropertyModal"
                    data-id="{{rowId}}" data-type="{{type}}">
                <i class="fa fa-pencil"></i>
            </button>
        </td>
        <td class="text-left">
            <button type="button" class="delete btn btn-danger deleteExpenseProperty"
                    data-id="{{rowId}}" data-type="{{type}}">
                <i class="fa fa-trash"></i>
            </button>
        </td>
    </tr>
{{/if}}
