<div id="{{group}}-{{tableId}}" class="table-responsive mt-2">
    <table class="table table-striped">
        <thead>
        <tr>
            <th class="header-45-percent">Files Required</th>
            <th class="header-15-percent"></th>
            <th class="header-25-percent">Explanation</th>
        </tr>
        </thead>
        <tbody>
        {{#each files}}
            <tr>
                <td>{{external}}</td>
                <td class="text-center">
                    <button
                            type="button"
                            class="btn solid royal-blue upload-file-btn {{#if present}}uploaded-files-btn {{/if}}"
                            data-toggle="modal"
                            data-target="#upload-temp-modal"
                            id="btn-{{../group}}-{{../name}}-{{@key}}"
                            data-id="{{ id }}"
                            data-incorporation-id="{{../incorporationId}}"
                            data-mcc="{{../masterClientCode}}"
                            data-row="{{@key}}"
                            data-relation-id="{{ ../relationId}}"
                            data-file-type="{{ ../group}}"
                            data-field="{{ internal }}"
                            data-file-group="{{ ../name}}"
                            data-related-check-id="{{../group}}-{{../name}}-files-{{@key}}-present"
                    >
                        {{#if present}}Modify{{else}}Upload{{/if}}
                    </button>
                </td>
                <td>
                <textarea
                        class="form-control"
                        name="{{../name}}[files][{{ @key}}][explanation]"
                        id="{{../group}}-{{../name}}-{{@key}}-explanation-file"
                        rows="1"
                >{{explanation}}</textarea>
                    <label for="{{../group}}-{{../name}}-{{@key}}-explanation-file" hidden></label>
                </td>
            </tr>
        {{/each}}
        </tbody>
    </table>
</div>
