const sqlDb = require('../../models-sql');
const Company = require('../../models/company').schema;
const MasterClientCode = require('../../models/masterClientCode');
const mem_DirectorsModel = sqlDb.mem_Directors;
const mem_ShareholdersModel = sqlDb.mem_Shareholders;
const mem_EntitiesModel = sqlDb.mem_Entities;
const MailController = require('../../controllers/mailController');
const MailFormatter = require('../../controllers/mailFormatController');
const {
  DIRMEMBER_STATUS,
} = require('../../utils/directorAndMemberConstants');
const { Op } = require("sequelize");
const { getCompanyData } = require('../../utils/companyInformationUtils');

// Display list of all Companys.
exports.listCompanies = async function (req, res, next) {
  try {
    const mcc = await MasterClientCode.findOne({ 'code': req.params.masterclientcode });

    if (mcc == null || mcc.owners.indexOf(req.user.email.toLowerCase()) == -1) {
      const err = new Error('Masterclient not found');
      err.status = 404;
      return next(err);
    }

    let companies = [];

    try {
      // First, try to get companies from MongoDB
      let companyfilter = {
        'masterclientcode': req.params.masterclientcode,
        'dirboModule.active': true
      };

      if (req.query.filterCompanyName && req.query.filterCompanyName.length > 2) {
        companyfilter['name'] = { $regex: req.query.filterCompanyName, $options: 'i' };
      }
      if (req.query.filterIncorporationCode && req.query.filterIncorporationCode.length > 2) {
        companyfilter['incorporationcode'] = { $regex: req.query.filterIncorporationCode, $options: 'i' };
      }

      let mongoCompanies = await Company.find(
        companyfilter,
        { code: 1, name: 1, incorporationcode: 1, masterclientcode: 1 }
      ).sort({ "name": 1 });

      // Convert to the format expected by the view
      companies = mongoCompanies.map(company => {
        return {
          company: company,
          unconfirmedDataMember: false,
          unconfirmedDataDirector: false,
          hasDirectorsAndMembers: false,
        };
      });

      // Check if any companies have directors or members
      if (companies.length > 0) {
        // Get all company codes
        const companyCodes = companies.map(c => c.company.code);
        const vpEntities = await mem_EntitiesModel.findAll({
          where: {
            EntityLegacyID: companyCodes,
          },
          raw: true
        });

        const vpEntityCodes = vpEntities.map(e => e.EntityLegacyID);

        companies = companies.filter(c => vpEntityCodes.includes(c.company.code));

        // Check for mem_Directors where there is no confirmed history record
        const unconfirmedDirectors = await mem_DirectorsModel.findAll({
          where: {
            EntityLegacyID: companyCodes,
            UniqueRelationID: {
              [Op.notIn]: [
                sqlDb.sequelize.literal(`(
                                    SELECT UniqueRelationID
                                    FROM mem_DirectorsHistory
                                    WHERE EntityLegacyID = mem_Directors.EntityLegacyID
                                    AND Status = '${DIRMEMBER_STATUS.CONFIRMED}'
                                )`)
              ]
            }
          },
          raw: true
        });

        // Check for mem_Shareholders where there is no confirmed history record
        const unconfirmedMembers = await mem_ShareholdersModel.findAll({
          where: {
            EntityLegacyID: companyCodes,
            UniqueRelationID: {
              [Op.notIn]: [
                sqlDb.sequelize.literal(`(
                                    SELECT UniqueRelationID
                                    FROM mem_ShareholdersHistory
                                    WHERE EntityLegacyID = mem_Shareholders.EntityLegacyID
                                    AND Status = '${DIRMEMBER_STATUS.CONFIRMED}'
                                )`)
              ]
            }
          },
          raw: true
        });

        // Check for mem_Entities where there is no confirmed history record and it has stock or mutual fund information
        const unconfirmedEntities = await mem_EntitiesModel.findAll({
          where: {
            EntityLegacyID: companyCodes,
            [Op.or]: [
              { STXName: { [Op.ne]: null } },
              { STXTicker: { [Op.ne]: null } },
              { STXJurisdiction: { [Op.ne]: null } },
              { STXRegulator: { [Op.ne]: null } },
              { STXListingDate: { [Op.ne]: null } },
              { BusRegNr: { [Op.ne]: null } },
              { BusRegType: { [Op.ne]: null } },
              { BusRegStartDate: { [Op.ne]: null } }
            ],
            [Op.and]: [
              sqlDb.sequelize.literal(`NOT EXISTS (
                                SELECT 1
                                FROM mem_EntitiesStockHistory
                                WHERE EntityLegacyID = mem_Entities.EntityLegacyID
                                AND Status = '${DIRMEMBER_STATUS.CONFIRMED}'
                            )`),
              sqlDb.sequelize.literal(`NOT EXISTS (
                                SELECT 1
                                FROM mem_EntitiesMutualFundHistory
                                WHERE EntityLegacyID = mem_Entities.EntityLegacyID
                                AND Status = '${DIRMEMBER_STATUS.CONFIRMED}'
                            )`)
            ]
          },
          raw: true
        });

        // Set flags for each company (initial unconfirmed data check)
        companies.forEach(company => {
          company.unconfirmedDataDirector = unconfirmedDirectors.some(d => d.EntityLegacyID === company.company.code);
          company.unconfirmedDataMember = unconfirmedMembers.some(m => m.EntityLegacyID === company.company.code) || unconfirmedEntities.some(e => e.EntityLegacyID === company.company.code);
        });

        // Get all companies with directors in a single query
        let companiesWithDirectors = await mem_DirectorsModel.findAll({
          attributes: ['EntityLegacyID'],
          where: {
            EntityLegacyID: companyCodes,
          },
          group: ['EntityLegacyID'],
          raw: true
        });

        // Get all companies with members in a single query
        const companiesWithMembers = await mem_ShareholdersModel.findAll({
          attributes: ['EntityLegacyID'],
          where: {
            EntityLegacyID: companyCodes,
          },
          group: ['EntityLegacyID'],
          raw: true
        });

        // Get all companies with stock or mutual fund information
        const companiesWithEntityInfo = await mem_EntitiesModel.findAll({
          attributes: ['EntityLegacyID'],
          where: {
            EntityLegacyID: companyCodes,
            [Op.or]: [
              { STXName: { [Op.ne]: null } },
              { STXTicker: { [Op.ne]: null } },
              { STXJurisdiction: { [Op.ne]: null } },
              { STXRegulator: { [Op.ne]: null } },
              { STXListingDate: { [Op.ne]: null } },
              { BusRegNr: { [Op.ne]: null } },
              { BusRegType: { [Op.ne]: null } },
              { BusRegStartDate: { [Op.ne]: null } }
            ]
          },
          raw: true
        });

        // Create lookup sets for faster checking
        const directorCompanyCodes = new Set(companiesWithDirectors.map(c => c.EntityLegacyID));
        const memberCompanyCodes = new Set(companiesWithMembers.map(c => c.EntityLegacyID));
        const entityInfoCompanyCodes = new Set(companiesWithEntityInfo.map(c => c.EntityLegacyID));

        // Set flags for each company
        companies.forEach(company => {
          company.hasDirectors = directorCompanyCodes.has(company.company.code);
          company.hasMembers = memberCompanyCodes.has(company.company.code);

          // For members, we need to check if there's any information at all (members OR entity info)
          // This matches the hasInformation logic in getMemberEntries
          const hasMemberInformation = company.hasMembers || entityInfoCompanyCodes.has(company.company.code);

          // Update unconfirmed data flags to also include cases where there's no data at all
          // (when request assistance button should appear)
          company.unconfirmedDataDirector = company.unconfirmedDataDirector || !company.hasDirectors;
          company.unconfirmedDataMember = company.unconfirmedDataMember || !hasMemberInformation;
        });
      }
    } catch (err) {
      console.error("Error fetching companies from MongoDB:", err);
    }

    return res.render('director-and-members/companies', {
      title: 'Companies',
      masterClientCode: req.params.masterclientcode,
      companies: companies,
      user: req.user,
      messages: req.session.messages,
      hideMembers: process.env.HIDE_MEMBERS === 'true'
    });

  } catch (e) {
    console.error("Error in listDirectorAndMemberCompanies:", e);
    const err = new Error('Internal server error');
    err.status = 500;
    return next(err);
  }
};

exports.requestToAssistance = async function (req, res) {
  try {
    const company = await getCompanyData(req.params.code, req.params.masterclientcode, req.user.email);

    if (company?.error) {
      return res.status(company.status).json({
        "status": company.status,
        "error": company.error
      });
    }

    // Determine the request type based on the URL path
    let requestType;
    let position;

    if (req.originalUrl.includes('/directors/')) {
      requestType = "No Director";
      position = "Director";
    } else if (req.originalUrl.includes('/members/')) {
      requestType = "No Member";
      position = "Member";
    } else {
      // Default to director if path is unclear
      requestType = "No Director";
      position = "Director";
    }

    let emailTo = process.env.REQUEST_UPDATE_EMAIL_THKO;

    // Get entity to determine production office
    const entity = await mem_EntitiesModel?.findOne({
      where: {
        EntityLegacyID: company.code,
      },
      raw: true
    });
    let noProductionOffice = false;
    // Set correct production office
    if (entity && entity.ProductionOffice) {
      if (entity.ProductionOffice === 'TBVI') {
        emailTo = process.env.REQUEST_UPDATE_EMAIL_TBVI;
      } else if (entity.ProductionOffice === 'THKO') {
        emailTo = process.env.REQUEST_UPDATE_EMAIL_THKO;
      } else if (entity.ProductionOffice === 'TCYP') {
        emailTo = process.env.REQUEST_UPDATE_EMAIL_TCYP;
      } else if (entity.ProductionOffice === 'TPANVG') {
        emailTo = process.env.REQUEST_UPDATE_EMAIL_TPANVG;
      }
    } else {
      noProductionOffice = true;
    }

    // Create email subject
    const subject = `${process.env.EMAIL_SUBJECT_PREFIX || ''} ${company.name} – No ${position} found in the portal`;

    // Prepare email data
    const emailData = {
      "companyCode": entity ? `${entity.EntityCode} (${company.code})` : company.code,
      "companyName": entity ? `${entity.EntityName} (${company.name})` : company.name,
      "mcc": entity ? `${entity.ClientCode} (${company.masterclientcode})` : company.masterclientcode,
      "requestor": req.user.username,
      "requestType": requestType,
    };

    // Send the email
    let email = MailFormatter.generateDirMemRequestAssistanceEmail(emailData);
    let emailResponse = await MailController.asyncSend(
      emailTo,
      noProductionOffice ? '(!Production office unknown) ' + subject : subject,
      email.textString,
      email.htmlString
    );

    if (emailResponse.error) {
      console.error("Send director email error: ", emailResponse);
      return res.status(500).json({ "status": 500, "message": "There was an error sending the request for assistance" });
    }

    return res.status(200).json({ "status": 200, "message": "We have received your request for assistance. A Trident Trust Representative will be in touch shortly." });
  } catch (e) {
    console.error("Error creating request assistance: ", e);
    return res.status(500).json({
      "status": 500,
      "error": 'Internal server error'
    });
  }
}
