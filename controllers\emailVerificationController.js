const fs = require("fs");
const nodemailer = require('nodemailer')
const {
  v4: uuidv4
} = require('uuid');

const path = "email_check.json";

const transporter = nodemailer.createTransport({
    host: process.env.SMTP_SERVER,
    port: process.env.SMTP_PORT ? process.env.SMTP_PORT :
      process.env.SMTP_SECURE === 'true' ? 465 : 587,
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASSWORD
    }
});


exports.emailVerification = async function (email) {
  try {
    const { code, previousSentDate } = getCode(email);
    let sendEmail = true;

    if (previousSentDate){
      const currentDate  = new Date();
      // verify if the last email was sent previous 1 minute 
      if ((currentDate - previousSentDate) < (60 * 1000)){
        sendEmail = false;
      }
    }

    if(!sendEmail){
      console.log(`A verification code was sent to the email ${email} in less than a minute.`);
      return { status: 400, message: 'A verification code was sent to the email in less than a minute.' }
    }

    const info = await transporter.sendMail({
      from: process.env.SMTP_FROM,
      to: email,
      subject: 'Code Confirmation',
      text: `The Confirmation Code is: ${code}`,
      html: "",
    });

    console.log("New Email verification code sent: ", info);
    return {status: 200, message: "success"}

  } catch (e) {
    console.log(e);
    throw new Error(e);
  }
}

exports.verifyCode = function (email, code) {
  const data = getUser(email);
  let error, reason;
  if (data) {
    if (new Date(data.expirateDate) > new Date()) {
      error = Number(data.code) !== Number(code);
      reason =
        Number(data.code) !== Number(code) ? "This code is not Valid!" : "";
      Number(data.code) === Number(code) ? removeUser(email) : "";
    } else {
      removeUser(email);
      error = true;
      reason = "This code has expirated, Please retry again!";
    }
  } else {
    error = true;
    reason = "Email not found!";
  }
  return {
    error,
    reason,
  };
};

const getCode = function (email) {
  const oldData = getUser(email);
  let code, token, previousSentDate;
  if (oldData ) {
    if (new Date(oldData.expirateDate) > new Date()) {
      code = Number(oldData.code);
      token = oldData.token;
      previousSentDate = new Date(oldData.createdDate)
    } else removeUser(email);
  }
  code = code || Math.floor(Math.random() * 900000 + 100000);
  token = token || uuidv4();
  addUser(email, { code, token });
  return { code, token, previousSentDate };
};

const addUser = (email, { code, token }) => {
  const data = JSON.parse(fs.readFileSync(path));
  data[email] = {
    code,
    token,
    createdDate: new Date(),
    // Code expires in 5 minutes
    expirateDate: new Date(new Date().getTime() + 1000 * 60 * 5),
  };
  fs.writeFileSync(path, JSON.stringify(data, null, 2));
};

const removeUser = (email) => {
  let data;
  data = JSON.parse(fs.readFileSync(path));
  delete data[email];
  fs.writeFileSync(path, JSON.stringify(data, null, 2));
};

const getUser = (email) => {

  if (fs.existsSync(path)) {
    const data = JSON.parse(fs.readFileSync(path));
    return data[email]
  }
  const defaultJsonData = {}
  fs.writeFileSync(path, JSON.stringify(defaultJsonData));
  return defaultJsonData[email]
};
