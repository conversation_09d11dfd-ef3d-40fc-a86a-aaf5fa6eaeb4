const moment = require('moment');

module.exports = (sequelize, Sequelize) => {
    // eslint-disable-next-line no-unused-vars
    const VpDirectorInfoHistory = sequelize.define("VPDirectorInfoHistory", {
        Id: {
            type: Sequelize.INTEGER,
            primaryKey: true,
            allowNull: false,
            autoIncrement: true
        },
        MasterClientCode: {
            type: Sequelize.STRING,
            allowNull: false,
            unique: false
        },
        CompanyNumber: {
            type: Sequelize.STRING,
            allowNull: false,
            unique: false
        },
        EntityCode: {
            type: Sequelize.STRING,
            allowNull: false,
            unique: false, 
        },
        EntityName: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false
        },
        Name: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false
        },
        Code: {
            type: Sequelize.STRING,
            allowNull: false,
            unique: false 
        },
        FormerName: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false
        },
        FileType: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false
        },
        UniqueRelationId: {
            type: Sequelize.STRING,
            allowNull: false,
            unique: false 
        },
        RelationType: {
            type: Sequelize.STRING,
            allowNull: false,
            unique: false
        },
        OfficerType: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false,
        },
        DirectorIsAlternateToId: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false,
        },
        DirectorIsAlternateToName: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false,
        },
        FromDate: {
            type: Sequelize.DATEONLY,
            allowNull: true,
            get() {
                const date = this.getDataValue('FromDate') ?
                    moment.utc(this.getDataValue('FromDate')).format('YYYY-MM-DD') : null;
                return date;
            }
        },
        ToDate: {
            type: Sequelize.DATEONLY,
            allowNull: true,
            get() {
                const date = this.getDataValue('ToDate') ?
                    moment.utc(this.getDataValue('ToDate')).format('YYYY-MM-DD') : null;
                return date;
            }
        },
        ServiceAddress: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false
        },
        ResidentialOrRegisteredAddress: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false
        },
        DateOfBirthOrIncorp: {
            type: Sequelize.DATEONLY,
            allowNull: true,
            unique: false,
            get() {
                const date = this.getDataValue('DateOfBirthOrIncorp') ?
                    moment.utc(this.getDataValue('DateOfBirthOrIncorp')).format('YYYY-MM-DD') : null;
                return date;
            }
        },
        PlaceOfBirthOrIncorp: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false
        },
        Nationality: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false
        },
        Country: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false
        },
        ProductionOffice: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false
        },
        CorporateRegistrationNo: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false
        },
        UpdateRequestDate: {
            type: Sequelize.DATE,
            allowNull: true
        },
        ConfirmedDate: {
            type: Sequelize.DATE,
            allowNull: true
        },
        VPDataReceived: {
            type: Sequelize.DATE,
            allowNull: true
        },
        Status: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false
        },
        UserEmail: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false
        },
        TypeOfUpdateRequest: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false
        },
        UpdateRequestComments: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false
        },
        TIN: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false
        },
        NameOfRegulator: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false
        },
        StockExchange: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false
        },
        StockCode: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false
        },
        JurisdictionOfRegulationOrSovereignState: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false
        },
        BoDirIncorporationNumber: {
            type: Sequelize.STRING,
            allowNull: true,
            unique: false
        },
    },{
        sequelize,
        tableName: 'VPDirectorInfoHistory',
        schema: 'dbo',
        timestamps: true,
        createdAt: 'CreatedAt',
        updatedAt: 'UpdatedAt',
        indexes: [
            {
                name: "PK__VPDirectorInfoHistory",
                unique: false,
                fields: [
                    { name: "CompanyNumber" },
                ]
            },
        ]
    });
    return VpDirectorInfoHistory;
}