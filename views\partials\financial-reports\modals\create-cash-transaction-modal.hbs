<div class="modal fade" id="newCashTransactionModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable " role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 id="bankAccountModalLbl" class="modal-title">New Cash/ Bank Account</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-3">
                <form class="form no-border p-1" id="newCashTransactionForm" novalidate>

                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="bankDescription">2.1.1. Description</label> 
                                <input type="text" name="bankDescription" id="bankDescription" class="form-control" required>
                            </div>
                        </div>

                    </div>
                    <div class="row">
                        <div class="col-8">
                            <label for="bankAccountType">2.1.2. Account type</label>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <select name="bankAccountType" id="bankAccountType" class="form-control w-100" data-toggle="select2"
                                    required>
                                    <option value="" disabled>
                                        Select an option
                                    </option>
                                    <option  value="Current">
                                        Current
                                    </option>
                                    <option  value="Deposit">
                                        Deposit
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-8">
                            <label  for="openingAmount">2.1.3. Opening amount</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="openingAmount" class="form-control autonumber text-right" data-a-sep=","
                                    required data-m-dec="2" name="openingAmount" >
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <label for="closingAmount">2.1.4. Closing amount</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="closingAmount" class="form-control autonumber text-right" data-a-sep=","
                                    required data-m-dec="2" name="closingAmount">
                            </div>
                        </div>
                    </div>


                    <div class="row">
                        <div class="col-md-8">
                            <h5> Bank Transactions during the reporting period</h5>
                        </div>
                        <div class="col-md-4">
                            <h5 >Bank Transactions during the reporting period</h5>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <span><b>Assets</b> </span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <label  for="assetsBankTransfers">2.1.5. Receipts from / payments to other Bank Accounts of the Company</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="assetsBankTransfers" class="form-control autonumber text-right bank-transaction-value" data-a-sep=","
                                    required data-m-dec="2"  name="assetsBankTransfers" >
                            </div>
                        </div>
                    </div>


                    <div class="row">
                        <div class="col-md-8">
                            
                                <label for="assetsInvestmentsAcquisition">2.1.6. Acquisition of Investments and other financial assets</label>

                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="assetsInvestmentsAcquisition" class="form-control autonumber-neg  text-right bank-transaction-value" 
                                    required  name="assetsInvestmentsAcquisition" >
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                                <label for="assetsInvestmentsSale">2.1.7. Sale of Investments and other financial assets</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="assetsInvestmentsSale" class="form-control autonumber-pos text-right bank-transaction-value" 
                                    required name="assetsInvestmentsSale" >
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                                <label for="assetsTangibleAcquisition">2.1.8. Acquisition of Tangible Fixed Asset</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="assetsTangibleAcquisition" class="form-control autonumber-neg text-right bank-transaction-value" 
                                    required  name="assetsTangibleAcquisition" >
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                                <label for="assetsTangibleSale">2.1.9. Sale of Tangible Fixed Asset</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="assetsTangibleSale" class="form-control autonumber-pos text-right bank-transaction-value" 
                                    required  name="assetsTangibleSale" >
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                                <label for="assetsIntangibleAcquisition">2.1.10. Acquisition of Intangible Assets</label>

                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="assetsIntangibleAcquisition" class="form-control autonumber-neg text-right bank-transaction-value" 
                                    required  name="assetsIntangibleAcquisition" >
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                                <label for="assetsIntangibleSale">2.1.11. Sale of Intangible Assets</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="assetsIntangibleSale" class="form-control autonumber-pos text-right bank-transaction-value" 
                                    required  name="assetsIntangibleSale" >
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                                <label for="assetsOtherAcquisition">2.1.12. Acquisition of Other Assets</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="assetsOtherAcquisition" class="form-control autonumber-neg text-right bank-transaction-value" 
                                    required  name="assetsOtherAcquisition" >
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                            <label for="assetsOtherSale">2.1.13. Sale of Other Assets</label>

                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="assetsOtherSale" class="form-control autonumber-pos text-right bank-transaction-value" 
                                    required  name="assetsOtherSale" >
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                            <label for="assetsLoanReceivablePaid">2.1.14. Loans receivable - amounts paid</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="assetsLoanReceivablePaid" class="form-control autonumber-neg text-right bank-transaction-value" 
                                    required  name="assetsLoanReceivablePaid">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                            <label for="assetsLoanReceivableReceived">2.1.15. Loans receivable - amounts received</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="assetsLoanReceivableReceived" class="form-control autonumber-pos text-right bank-transaction-value" 
                                    required  name="assetsLoanReceivableReceived" > 
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                            <label for="assetsReceivablesPaid">2.1.16. Receivables - amounts paid</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="assetsReceivablesPaid" class="form-control autonumber-neg text-right bank-transaction-value" 
                                    required  name="assetsReceivablesPaid" >
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                            <label for="assetsReceivablesReceived">2.1.17. Receivables - amounts received</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="assetsReceivablesReceived" class="form-control autonumber-pos text-right bank-transaction-value" 
                                    required  name="assetsReceivablesReceived" >
                            </div>
                        </div>
                    </div>


                    <div class="row">
                        <div class="col-12">
                            <span><b>Liabilities</b> </span>
                        </div>
                    </div>


                    <div class="row">
                        <div class="col-md-8">
                            
                            <label for="liabilitiesAccountsPayableReceived">2.1.18. Accounts payable - amounts received</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="liabilitiesAccountsPayableReceived" class="form-control autonumber-pos text-right bank-transaction-value" 
                                    required  name="liabilitiesAccountsPayableReceived" >
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                                <label for="liabilitiesAccountsPayablePaid">2.1.19. Accounts payable - amounts paid</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="liabilitiesAccountsPayablePaid" class="form-control autonumber-neg text-right bank-transaction-value" 
                                    required  name="liabilitiesAccountsPayablePaid" >
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                            <label for="liabilitiesLoansPayableReceived">2.1.20. Loans payable - amounts received</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="liabilitiesLoansPayableReceived" class="form-control autonumber-pos text-right bank-transaction-value" 
                                    required  name="liabilitiesLoansPayableReceived" >
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                            <label for="liabilitiesLoansPayablePaid">2.1.21. Loans payable - amounts paid</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="liabilitiesLoansPayablePaid" class="form-control autonumber-neg text-right bank-transaction-value" 
                                    required  name="liabilitiesLoansPayablePaid" >
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <span><b>Expenses</b> </span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                                <label for="expensesCompAdminFees">2.1.22. Company Administration fees</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="expensesCompAdminFees" class="form-control autonumber-neg text-right bank-transaction-value" 
                                    required  name="expensesCompAdminFees" >
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                            <label for="expensesPortMngmntFees">2.1.23. Portfolio management fees and Related Services</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="expensesPortMngmntFees" class="form-control autonumber-neg text-right bank-transaction-value" 
                                    required  name="expensesPortMngmntFees" >
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                            <label for="expensesBankFeesCharges">2.1.24. Bank Fees / Charges</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="expensesBankFeesCharges" class="form-control autonumber-neg text-right bank-transaction-value" 
                                    required  name="expensesBankFeesCharges">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                            <label for="expensesLoanInterest">2.1.25. Loan interest expense</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="expensesLoanInterest" class="form-control autonumber-neg text-right bank-transaction-value" 
                                    required  name="expensesLoanInterest"> 
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                            <label for="expensesIncomeTax">2.1.26. Income tax expense</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="expensesIncomeTax" class="form-control autonumber-neg text-right bank-transaction-value" 
                                    required  name="expensesIncomeTax" >
                            </div>
                        </div>
                    </div>

                    <div class="row hidden">
                        <div class="col-md-8">
                            
                            <label for="bankOtherExpenses">Other Expenses</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="bankOtherExpenses" class="form-control autonumber-neg text-right bank-transaction-value" 
                                     name="bankOtherExpenses" >
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <span><b>Income</b> </span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                            <label for="incomeDividendReceived">2.1.27. Dividend Income received</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="incomeDividendReceived" class="form-control autonumber-pos text-right bank-transaction-value" 
                                    required  name="incomeDividendReceived" >
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                            <label for="incomeCouponInterestReceived">2.1.28. Coupon interest income received</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="incomeCouponInterestReceived" class="form-control autonumber-pos text-right bank-transaction-value" 
                                    required  name="incomeCouponInterestReceived" >
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                            <label for="incomeLoanInterestReceived">2.1.29. Loan interest income received</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="incomeLoanInterestReceived" class="form-control autonumber-pos text-right bank-transaction-value" 
                                    required  name="incomeLoanInterestReceived">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                            <label for="incomeBankInterestReceived">2.1.30. Bank interest income received</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="incomeBankInterestReceived" class="form-control autonumber-pos text-right bank-transaction-value" 
                                    required  name="incomeBankInterestReceived" >
                            </div>
                        </div>
                    </div>

                    <div class="row hidden">
                        <div class="col-md-8">
                            
                            <label for="bankOtherIncome">Other income received</label>

                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="bankOtherIncome" class="form-control autonumber-pos text-right bank-transaction-value" 
                                      name="bankOtherIncome" >
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <span><b>Equity</b> </span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                            <label for="equityPaymentToShareholder">2.1.31. Payment to the shareholder as Dividend paid or Return of Capital Contributions or Reduction of Share Capital / Premium</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="equityPaymentToShareholder" class="form-control autonumber-neg text-right bank-transaction-value" 
                                    required  name="equityPaymentToShareholder" >
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            
                            <label for="equityReceiptsFromShareholder">2.1.32. Receipts from the shareholder for settlement of the Share Capital/Premium</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="equityReceiptsFromShareholder" class="form-control autonumber-pos text-right bank-transaction-value" 
                                    required  name="equityReceiptsFromShareholder" >
                            </div>
                        </div>
                    </div>


                    <div class="row">
                        <div class="col-md-8">
                            
                            <label for="equityCapitalContribution">2.1.33. Receipts from the shareholder as Capital Contribution</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="equityCapitalContribution" class="form-control autonumber-pos text-right bank-transaction-value" 
                                    required  name="equityCapitalContribution" >
                            </div>
                        </div>
                    </div>


                    <div class="row">
                        <div class="col-md-8">
                            
                            <label for="closingBalancePerBankStatement">2.1.34. Closing balance per Bank Statement</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="closingBalancePerBankStatement" class="form-control autonumber text-right" data-a-sep=","
                                    data-m-dec="2" name="closingBalancePerBankStatement" readonly value="0.00">
                                <div class="invalid-feedback" id="closingBalancePerBankError" >
                                    The Cash in Bank is not reconciled by 0
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="row">
                        <div class="col-md-8">
                            
                            <label for="cashTransactionCurrency">2.1.35. Please indicate the currency of the bank account:</label>

                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <select name="cashTransactionCurrency" id="cashTransactionCurrency" class="form-control w-100">
                                    {{#each currencies}}
                                    <option value="{{cc}}" {{#ifEquals cc 'USD' }} selected {{/ifEquals}} >
                                        {{cc}} - {{name}}
                                    </option>
                                    {{/each}}
                                
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row" id="foreingExchangeRateRows">
                        <div class="col-md-8">
            
                            <label for="foreingExchangeRate">If the currency is different than the reporting currency then please indicate the foreign exchange rate to be
                                multiplied:</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="foreingExchangeRate" class="form-control autonumber text-right " data-a-sep=","
                                    required data-m-dec="4" name="foreingExchangeRate" value="1.00">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <label for="cashAtBankTotal">2.1.36. Cash at bank:</label>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <input type="text" id="cashAtBankTotal" class="form-control autonumber text-right" data-a-sep=","
                                    data-m-dec="2" name="cashAtBankTotal" readonly value="0.00">
                            </div>
                        </div>
                    </div>

                </form>
            </div>
            <div class="modal-footer justify-content-end">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>
                <button id="loadingCashTransactionBtn" class="btn btn-primary" type="button" disabled >
                    <span class="spinner-border spinner-border-sm" aria-hidden="true"></span>
                    Loading...
                </button>
                <button id="submitCashTransactionBtn" class="btn solid royal-blue" type="submit" form="newCashTransactionForm">Save</button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="/views-js/partials/financial-reports/modals/create-cash-transaction-modal.js"></script>