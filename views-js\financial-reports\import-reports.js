let importedData;
Dropzone.autoDiscover = false;
$(async function () {
    let field = '';
    const csrfToken = $("input[name='csrf-token']").val()
    let myDropZone = new Dropzone('#uploadFile', {
        url: './import-entries/load-file',
        acceptedFiles: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        uploadMultiple: false,
        autoProcessQueue: true,
        parallelUploads: 1,
        maxFiles: 1,
        maxFilesize: 5,
        paramName: function () {
            return 'fileUploaded';
        },
        headers: {
            'x-csrf-token': csrfToken
        },
        init: function () {
            this.on('processing', function () {
                this.options.url = './import/load-file';
            });
            this.on("success", function (file, response) {
                if (response.error) {
                    toastr["warning"](response.error);
                    if (this.files.length !== 0) {
                        for (let i = 0; i < this.files.length; i++) {
                            this.files[i].previewElement.remove();
                        }
                        this.files.length = 0;
                    }
                } else {
                    $('#loadDataBtn').show();
                    importedData = response;
                }
            })
            this.on("sending", function (file, xhr, formData) {
                $("#btnSubmit").prop('disabled', true);
                if (!formData.has('filetype')) {
                    formData.append("filetype", field);
                }
                formData.append('csrf-token', csrfToken);
            })

            this.on('maxfilesexceeded', function (file) {
            });

            this.on('resetFiles', function () {
                if (this.files.length !== 0) {
                    for (let i = 0; i < this.files.length; i++) {
                        this.files[i].previewElement.remove();
                    }
                    this.files.length = 0;
                }
                $('#maxUpload').text(this.options.maxFiles);
            });
        },
    });
});

$('#loadTable').click(function () {
    loadTable()
})

$('#saveData').click(function () {
    saveData()
})

function loadTable() {
    $('#loadDataBtn').hide();
    $('#uploadRow').hide();

    let template = Handlebars.templates.importdetailstable;
    let importTable = template({reports: importedData.reports});

    $('#importTable').html(importTable);
    $('#saveDataBtn').show();
    let table = $("#scroll-horizontal-datatable").DataTable({
        "pageLength": 50,
        "order": [[0, "asc"]],
        scrollX: !0,
        language: {
            paginate: {
                previous: "<i class='mdi mdi-chevron-left'>",
                next: "<i class='mdi mdi-chevron-right'>"
            }
        },
        drawCallback: function () {
            $(".dataTables_paginate > .pagination").addClass("pagination-rounded")
        }
    });
}

function saveData() {
    setTimeout(function () {
        $('#saveDataBtn').prop('disabled', true);
    }, 0);
    $.ajax({
        type: "POST",
        url: "./import",
        contentType: "application/json; charset=utf-8",
        data: JSON.stringify({importedData}),
        success: function (data) {
            if (data.success) {
                toastr.success(`Data saved successfully. ${data.inserted} records were inserted.`);
                window.setTimeout(function () {
                    document.location.reload();
                }, 1500)
            } else {
                toastr["warning"]('Sorry, there was an error saving the data.');
                $('#saveDataBtn').prop('disabled', false);
            }
        }
    });
}