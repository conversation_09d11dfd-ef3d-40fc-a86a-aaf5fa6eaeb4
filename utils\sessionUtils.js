exports.onSessionDestroyed = function(req, res, redirectToHome=true){
    res.removeHeader('Set-Cookie')
    // Pass parameter true to overwrite the existing token and generate a new one
    res.removeHeader('Set-<PERSON>ie');
    const csrfToken = req.csrfToken(true);
    res.setHeader('x-csrf-token', csrfToken);
    res.locals.csrfToken = csrfToken;
    if(redirectToHome){
        res.redirect("/");
    }
    return res
};