const appInsightsClient = require("applicationinsights").defaultClient;
const User = require('../models/user');
const MasterClientCode = require('../models/masterClientCode');
const MailController = require('./mailController');
const { emailVerification, verifyCode } = require('./emailVerificationController');
const crypto = require('crypto');
const bcrypt = require('bcryptjs');
const speakeasy = require('speakeasy');
const qrcode = require('qrcode');
const httpConstants = require('http2').constants;
const moment = require('moment');
const axios = require('axios');
const sessionUtils = require('../utils/sessionUtils');

exports.login = function (req, res) {
    let blocked = (req.session.invalidLoginAttempts >= 5);
    let message = req.flash('message');
    if (blocked) {
        message = 'You have been blocked because of too many invalid login attempts'
    }
    res.render('user/login', {
        title: 'Welcome to the Trident Trust Client Portal',
        message: message,
        user: req.user,
        blocked: blocked,
    });
};

exports.register = function (req, res) {
    res.render('user/register', { 
        title: 'Register',
        registered: false,
    });
};

exports.forgot = function (req, res) {
    res.render('user/forgot', { 
        title: 'Reset password', 
        activated: false,
    });
};

// get view to set mfa code first time
exports.getSetup2fa = async function (req, res, next) {
    try{

        if (req.user.mfa_type !== "email" && req.user.secret_2fa && req.user.secret_2fa !== null && req.user.secret_2fa !== '' ) {
            let err = new Error('Sorry, you are not allowed to access this page at this time.');
            err.status = httpConstants.HTTP_STATUS_FORBIDDEN;
            return next(err);
        }


        if (req.user.mfa_type === "email") {
            await exports.getCode2fa(req, res, next);
        } else {
            let secret = speakeasy.generateSecret({
                length: 20,
                name: "Trident BVI Portal",
            });
            req.session.secret_2fa = secret;
            qrcode.toDataURL(secret.otpauth_url, function (err, image_data) {
                res.render("user/setup-2fa-step1", {
                    title: "Setup two-factor authentication",
                    imageData: image_data,
                });
            });
        }
    }catch(e){
        console.log(e);
        next(e);
    }

  };


// set mfa code first time
exports.doSetup2fa = async function (req, res, next) {
    if (req.user.mfa_type !== "email" && req.user.secret_2fa && req.user.secret_2fa !== null && req.user.secret_2fa !== '') {
        let err = new Error('Sorry, you are not allowed to access this page at this time.');
        err.status = httpConstants.HTTP_STATUS_FORBIDDEN;
        return next(err);
    }

    let secret = req.session.secret_2fa;

    let valid = speakeasy.totp.verify({
        secret: secret.base32,
        encoding: 'base32',
        token: req.body.code
    });

    if (valid) {
        User.findOneAndUpdate({ _id: req.user._id, email: req.user.email.toLowerCase() }, { 'secret_2fa': secret.base32 }, function (err) {
            if (!err) {
                req.session.auth2fa = true;
                res.redirect('/');
            }
        });

    } else {
        let blocked = await validateMfaAttempts(req.user.email.toLowerCase(), req.session);
        if (blocked) {
            return showLockedUserPage(req, res, next, 'You have been blocked because of too many invalid login attempts');
        }
        qrcode.toDataURL(secret.otpauth_url, function (err, image_data) {
            res.render('user/setup-2fa-step1', { 
                title: 'Setup two-factor authentication', 
                imageData: image_data, message: 'Invalid code! Please try again' 
            });
        });
    }
};

// get view to mfa step 
exports.getCode2fa = async function (req, res, next) {
    try{
        let title = "Two-factor authentication";
        let dialog = "Please enter the 6-digit code from your two-factor authenticator application for Trident BVI Portal.";
        if (req.user.mfa_type == "email") {
            await emailVerification(req.user.email);
            title = "Email authentication"
            dialog = "Please enter the 6-digit code from your email."
        }
        res.render("user/code-2fa", { 
            title: title, 
            dialog: dialog,
            method: req.user.mfa_type

        });
    }catch(e){
        console.log(e);
        next(e);
    }

};

// Send new email verification  code
exports.sendNewEmailCode = async function (req, res) {
    try {
        const response = await emailVerification(req.user.email);
        res.status(200).json({ success: response.status === 200, message: response.message });
    } catch (e) {
        res.status(500).json({ success: false, message: "Verification email could not be sent." });
    }
};


//validate mfa/email code 
exports.doCode2fa = async function (req, res, next) {
    try {
        if (req.body.method == "email" || req.user.mfa_type == "email") {
            const response = verifyCode(req.user.email, req.body.code);
            if (!response.error) {
                req.session.auth2fa = true;
                res.redirect("/");
            } else {
                let blocked = await validateMfaAttempts(req.user.email.toLowerCase(), req.session);
                if (blocked) {
                    return showLockedUserPage(req, res, next, 'You have been blocked because of too many invalid login attempts');
                }
                res.render("user/code-2fa", {
                    title: "Email authentication",
                    dialog: "Please enter the 6-digit code from your email.",
                    message: "Invalid code! Please try again",
                    method: req.user.mfa_type
                });
            }
        }
        else {
            const user = await User.findOne({ _id: req.user._id, email: req.user.email.toLowerCase() })

            if(!user){
                req.logout(function (err) {
                    if (err) { return next(err) }
                    req.session.destroy(function () {
                        // cannot access session here
                        sessionUtils.onSessionDestroyed(req, res);
                    });
                }); 
            }
            let valid = speakeasy.totp.verify({
                secret: user.secret_2fa,
                encoding: "base32",
                token: req.body.code,
            });

            if (valid) {
                req.session.auth2fa = true;
                res.redirect("/");
            } else {
                let blocked = await validateMfaAttempts(req.user.email.toLowerCase(), req.session);
                if (blocked) {
                    return showLockedUserPage(req, res, next, 'You have been blocked because of too many invalid login attempts');
                }
                res.render("user/code-2fa", {
                    title: "Two-factor authentication",
                    dialog: "Please enter the 6-digit code from your two-factor authenticator application for Trident BVI Portal.",
                    message: "Invalid code! Please try again",
                    method: req.user.mfa_type
                });
            }
        }
    }catch(e){
        console.log(e);
        next(e);
    }

};


// reset password and send email with link to set new password
exports.doForgot = async function (req, res) {
    const user = await User.findOne({ email: req.body.email.toLowerCase()});
    if(!user || user.locked){
        return res.render('user/forgot', {
            title: 'Reset password',
            message: "If your email address is known, you will receive an email with futher instructions",
            activated: true
        })
    }

    const token = crypto.randomBytes(20).toString('hex');
    user.resetPasswordToken = token;
    user.sessionId = null;
    const link = process.env.SUBSTANCE_APP_HOST + '/users/reset/' + token;


    //Check if the user send a reset email 5 minutes ago
    const checkLastPasswordDateObj = checkLastPasswordResetByUser(user);

    if (checkLastPasswordDateObj.isValid === false) {
        console.log("The password was reset less than 5 minutes ago for user: ", req.body.email)
        return res.render('user/forgot', {
            title: 'Reset password',
            message: "If your email address is known, you will receive an email with futher instructions",
            activated: true
        });
    } else {
        user.lastResetPasswordDate = checkLastPasswordDateObj.lastResetPasswordDate;
    }
    
    user.save();
    appInsightsClient.trackEvent({ name: "user forgot password", properties: { email: user.email } });
    MailController.send(user.email, 'Reset password', 'Reset your password with this link: ' + link, 'Reset your password with this <a href="' + link + '">link</a>');
    res.render('user/forgot', { title: 'Reset password', message: "If your email address is known, you will receive an email with futher instructions", activated: true });
};

exports.activate = function (req, res) {
    //show error in case no user found
    //show error in case user is already activated
    User.findOne({ resetPasswordToken: req.params.token }, function (err, user) {
        if (!user) {
            res.render('user/activate', { title: 'Activate', message: "No user found for this token", activated: false, });
        } else if (user.active) {
            res.render('user/activate', { title: 'Activate', message: "This user is already active", activated: false, });
        } else {
            res.render('user/activate', { title: 'Activate', activated: false, });
        }
    });
};


// validate new password and activate user
exports.doActivate = async function (req, res) {
    //confirm passwords are the same and conform complexity
    //Regular Expressions
    let regex = new Array();
    regex.push("[A-Z]"); //For Uppercase Alphabet
    regex.push("[a-z]"); //For Lowercase Alphabet
    regex.push("[0-9]"); //For Numeric Digits
    // eslint-disable-next-line no-useless-escape
    regex.push(/[$@$!%*#?&\^\-\(\)]/); //For Special Characters

    let passed = 0;

    //Validation for each Regular Expression
    for (let regexValidation of regex) {
        if ((new RegExp(regexValidation)).test(req.body.password)) {
            passed++;
        }
    }

    //check for same passwords
    if (req.body.password !== req.body.confirm) {
        return res.render('user/reset', { title: 'Reset password', message: "The passwords do not match", activated: false });
    }

    //Validation for Length of Password
    if (passed > 3 && req.body.password.length >= 12) {
        
        
        User.findOne({ resetPasswordToken: req.params.token }, function (err, user) {
            if (!user) {
                //show error in case no user found
                res.render('user/activate', { title: 'Activate', message: "No user found for this token", activated: false });
            } else if (user.active) {
                //show error in case user is already activated
                res.render('user/activate', { title: 'Activate', message: "This user is already active", activated: false });
            } else {    

                if (!user.passwordHistory?.details) {
                    user.passwordHistoy = {
                        details: []
                    }
                }
                
                if (user.passwordHistoy?.details.length > 0){
                    const existsSamePassword = user.passwordHistoy.details.find((oldPassword) => 
                        bcrypt.compareSync(req.body.password, oldPassword) === true);

                    if (existsSamePassword){
                        
                        return res.render('user/activate', {
                            title: 'Activate',
                            message: "The password does not meet the security requirements. Please choose a different password", 
                            activated: false 
                        });
                    }
                }


                const SALT_FACTOR = 5;
                const salt = bcrypt.genSaltSync(SALT_FACTOR);
                const hash = bcrypt.hashSync(req.body.password, salt);

                user.active = true;
                user.password = hash;
                user.passwordHistoy.details.push(hash);
                user.resetPasswordToken = null;
                user.resetPasswordExpires = undefined;
                

                User.findOneAndUpdate({ _id: user.id, email: user.email }, user, {}, function (err) {
                    if (err) {
                        res.render('user/activate', { title: 'Activate', message: "An error occured. Please try again later.", activated: false });
                    } else {
                        appInsightsClient.trackEvent({ name: "user activated", properties: { email: user.email } });
                        res.render('user/activate', { title: 'Activate', message: "Your account is activated", activated: true });
                    }

                });
            }
        });
    } else {
        res.render('user/activate', { 
            title: 'Activate', 
            message: "The password must be at least 12 characters long and include the following: lowercase letters, uppercase letters, numbers, and special characters.", 
            activated: false ,
           
        });
    }
}


//show view to set new password after reset
exports.reset = function (req, res) {
    //show error in case no user found
    //show error in case user is already activated
    User.findOne({ resetPasswordToken: req.params.token }, function (err, user) {
        if (!user) {
            res.render('user/reset', { title: 'Reset password', message: "No user found for this token", activated: false,});
        } else {
            res.render('user/reset', { title: 'Reset password', activated: false,});
        }
    });
};


//validate new password
exports.doReset = async function (req, res) {
    //confirm passwords are the same and conform complexity
    //Regular Expressions
    let regex = new Array();
    regex.push("[A-Z]"); //For Uppercase Alphabet
    regex.push("[a-z]"); //For Lowercase Alphabet
    regex.push("[0-9]"); //For Numeric Digits
    // eslint-disable-next-line no-useless-escape
    regex.push(/[$@$!%*#?&\^\-\(\)]/); //For Special Characters

    let passed = 0;

    //Validation for each Regular Expression
    for (let regexValidation of regex) {
        if ((new RegExp(regexValidation)).test(req.body.password)) {
            passed++;
        }
    }

    //check for same passwords
    if (req.body.password !== req.body.confirm) {
        return res.render('user/reset', { title: 'Reset password', message: "The passwords do not match", activated: false });
    }

    //Validation for Length of Password
    if (passed > 3 && req.body.password?.length >= 12) {
        
        //show error in case no user found
        //show error in case user is already activated
        User.findOne({ resetPasswordToken: req.params.token }, function (err, user) {
            if (!user) {
                res.render('user/reset', { title: 'Reset password', message: "No user found for this token", activated: false });
            } else {

                //check for previous passwords
                if (!user.passwordHistoy?.details) {
                    user.passwordHistoy = {
                        details: user.password ? [user.password ] : []
                    };
                }
                
                if (user.passwordHistoy.details.length > 0) {
                    const existsSamePassword = user.passwordHistoy.details.find((oldPassword) =>
                        bcrypt.compareSync(req.body.password, oldPassword) === true);

                    if (existsSamePassword) {
                        return res.render('user/reset', {
                            title: 'Reset password',
                            message: "The password does not meet the security requirements. Please choose a different password",
                            activated: false
                        });
                    }
                }

                const SALT_FACTOR = 5;
                const salt = bcrypt.genSaltSync(SALT_FACTOR);
                const hash = bcrypt.hashSync(req.body.password, salt);

                user.sessionId = null;
                user.active = true;
                user.password = hash;
                user.passwordHistoy.details.push(hash);
                user.resetPasswordToken = null;
                user.resetPasswordExpires = undefined;

                User.findOneAndUpdate({ _id: user.id, email: user.email }, user, {}, function (err) {
                    if (err) {
                        res.render('user/reset', { title: 'Reset password', message: "An error occured. Please try again later.", activated: false });
                    } else {
                        appInsightsClient.trackEvent({ name: "reset password", properties: { email: user.email } });
                        res.render('user/reset', { title: 'Reset password', message: "Your password has been reset", activated: true });
                    }

                });

            }
        });
    } else {
        res.render('user/reset', { 
            title: 'Reset password', 
            message: "The password must be at least 12 characters long and include the following: lowercase letters, uppercase letters, numbers, and special characters.", 
            activated: false 
        });
    }
}

exports.getVerificationMethodView = async function(req, res, next){
    try {
        if (req.user.mfa_type && req.user.mfa_type !== null && req.user.mfa_type !== "") {
            let err = new Error('Sorry, you are not allowed to access this page at this time.');
            err.status = httpConstants.HTTP_STATUS_FORBIDDEN;
            return next(err);
        }
        res.render("user/validation-method");
    } catch (error) {
        console.log("Error verification method page", error);
        next(error);
    }
}

exports.verificationMethod = async function (req, res, next) {
    try {
        if (req.user.mfa_type && req.user.mfa_type !== null && req.user.mfa_type !== "") {
            let err = new Error('Sorry, you are not allowed to access this page at this time.');
            err.status = httpConstants.HTTP_STATUS_FORBIDDEN;
            return next(err);
        }


        User.findOneAndUpdate({ email: req.user.email.toLowerCase() }, { mfa_type: req.body.method }, function (err, raw) {
            if (err) return next(err);
            console.log('The raw response from Mongo was ', raw);
        });

        let title = "Two-factor authentication";
        let dialog = "Please enter the 6-digit code from your two-factor authenticator application for Trident BVI Portal.";

        if (req.body.method == "email") {
            await emailVerification(req.user.email);
            title = "Email authentication"
            dialog = "Please enter the 6-digit code from your email."

            res.render("user/code-2fa", {
                title: title,
                message: "",
                dialog: dialog,
                method: req.body.method,
            });
        } else {
            res.redirect("/users/2fa-setup");
        }
    }catch(e){
        console.log(e);
        next(e);
    }

  }

exports.doRegistration = async function (req, res) {
    //check if user already exists for this email -> tolower
    User.findOne({ email: req.body.email.toLowerCase() }, function (err, user) {
        if (user) {
            res.render('user/register', { title: 'Register', message: "We have sent you an email with further instructions", registered: true });
        } else {
            //check valid combination for email (lowercase) and masterclientcode
            MasterClientCode.findOne({ owners: req.body.email.toLowerCase(), code: req.body.masterClientCode }, function (err, masterClientCode) {

                if (!masterClientCode) {
                    res.render('user/register', { title: 'Register', message: "We have sent you an email with further instructions", registered: true });
                } else {
                    //if valid, create user and temp password, then send email with token to reset the password
                    const token = crypto.randomBytes(20).toString('hex');
                    const user = new User({
                        username: req.body.email.toLowerCase(),
                        email: req.body.email.toLowerCase(),
                        active: false,
                        resetPasswordToken: token
                    });
                    const link = process.env.SUBSTANCE_APP_HOST + '/users/activate/' + token;

                    user.save(function (err) {
                        if (err) {
                            appInsightsClient.trackEvent({ name: "user register error", properties: { error: err, email: user.email, masterclientcode: req.body.masterClientCode } });
                            res.render('user/register', { title: 'Register', message: "An error occured. Please try again later." + err, registered: true });
                        } else {
                            MailController.send(user.email, 'Registration', 'Finish your registration with this link ' + link, 'Finish your registration with this <a href="' + link + '">link</a>');
                            appInsightsClient.trackEvent({ name: "user registered", properties: { email: user.email, masterclientcode: req.body.masterClientCode } });
                            res.render('user/register', { title: 'Register', message: "We have sent you an email with further instructions", registered: true });
                        }
                    });
                }
            });
        }
    });

}


exports.ensureAuthenticated = function (req, res, next) {
    if (req.user && req.session.id == req.user.sessionId) {
        next();
    }
    else {
        req.logout(function (err) {
            if (err) { return next(err) }
            req.session.destroy(function () {
                // cannot access session here
                sessionUtils.onSessionDestroyed(req, res);
            });
        });
    }
}


exports.getChangePasswordView = function (req, res) {
    res.render('user/change-password', { title: 'Change password', changeSuccess: false, user: req.user, });
};


exports.startChangePasswordProcess = async function (req, res, next){
    try{
        const user = await User.findOne({ email: req.user.email});
        if(!user){
            req.session.destroy(function () {
                // cannot access session here
                sessionUtils.onSessionDestroyed(req, res);
            });
        }

        const isValidPassword = bcrypt.compareSync(req.body.password, user.password) === true;

        if(!isValidPassword){
            const limitAttempts = process.env.CHANGE_PASSWORD_ATTEMPTS_LIMIT || 5;
            if (req.session.changePasswordAttempts) {
                req.session.changePasswordAttempts +=1;
            }else{
                req.session.changePasswordAttempts = 1;
            }

            if (req.session.changePasswordAttempts >= limitAttempts){
                user.locked = true;
                user.sessionId = null;
                await user.save();
                return showLockedUserPage(req, res, next, 'You have been blocked because of too many invalid change password attempts');
            }else{
                return res.render('user/change-password', { 
                    title: 'Reset password', 
                    message: "The password you entered is incorrect, please try again", 
                    activated: false
                });
            }
            
        }

       
        const token = crypto.randomBytes(20).toString('hex');
        user.resetPasswordToken = token;
        user.sessionId = null;
        await user.save();
        const link = process.env.SUBSTANCE_APP_HOST + '/users/reset/' + token;
        appInsightsClient.trackEvent({ name: "user change password", properties: { email: user.email } });
        MailController.send(user.email, 'Reset password', 'Reset your password with this link: ' + link, 'Reset your password with this <a href="' + link + '">link</a>');
        req.session.destroy(function () {
            sessionUtils.onSessionDestroyed(req, res, false);
            return res.render('user/change-password', { title: 'Change password',
             message: "Success, You will receive an email with futher instructions to continue with the change of the password", changeSuccess: true });
        });


    }catch(e){
        console.log(e);
        req.session.destroy(function () {
            sessionUtils.onSessionDestroyed(req, res, false);
            return res.render('user/change-password', {
                title: 'Change password',
                message: "Success, You will receive an email with futher instructions to continue with the change of the password", changeSuccess: true
            });
        });
    }
}


async function validateMfaAttempts(email, sessionData){
    let blocked = false;
    const limitAttempts = process.env.MFA_ATTEMPTS_LIMIT || 5;
    if (!sessionData.mfaAttempts){
        sessionData.mfaAttempts = 1;
        return blocked
    }

    sessionData.mfaAttempts += 1;
    if (sessionData.mfaAttempts >= limitAttempts) {
        const user = await User.findOne({ email: email });
        
        if(user){
            user.locked = true;
            await user.save();
            blocked = true;
        }
    }
    return blocked
}


function showLockedUserPage(req, res, next, message) {
    req.logout(function (err) {
        if (err) { return next(err) }
        req.session.destroy(function () {
            sessionUtils.onSessionDestroyed(req, res, false);
            return res.render('user/login', {
                title: 'Welcome to the Trident Trust Client Portal',
                message: message,
                user: req.user,
                blocked: true
            });
        });
    });

}


exports.validateCaptcha = async function (req, res, next) {
    const ROUTES = [
        {
            path: "/login",
            view: 'user/login',
            title: 'Welcome to the Trident Trust Client Portal'
        },
        {
            path: "/register",
            view: 'user/register',
            title: 'Register',

        },
        {
            path: "/activate",
            view: 'user/activate',
            title: 'Activate',
        },
        {
            path: "/reset",
            view: 'user/reset',
            title: 'Reset password',
        },
        {
            path: "/forgot",
            view: 'user/forgot',
            title: 'Reset password',
        },

    ]
    const currentRoute = ROUTES.find((route) => req.path.indexOf(route.path) > -1)


    if(!currentRoute){
        let err = new Error('Not found');
        err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
        return next(err);
    }

    try {

        const validateCatpchaResponse = await verifyCaptcha(req);

        if (validateCatpchaResponse.status !== 200){
            

            return res.render(currentRoute.view, {
                title: currentRoute.title,
                message: 'An error occurred during the process. Please try again later.',
                user: req.user,
                blocked: false,
            });
        }

        next();

    } catch (e) {
        console.log("Error captcha validation", e)
        return res.render(currentRoute.view, {
            title: currentRoute.title,
            message: 'An error occurred during the process. Please try again later.',
            user: req.user,
            blocked: false,
           
        });
    }

}

async function verifyCaptcha(req){
    try {
        console.log("recaptchaToken ", req.body['g-recaptcha-response'])
        if (req.body['g-recaptcha-response'] === undefined || req.body['g-recaptcha-response'] === '' || req.body['g-recaptcha-response'] === null) {
            console.log("something goes to wrong with captcha verification");
            return { status: 400, error: 'something goes to wrong with captcha verification.' }
        }

        const recaptchaVerifyUrl = process.env.RECAPTCHA_VERIFY_URL;
        const secretKey = process.env.RECAPTCHA_SECRET_KEY;
        const minAllowedScore = process.env.RECAPTCHA_SCORE || 0.7;

        const verificationURL = `${recaptchaVerifyUrl}?secret=${secretKey}&response=${req.body['g-recaptcha-response']}&remoteip=${req.connection.remoteAddress}`;
        const options = {
            url: verificationURL,
            method: 'get'
        }
        const response = await axios(options);
        const responseBody = await response.data;


        if ((responseBody.success !== undefined && !responseBody.success) || (responseBody.success === true && responseBody.score < minAllowedScore)) {
            console.log("Failed captcha verification");
            return { status: 500, error: 'Failed captcha verification' }
        }
        return { status: 200, message: 'valid captcha' }
    } catch (e) {
        console.log("Error captcha validation", e)
        return { status: 500, error: 'Internal server error' }
    }
}

function checkLastPasswordResetByUser(user) {
    let isValid = true;

    const lastResetDate = user.lastResetPasswordDate ? moment(user.lastResetPasswordDate).utc() : null;
    const currentDate = moment().utc();


    if (!lastResetDate) {
        user.lastResetPasswordDate = currentDate.toDate();
    }
    else {
        const diffMinutes = currentDate.diff(lastResetDate, 'minutes');

        if (diffMinutes < 5) {
            isValid = false;
        } else {
            user.lastResetPasswordDate = currentDate.toDate();
        }
    }

    return {
        "isValid": isValid,
        "lastResetPasswordDate": user.lastResetPasswordDate
    }
}

