exports.ACCOUNTING_SERVICE_TYPES = {
    SELF_SERVICE_COMPLETE: "self-service-complete",
    SELF_SERVICE_PREPARE: "self-service-prepare",
    TRIDENT_SERVICE_COMPLETE: "trident-service-complete",
    TRIDENT_SERVICE_DROP: "trident-service-drop"
}

exports.ACCOUNTING_SERVICE_TYPES_LABELS = {
    "self-service-complete": "1.2.1 Self Service: Complete the Company's Annual Return in the prescribed format",
    "self-service-prepare": "1.2.2 Self Service: Prepare the Company's Annual Return using the Trident Accounting Portal",
    "trident-service-complete": "1.2.3 Trident Service: Reformatting existing annual accounts, to the prescribed format, and submission (starting at $350)",
    "trident-service-drop": "1.2.4 Trident Service: Preparation of the accounts including a statement of Income and Expenses, and submission of the annual return(starting at $600)"
}

exports.REPORT_STATUS = {
    SAVED: "SAVED",
    IN_PROGRESS: "IN PROGRESS",
    IN_PENALTY: "IN PENALTY",
    UNDER_REVIEW: "UNDER REVIEW",
    CONFIRMED: "CONFIRMED",
    PAID: "PAID",
    RE_OPEN: "RE-OPEN",
    DELETED: "DELETED",
    REQUEST_HELP: "HELP REQUEST",
    HELP_PROGRESS: "HELP IN PROGRESS",
    REQUEST_INFO: "INFORMATION REQUEST",
    HELP_COMPLETED: "HELP COMPLETED"
}

exports.REPORT_REQUEST_INFO_STATUS = {
    REQUESTED: "REQUESTED",
    CANCELLED: "CANCELLED",
    RETURNED: "RETURNED",
}

exports.ACCOUNTING_FORM_STEPS = {
    DETAILS: "details",
    CASH: "cash-transactions",
    ASSETS: "assets",
    LIABILITIES: "liabilities",
    COMP_INCOME_EXPENSES: "complete-income-expenses",
    COMP_ASSETS_LBT: "complete-assets-lbt",
    SUMMARY: "summary",
    DECLARATION: "declaration",
    CONFIRMATION: "confirmation"
}

exports.BANK_ACCOUNTS_INPUT_NUMBER_VALIDATION_ERRORS = [
    { "input": "openingAmount", "validation": "num-any", "error": "Provide a valid value to opening amount" },
    { "input": "closingAmount", "validation": "num-any", "error": "Provide a valid value to closing amount" },
    { "input": "assetsBankTransfers", "validation": "num-any", "error": "Provide a valid value to receipts from / payments to other Bank Accounts " },
    { "input": "assetsInvestmentsAcquisition", "validation": "num-negative", "error": "Provide a negative value to acquisition of Investments" },
    { "input": "assetsInvestmentsSale", "validation": "num-positive", "error": "Provide a positive value to sale of Investments" },
    { "input": "assetsTangibleAcquisition", "validation": "num-negative", "error": "Provide a negative value to acquisition of tangible fixed asset" },
    { "input": "assetsTangibleSale", "validation": "num-positive", "error": "Provide a positive value to sale of tangible fixed asset" },
    { "input": "assetsIntangibleAcquisition", "validation": "num-negative", "error": "Provide a negative value to acquisition of intangible Assets" },
    { "input": "assetsIntangibleSale", "validation": "num-positive", "error": "Provide a positive value to sale of intangible Assets" },
    { "input": "assetsOtherAcquisition", "validation": "num-negative", "error": "Provide a positive negative to acquisition of other Assets" },
    { "input": "assetsOtherSale", "validation": "num-positive", "error": "Provide a positive value to sale of other Assets" },
    { "input": "assetsLoanReceivablePaid", "validation": "num-negative", "error": "Provide a positive negative to loans receivable - amounts paid" },
    { "input": "assetsLoanReceivableReceived", "validation": "num-positive", "error": "Provide a positive value to loans receivable - amounts received " },
    { "input": "assetsReceivablesPaid", "validation": "num-negative", "error": "Provide a negative value to receivables - amounts paid" },
    { "input": "assetsReceivablesReceived", "validation": "num-positive", "error": "Provide a positive value to receivables - mounts received" },

    { "input": "liabilitiesAccountsPayableReceived", "validation": "num-positive", "error": "Provide a positive value to accounts payable - amounts received" },
    { "input": "liabilitiesAccountsPayablePaid", "validation": "num-negative", "error": "Provide a negative value to accounts payable - amounts paid" },
    { "input": "liabilitiesLoansPayableReceived", "validation": "num-positive", "error": "Provide a positive value to loans payable - amounts received" },
    { "input": "liabilitiesLoansPayablePaid", "validation": "num-negative", "error": "Provide a negative value to loans payable - amounts paid" },

    { "input": "expensesCompAdminFees", "validation": "num-negative", "error": "Provide a negative value to company administration fees" },
    { "input": "expensesPortMngmntFees", "validation": "num-negative", "error": "Provide a negative value to portfolio management fees" },
    { "input": "expensesLoanInterest", "validation": "num-negative", "error": "Provide a negative value to loan interest expense" },
    { "input": "expensesBankFeesCharges", "validation": "num-negative", "error": "Provide a negative value to bank fees / charges" },
    { "input": "expensesIncomeTax", "validation": "num-negative", "error": "Provide a negative value to income tax expense" },

    { "input": "incomeDividendReceived", "validation": "num-positive", "error": "Provide a positive value to dividend Income received" },
    { "input": "incomeCouponInterestReceived", "validation": "num-positive", "error": "Provide a positive value to coupon interest income received" },
    { "input": "incomeLoanInterestReceived", "validation": "num-positive", "error": "Provide a positive value to loan interest income received" },
    { "input": "incomeBankInterestReceived", "validation": "num-positive", "error": "Provide a positive value to bank interest income received" },
    { "input": "equityPaymentToShareholder", "validation": "num-negative", "error": "Provide a negative value to payment to the shareholder as Dividend paid" },
    { "input": "equityReceiptsFromShareholder", "validation": "num-positive", "error": "Provide a positive value to receipts from the shareholder for settlement of the Share Capital/Premium" },
    { "input": "equityCapitalContribution", "validation": "num-positive", "error": "Provide a positive value to receipts from the shareholder as Capital Contribution" },
    { "input": "foreingExchangeRate", "validation": "num-any", "error": "Provide a valid value to foreing exchange rate" }
]


exports.FORM_COMPLETE_INCOME_EXPENSES_INPUT_NUMBER_VALIDATION_ERRORS = [
    { "input": "revenue", "validation": "num-positive", "error": "Provide a positive value to revenue" },
    { "input": "costOfSales", "validation": "num-positive", "error": "Provide a positive value to cost of sales" },
    { "input": "operatingExpenses", "validation": "num-positive", "error": "Provide a positive value to operating expenses" },
    { "input": "totalOtherExpenses", "validation": "num-positive", "error": "Provide a positive value to other expenses" },
    { "input": "incomeTax", "validation": "num-positive", "error": "Provide a positive value to income tax expenses" }
]

exports.FORM_COMPLETE_ASSETS_LBT_INPUT_NUMBER_VALIDATION_ERRORS = [
    { "input": "cashAmount", "validation": "num-positive", "error": "Provide a positive value to cash and cash equivalents" },
    { "input": "loansAndReceivables", "validation": "num-positive", "error": "Provide a positive value to loans and receivables" },
    { "input": "investmentsAssetsAmount", "validation": "num-positive", "error": "Provide a positive value to investments and other financial assets" },
    { "input": "fixedAssetsAmount", "validation": "num-positive", "error": "Provide a positive value to fixed assets" },
    { "input": "intangibleAssetsAmount", "validation": "num-positive", "error": "Provide a positive value to intangible assets" },
    { "input": "totalOtherAssets", "validation": "num-positive", "error": "Provide a positive value to other assets" },
    { "input": "accountsPayable", "validation": "num-positive", "error": "Provide a positive value to accounts payable " },
    { "input": "longTermDebts", "validation": "num-positive", "error": "Provide a positive value to long-term debts" },
    { "input": "totalOtherLiabilities", "validation": "num-positive", "error": "Provide a positive value to other liabilities" },
]