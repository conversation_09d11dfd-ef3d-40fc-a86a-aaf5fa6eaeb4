<main class="">
	<div class="container">
		<div class="row">
			<div class="col-12">
				<div class='contour'>
					<h2>5. {{title}}</h2>
					{{#if validationErrors }}
					{{#each validationErrors }}
					{{renderValidationMessage this.msg this.field}}
					{{/each}}
					{{/if}}
					<form method="POST" class='enquiry' autocomplete="off">
						<input type="text" hidden name="csrf-token" value="{{csrfToken}}">
						<!-- Start Content-->
						<div class="container-fluid">
							<div class="row">
								<div class="col-md-12">
									<div class="card-box">
										<div class="card-body">
											<h4 class="header-title "><br><br>Gross Income</h4>
											<!-- 4A.a -->
											<div class="row ">
												<div class="col-md-8">
													<label for="activityTotalGrossIncome">
														Total Gross Income for the relevant activity during the
														financial period:
													</label>
												</div>

												<div class="col-md-4 ">

													<input type="text" name="activityTotalGrossIncomeCurrency"
														id="activityTotalGrossIncomeCurrency" class="form-control"
														
														{{#if selectedCurrency}}
															value="{{selectedCurrency.cc}} - {{selectedCurrency.name}}"
														{{else}}
															value="{{defaultCurrency.cc}} - {{defaultCurrency.name}}"
														{{/if}}
														disabled
													/>

													<input type="text" id="activityTotalGrossIncome"
														class="form-control mt-2  autonumber" data-a-sep=","
														data-m-dec="2" data-min="0" data-max="1000000000"
														placeholder="0.0" name="activityTotalGrossIncome"
														value="{{data.gross_income_total}}" />

												</div>
											</div>

											<!-- 4A.b -->
											<div class="row mb-2">
												<div class="col-md-8">
													<label for="activityTypeOfGrossIncome">
														Type of gross income in relation to the relevant activity:
													</label>
												</div>

												<div class="col-md-4">
													<input type="text" id="activityTypeOfGrossIncome"
														class="form-control" maxlength="255"
														name="activityTypeOfGrossIncome"
														value="{{data.gross_income_type}}" />

												</div>
											</div>


											<!-- SHOW QUESTIONS 4A.C, 4A.D, DIRECTION, EMPLOYEES, PREMISES FOR RELEVANT ACTIVITIES !== HOLDING-->
											{{#ifCond activityType '!==' 'HLD' }}
											<!-- 4A.c -->
											<div class="row mb-2">
												<div class="col-md-8">
													<label for="activityAmountAssets">
														Amount and type of assets and premises held in the course
														of carrying out the relevant activity:
													</label>
												</div>

												<div class="col-md-4 ">

													<input type="text" id="activityAssetsAmount"
														class="form-control  " data-step="0.01"
														data-firstclickvalueifempty="0" data-decimals="2" data-toggle="touchspin"
														data-min="-999999999999" data-max="100000000000" name="activityAssetsAmount"
														placeholder="0.0"
														value="{{data.activity_assets_amount}}" />

													<input type="text" id="activityAssetsType" class="form-control mt-2"
														name="activityAssetsType" value="{{data.activity_assets_type}}"
														placeholder="Type of assets and premises" />

												</div>
											</div>

											<!-- 4A.d -->
											<div class="row mb-2">
												<div class="col-md-8">
													<label for="activityNetBookValues">
														Net book values of tangible assets held in the course of
														carrying out the relevant activity:
													</label>
												</div>

												<div class="col-md-4">
													<input type="text" id="activityNetBookValues"
														class="form-control " data-a-sep="," data-decimals="2"
														data-step="0.01" data-firstclickvalueifempty="0" data-toggle="touchspin"
														data-min="0" data-max="100000000000" name="activityNetBookValues"
														value="{{data.activity_netbook_values}}" placeholder="0.0" />

												</div>
											</div>

											<!-- 4B DIRECTION AND MANAGEMENT -->
											<h4 class="header-title">Direction and Management</h4>
											<p class="sub-header">
											</p>
											{{> substance/business-sections/direction-and-management data=data
											entryId=entryId activityType=activityType}}

											<!-- 4C EXPENDITURE -->
											<h4 class="header-title "><br><br>Expenditure</h4>
											{{> substance/business-sections/expenditure data=data entryId=entryId
											selectedCurrency=selectedCurrency
											defaultCurrency=defaultCurrency
											}}

											<!-- 4D EMPLOYEES -->
											<h4 class="header-title"><br><br>Employees</h4>
											{{> substance/business-sections/employees data=data entryId=entryId }}


											{{else}}
											<!-- 4C EXPENDITURE -->
											<h4 class="header-title "><br><br>Expenditure</h4>
											{{> substance/business-sections/expenditure data=data entryId=entryId
											selectedCurrency=selectedCurrency
											defaultCurrency=defaultCurrency
											}}

											<!--HLD - HOLDING BUSINESS INPUTS -->
											<h4 class="header-title"><br><br>Pure Equity Holding Entity</h4>

											<!--4G.a-->
											<div class="row">
												<div class="col-md-8">
													<div class="form-group mb-3">
														<label class="mb-2" for="manage_equity_participations">Does
															the entity actively manage its equity participation?</label>
													</div>
												</div>
												<div class="col-md-4" align="right">
													<div class="radio form-check-inline">
														<input type="radio" id="manage_equity_participations_yes"
															name="manage_equity_participations" value="Yes" {{#if
															data.manage_equity_participations}}checked{{/if}}
															data-toggle="tooltip" data-placement="top"
															title="Managing equity participations means actively participating in decision-making regarding the ownership of the shares or equities. Where the entity only receives dividends and/or capital gains and takes no active role in generating such income, it does not manage its equity participations" />
														<label for="manage_equity_participations_yes">Yes</label>
													</div>
													<div class="radio form-check-inline">
														<input type="radio" id="manage_equity_participations_no"
															name="manage_equity_participations" value="No" {{#unless
															data.manage_equity_participations}}checked{{/unless}}
															data-toggle="tooltip" data-placement="top"
															title="Managing equity participations means actively participating in decision-making regarding the ownership of the shares or equities. Where the entity only receives dividends and/or capital gains and takes no active role in generating such income, it does not manage its equity participations" />
														<label for="manage_equity_participations_no">No</label>
													</div>
												</div>
											</div>

											<!-- EMPLOYEES -->
											<div id="manageEquityParticipationsYesRows" {{#ifCond
												data.manage_equity_participations "!==" true}}class="hide-element "
												{{/ifCond}}>

												<h4 class="header-title"><br><br>Employees</h4>
												{{> substance/business-sections/employees data=data entryId=entryId}}
												<br>
											</div>

											<div id="manageEquityParticipationsNoRows" {{#ifCond
												data.manage_equity_participations "!==" false}} class="hide-element"
												{{/ifCond}}>
												<!--4H.a-->
												<div class="row">
													<div class="col-md-8">
														<div class="form-group mb-3">
															<label class="mb-2"
																for="compliant_with_statutory_obligations">
																Does the entity comply with its statutory obligations
																under the BVI Business Companies Act, 2004 or the
																Limited
																Partnership Act, 2017 (whichever is relevant)?
															</label>
														</div>
													</div>
													<div class="col-md-4" align="right">
														<div class="radio form-check-inline">
															<input type="radio"
																id="compliant_with_statutory_obligations_yes"
																name="compliant_with_statutory_obligations" value="Yes"
																{{#if
																data.compliant_with_statutory_obligations}}checked{{/if}}
																data-toggle="tooltip" data-placement="top"
																title="Where the entity is a company, the statutory obligations under the BVI Business Companies Act, 2004. Where the entity is a limited partnership, the statutory obligations under the Limited Partnership Act, 2017" />
															<label
																for="compliant_with_statutory_obligations_yes">Yes</label>
														</div>
														<div class="radio form-check-inline">
															<input type="radio"
																id="compliant_with_statutory_obligations_no"
																name="compliant_with_statutory_obligations" value="No"
																{{#unless
																data.compliant_with_statutory_obligations}}checked{{/unless}}
																data-toggle="tooltip" data-placement="top"
																title="Where the entity is a company, the statutory obligations under the BVI Business Companies Act, 2004. Where the entity is a limited partnership, the statutory obligations under the Limited Partnership Act, 2017" />
															<label
																for="compliant_with_statutory_obligations_no">No</label>
														</div>
													</div>
												</div>
											</div>
											{{/ifCond}}

											<!-- PREMISES -->
											<h4 class="header-title"><br><br>Premises</h4>
											{{> substance/business-sections/premises data=data entryId=entryId}}


											<!-- BNK, INS, FM, FL, HQM, SHP, DS INPUTS -->
											{{#ifCond isNotActivityHldOrIp "===" true}}
											<!--4F CIGA -->
											<h4 class="header-title"><br><br>CIGA</h4>
											{{> substance/business-sections/ciga data=data entryId=entryId}}


											<!--4G OUTSOURCINGS -->
											<h4 class="header-title">Outsourcing</h4>
											{{> substance/business-sections/outsourcing data=data entryId=entryId
												selectedCurrency=selectedCurrency
												defaultCurrency=defaultCurrency
											}}
											{{/ifCond}}


											<!--IP - INTELLECTUAL PROPERTY INPUTS -->
											{{#ifCond activityType '===' 'IP'}}
											<!--4F HIGH RISK IP DETAILS-->
											<h4 class="header-title"><br>High Risk IP Legal entity</h4>
											{{> substance/business-sections/ip-details data=data entryId=entryId
												selectedCurrency=selectedCurrency
												defaultCurrency=defaultCurrency
											}}


											<!--4I OTHER CIGA (ONLY DISPLAY IF HIGH RISK IS NO)-->
											<div id="isHighRiskIntellectualPropertyNo" class="is-high-risk-ip-no hide-element">
												<h4 class="header-title">Other CIGA</h4>
												{{> substance/business-sections/other-ciga data=data entryId=entryId}}

											</div>

											<!-- CIGA (ONLY DISPLAY IF HIGH RISK IS NO AND OTHER CIGA IS NO)-->
											<div id="cigaDetailsRows" class="hide-element">
												<!--CIGA ONLY display IF 4I.a and 4i.b are NO -->
												<h4 class="header-title"><br>CIGA</h4>
												{{> substance/business-sections/ciga data=data entryId=entryId}}
											</div>

											<!--4G OUTSOURCING (ONLY DISPLAY IF IS HIGH RISK OR OTHER CIGA IS NO).-->
											<div id="OutsourcingRows" >
												<h4 class="header-title">Outsourcing</h4>
												{{> substance/business-sections/outsourcing data=data entryId=entryId
													selectedCurrency=selectedCurrency
													defaultCurrency=defaultCurrency
												}}
											</div>

											<!-- EQUIPMENT  (ONLY DISPLAY IF OTHER CIGA IS NO)-->
											<div id="EquipmentRows" class="hide-element">
												<h4 class="header-title">Equipment</h4>

												<div class="row mb-2">
													<div class="col-md-12">
														<label for="equipmentNatureDescription">
															Please provide a description of the nature of any equipment
															located within the Virgin Islands used in connection with
															the relevant activity.
														</label>
													</div>

													<div class="col-md-12">
														<textarea type="text" id="equipmentNatureDescription"
															class="form-control" name="equipmentNatureDescription"
															rows="2"
															maxlength="255">{{data.equipment_nature_description}}</textarea>

													</div>
												</div>
											</div>


											{{/ifCond}}

											<br>
											<div class="row">
												<div class="col-md-12">
													<div class="progress">
														<div class="progress-bar" role="progressbar" id="progressBar" aria-valuenow="5" aria-valuemin="0"
															aria-valuemax="7">5 of 7
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-md-8">
									<div class="form-group mb-2">
										<input type="submit" name="submit" value="Previous page"
											class="btn btn-secondary waves-effect waves-light width-xl" />
									</div>
								</div>
								<div class="col-md-4">
									<div class="form-group mb-2" align="right">
										<input type="submit" name="submit" value="Save & next page"
											class="btn btn-primary waves-effect waves-light width-xl" />
									</div>
								</div>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>
	{{> managerModal entryId=entryId title='Person'}}
	{{> premisesModal entryId=entryId }}
	{{> uploadModal }}

	<!-- MODAL FOR BOARD MEETINGS -->
	{{>substance/modals/board-meeting-modal}}

	<!-- MODAL FOR EMPLOYEES -->
	{{>substance/modals/employee-modal}}

	<!-- MODAL FOR OUTSOURCING PROVIDERS -->
	{{>substance/modals/outsourcing-provider-modal}}
</main>

<!-- Plugins js -->
<script src="/javascripts/libs/jquery-mask-plugin/jquery.mask.min.js"></script>
<script src="/javascripts/libs/autonumeric/autoNumeric-min.js"></script>

<!-- Init js-->
<script src="/javascripts/form-masks.init.js"></script>

<script src="/templates/managers.precompiled.js"></script>
<script src="/templates/premises.precompiled.js"></script>
<script src="/templates/uploadedfiles.precompiled.js"></script>

<script type="text/javascript" src="/views-js/entry/v5/Bussiness.js"></script>