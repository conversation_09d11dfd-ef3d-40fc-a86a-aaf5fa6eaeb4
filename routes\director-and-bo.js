const express = require('express');
const router = express.Router({mergeParams: true});
const sessionUtils = require('../utils/sessionUtils');

// Require controller modules.
const directorAndBoController = require('../controllers/directorAndBoController');


router.get('/', ensureAuthenticated, directorAndBoController.listDirectorAndBoCompanies);
router.get('/:code/:type', ensureAuthenticated, directorAndBoController.getDirAndBoEntries);
router.post('/:code/:type/request-assistance', ensureAuthenticated, directorAndBoController.requestToAssistance); // jquery / ajax
router.post('/:code/:type/:id/request-update', ensureAuthenticated, directorAndBoController.requestToUpdate); // jquery / ajax
router.post('/:code/:type/:id/confirm', ensureAuthenticated, directorAndBoController.createDirectorConfirmationLog); // jquery / ajax

module.exports = router;

function ensureAuthenticated(req, res, next) {
  if ((req.user && req.session.id === req.user.sessionId) && req.session.auth2fa) {
    next();
  } else if ((req.user && req.session.id === req.user.sessionId) && !req.session.auth2fa) {
    if (req.user.secret_2fa) {
      res.redirect('/users/2fa-code');
    } else {
      res.redirect('/users/2fa-setup');
    }
  } else {
    req.logout(function (err) {
      if (err) { return next(err) }
      
      req.session.destroy(function () {
        // cannot access session here
        sessionUtils.onSessionDestroyed(req, res);
      });
    });
  }
}
