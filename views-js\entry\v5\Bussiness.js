$(document).ready(function () {

	if (document.URL.endsWith('intellectual-property-business')) {
		const isHighRisk = showHighRiskIntellectualPropertyRows();

		if (isHighRisk === 'Yes') {
			showProvidedPresumptionEvidencesRows();
			showHideOutsourcing();
		}

	} else if (document.URL.endsWith('holding-business')) {
		showManageEquityParticipationsRows();
	} else {
		showHideOutsourcing()
	}

	refreshManagers();
	refreshPremises();
	refreshUploadedFiles();

	$('#AddPremises').on('hide.bs.modal', function (event) {
		refreshPremises();
	});
	$('#AddPerson').on('hide.bs.modal', function (event) {
		refreshManagers();
	});

});

$('#manage_equity_participations_no').click(function () {
	showManageEquityParticipationsRows()
})

$('#manage_equity_participations_yes').click(function () {
	showManageEquityParticipationsRows()
})

function refreshUploadedFiles() {
	const entryId = window.location.pathname.split('/')[3]

	$.get('/substance/entry/' + entryId+'/uploaded-files', {}, function (data) {

		var template = Handlebars.templates.uploadedfiles;

		var equipment_files = [];
		var outsourcing_files = [];
		var highrisk_ip_files = [];
		var support_files = [];
		var tangible_assets_explanation_files = [];
		var intangible_assets_decisions_files = [];
		var intangible_assets_nature_files = [];
		var intangible_assets_trading_nature_files = [];
		var other_ciga_business_files = [];
		var other_ciga_decisions_files = [];
		var other_ciga_evidence_files = [];
		var other_ciga_files = [];
		switch (urlPart) {
			
			case "banking-business":
				equipment_files = data.evidence_equipment_banking_business;
				outsourcing_files = data.evidence_outsourcing_banking_business;
				support_files = data.support_documents_banking_business;
				break;
			case "insurance-business":
				equipment_files = data.evidence_equipment_insurance_business;
				outsourcing_files = data.evidence_outsourcing_insurance_business;
				support_files = data.support_documents_insurance_business;
				break;
			case "fund-management-business":
				equipment_files = data.evidence_equipment_fund_management_business;
				outsourcing_files = data.evidence_outsourcing_fund_management_business;
				support_files = data.support_documents_fund_management_business;
				break;
			case "finance-leasing-business":
				equipment_files = data.evidence_equipment_finance_leasing_business;
				outsourcing_files = data.evidence_outsourcing_finance_leasing_business;
				support_files = data.support_documents_finance_leasing_business;
				break;
			case "headquarters-business":
				equipment_files = data.evidence_equipment_headquarters_business;
				outsourcing_files = data.evidence_outsourcing_headquarters_business;
				support_files = data.support_documents_headquarters_business;
				break;
			case "shipping-business":
				equipment_files = data.evidence_equipment_shipping_business;
				outsourcing_files = data.evidence_outsourcing_shipping_business;
				support_files = data.support_documents_shipping_business;
				break;
			case "intellectual-property-business":
				equipment_files = data.evidence_equipment_intellectual_property_business;
				outsourcing_files = data.evidence_outsourcing_intellectual_property_business;
				highrisk_ip_files = data.evidence_high_risk_intellectual_property_business;
				support_files = data.support_documents_intellectual_property_business;
				tangible_assets_explanation_files = data.evidence_tangible_assets_explanation;
				intangible_assets_decisions_files = data.evidence_intangible_assets_decisions;
				intangible_assets_nature_files = data.evidence_intangible_assets_nature;
				intangible_assets_trading_nature_files = data.evidence_intangible_assets_trading_nature;
				other_ciga_business_files = data.evidence_other_ciga_business;
				other_ciga_decisions_files = data.evidence_other_ciga_decisions;
				other_ciga_evidence_files = data.evidence_other_ciga_evidence;
				other_ciga_files = data.evidence_other_ciga_files;
				break;
			case "holding-business":
				equipment_files = data.evidence_equipment_holding_business;
				outsourcing_files = data.evidence_outsourcing_holding_business;
				support_files = data.support_documents_holding_business;
				break;
			case "service-centre-business":
				equipment_files = data.evidence_equipment_service_centre_business;
				outsourcing_files = data.evidence_outsourcing_service_centre_business;
				support_files = data.support_documents_service_centre_business;
				break;

			default:
				equipment_files = [];
				outsourcing_files = [];
				highrisk_ip_files = [];
				support_files = [];
				tangible_assets_explanation_files = [];
				intangible_assets_decisions_files = [];
				intangible_assets_nature_files = [];
				intangible_assets_trading_nature_files = [];
				other_ciga_business_files = [];
				other_ciga_decisions_files = [];
				other_ciga_evidence_files = [];
				other_ciga_files = [];
				break;
		}

		var equipment_data = {
			title: "",
			files: equipment_files,
			field: "EvidenceEquipment-" + urlPart
		}
		var equipment_html = template(equipment_data);

		$("#equipment_uploaded_files").html(equipment_html);

		var outsourcing_data = {
			title: "",
			files: outsourcing_files,
			field: "EvidenceOutsourcing-" + urlPart
		}
		var outsourcing_html = template(outsourcing_data);

		$("#outsourcing_uploaded_files").html(outsourcing_html);

		var highrisk_ip_data = {
			title: "",
			files: highrisk_ip_files,
			field: "EvidenceHighRiskIp-" + urlPart
		}
		var highrisk_ip_html = template(highrisk_ip_data);

		$("#high_risk_ip_uploaded_files").html(highrisk_ip_html);

		var support_data = {
			title: "",
			files: support_files,
			field: "SupportDocuments-" + urlPart
		}
		var support_html = template(support_data);

		$("#support_documents_uploaded_files").html(support_html);

		var tangible_assets_explanation_data = {
			title: "",
			files: tangible_assets_explanation_files,
			field: "EvidenceTangibleAssetsExplanation-" + urlPart
		}
		var tangible_assets_explanation_html = template(tangible_assets_explanation_data);

		$("#tangible_assets_explanation_uploaded_files").html(tangible_assets_explanation_html);

		var intangible_assets_decisions_data = {
			title: "",
			files: intangible_assets_decisions_files,
			field: "EvidenceIntangibleAssetsDecisions-" + urlPart
		}
		var intangible_assets_decisions_html = template(intangible_assets_decisions_data);

		$("#intangible_assets_decisions_uploaded_files").html(intangible_assets_decisions_html);

		var intangible_assets_nature_data = {
			title: "",
			files: intangible_assets_nature_files,
			field: "EvidenceIntangibleAssetsNature-" + urlPart
		}
		var intangible_assets_nature_html = template(intangible_assets_nature_data);

		$("#intangible_assets_nature_uploaded_files").html(intangible_assets_nature_html);

		var intangible_assets_trading_nature_data = {
			title: "",
			files: intangible_assets_trading_nature_files,
			field: "EvidenceIntangibleAssetsTradingNature-" + urlPart
		}
		var intangible_assets_trading_nature_html = template(intangible_assets_trading_nature_data);

		$("#intangible_assets_trading_nature_uploaded_files").html(intangible_assets_trading_nature_html);

		var other_ciga_business_data = {
			title: "",
			files: other_ciga_business_files,
			field: "EvidenceOtherCigaBusiness-" + urlPart
		}
		var other_ciga_business_html = template(other_ciga_business_data);

		$("#other_ciga_business_uploaded_files").html(other_ciga_business_html);

		var other_ciga_decisions_data = {
			title: "",
			files: other_ciga_decisions_files,
			field: "EvidenceOtherCigaDecisions-" + urlPart
		}
		var other_ciga_decisions_html = template(other_ciga_decisions_data);

		$("#other_ciga_decisions_uploaded_files").html(other_ciga_decisions_html);

		var other_ciga_evidence_data = {
			title: "",
			files: other_ciga_evidence_files,
			field: "EvidenceOtherCigaEvidence-" + urlPart
		}
		var other_ciga_evidence_html = template(other_ciga_evidence_data);
		$("#other_ciga_evidence_uploaded_files").html(other_ciga_evidence_html);


		var other_ciga_files_data = {
			title: "",
			files: other_ciga_files,
			field: "EvidenceOtherCigaFiles-" + urlPart
		}
		var other_ciga_files_html = template(other_ciga_files_data);
		$("#other_ciga_files_uploaded_files").html(other_ciga_files_html);
	});
}

function refreshManagers() {
	const entryId = window.location.pathname.split('/')[3]
	$("#iframeDirector").prop('src', 'about:blank');
	$.get('/substance/entry/' + entryId+'/managers', {}, function (data) {


		var template = Handlebars.templates.managers;
		//get part of url to define which managers should be shown
		var managers = [];
		switch (urlPart) {
			
			case "banking-business":
				managers = data.banking_business_managers;
				break;
			case "insurance-business":
				managers = data.insurance_business_managers;
				break;
			case "fund-management-business":
				managers = data.fund_management_business_managers;
				break;
			case "finance-leasing-business":
				managers = data.finance_leasing_business_managers;
				break;
			case "headquarters-business":
				managers = data.headquarters_business_managers;
				break;
			case "shipping-business":
				managers = data.shipping_business_managers;
				break;
			case "intellectual-property-business":
				managers = data.intellectual_property_business_managers;
				break;
			case "holding-business":
				managers = data.holding_business_managers;
				break;
			case "service-centre-business":
				managers = data.service_centre_business_managers;
				break;

			default:
				managers = [];
				break;
		}

		var d = {
			title: "Managers",
			version: data.entryVersion,
			data: {
				managers: managers
			}
		}
		var html = template(d);

		$("#tbl_managers").html(html);

		if (managers?.length > 0) {
			$("#directorsDetailsTable").show(200);
		} else {
			$("#directorsDetailsTable").hide(200);
		}

		$('.editmanager').click(function () {
			//oad manager data and prefille
			var id = $(this).data('id')
			editManager(id);
		});

		$('.deletemanager').click(async function () {
			//oad manager data and prefille
			var id = $(this).data('id')
			await deleteManager(id);
		});
	});

}

function refreshPremises() {
	const entryId = window.location.pathname.split('/')[3]
	$.get('/substance/entry/' + entryId+'/premises', {}, function (data) {
		var template = Handlebars.templates.premises;
		//get part of url to define which managers should be shown
		var premises = [];
		switch (urlPart) {
			case "banking-business":
				premises = data.banking_business_premises;
				break;
			case "insurance-business":
				premises = data.insurance_business_premises;
				break;
			case "fund-management-business":
				premises = data.fund_management_business_premises;
				break;
			case "finance-leasing-business":
				premises = data.finance_leasing_business_premises;
				break;
			case "headquarters-business":
				premises = data.headquarters_business_premises;
				break;
			case "shipping-business":
				premises = data.shipping_business_premises;
				break;
			case "intellectual-property-business":
				premises = data.intellectual_property_business_premises;
				break;
			case "holding-business":
				premises = data.holding_business_premises;
				break;
			case "service-centre-business":
				premises = data.service_centre_business_premises;
				break;

			default:
				premises = [];
				break;
		}

		var d = {
			title: "Premises",
			version: data.entryVersion,
			data: {
				premises: premises,
			}
		}
		var html = template(d);

		$("#tbl_premises").html(html);
		$('.editpremises').click(function () {
			//oad manager data and prefille
			var id = $(this).data('id')
			editPremises(id);
		});

		$('.deletepremises').click(async function () {
			//oad manager data and prefille
			var id = $(this).data('id')
			await deletePremises(id);
		});
	});

}

function showManageEquityParticipationsRows() {
	const val = $('input[name=manage_equity_participations]:checked').val()
	if (val === 'Yes') {
		$("#manageEquityParticipationsNoRows").hide();
		$("#manageEquityParticipationsYesRows").show(200);
	} else if (val === 'No') {
		$("#manageEquityParticipationsYesRows").hide();
		$("#manageEquityParticipationsNoRows").show(200);
	} else {
		$("#manageEquityParticipationsYesRows").hide();
		$("#manageEquityParticipationsNoRows").hide();
	}

}

function showHighRiskIntellectualPropertyRows() {
	const val = $('input[name=isHighRiskIntellectualProperty]:checked').val()
	if (val === 'Yes') {
		$(".is-high-risk-ip-yes").show(200);
		$("#OutsourcingRows").show(200);
		$(".is-high-risk-ip-no").hide();
		$("#cigaDetailsRows").hide();
		$("#EquipmentRows").show();
	} else if (val === 'No') {
		$(".is-high-risk-ip-yes").hide();
		$(".is-high-risk-ip-no").show(200);
		showOtherCigaRows();
	} else {
		$(".is-high-risk-ip-yes").hide();
		$(".is-high-risk-ip-no").hide();
		$("#OutsourcingRows").hide();
		$("#EquipmentRows").hide();
	}
	return val;
}

function showProvidedPresumptionEvidencesRows() {
	const val = $('input[name=providedPresumptionEvidences]:checked').val()
	if (val === 'Yes') {
		$("#providedPresumptionEvidencesRows").show(200);
	} else {
		$("#providedPresumptionEvidencesRows").hide();
	}
}

function showHideOutsourcing() {
	const coreIncomeGeneratingOutsourcedYes = document.getElementById("CoreIncomeGeneratingOutsourcedYes");

	if (coreIncomeGeneratingOutsourcedYes.checked)
		$("#Outsourcing").show(200)
	else
		$("#Outsourcing").hide()


}


function showOtherCigaRows() {
	const val = $('input[name=otherCigaLegalEntityConductYesNo]:checked').val()
	if (val === 'Yes') {
		$("#cigaDetailsRows").hide();
		$("#otherCigaRows").show(200);
		$("#OutsourcingRows").show(200);
		$("#EquipmentRows").show(200);
		showOtherCigaEvidencesRows();
	} else if (val === 'No') {
		$("#otherCigaRows").hide();
		$("#cigaDetailsRows").show(200);
		$("#OutsourcingRows").show(200);
		$("#EquipmentRows").show(200);
	} else {
		$("#otherCigaRows").hide();
		$("#OutsourcingRows").hide();
		$("#cigaDetailsRows").hide();
		$("#EquipmentRows").hide();
	}
	return val;
}

function showOtherCigaEvidencesRows() {
	const val = $('input[name=otherCigaProvideEvidencesYesNo]:checked').val()
	if (val === 'Yes') {
		$("#otherCigaProvideEvidencesRows").show(200);
	} else if (val === 'No') {
		$("#otherCigaProvideEvidencesRows").hide();
	} else {
		$("#otherCigaProvideEvidencesRows").hide();
	}
}

$(document).on('click', '#otherCigaLegalEntityConductYes', function () {
	showOtherCigaRows()
})

$(document).on('click', '#otherCigaLegalEntityConducNo', function () {
	showOtherCigaRows()
})

$(document).on('click', '#otherCigaProvideEvidencesYes', function () {
	showOtherCigaEvidencesRows()
})

$(document).on('click', '#otherCigaProvideEvidencesNo', function () {
	showOtherCigaEvidencesRows()
})

$(document).on('click', '#CoreIncomeGeneratingOutsourcedYes', function () {
	showHideOutsourcing()
})

$(document).on('click', '#CoreIncomeGeneratingOutsourcedNo', function () {
	showHideOutsourcing()
})

$(document).on('click', '#isHighRiskIntellectualPropertyYes', function () {
	showHighRiskIntellectualPropertyRows()
})

$(document).on('click', '#isHighRiskIntellectualPropertyNo', function () {
	showHighRiskIntellectualPropertyRows()
})

$(document).on('click', '#providedPresumptionEvidencesYes', function () {
	showProvidedPresumptionEvidencesRows()
})

$(document).on('click', '#providedPresumptionEvidencesNo', function () {
	showProvidedPresumptionEvidencesRows()
})

$("#activityCIGACore").on("change", function () {
	const val = $("#activityCIGACore").val();

	if (val === '0.2') {
		$("#activityCIGACoreOtherValue").show(200);
	} else {
		$("#activityCIGACoreOtherValue").hide();
	}
})


$("#AmountOfBoardMeetingsInBVI").on('change', function (e) {
	const val = $(this).val();

	if (val > 0) {
		$("#AmountOfBoardMeetingsOutsideBVIRows").show(200);
	} else {
		$("#AmountOfBoardMeetingsOutsideBVIRows").hide();
	}
});


$("#TotalSuitableFTEInBVI").on('change', function (e) {
	const val = $(this).val();

	if (val > 0) {
		$("#employeesDetailsTableRows").show(200);
	} else {
		$("#employeesDetailsTableRows").hide();
	}
});