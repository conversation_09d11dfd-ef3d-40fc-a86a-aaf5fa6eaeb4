const express = require('express');
const router = express.Router();

// Require controller modules.
const masterClientController = require('../controllers/masterClientController');
const paymentController = require('../controllers/paymentController');
const masterClientFileController = require('../controllers/masterClientFileController');


router.get('/', masterClientController.ensureAuthenticated, masterClientController.masterClientList);
router.get('/:masterclientcode', masterClientController.ensureAuthenticated, masterClientController.validateMasterClient, masterClientController.masterClientDashboard);

router.get('/:masterclientcode/payments', paymentController.ensureAuthenticated, paymentController.getPendingPayments);
router.get('/:masterclientcode/payments-history', paymentController.ensureAuthenticated, paymentController.getPaymentsHistory);
router.post('/:masterclientcode/payments', paymentController.ensureAuthenticated, paymentController.startBatchPayment);
//router.get('/:masterclientcode/payments/process-status', paymentController.ensureAuthenticated, paymentController.processStatus);
router.get('/:masterclientcode/payments/process-status/:usssid', paymentController.ensureAuthenticated_ProcessStatus, paymentController.processStatus);

router.get('/:masterclientcode/files', masterClientFileController.ensureAuthenticated, masterClientFileController.getFiles);
router.get('/:masterclientcode/files/download', masterClientFileController.ensureAuthenticated, masterClientFileController.downloadFile);

module.exports = router;

