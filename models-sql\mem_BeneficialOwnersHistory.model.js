const moment = require('moment');

module.exports = (sequelize, Sequelize) => {
  const BeneficialOwnersHistory = sequelize.define(
    'mem_BeneficialOwnersHistory',
    {
      Id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        primaryKey: true,
        autoIncrement: true
      },
      PreviousUniqueRelationID: Sequelize.STRING(21),
      UniqueRelationID: Sequelize.STRING(32),
      BOMemberType: {
        type: Sequelize.STRING(23),
        allowNull: false
      },
      EntityCode: {
        type: Sequelize.STRING(10),
        allowNull: false
      },
      EntityName: Sequelize.STRING(356),
      EntityUniqueNr: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      EntityLegacyID: Sequelize.STRING(30),
      ReportingJurisdictionCode: Sequelize.STRING(10),
      ReportingJurisdiction: Sequelize.STRING(50),
      BOCode: {
        type: Sequelize.STRING(10),
        allowNull: false
      },
      BOName: Sequelize.STRING(356),
      BOFormerName: Sequelize.STRING(356),
      BOAliases: Sequelize.STRING(100),
      BOGender: Sequelize.STRING(50),
      BOUniqueNr: {
        type: Sequelize.INTEGER,
        allowNull: false
      },
      BODateCommenced: {
        type: Sequelize.DATE,
        get() {
          const d = this.getDataValue('BODateCommenced');
          return d ? moment.utc(d).format('YYYY-MM-DD') : null;
        }
      },
      BODateCeased: {
        type: Sequelize.DATE,
        get() {
          const d = this.getDataValue('BODateCeased');
          return d ? moment.utc(d).format('YYYY-MM-DD') : null;
        }
      },
      BORegistrableTypeCode: Sequelize.STRING(10),
      BORegistrableType: Sequelize.STRING(255),
      BOExemptedOwnerTypeCode: Sequelize.STRING(10),
      BOExemptedOwnerType: Sequelize.STRING(255),
      BOReportingStatusCode: Sequelize.STRING(10),
      BOReportingStatus: Sequelize.STRING(255),
      BOSTXJurisdictionCode: Sequelize.STRING(10),
      BOSTXJurisdiction: Sequelize.STRING(50),
      BOSTXRecognisedCode: Sequelize.STRING(20),
      BOSTXRecognisedName: Sequelize.STRING(255),
      BOSTXTickerID: Sequelize.STRING(100),
      BOSTXRegulator: Sequelize.TEXT,
      BOSTXListingDate: {
        type: Sequelize.DATE,
        get() {
          const d = this.getDataValue('BOSTXListingDate');
          return d ? moment.utc(d).format('YYYY-MM-DD') : null;
        }
      },
      BOIncorpCountryCode: Sequelize.STRING(10),
      BOIncorpCountry: Sequelize.STRING(50),
      BOIncorpNr: Sequelize.STRING(100),
      BOIncorpDate: {
        type: Sequelize.DATE,
        get() {
          const d = this.getDataValue('BOIncorpDate');
          return d ? moment.utc(d).format('YYYY-MM-DD') : null;
        }
      },
      BOFundLicenceTypeCode: Sequelize.STRING(4),
      BOFundLicenceType: Sequelize.STRING(50),
      BOFundLicenceNr: Sequelize.STRING(35),
      BOFundLicenceGrantDate: {
        type: Sequelize.DATE,
        get() {
          const d = this.getDataValue('BOFundLicenceGrantDate');
          return d ? moment.utc(d).format('YYYY-MM-DD') : null;
        }
      },
      BOLicensedPersonCode: Sequelize.STRING(10),
      BOLicensedPerson: Sequelize.STRING(356),
      BOLicensedPersonUniqueNr: Sequelize.INTEGER,
      BOLicensedPersonTypeCode: Sequelize.STRING(20),
      BOLicensedPersonType: Sequelize.STRING(255),
      BOLicensedPersonEmail: Sequelize.STRING(250),
      BOLicensedPersonAddress: Sequelize.TEXT,
      BOForeignFundIntlStd: Sequelize.STRING(100),
      BOForeignFundExemptionCode: Sequelize.STRING(20),
      BOForeignFundExemption: Sequelize.STRING(255),
      BOForeignFundDislosureRules: Sequelize.TEXT,
      BOForeignFundAddress: Sequelize.TEXT,
      BONatureOfInterestCode: Sequelize.STRING(10),
      BONatureOfInterest: Sequelize.STRING(255),
      BOInterestByControl1Code: Sequelize.STRING(10),
      BOInterestByControl1: Sequelize.STRING(255),
      BOInterestByControl2Code: Sequelize.STRING(10),
      BOInterestByControl2: Sequelize.STRING(255),
      BOInterestByControl3Code: Sequelize.STRING(10),
      BOInterestByControl3: Sequelize.STRING(255),
      BOInterestByControlOther: Sequelize.TEXT,
      BOBirthCountryCode: Sequelize.STRING(10),
      BOBirthCountry: Sequelize.STRING(50),
      BODateOfBirth: {
        type: Sequelize.DATE,
        get() {
          const d = this.getDataValue('BODateOfBirth');
          return d ? moment.utc(d).format('YYYY-MM-DD') : null;
        }
      },
      BONationality: Sequelize.STRING(255),
      BOOccupation: Sequelize.STRING(255),
      BOResidentialAddress: Sequelize.TEXT,
      BOJointOwnerCode: Sequelize.STRING(10),
      BOJointOwnerName: Sequelize.STRING(356),
      BOJointOwnerUniqueNr: Sequelize.INTEGER,
      BOInterestPercent: Sequelize.FLOAT,
      BOVotePercent: Sequelize.FLOAT,
      BORegistrableCapacityCode: Sequelize.STRING(10),
      BORegistrableCapacity: Sequelize.STRING(255),

      // Extra history-specific fields
      UpdateRequestDate: {
        type: Sequelize.DATE,
        get() {
          const d = this.getDataValue('UpdateRequestDate');
          return d ? moment.utc(d).format('YYYY-MM-DD') : null;
        }
      },
      ConfirmedDate: {
        type: Sequelize.DATE,
        get() {
          const d = this.getDataValue('ConfirmedDate');
          return d ? moment.utc(d).format('YYYY-MM-DD') : null;
        }
      },
      Status: Sequelize.STRING(50),
      UserEmail: Sequelize.STRING(255),
      TypeOfUpdateRequest: Sequelize.STRING(255),
      UpdateRequestComments: Sequelize.STRING(2500),
      CreatedAt: {
        type: Sequelize.DATE,
        get() {
          const d = this.getDataValue('CreatedAt');
          return d ? moment.utc(d).format('YYYY-MM-DD') : null;
        }
      },
      UpdatedAt: {
        type: Sequelize.DATE,
        get() {
          const d = this.getDataValue('UpdatedAt');
          return d ? moment.utc(d).format('YYYY-MM-DD') : null;
        }
      }
    },
    {
      sequelize,
      tableName: 'mem_BeneficialOwnersHistory',
      schema: 'dbo',
      timestamps: false
    }
  );

  return BeneficialOwnersHistory;
};
