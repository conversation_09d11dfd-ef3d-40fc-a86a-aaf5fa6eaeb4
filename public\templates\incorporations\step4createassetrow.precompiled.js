(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['step4createassetrow'] = template({"1":function(container,depth0,helpers,partials,data) {
    var stack1, alias1=container.lambda, alias2=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "            Registration Number: "
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"registrationNumber") : stack1), depth0))
    + "<br>\r\n            Jurisdiction of Registration:  "
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"jurisdictionOfRegistration") : stack1), depth0))
    + "\r\n";
},"3":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "            Details: "
    + container.escapeExpression(container.lambda(((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"details") : stack1), depth0))
    + "\r\n";
},"5":function(container,depth0,helpers,partials,data) {
    var stack1, alias1=container.lambda, alias2=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "            Name of institution: "
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"nameOfInstitution") : stack1), depth0))
    + " <br>\r\n            Address of institution:  "
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"addressOfInstitution") : stack1), depth0))
    + "\r\n";
},"7":function(container,depth0,helpers,partials,data) {
    var stack1, alias1=container.lambda, alias2=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "            Name of bank: "
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"nameOfBank") : stack1), depth0))
    + " <br>\r\n            Address of bank: "
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"addressOfBank") : stack1), depth0))
    + "\r\n";
},"9":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "            Name of trust: "
    + container.escapeExpression(container.lambda(((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"nameOfTrust") : stack1), depth0))
    + "\r\n";
},"11":function(container,depth0,helpers,partials,data) {
    var stack1, alias1=container.lambda, alias2=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "            Type: "
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"realEstateType") : stack1), depth0))
    + "<br>\r\n            Location: "
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"location") : stack1), depth0))
    + "\r\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data) {
    var stack1, alias1=container.lambda, alias2=container.escapeExpression, alias3=depth0 != null ? depth0 : (container.nullContext || {}), alias4=container.hooks.helperMissing, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "<tr id=\"asset-table-row-"
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"_id") : stack1), depth0))
    + "\" class=\"asset-row\">\r\n    <td>"
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"type") : stack1), depth0))
    + "</td>\r\n\r\n    <td>\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||alias4).call(alias3,((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"type") : stack1),"Aircraft",{"name":"ifEquals","hash":{},"fn":container.program(1, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":5,"column":8},"end":{"line":8,"column":21}}})) != null ? stack1 : "")
    + "\r\n\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||alias4).call(alias3,((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"type") : stack1),"Vessel (ship/yacht)",{"name":"ifEquals","hash":{},"fn":container.program(1, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":11,"column":8},"end":{"line":14,"column":21}}})) != null ? stack1 : "")
    + "\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||alias4).call(alias3,((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"type") : stack1),"Intellectual property rights",{"name":"ifEquals","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":16,"column":8},"end":{"line":18,"column":21}}})) != null ? stack1 : "")
    + "\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||alias4).call(alias3,((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"type") : stack1),"Shares/equity participations",{"name":"ifEquals","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":20,"column":8},"end":{"line":22,"column":21}}})) != null ? stack1 : "")
    + "\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||alias4).call(alias3,((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"type") : stack1),"Debt",{"name":"ifEquals","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":24,"column":8},"end":{"line":26,"column":21}}})) != null ? stack1 : "")
    + "\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||alias4).call(alias3,((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"type") : stack1),"Other",{"name":"ifEquals","hash":{},"fn":container.program(3, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":28,"column":8},"end":{"line":30,"column":21}}})) != null ? stack1 : "")
    + "\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||alias4).call(alias3,((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"type") : stack1),"Investment portfolio",{"name":"ifEquals","hash":{},"fn":container.program(5, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":32,"column":8},"end":{"line":35,"column":21}}})) != null ? stack1 : "")
    + "\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||alias4).call(alias3,((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"type") : stack1),"Bank account",{"name":"ifEquals","hash":{},"fn":container.program(7, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":37,"column":8},"end":{"line":40,"column":21}}})) != null ? stack1 : "")
    + "\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||alias4).call(alias3,((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"type") : stack1),"Trust assets",{"name":"ifEquals","hash":{},"fn":container.program(9, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":42,"column":8},"end":{"line":44,"column":21}}})) != null ? stack1 : "")
    + "\r\n"
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||alias4).call(alias3,((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"type") : stack1),"Real estate",{"name":"ifEquals","hash":{},"fn":container.program(11, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":46,"column":8},"end":{"line":49,"column":21}}})) != null ? stack1 : "")
    + "    </td>\r\n    <td class=\"text-right\">\r\n        <button type=\"button\" class=\"btn btn-outline-secondary openEditAssetModal\"\r\n                data-id=\""
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"_id") : stack1), depth0))
    + "\">\r\n            <i class=\"fa fa-pencil mr-2\"></i>Edit\r\n        </button>\r\n    </td>\r\n    <td class=\"text-left\">\r\n        <button type=\"button\" class=\"delete btn btn-danger deleteAsset\" data-id=\""
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"asset") : depth0)) != null ? lookupProperty(stack1,"_id") : stack1), depth0))
    + "\">\r\n            <i class=\"fa fa-trash mr-2\"></i>Delete\r\n        </button>\r\n    </td>\r\n</tr>\r\n";
},"useData":true});
})();