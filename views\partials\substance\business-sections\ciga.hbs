<div id="cigaContent">
    <!-- 4F.a -->
    <div class="row">
        <div class="col-md-8">
            <div class="form-group mb-3">
                <label class="mb-2" for="activityCIGACore">
                    Select Core Income Generating Activities conducted carried out in the
                    Virgin Islands for the relevant activity:
    
                </label>
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group mb-3">
                <select class="form-control w-100" id="activityCIGACore" name="activityCIGACore"
                    data-toggle="select2" data-value="{{data.activity_ciga_core}}">
                    <option value="">Select an option...</option>
                    {{#each cigaCodes}}
                    <option value="{{code}}" {{#ifCond code '===' ../data.activity_ciga_core}} selected {{/ifCond}}>
                        {{description}}
                    </option>
                    {{/each}}
                </select>
            </div>
        </div>
    </div>
    
    <!-- 4F.b -->
    <div id="activityCIGACoreOtherValue" class="{{#ifCond data.activity_ciga_core "!==" '0.2'}} hide-element {{/ifCond}}">

        <div class="row">
            <div class="col-md-8">
                <div class="form-group mb-3">
                    <label class="mb-2" for="activityCIGACoreOther">
                        Other option:
        
                    </label>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <input id="activityCIGACoreOther" name="activityCIGACoreOther" class="form-control" type="text"
                        maxlength="100" value="{{data.activity_ciga_core_other}}">
                </div>
            </div>
        </div>
    </div>

</div>