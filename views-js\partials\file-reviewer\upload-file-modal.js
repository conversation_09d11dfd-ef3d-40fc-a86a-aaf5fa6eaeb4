$("#uploadedFiles").html('');
let mccId = '';
Dropzone.autoDiscover = false;
button;
$(async function () {
    let field = '';
    let id = '';
    let fieldName;

    const csrfToken = $("input[name='csrf-token']").val()
    const myDropZone = new Dropzone('#uploadModalForm', {
        url: '/',
        acceptedFiles: 'application/pdf',
        autoProcessQueue: true,
        parallelUploads: 3,
        maxFiles: 3,
        maxFilesize: 5,
        paramName: function () {
            return 'fileUploaded';
        },
        headers: {
            'x-csrf-token': csrfToken
        },
        uploadMultiple: true,
        init: function () {
            this.on('processing', function () {
                this.options.url =  '/masterclients/'+ mccId+'/incorporate-company/' + id + '/upload-files';
            });
            this.on('success', function () {
                $('#standard-file-present-' + row).prop('checked', true);
                let $fileUpload = $("#" + button[0].id);
                $fileUpload.text('Modify');
                $fileUpload.css({
                    'background-color': '#0AC292',
                    'border-color': '#0AC292',
                });
                refreshUploadedFiles(id, field);
                
            });
            this.on('sending', function (file, xhr, formData) {
                if (!formData.has('fieldName')) {
                    formData.append('fieldName', field);
                }
            });

            this.on('errormultiple', function (files, response) {
            });

            this.on('maxfilesexceeded', function (file) {
            });

            this.on('resetFiles', function () {
                if (this.files.length !== 0) {
                    for (i = 0; i < this.files.length; i++) {
                        this.files[i].previewElement.remove();
                    }
                    this.files.length = 0;
                }
                $('#maxUpload').text(this.options.maxFiles);
            });
        },
    });

    $('#upload-modal').on('show.bs.modal', function (event) {
        button = $(event.relatedTarget); // Button that triggered the modal
        fieldName = button.data('field'); //name of the file
        mccId =  button.data('mcc');
        field = fieldName.replace(/[\s\’\'\/\(\)]/g, ''); //formatted name with no special chars
        id = button.data('incorporation-id'); // _id
        const fileGroup = button.data('file-group');
        $('#upload-modal-file-label').text(fileGroup);
        // GET FILES UPLOADED PREVIOUSLY
        refreshUploadedFiles(id, field);
        const modal = $(this);
        const objDZ = Dropzone.forElement('#uploadModalForm');
        objDZ.emit('resetFiles');
    });
});

async function deleteIncorporationFile(incorporationId, fieldName, fileId) {
    $.ajax({
        type: 'DELETE',
        url: '/masterclients/'+mccId+ '/incorporate-company/'+ incorporationId+'/files/',
        data: {
            fieldName: fieldName,
            fileId: fileId
        },
        success: function (res) {
            if (res.result) {
                refreshUploadedFiles(incorporationId, fieldName);
            }
        },
        dataType: 'json',
    });
    return false;
}

$(document).on('click', '.downloadIncorporationFile', function () {
    downloadIncorporationFile($(this).attr('data-id'),$(this).attr('data-name'),$(this).attr('data-field-id'))
})

$(document).on('click', '.deleteIncorporationFile', async function () {
    await deleteIncorporationFile($(this).attr('data-id'),$(this).attr('data-name'),$(this).attr('data-field-id'))
})

function downloadIncorporationFile(incorporationId,fieldName, fileId) {
    const url = '/masterclients/'+mccId+ '/incorporate-company/'+ incorporationId+'/files/' + fileId + '/download';
    window.open(url,"_blank");

    return false;
}

function refreshUploadedFiles(id, field) {
    $.ajax({
        type: 'GET',
        url: '/masterclients/'+mccId+ '/incorporate-company/'+ id+'/files',
        data: {
            fieldName: field,
        },
        timeout: 5000,
        success: function (data) {
            let template = Handlebars.templates.uploadincorporationfiles;
            let d = {
                id: id,
                files: data.files ? data.files : [],
                group: field,
            };
            let html = template(d);
            $('#uploadedFiles').html(html);
            if (data.files.length === 0) {
                $('#standard-file-present-' + row).prop('checked', false);
                let $fileUpload = $('#standard-file-' + row);
                $fileUpload.text('Upload');
                let buttonObject = $("#" + button[0].id);
                buttonObject.text('Upload');
                $fileUpload.css({
                    'background-color': '#0081b4',
                    'border-color': '#0081b4',
                });
            }
        },
        error: function (res) {
            Swal.fire('Error', 'There was an error getting files', 'error');
        },
    });

}


$("#upload-modal").on("hidden.bs.modal", function(){
    $("#uploadedFiles").html('');
});