exports.TYPE_OF_DIRECTOR = "Director";
exports.TYPE_OF_MEMBER = "Member";
exports.TYPE_OF_BO = "Beneficial Owner";

exports.DIRECTOR_REQUIRED_FIELDS = {
  "Individual": [
    {
      field: "DirOfficerType",
      name: "Director Type",
    },
    {
      field: "DirName",
      name: "Name",
    },
    {
      field: "DirFromDate",
      name: "Appointment Date",
    },
    {
      field: "ServiceAddress",
      name: "Service Address",
    },
    {
      field: "ResidentialOrRegisteredAddress",
      name: "Residential Address",
    },
    {
      field: "DateOfBirthOrIncorp",
      name: "Date of Birth",
    },
    {
      field: "PlaceOfBirthOrIncorp",
      name: "Place of Birth",
    },
    {
      field: "Nationality",
      name: "Nationality",
    },
  ],
  "Corporate": [
    {
      field: "DirOfficerType",
      name: "Director Type",
    },
    {
      field: "DirName",
      name: "Name",
    },
    {
      field: "MFIncropNr",
      name: "Corporate Number",
    },
    {
      field: "Dir<PERSON>romDate",
      name: "Appointment Date",
    },
    {
      field: "MF<PERSON>Add<PERSON>",
      name: "Address",
    },
    {
      field: "MFIncorpDate",
      name: "Incorporation Date",
    },
    {
      field: "MFIncorpCountry",
      name: "Incorporation Place",
    },
  ],
};

exports.MEMBER_REQUIRED_FIELDS = {
  // Individual Member
  "Individual": [
    {
      field: "MFDateOfBirth",
      name: "Date of Birth",
    },
    {
      field: "MFBirthCountry",
      name: "Place of Birth",
    },
    {
      field: "MFNationality",
      name: "Nationality",
    },
    {
      field: "MFRAAddress",
      name: "Address",
    },
    {
      field: "ShareIssueDate",
      name: "Date Entered",
    },
  ],
  // Corporate Member
  "Corporate": [
    {
      field: "MFIncropNr",
      name: "Corporate Number",
    },
    {
      field: "MFIncorpCountry",
      name: "Incorporation Place",
    },
    {
      field: "MFIncorpDate",
      name: "Incorporation Date",
    },
    {
      field: "MFROAddress",
      name: "Address",
    },
    {
      field: "ShareIssueDate",
      name: "Date Entered",
    },
  ],
  // Mutual Fund
  "MutualFund": [
    {
      field: "BusRegNr",
      name: "Licence Number",
    },
    {
      field: "BusRegType",
      name: "Licence Type",
    },
    {
      field: "BusRegStartDate",
      name: "Date Entered",
    },
  ],
  // Stock entity
  "Stock": [
    {
      field: "STXName",
      name: "Stock Exchange Name",
    },
    {
      field: "STXTicker",
      name: "Ticker Symbol",
    },
    {
      field: "STXJurisdiction",
      name: "Jurisdiction of Stock Exchange",
    },
    {
      field: "STXRegulator",
      name: "Name of Stock Exchange Regulator",
    },
    {
      field: "STXListingDate",
      name: "Date of Listing",
    },
  ],
  // Individual Nominator
  "IndividualNominator": [
    {
      field: "MemberName",
      name: "Name",
    },
    {
      field: "MemberDateStart",
      name: "Date Entered",
    },
    {
      field: "MFDateOfBirth",
      name: "Date of Birth",
    },
    {
      field: "MFBirthCountry",
      name: "Place of Birth",
    },
    {
      field: "MFNationality",
      name: "Nationality",
    },
    {
      field: "MFRAAddress",
      name: "Address",
    },
  ],
  // Corporate Nominator
  "CorporateNominator": [
    {
      field: "MemberName",
      name: "Name",
    },
    {
      field: "MemberDateStart",
      name: "Date Entered",
    },
    {
      field: "MFIncropNr",
      name: "Corporate Number",
    },
    {
      field: "MFIncorpCountry",
      name: "Incorporation Place",
    },
    {
      field: "MFIncorpDate",
      name: "Incorporation Date",
    },
    {
      field: "MFROAddress",
      name: "Address",
    },
  ],
};

exports.BENEFICIAL_OWNER_REQUIRED_FIELDS = {
  // Entity Exemptions (BOMemberType = "Exempt Reporting Entity")
  "Listed Company": [
    {
      field: "BOSTXRecognisedName",
      name: "Stock Exchange Name",
    },
    {
      field: "BOSTXTickerID",
      name: "Ticker Symbol",
    },
    {
      field: "BOSTXJurisdiction",
      name: "Jurisdiction of Stock Exchange",
    },
    {
      field: "BOSTXRegulator",
      name: "Name of Stock Exchange Regulator",
    },
    {
      field: "BOSTXListingDate",
      name: "Date of Listing",
    },
  ],
  "Subsidiaries": [
    {
      field: "BOJointOwnerName",
      name: "Company/Limited Partnership Name",
    },
    {
      field: "BOIncorpNr",
      name: "Company/Limited Partnership No.",
    },
    {
      field: "BOIncorpDate",
      name: "Date of Incorporation/Registration",
    },
  ],
  "Licensed Trustee": [
    {
      field: "BOJointOwnerName",
      name: "Name of Licensed Trustee",
    },
    {
      field: "BOFundLicenceNr",
      name: "License No.",
    },
    {
      field: "BOFundLicenceType",
      name: "License Type",
    },
    {
      field: "BOFundLicenceGrantDate",
      name: "License Grant Date",
    },
  ],
  "Foreign Fund": [
    {
      field: "BOName",
      name: "Entity Name",
    },
    {
      field: "BOIncorpNr",
      name: "Incorporation/Registration/Formation No.",
    },
    {
      field: "BOIncorpDate",
      name: "Date of Incorporation/Registration/Formation",
    },
    {
      field: "BOIncorpCountry",
      name: "Place of Incorporation/Registration/Formation",
    },
    {
      field: "BOForeignFundExemption",
      name: "Equivalent Exemption Criteria",
    },
    {
      field: "BOForeignFundDislosureRules",
      name: "Disclosure and Transparency Rules",
    },
    {
      field: "BOForeignFundIntlStd",
      name: "International Standards",
    },
    {
      field: "BOForeignFundAddress",
      name: "Address",
    },
  ],
  "Specified Fund": [
    {
      field: "BOFundLicenceNr",
      name: "License No.",
    },
    {
      field: "BOFundLicenceType",
      name: "License Type",
    },
    {
      field: "BOFundLicenceGrantDate",
      name: "License Grant Date",
    },
    {
      field: "BOLicensedPerson",
      name: "Name",
    },
    {
      field: "BOLicensedPersonType",
      name: "Type of Licensed Person",
    },
    {
      field: "BOLicensedPersonAddress",
      name: "Address",
    },
    {
      field: "BOLicensedPersonEmail",
      name: "E-Mail",
    },
  ],

  // Individual BOs (BOMemberType = "Beneficial Owner")
  "Individual BO": [
    {
      field: "BOName",
      name: "Name",
    },
    {
      field: "BODateOfBirth",
      name: "Date of Birth",
    },
    {
      field: "BOBirthCountry",
      name: "Place of Birth",
    },
    {
      field: "BONationality",
      name: "Nationality",
    },
    {
      field: "BOOccupation",
      name: "Occupation",
    },
    {
      field: "BOGender",
      name: "Gender",
    },
    {
      field: "BOResidentialAddress",
      name: "Address",
    },
    {
      field: "BODateCommenced",
      name: "Effective Date",
    },
    // BO Interest is validated separately in logic (VGNI10/11/12)
  ],

  // Joint BOs (BOMemberType = "Joint Owner")
  "Joint BO": [
    {
      field: "BOName",
      name: "Name",
    },
    {
      field: "BODateOfBirth",
      name: "Date of Birth",
    },
    {
      field: "BOBirthCountry",
      name: "Place of Birth",
    },
    {
      field: "BONationality",
      name: "Nationality",
    },
    {
      field: "BOOccupation",
      name: "Occupation",
    },
    {
      field: "BOGender",
      name: "Gender",
    },
    {
      field: "BOResidentialAddress",
      name: "Address",
    },
    {
      field: "BODateCommenced",
      name: "Effective Date",
    },
    {
      field: "BORegistrableCapacityCode",
      name: "Joint Owner Type",
    },
    // BO Interest is validated separately in logic (VGNI10/11/12)
  ],

  // Exempted Owners (BOMemberType = "Exempt Owner")
  "Exempted Owner Listed Company": [
    {
      field: "BOName",
      name: "Listed Company Name",
    },
    {
      field: "BOSTXRecognisedName",
      name: "Stock Exchange Name",
    },
    {
      field: "BOSTXTickerID",
      name: "Ticker Symbol",
    },
    {
      field: "BOSTXJurisdiction",
      name: "Jurisdiction of Stock Exchange",
    },
    {
      field: "BOSTXRegulator",
      name: "Name of Stock Exchange Regulator",
    },
    {
      field: "BOSTXListingDate",
      name: "Date of Listing",
    },
  ],
  "Exempted Owner Licensed Trustee": [
    {
      field: "BOName",
      name: "Name of Licensed Trustee",
    },
    {
      field: "BOFundLicenceNr",
      name: "License No.",
    },
    {
      field: "BOFundLicenceType",
      name: "License Type",
    },
    {
      field: "BOFundLicenceGrantDate",
      name: "License Grant Date",
    },
  ],
  "Exempted Owner Foreign Fund": [
    {
      field: "BOName",
      name: "Entity Name",
    },
    {
      field: "BOIncorpNr",
      name: "Incorporation/Registration/Formation No.",
    },
    {
      field: "BOIncorpDate",
      name: "Date of Incorporation/Registration/Formation",
    },
    {
      field: "BOIncorpCountry",
      name: "Place of Incorporation/Registration/Formation",
    },
    {
      field: "BOForeignFundExemption",
      name: "Equivalent Exemption Criteria",
    },
    {
      field: "BOForeignFundDislosureRules",
      name: "Disclosure and Transparency Rules",
    },
    {
      field: "BOForeignFundIntlStd",
      name: "International Standards",
    },
    {
      field: "BOForeignFundAddress",
      name: "Address",
    },
  ],
  "Exempted Owner Specified Fund": [
    {
      field: "BOName",
      name: "Name of Specified Fund",
    },
    {
      field: "BOFundLicenceNr",
      name: "License No.",
    },
    {
      field: "BOFundLicenceType",
      name: "License Type",
    },
    {
      field: "BOFundLicenceGrantDate",
      name: "License Grant Date",
    },
  ],
};

exports.DIRMEMBER_STATUS = {
  INITIAL: "INITIAL",
  PENDING: "PENDING UPDATE REQUEST",
  RECEIVED: "VP DATA RECEIVED",
  CONFIRMED: "CONFIRMED",
  REFRESHED: "REFRESHED",
};
