<div class="col-lg-12">
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="additionalServicesControl">Would you like to request additional
                            services?</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="row">
                        <div class="col-6">
                            <div class="radio form-check-inline">
                                <input required type="radio" id="additionalServicesYes" name="additionalServicesControl"
                                    value="Yes" {{#ifCond incorporation.requestAdditionalServices '!=' false}} checked
                                    {{/ifCond}}>
                                <label for="additionalServicesYes">Yes</label>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="radio form-check-inline">
                                <input required type="radio" id="additionalServicesNo" name="additionalServicesControl"
                                    value="No" {{#ifEquals incorporation.requestAdditionalServices false}} checked
                                    {{/ifEquals}}>
                                <label for="additionalServicesNo">No</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="additionalServicesRow" class="pl-3 {{#ifCond incorporation.requestAdditionalServices '==' false}} 
                hide-element {{/ifCond}}">
                <div class="row">
                    <div class="col-md-6 custom-control custom-checkbox">
                        <input required type="checkbox" class="custom-control-input" id="directorServices"
                            name="additionalServices" value="Director services" {{#ifContains "Director services"
                            incorporation.additionalServices}} checked {{/ifContains}}>
                        <label class="custom-control-label" for="directorServices">Director services</label>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 custom-control custom-checkbox">
                        <input required type="checkbox" class="custom-control-input" id="economicSubstanceServices"
                            name="additionalServices" value="Economic substance" {{#ifContains "Economic substance"
                            incorporation.additionalServices}} checked {{/ifContains}}>
                        <label class="custom-control-label" for="economicSubstanceServices">Economic substance
                            services</label>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 custom-control custom-checkbox">
                        <input required type="checkbox" class="custom-control-input" id="nomineeShareholderServices"
                            name="additionalServices" value="Nominee shareholder" {{#ifContains "Nominee shareholder"
                            incorporation.additionalServices}} checked {{/ifContains}}>
                        <label class="custom-control-label" for="nomineeShareholderServices">Nominee shareholder
                            services</label>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 custom-control custom-checkbox">
                        <input required type="checkbox" class="custom-control-input" id="secretaryServices"
                            name="additionalServices" value="Secretary services" {{#ifContains "Secretary services"
                            incorporation.additionalServices}} checked {{/ifContains}}>
                        <label class="custom-control-label" for="secretaryServices">Secretary services</label>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 custom-control custom-checkbox">
                        <input required type="checkbox" class="custom-control-input" id="trusteeServices"
                            name="additionalServices" value="Trustee Services for existing trust"
                            {{#ifContains "Trustee Services for existing trust" incorporation.additionalServices}}
                            checked {{/ifContains}}>
                        <label class="custom-control-label" for="trusteeServices">Trustee Services for existing
                            trust</label>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 custom-control custom-checkbox">
                        <input required type="checkbox" class="custom-control-input" id="sibaLicence" name="additionalServices"
                            value="SIBA licence" {{#ifContains "SIBA licence" incorporation.additionalServices}} checked
                            {{/ifContains}}>
                        <label class="custom-control-label" for="sibaLicence">SIBA licence</label>
                    </div>
                    <div class="col-md-4">
                        <input required type="text" class="form-control {{#ifNotIn "SIBA licence" incorporation.additionalServices}} hide-element {{/ifNotIn}}" id="sibaLicenceTypeControl" name="sibaLicenceType"
                            placeholder="Licence type" value="{{incorporation.sibaLicence}}"
                           >
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 custom-control custom-checkbox">
                        <input required type="checkbox" class="custom-control-input" id="trustLicence" name="additionalServices"
                            value="Trust licence" {{#ifContains "Trust licence" incorporation.additionalServices}}
                            checked {{/ifContains}}>
                        <label class="custom-control-label" for="trustLicence">Trust licence</label>
                    </div>
                    <div class="col-md-4">
                        <input required type="text" class="form-control {{#ifNotIn "Trust licence" incorporation.additionalServices}} hide-element {{/ifNotIn}}" id="trustLicenceTypeControl" name="trustLicenceType"
                            placeholder="Licence type" value="{{incorporation.trustLicence}}"
                            >
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 custom-control custom-checkbox">
                        <input required type="checkbox" class="custom-control-input" id="localRepresentative"
                            name="additionalServices" value="Local representative" {{#ifContains "Local representative"
                            incorporation.additionalServices}} checked {{/ifContains}}>
                        <label class="custom-control-label" for="localRepresentative">Local representative</label>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 custom-control custom-checkbox">
                        <input required type="checkbox" class="custom-control-input" id="otherServices" name="additionalServices"
                            value="Other" {{#ifContains "Other" incorporation.additionalServices}} checked
                            {{/ifContains}}>
                        <label class="custom-control-label" for="otherServices">Other</label>
                    </div>
                    <div class="col-md-4">
                        <input type="text" class="form-control" id="otherServicesDetailsControl" 
                            name="otherServicesDetails" value="{{incorporation.otherServices}}">
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-12">
                    <div class="progress">
                        <div class="progress-bar w-75" role="progressbar" aria-valuenow="6"
                            aria-valuemin="0" aria-valuemax="8">6 of 8
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript" src="/views-js/partials/incorporate-form/step-6.js"></script>
