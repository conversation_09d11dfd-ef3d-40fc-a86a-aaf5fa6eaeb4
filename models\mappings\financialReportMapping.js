const mongoose = require('mongoose');
const { getBooleanFormat, getServiceTypeFormat } = require('../../utils/schemaUtils');

const reportDetailsLabelsSchema = new mongoose.Schema({
    isExemptCompany: {
        type: Boolean,
        label: '1.1 Is your company an exempt company?',
        order: 1.1,
        get: getBooleanFormat,
        hideFromDisplay: false,
        isCalculated: false,

    },
    exemptCompanyType: {
        type: String,
        label: 'Exemption type',
        order: 1.11,
        hideFromDisplay: false,
        isCalculated: false,
    },
    exemptCompanyExplanation: {
        type: String,
        label: 'Please provide a motivation for your choice',
        order: 1.12,
        hideFromDisplay: false,
        isCalculated: false,
    },
    serviceType: {
        type: String,
        label: '1.2 Select the service that you would like to avail (one at a time)',
        order: 1.2,
        get: getServiceTypeFormat,
        hideFromDisplay: false,
        isCalculated: false,
    },
    isFirstYearOperation: {
        type: Boolean,
        label: "1.3 Is this the company's first year of operation?",
        order: 1.3,
        get: getBooleanFormat,
        hideFromDisplay: false,
        isCalculated: false,
    },
    isThereFinancialYearChange: {
        type: Boolean,
        label: '1.4 Is there a change of financial year?',
        order: 1.4,
        get: getBooleanFormat,
        hideFromDisplay: false,
        isCalculated: false,
    },
    assistanceRequired: {
        type: String,
        label: 'SPECIFY ASSISTANCE REQUIRED',
        order: 1.41,
        hideFromDisplay: false,
        isCalculated: false,
    },
})

const bankAccountLabelsSchema = new mongoose.Schema({
    description: {
        type: String,
        label: "2.1.1. Description",
        order: 2.2,
        hideFromDisplay: false,
        isCalculated: false,
    },
    accountType: {
        type: String,
        label: "2.1.2. Account Type",
        order: 2.2,
        hideFromDisplay: false,
        isCalculated: false,
    },
    openingAmount: {
        type: Number,
        label: "2.1.3. Opening Amount",
        order: 2.2,
        hideFromDisplay: false,
        isCalculated: false,
    },
    closingAmount: {
        type: Number,
        label: "2.1.4. Closing Amount",
        order: 2.2,
        hideFromDisplay: false,
        isCalculated: false,
    },
    assets: {
        bankTransfers: {
            type: Number,
            label: "2.1.5. Receipts from / payments to other Bank Accounts of the Company",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
        investmentsAcquisition: {
            type: Number,
            label: "2.1.6. Acquisition of Investments and other financial assets",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
        investmentsSale: {
            type: Number,
            label: "2.1.7. Sale of Investments and other financial assets",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
        tangibleAcquisition: {
            type: Number,
            label: "2.1.8. Acquisition of Tangible Fixed Asset",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
        tangibleSale: {
            type: Number,
            label: "2.1.9. Sale of Tangible Fixed Asset",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
        intangibleAcquisition: {
            type: Number,
            label: "2.1.10. Acquisition of Intangible Assets",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
        intangibleSale: {
            type: Number,
            label: "2.1.11. Sale of Intangible Assets",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
        otherAcquisition: {
            type: Number,
            label: "2.1.12. Acquisition of Other Assets",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
        otherSale: {
            type: Number,
            label: "2.1.13. Sale of Other Assets",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
        loanReceivablePaid: {
            type: Number,
            label: "2.1.14. Loans receivable - amounts paid",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
        loanReceivableReceived: {
            type: Number,
            label: "2.1.15. Loans receivable - amounts received",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
        receivablesPaid: {
            type: Number,
            label: "2.1.16. Receivables - amounts paid",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
        receivablesReceived: {
            type: Number,
            label: "2.1.17. Receivables - amounts received",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
    },
    liabilities: {
        accountsPayableReceived: {
            type: Number,
            label: "2.1.18. Accounts payable - amounts received",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
        accountsPayablePaid: {
            type: Number,
            label: "2.1.19. Accounts payable - amounts paid",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
        loansPayableReceived: {
            type: Number,
            label: "2.1.20. Loans payable - amounts received",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
        loansPayablePaid: {
            type: Number,
            label: "2.1.21. Loans payable - amounts paid",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
    },
    expenses: {
        compAdminFees: {
            type: Number,
            label: "2.1.22. Company Administration fees",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
        portMngmntFees: {
            type: Number,
            label: "2.1.23. Portfolio management fees and Related Services",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
        bankFees: {
            type: Number,
            label: "2.1.24. Bank Fees / Charges",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
        loanInterest: {
            type: Number,
            label: "2.1.25. Loan interest expense",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
        incomeTax: {
            type: Number,
            label: "2.1.26. Income tax expense",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        }
    },
    income: {
        dividendReceived: {
            type: Number,
            label: "2.1.27. Dividend Income received",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
        couponInterestReceived: {
            type: Number,
            label: "2.1.28. Coupon interest income received",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
        loanInterestReceived: {
            type: Number,
            label: "2.1.29. Loan interest income received",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
        bankInterestReceived: {
            type: Number,
            label: "2.1.30. Bank interest income received",
            order: 2.2,
            hideFromDisplay: false,
        isCalculated: false,
        },
    },
    equity: {
        paymentToShareholder: {
            type: Number,
            label: "2.1.31. Payment to the shareholder as Dividend paid or Return of Capital Contributions or Reduction of Share Capital / Premium",
            order: 2.2,
            hideFromDisplay: false,
            isCalculated: false,
        },
        receiptsFromShareholder: {
            type: Number,
            label: "2.1.32.Receipts from the shareholder for settlement of the Share Capital/Premium",
            order: 2.2,
            hideFromDisplay: false,
            isCalculated: false,
        },
        capitalContribution: {
            type: Number,
            label: "2.1.33. Receipts from the shareholder as Capital Contribution",
            order: 2.2,
            hideFromDisplay: false,
            isCalculated: false,
        },
    },
    closingBalancePerBank: {
        type: Number,
        label: "2.1.34. Closing balance per Bank Statement",
        order: 2.2,
        hideFromDisplay: false,
        isCalculated: false,
    },
    transactionCurrency: {
        type: String,
        label: "2.1.35. Please indicate the currency of the bank account",
        order: 2.2,
        hideFromDisplay: false,
        isCalculated: false,
    },
    foreingExchangeRate: {
        type: Number,
        label: "If the currency is different than the reporting currency then please indicate the foreign exchange rate to be multiplied",
        order: 2.2,
        hideFromDisplay: false,
        isCalculated: false,
    },
    cashAtBank: {
        type: Number,
        label: "2.1.36. Cash at bank",
        order: 2.2,
        hideFromDisplay: false,
        isCalculated: false,
    }
})

const cashTransactionsLabelsSchema = new mongoose.Schema({
    companyOwnsCashOrEquivalents: {
        type: Boolean,
        label: "2.1. Does the company own any cash or cash equivalents",
        order: 2.1,
        get: getBooleanFormat,
        hideFromDisplay: false,
        isCalculated: false,
    },
    bankAccounts: {
        type: [],
        label: "Bank accounts",
        order: 2.2,
        isArray: true,
        childType: bankAccountLabelsSchema,
        hideFromDisplay: false,
        isCalculated: false,
    },
    totalBankAccounts: {
        type: Number,
        label: "2.2. Total Cash and cash equivalent",
        order: 2.3,
        hideFromDisplay: false,
        isCalculated: false,
    },
})

const assetsLabelsSchema = new mongoose.Schema({
    isFirstYearReporting: {
        type: Boolean,
        label: "3.1.1. Is this the Company's first year of operation?",
        order: 3.12,
        get: getBooleanFormat,
        hideFromDisplay: false,
        isCalculated: false,
    },
    loansAndReceivables: {
        type: Number,
        label: '3.1.2.1. Loans and receivables',
        order: 3.131,
        hideFromDisplay: false,
        isCalculated: false,
    },
    otherFinancialAssets: {
        type: Number,
        label: '3.1.2.2. Investments and other financial assets',
        order: 3.132,
        hideFromDisplay: false,
        isCalculated: false,
    },
    tangibleFixedAssets: {
        type: Number,
        label: '3.1.2.3. Tangible fixed assets',
        order: 3.133,
        hideFromDisplay: false,
        isCalculated: false,
    },
    intangibleAssets: {
        type: Number,
        label: '3.1.2.4. Intangible assets',
        order: 3.134,
        hideFromDisplay: false,
        isCalculated: false,
    },
    valueOfOtherAssetsStartPeriod: {
        type: Number,
        label: "3.1.2.5. Other Assets Start Period",
        order: 3.135,
        hideFromDisplay: false,
        isCalculated: false,
    },
    isLoansAndReceivablesEndPeriod: {
        type: Boolean,
        label: '3.2.1. Did the Company have any Loans and receivables at the end of the financial period?',
        order: 3.21,
        get: getBooleanFormat,
        hideFromDisplay: false,
        isCalculated: false,
    },
    amountsOfLoansReceivables: {
        type: Number,
        label: '3.2.2. Please indicate if any loan receivable amounts were received or paid by the shareholder or another party on behalf of the Company',
        order: 3.22,
        hideFromDisplay: false,
        isCalculated: false,
    },
    interestReceivableOnTheLoan: {
        type: Number,
        label: '3.2.3. Please indicate the total amount of loan receivable interest accrued for the period',
        order: 3.23,
        hideFromDisplay: false,
        isCalculated: false,
    },
    loansReceivedByShareholder: {
        type: Number,
        label: '3.2.4. Please indicate if any loans and receivable amounts were received by the shareholder or another party on behalf of the Company',
        order: 3.241,
        hideFromDisplay: false,
        isCalculated: false,
    },
    paymentsOfLoansByShareholder: {
        type: Number,
        label: '3.2.5. Please indicate if the shareholder or another party made payments on behalf of the Company in relation to the Loans and receivables',
        order: 3.251,
        hideFromDisplay: false,
        isCalculated: false,
    },
    invoicesIssued: {
        type: Number,
        label: '3.2.6. Please indicate any invoices issued by the Company during the financial period',
        order: 3.26,
        hideFromDisplay: false,
        isCalculated: false,
    },
    declaredDividedIncome: {
        type: Number,
        label: '3.2.7. Please indicate if a subsidiary/associate declared dividend income to the Company during the reporting period',
        order: 3.27,
        hideFromDisplay: false,
        isCalculated: false,
    },
    balanceOfLoansReceivables: {
        type: Number,
        label: '3.2.8. Closing balance of Loans and receivables',
        order: 3.28,
        hideFromDisplay: false,
        isCalculated: true,
    },
    anyInvestments: {
        type: Boolean,
        label: '3.3.1. Did the Company have any Investments and other financial assets at the end of the financial period?',
        order: 3.31,
        get: getBooleanFormat,
        hideFromDisplay: false,
        isCalculated: false,
    },
    totalAmountOfInvestments: {
        type: Number,
        label: '3.3.2. What is the total market value of the Investments and other financial assets as of the year-end?',
        order: 3.32,
        hideFromDisplay: false,
        isCalculated: false,
    },
    investmentsTransferredByShareholder: {
        type: Number,
        label: '3.3.3. Please indicate if any investments were transferred into the Company by the shareholder during the financial period',
        order: 3.33,
        hideFromDisplay: false,
        isCalculated: false,
    },
    investmentTransferredToTheShareholder: {
        type: Number,
        label: '3.3.4. Please indicate if any investments were assigned / transferred to the shareholder during the financial period',
        order: 3.34,
        hideFromDisplay: false,
        isCalculated: false,
    },
    balanceOfInvestment: {
        type: Number,
        label: '3.3.5. Net Gain/(Loss) on Investments',
        order: 3.35,
        hideFromDisplay: false,
        isCalculated: true,
    },
    isTangibleFixAssets: {
        type: Boolean,
        label: '3.4.1. Did the company own any Tangible fixed assets (building, land, etc.) at the end of the financial period?',
        order: 3.41,
        get: getBooleanFormat,
        hideFromDisplay: false,
        isCalculated: false,
    },
    deprecationExpenses: {
        type: Number,
        label: '3.4.2. Indicate the depreciation expense for the year',
        order: 3.412,
        hideFromDisplay: false,
        isCalculated: false,
    },
    isFixedAssetsCotributed: {
        type: Boolean,
        label: '3.4.3. Was a Tangible Fixed Asset contributed to the company by the shareholder during this reporting period?',
        order: 3.42,
        get: getBooleanFormat,
        hideFromDisplay: false,
        isCalculated: false,
    },
    tangibleAssetsEndPeriod: {
        type: Number,
        label: '3.4.4. Value of Tangible Fixed Asset at the end of the financial period',
        order: 3.44,
        hideFromDisplay: false,
        isCalculated: true,
    },
    tangibleAssetsContributed: {
        type: Number,
        label: '3.4.5. Value of Tangible Fixed Asset when contributed to the Company by the shareholder',
        order: 3.45,
        hideFromDisplay: false,
        isCalculated: false,
    },
    valueTangibleAssetsEndPeriod: {
        type: Number,
        label: '3.4.6. Value of Tangible Fixed Asset at the end of the financial period',
        order: 3.46,
        hideFromDisplay: false,
        isCalculated: true,
    },
    isIntangibleAssets: {
        type: Boolean,
        label: '3.5.1. Did the company own any Intangible assets at the end of the financial period?',
        order: 3.51,
        get: getBooleanFormat,
        hideFromDisplay: false,
        isCalculated: false,
    },
    amortisationExpenses: {
        type: Number,
        label: '3.5.2. Indicate the amortisation expense for the year',
        order: 3.52,
        hideFromDisplay: false,
        isCalculated: false,
    },
    isIntagibleAssetsContributed: {
        type: Boolean,
        label: '3.5.3. Was an Intangible Asset contributed to the company by the shareholder during the reporting period?',
        order: 3.53,
        get: getBooleanFormat,
        hideFromDisplay: false,
        isCalculated: false,
    },
    intangibleAssetsEndReportPeriod: {
        type: Number,
        label: '3.5.4. Value of Intangible Asset at the end of the reporting period',
        order: 3.54,
        hideFromDisplay: false,
        isCalculated: true,
    },
    intangibleAssetsContributed: {
        type: Number,
        label: '3.5.5. Value of Intangible Asset when contributed to the Company by the shareholder',
        order: 3.55,
        hideFromDisplay: false,
        isCalculated: false,
    },
    intangibleAssetsEndFinancialPeriod: {
        type: Number,
        label: '3.5.6. Value of the Intangible Assets at the end of the financial period',
        order: 3.56,
        hideFromDisplay: false,
        isCalculated: true,
    },
    isOtherAssets: {
        type: Boolean,
        label: '3.6.1. Did the Company have any Other assets at the end of the financial period?',
        order: 3.61,
        get: getBooleanFormat,
        hideFromDisplay: false,
        isCalculated: false,
    },
    valueOfOtherAssetsEndPeriod: {
        type: Number,
        label: "3.6.2. Value of Other assets at the end of the financial period",
        order: 3.612,
        hideFromDisplay: false,
        isCalculated: false,
    },
    balanceOfTangibleAssets: {
        type: Number,
        label: 'Gain/(Loss) on sale of Tangible fixed assets',
        order: 3.451,
        hideFromDisplay: true,
        isCalculated: true,
    },
    balanceOfIntangibleAssets: {
        type: Number,
        label: 'Gain/(Loss) on sale of Intangible assets',
        order: 3.12,
        hideFromDisplay: true,
        isCalculated: true,
    }
})

const liabilitiesLabelsSchema = new mongoose.Schema({
    isFirstYearOperation: {
        type: Boolean,
        label: "4.1.1. Is this the Company's first year of operation?",
        order: 4.11,
        get: getBooleanFormat,
        hideFromDisplay: false,
        isCalculated: false,
    },
    accountsPayable: {
        type: Number,
        label: '4.1.2.1. Accounts payable',
        order: 4.121,
        hideFromDisplay: false,
        isCalculated: false,
    },
    longTermDebts: {
        type: Number,
        label: '4.1.2.2. Long-term debts',
        order: 4.122,
        hideFromDisplay: false,
        isCalculated: false,
    },
    valueOfOtherLiabilitiesStartPeriod: {
        type: Number,
        label: '4.1.2.3. Other Liabilities',
        order: 4.123,
        hideFromDisplay: false,
        isCalculated: false,
    },
    anyCompanyAccPayable: {
        type: Boolean,
        label: '4.2.1. Did the Company have any accounts payable at the end of the financial period?',
        order: 4.21,
        get: getBooleanFormat,
        hideFromDisplay: false,
        isCalculated: false,
    },
    accountsPayableBalance: {
        type: Number,
        label: '4.2.1.1. Please indicate the total accounts payable balance at the end of the financial period',
        order: 4.211,
        hideFromDisplay: false,
        isCalculated: false,
    },
    compAdminFees: {
        type: Number,
        label: '4.2.2.1. Company Administration fees',
        order: 4.221,
        hideFromDisplay: false,
        isCalculated: false,
    },
    otherExpenses: {
        type: Number,
        label: '4.2.2.2. Other Expense',
        order: 4.222,
        hideFromDisplay: false,
        isCalculated: false,
    },
    costOfGoods: {
        type: Number,
        label: '4.2.2.3.  Cost of Goods / Services Acquired',
        order: 4.223,
        hideFromDisplay: false,
        isCalculated: false,
    },
    didCompanyOweLongTermDebts: {
        type: Boolean,
        label: '4.3.1. Did the Company owe any Long-term debts at the end of the financial period?',
        order: 4.31,
        get: getBooleanFormat,
        hideFromDisplay: false,
        isCalculated: false,
    },
    loanInterestAccrued: {
        type: Number,
        label: '4.3.2. Please indicate the total amount of loan interest expense accrued for the period',
        order: 4.32,
        hideFromDisplay: false,
        isCalculated: false,
    },
    ltDebtsReceived: {
        type: Number,
        label: '4.3.3. Please indicate if any Long-term debts amounts were received by the shareholder or another party on behalf of the Company',
        order: 4.33,
        hideFromDisplay: false,
        isCalculated: false,
    },
    ltDebtsRepayments: {
        type: Number,
        label: '4.3.4. Please indicate if the shareholder or another party made repayments on behalf of the Company in relation to the Long- term debts',
        order: 4.34,
        hideFromDisplay: false,
        isCalculated: false,
    },
    ltDebtsClosingBalance: {
        type: Number,
        label: '4.3.5. Closing balance of Long-term debts',
        order: 4.35,
        hideFromDisplay: false,
        isCalculated: true,
    },
    otherLiabilitiesOwed: {
        type: Boolean,
        label: '4.4.1. Did the company owe any Other liabilities (or accrued expsenses) at the end of the financial period?',
        order: 4.41,
        get: getBooleanFormat,
        hideFromDisplay: false,
        isCalculated: false,
    },
    valueOfOtherLiabilitiesEndPeriod: {
        type: Number,
        label: '4.4.2. Value of other liabilities at the end od the financial period',
        order: 4.42,
        hideFromDisplay: false,
        isCalculated: false,
    },
    totalInvoicesAndAccruedExpenses: {
        type: Number,
        label: '4.4.3. Please indicate the total balance of the invoices issued to the Company during the financial period in relation to other expenses (which was not indicated in 4.2.2.2 section). Please include also any accrued expenses for services received during the reporting period but not invoiced in the current financial period',
        order: 4.43,
        hideFromDisplay: false,
        isCalculated: false,
    }
})

const FinancialReportSelfPrepareMapperSchema = new mongoose.Schema({
    reportDetails: reportDetailsLabelsSchema,
    cashTransactions: cashTransactionsLabelsSchema,
    assets: assetsLabelsSchema,
    liabilities: liabilitiesLabelsSchema,
});


module.exports = {
    FinancialReportSelfPrepareMapperSchema,
};