const moment = require('moment');
const utils = require('../utils/utils');
const { BANK_ACCOUNTS_INPUT_NUMBER_VALIDATION_ERRORS,
  FORM_COMPLETE_INCOME_EXPENSES_INPUT_NUMBER_VALIDATION_ERRORS,
  FORM_COMPLETE_ASSETS_LBT_INPUT_NUMBER_VALIDATION_ERRORS,
  ACCOUNTING_FORM_STEPS
} = require('../utils/financialReportConstants');
const FinancialReportModel = require('../models/financial-report');

exports.validateFinancialReport = async function (newValues, currentReport) {
  let errors = [];



  // Step 1 - REPORT DETAILS
  if (newValues.currentStep === ACCOUNTING_FORM_STEPS.DETAILS) {

    if (newValues.isExemptCompany === "YES") {
      if (!newValues.exemptCompanyType) {
        errors.push('Please select a type of exemption');
      }

      if (newValues.exemptCompanyType && newValues.exemptCompanyType !== "in-liquidation" && !newValues.exemptCompanyExplanation) {
        errors.push('Please explain your choice of exemption');
      }

      if (newValues.exemptCompanyType != "" &&
        (!currentReport.files || !currentReport.files?.exemptEvidenceFiles || !currentReport.files?.exemptEvidenceFiles?.length)) {
        errors.push('Please upload the evidence files');
      }
    }
    else if (newValues.isExemptCompany === "NO") {
      if (!newValues.serviceType) {
        errors.push('Please select the service that you would like to avail');
      }

      if (!newValues.isFirstYearOperation){
        errors.push('Please select if is the first year of operation');
      }
      else {

        /* if (newValues.isFirstYearOperation === "NO" && !newValues.isThereFinancialYearChange) {
          errors.push('Please select if is there a change of financial year');
        } */

        if (newValues.isThereFinancialYearChange === "YES") {
          if (!currentReport.files || !currentReport.files?.copyResolutionFiles || !currentReport.files?.copyResolutionFiles?.length) {
            errors.push('Please upload the copy of resolution file');
          }

          if (!newValues.financialPeriod.start) {
            errors.push("Provide start of financial period")
          }

          if (!newValues.financialPeriod.end) {
            errors.push("Provide end of financial period")
          }

          if (newValues.financialPeriod.start && newValues.financialPeriod.end) {

            let periodStart = new Date(convertToDate(newValues.financialPeriod.start));
            let periodEnd = new Date(convertToDate(newValues.financialPeriod.end));
            let minDate = new Date(convertToDate('2023-01-01'))
            let maxDate = new Date(convertToDate(newValues.financialPeriod.start));
            maxDate.setFullYear(maxDate.getFullYear() + 1);

            if (periodStart < minDate) {
              errors.push("Financial period start cannot be earlier than 2023-01-01")
            }

            if (periodEnd <= periodStart) {
              errors.push("End of financial period must be maximum 12 months after the start of the financial period")
            }
            if (periodEnd >= maxDate) {
              errors.push("End of financial period must be maximum 12 months after the start of the financial period")
            }

            let dbReports = await FinancialReportModel.find({
              'companyData.code': currentReport.companyData.code,
              'companyData.masterclientcode': currentReport.companyData.masterclientcode,
              'status': {"$ne": "DELETED"},
              _id: { "$ne": currentReport._id }
            }, ['_id', 'financialPeriod']);


            for (let dbReport of dbReports) {
              if (periodStart >= dbReport.financialPeriod.start && periodStart < dbReport.financialPeriod.end) {
                errors.push('This financial period is already submitted');
                break;
              }

              if (periodEnd >= dbReport.financialPeriod.start && periodEnd <= dbReport.financialPeriod.end) {
                errors.push('This financial period is already submitted');
                break;
              }
            }
          }

        }
      }
    }
    else {
      errors.push('Please select if your company is a exempt company');
    }
  }

  // Step 2 - CASH TRANSACTIONS
  if (newValues.currentStep === ACCOUNTING_FORM_STEPS.CASH) {
    if (!newValues.reportCurrency || !utils.getCurrencyByCode(newValues.reportCurrency)) {
      errors.push('Please select a valid currency for the report');
    }

    if (!newValues.companyOwnsCashOrEquivalents) {
      errors.push('Please select if your company own any cash or cash equivalents');
    }

    if (newValues.companyOwnsCashOrEquivalents === "YES" && currentReport.cashTransactions?.bankAccounts?.length > 0) {
      const invalidBankAccounts = currentReport.cashTransactions.bankAccounts.filter((b) => b.invalid === true);

      if (invalidBankAccounts.length > 0) {
        errors.push('Invalid bank account(s) detected. Please review and correct the conversion of cash at bank');
      }
    } else if (newValues.companyOwnsCashOrEquivalents === "YES" && currentReport.cashTransactions?.bankAccounts?.length === 0) {
      errors.push('Please add your bank account(s).')
    }
  }

  // Step 3 - ASSETS
  if (newValues.currentStep === ACCOUNTING_FORM_STEPS.ASSETS) {

    if (!newValues.isLoansAndReceivablesEndPeriod) {
      errors.push('Please select if your company have any loans and receivables')
    }

    if (!newValues.anyInvestments) {
      errors.push('Please Select if your company have any investments and other financial assets')
    } 
    if (!newValues.valueOfOtherAssetsStartPeriod) {
      errors.push('Please provide the value of the other assets at the start of the period')
    } 

    if (!newValues.isTangibleFixAssets) {
      errors.push('Please select if your company have any tangible assets')
    } else {
      if (newValues.isTangibleFixAssets === 'YES') {
        if (!newValues.isFixedAssetsCotributed) {
          errors.push('Please select if in your company was a tangible Fixed Asset contributed to the company by the shareholder')
        }else{
          if(newValues.isFixedAssetsCotributed === 'YES' && newValues.tangibleAssetsContributed < 0){
            errors.push('Provide a positive value of tangible fixed asset when contributed to the company by the shareholder')
          }
        }
      }
    }

    if (!newValues.isIntangibleAssets) {
      errors.push('Please select if your company own any intangible assets')
    } else {
      if (newValues.isIntangibleAssets === 'YES') {
        if (!newValues.isIntagibleAssetsContributed) {
          errors.push('Provide select if in your company was a intangible Fixed Asset contributed to the company by the shareholder')
        }
      }
    }

    if (!newValues.isOtherAssets) {
      errors.push('Please select if your company have any other asset')
    } else {
      if (!newValues.valueOfOtherAssetsEndPeriod) {
        errors.push('Please provide the value of the other assets at the end of the period')
      }
    }
  }

  // Step 4 - LIABILITIES
  if (newValues.currentStep === ACCOUNTING_FORM_STEPS.LIABILITIES) {
  

    if (!newValues.anyCompanyAccPayable) {
      errors.push('Please select if the company have any accounts payable at the end of the fiscal period');
    }

    if (!newValues.didCompanyOweLongTermDebts) {
      errors.push('Please select if the company owe any Long-term debts at the end of the fiscal period');
    }

    if (!newValues.otherLiabilitiesOwed) {
      errors.push('Please select if the company owe any other liabilities at the end of the fiscal period');
    }
  }

  // STEP COMPLETE INCOME EXPENSES
  if (newValues.currentStep === ACCOUNTING_FORM_STEPS.COMP_INCOME_EXPENSES) {
    const completeDetailsValues = newValues.completeDetails || {}

    const numberErrors = getErrorsByInputNumberList(FORM_COMPLETE_INCOME_EXPENSES_INPUT_NUMBER_VALIDATION_ERRORS, completeDetailsValues)
    if (numberErrors?.length > 0) {
      errors = [...errors, ...numberErrors];
    }

    //console.log("completeDetailsValues ", completeDetailsValues);
    if (completeDetailsValues.otherIncome && completeDetailsValues.otherIncome?.length > 0) {

      for (let i = 0; i < completeDetailsValues.otherIncome.length; i++) {
        if (utils.isInvalidNumber(completeDetailsValues.otherIncome[i].value)) {
          errors.push(`Provide a valid value for other income (${i + 1})`)
        }

        if (completeDetailsValues.otherIncome[i].description === ""){
          errors.push(`Provide a description for other income (${i + 1})`)
        }

      }

    }

    if (completeDetailsValues.otherExpenses && completeDetailsValues.otherExpenses?.length > 0) {

      for (let i = 0; i < completeDetailsValues.otherExpenses.length; i++) {
        if (utils.isInvalidNumber(completeDetailsValues.otherExpenses[i].value)) {
          errors.push(`Provide a valid value for other expense (${i + 1})`)
        }

        if (completeDetailsValues.otherExpenses[i].description === "") {
          errors.push(`Provide a description for other expense (${i + 1})`)
        }
      }

    }

  }
  // STEP COMPLETE ASSETS LIABILITIES
  if (newValues.currentStep === ACCOUNTING_FORM_STEPS.COMP_ASSETS_LBT) {
    const completeDetailsValues = newValues.completeDetails || {};

    const numberErrors = getErrorsByInputNumberList(FORM_COMPLETE_ASSETS_LBT_INPUT_NUMBER_VALIDATION_ERRORS, completeDetailsValues)
    if (numberErrors?.length > 0) {
      errors = [...errors, ...numberErrors];
    }

    if (completeDetailsValues.otherAssets && completeDetailsValues.otherAssets?.length > 0) {

      for (let i = 0; i < completeDetailsValues.otherAssets.length; i++) {
        if (utils.isInvalidNumber(completeDetailsValues.otherAssets[i].value)) {
          errors.push(`Provide a valid value for other asset (${i + 1})`)
        }

        if (completeDetailsValues.otherAssets[i].description === "") {
          errors.push(`Provide a description for other asset (${i + 1})`)
        }
      }

    }

    if (completeDetailsValues.otherLiabilities && completeDetailsValues.otherLiabilities?.length > 0) {
      for (let i = 0; i < completeDetailsValues.otherLiabilities.length; i++) {
        if (utils.isInvalidNumber(completeDetailsValues.otherLiabilities[i].value)) {
          errors.push(`Provide a valid value for other liability (${i + 1})`)
        }

        if (completeDetailsValues.otherLiabilities[i].description === "") {
          errors.push(`Provide a description for other liability (${i + 1})`)
        }
      }

    }
  }

  // Step 8 CONFIRMATION
  if (newValues.currentStep === ACCOUNTING_FORM_STEPS.DECLARATION) {

    const generateInvoice = currentReport.reportDetails.isExemptCompany !== true

    if (parseFloat(currentReport.version) > 1){
      if (newValues.clientPurpose !== 'on' ||  newValues.legalAdviseObtain !== 'on' ) {
        errors.push('Please complete all of the fields');
      }
    }else{
      if (newValues.information !== 'on' ||
        newValues.declarationAmount !== 'on' ||
        newValues.clientPurpose !== 'on' ||
        newValues.legalAdviseObtain !== 'on' || (generateInvoice === true && newValues.assetsLiabilities !== 'on')) {
        errors.push('Please complete all of the fields');
      }
    }



    if (!utils.validateEmail(newValues.declarationEmail)) {
      errors.push('Please enter valid email');
    }

  }
  return errors;
}

exports.validateBankAccountDetails = function (bankAccount) {
  try {
    let errors = [];


    if (!bankAccount.bankDescription || bankAccount.bankDescription === "") {
      errors.push("Provide a description for the bank account");
    }

    if (!bankAccount.bankAccountType) {
      errors.push("Please select the type of account");
    }

    const numberErrors = getErrorsByInputNumberList(BANK_ACCOUNTS_INPUT_NUMBER_VALIDATION_ERRORS, bankAccount)
    if (numberErrors?.length > 0) {
      errors = [...errors, ...numberErrors];
    }


    if (!utils.isInvalidNumber(bankAccount.closingAmount)){
      const closingAmount = utils.parseStringNumberToFloat(bankAccount.closingAmount);

      const totalIncome = utils.parseStringNumberToFloat(bankAccount.incomeDividendReceived) +
        utils.parseStringNumberToFloat(bankAccount.incomeCouponInterestReceived) +
        utils.parseStringNumberToFloat(bankAccount.incomeLoanInterestReceived) +
        utils.parseStringNumberToFloat(bankAccount.incomeBankInterestReceived) +
        utils.parseStringNumberToFloat(bankAccount.bankOtherIncome);

      const totalExpenses = utils.parseStringNumberToFloat(bankAccount.expensesCompAdminFees) +
        utils.parseStringNumberToFloat(bankAccount.expensesPortMngmntFees) +
        utils.parseStringNumberToFloat(bankAccount.expensesLoanInterest) +
        utils.parseStringNumberToFloat(bankAccount.expensesBankFeesCharges) +
        utils.parseStringNumberToFloat(bankAccount.expensesIncomeTax) +
        utils.parseStringNumberToFloat(bankAccount.bankOtherExpenses);

      const totalAssets = utils.parseStringNumberToFloat(bankAccount.assetsBankTransfers) +
        utils.parseStringNumberToFloat(bankAccount.assetsInvestmentsAcquisition) +
        utils.parseStringNumberToFloat(bankAccount.assetsInvestmentsSale) +
        utils.parseStringNumberToFloat(bankAccount.assetsTangibleAcquisition) +
        utils.parseStringNumberToFloat(bankAccount.assetsTangibleSale) +
        utils.parseStringNumberToFloat(bankAccount.assetsIntangibleAcquisition) +
        utils.parseStringNumberToFloat(bankAccount.assetsIntangibleSale) +
        utils.parseStringNumberToFloat(bankAccount.assetsOtherAcquisition) +
        utils.parseStringNumberToFloat(bankAccount.assetsOtherSale) +
        utils.parseStringNumberToFloat(bankAccount.assetsLoanReceivablePaid) +
        utils.parseStringNumberToFloat(bankAccount.assetsLoanReceivableReceived) +
        utils.parseStringNumberToFloat(bankAccount.assetsReceivablesPaid) +
        utils.parseStringNumberToFloat(bankAccount.assetsReceivablesReceived);

      const totalLiabilities = utils.parseStringNumberToFloat(bankAccount.liabilitiesAccountsPayableReceived) +
        utils.parseStringNumberToFloat(bankAccount.liabilitiesAccountsPayablePaid) +
        utils.parseStringNumberToFloat(bankAccount.liabilitiesLoansPayableReceived) +
        utils.parseStringNumberToFloat(bankAccount.liabilitiesLoansPayablePaid);


      const totalEquity = utils.parseStringNumberToFloat(bankAccount.equityPaymentToShareholder) +
        utils.parseStringNumberToFloat(bankAccount.equityReceiptsFromShareholder) +
        utils.parseStringNumberToFloat(bankAccount.equityCapitalContribution);

      const closingBalancePerBank = utils.parseStringNumberToFloat(bankAccount.openingAmount) + (totalIncome + totalExpenses + totalAssets + totalLiabilities + totalEquity);

      if (closingAmount !== closingBalancePerBank) {
        errors.push(`The Cash in Bank is not reconciled with the closing amount`);
      }
    }


    return errors;
  }
  catch (e) {
    console.log("Error with bank accounts validations ", e);
    return ["There was an error validating the information, try again later"]
  }
}



function getErrorsByInputNumberList(listOfInputs, formValues) {
  const errors = [];

  listOfInputs.forEach((inputObj) => {
    const inputVal = formValues[inputObj.input];
    const isInvalidNumber = utils.isInvalidNumber(inputVal);
    
    if (Array.isArray(inputVal)) {
        inputVal.forEach((input, index) => {
          const invalidInput = utils.isInvalidNumber(input)
          if (!invalidInput) {
            const error = validateInput(inputObj, input);
            if (error !== false) {
              errors.push(error + ' (' + (index + 1) + ')');
            }
          }
        })
    } else {
      if (isInvalidNumber) {
        errors.push(inputObj.error);
      } else {
        const error = validateInput(inputObj, inputVal)
        if (error !== false) {
          errors.push(error);
        }
      }
    }
    
  })
  return errors;
}

function validateInput (inputObj, val) {
  if (inputObj.validation === "num-positive" && parseFloat(val) < 0) {
    return inputObj.error
  } else if (inputObj.validation === "num-negative" && parseFloat(val) > 0) {
    return inputObj.error
  } else {
    return false
  }
}

function convertToDate(dateStr) {
  var dateMomentObject = moment(dateStr, "YYYY-MM-DD"); // 1st argument - string, 2nd argument - format
  var dateObject = dateMomentObject.toDate();

  var userTimezoneOffset = dateObject.getTimezoneOffset() * 60000;
  return new Date(dateObject.getTime() - userTimezoneOffset);

  //return dateObject;
}
