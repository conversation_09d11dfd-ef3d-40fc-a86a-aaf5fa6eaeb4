const express = require('express');
const router = express.Router({ mergeParams: true });

// Require controller modules.
const companyController = require('../controllers/companyController');
const companyFileController = require('../controllers/companyFileController');
const downloadController = require('../controllers/downloadController');
const uploadController = require('../controllers/uploadController');

router.get('/', companyController.ensureAuthenticated, companyController.validateMasterClient,
    companyFileController.getRequestedFilesDashboard);
router.get('/:requestId', companyController.ensureAuthenticated, companyController.validateMasterClient,
  companyFileController.getRequestedFileView);
router.post('/:requestId', companyController.ensureAuthenticated, companyController.validateMasterClient,
  companyFileController.saveRequestedFileInfo); // jquery / ajax

// Financial Reports
router.get('/financial-reports/:company/:reportId', companyController.ensureAuthenticated, companyController.validateMasterClient,
  companyFileController.getRequestedFileViewFinancialReport);
router.put('/financial-reports/:company/:reportId', companyController.ensureAuthenticated, companyController.validateMasterClient,
  companyFileController.saveRequestedFileViewFinancialReport);
router.get('/financial-reports/:company/:reportId/download-rfi-file/:requestId/:fileId', companyController.ensureAuthenticated,
  companyController.validateMasterClient, downloadController.downloadRFIFinancialReportFile);

router.post('/financial-reports/:company/:reportId/upload-files',companyController.ensureAuthenticated, companyController.validateMasterClient, 
    uploadController.uploadErrorHandler(uploadController.uploadFinancialReportsFile.fields([{ name: 'fileUploaded', maxCount: 5 }])),
    companyFileController.saveTemporalFileFinancialReport
);

router.get('/financial-reports/:company/:reportId/files', companyController.ensureAuthenticated,
  companyController.validateMasterClient, companyFileController.getFileListFinancialReport); // jquery / ajax

router.delete('/financial-reports/:company/:reportId/files/:fileId', companyController.ensureAuthenticated, companyController.validateMasterClient, 
  companyFileController.deleteFileFinancialReport
); // jquery / ajax

//Substance
router.get('/substance/:company/:entryId', companyController.ensureAuthenticated, companyController.validateMasterClient,
  companyFileController.getRequestedFileViewSubstance);
router.put('/substance/:company/:entryId', companyController.ensureAuthenticated, companyController.validateMasterClient,
  companyFileController.saveRequestedFileInfoSubstance); // jquery / ajax
router.get('/substance/:company/:entryId/download-rfi-file/:requestId/:fileId', companyController.ensureAuthenticated,
  companyController.validateMasterClient, downloadController.downloadRFISubstanceFile); // jquery / ajax
// files
router.get('/:requestId/search-files', companyController.ensureAuthenticated,
  companyController.validateMasterClient, companyFileController.getFileList); // jquery / ajax

router.get('/substance/:company/:entryId/files', companyController.ensureAuthenticated,
  companyController.validateMasterClient, companyFileController.getFileListSubstance); // jquery / ajax

router.post('/:requestId/upload-files',companyController.ensureAuthenticated, companyController.validateMasterClient, 
    uploadController.uploadErrorHandler(uploadController.uploadRequestedFile.fields([{ name: 'fileUploaded', maxCount: 5 }])),
    companyFileController.saveTemporalFile 
);

router.post('/substance/:company/:entryId/upload-files',companyController.ensureAuthenticated, companyController.validateMasterClient, 
    uploadController.uploadErrorHandler(uploadController.uploadFile.fields([{ name: 'fileUploaded', maxCount: 5 }])),
    companyFileController.saveTemporalFileSubstance
);

router.delete('/:requestId/files',companyController.ensureAuthenticated, companyController.validateMasterClient, 
  companyFileController.deleteFile
);  // jquery / ajax

router.delete('/substance/:company/:entryId/files/:fileId', companyController.ensureAuthenticated, companyController.validateMasterClient,
  companyFileController.deleteFileSubstance
); // jquery / ajax

router.get('/:requestId/files/:fileTypeId/:fileId',companyController.ensureAuthenticated,  companyController.validateMasterClient,
   downloadController.downloadRequestedFiles 
); // jquery / ajax



module.exports = router;
