const Company = require('../models/company').schema;
const MasterClientCode = require('../models/masterClientCode');
const httpConstants = require('http2').constants;
const {
  DIRECTOR_REQUIRED_FIELDS,
  MEMBER_REQUIRED_FIELDS,
  BENEFICIAL_OWNER_REQUIRED_FIELDS,
  TYPE_OF_MEMBER,
  TYPE_OF_BO,
  TYPE_OF_DIRECTOR,
} = require('./directorAndMemberConstants');

exports.getCompanyData = async function (code, mcc, user) {
  try {
    let masterclient = await MasterClientCode.findOne({ 'code': mcc });
    if (masterclient == null || masterclient.owners?.indexOf(user.toLowerCase()) == -1) {
      return { status: httpConstants.HTTP_STATUS_NOT_FOUND, error: 'Masterclient not found' }
    }
    const company = await Company.findOne({ code: code, masterclientcode: mcc });
    return company || { status: httpConstants.HTTP_STATUS_NOT_FOUND, error: 'Company not found' }

  } catch (e) {
    console.error("Error getting company data: ", e);
    return { status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, error: 'Internal server error' }
  }

}

exports.getListFieldsToValidate = function (type, officerType) {
  let fieldsByType = null;
  if (type === TYPE_OF_MEMBER) {
    fieldsByType = MEMBER_REQUIRED_FIELDS;
  } else if (type === TYPE_OF_BO) {
    fieldsByType = BENEFICIAL_OWNER_REQUIRED_FIELDS;
  } else if (type === TYPE_OF_DIRECTOR) {
    fieldsByType = DIRECTOR_REQUIRED_FIELDS;
  }

  if (!fieldsByType || !officerType) {
    return [];
  }

  let fieldsToValidate = [];

  return fieldsToValidate[officerType];
}