$(document).ready(async function () {
    // Function code here.
    let showRelations = $('#showRelations').val();

    if (showRelations === "true") {
        const incorporationId = $(".changeStep:visible").data('id');
        await changeStep(1, 'NEXT', incorporationId);
    }
});
let incorporationId

$(document).on('click', '.submitNameReservation', async function () {
    await submitNameReservation($(this).attr('data-id'))
})


$(document).on('click', '.changeStep', async function () {
    await changeStep(parseInt($(this).attr('data-step')), $(this).attr('data-action'), $(this).attr('data-id'))
})


async function changeStep(currentStep, action, incorporationId) {
    incorporationId = incorporationId
    if (action === 'NEXT') {
        await saveForm(false, currentStep, incorporationId);
    } else if (action === 'BACK') {
        $('#step-' + (currentStep - 1)).show();
        $('#step-' + currentStep).hide();
    } else if (action === 'COMPLETE') {
        await saveForm(true, currentStep, incorporationId);
    }

}

$('input[type="text"][required], input[type="number"][required], input[type="date"][required]').on('keyup', function () {
    const empty = $(this).val() === "";
    $(this).toggleClass("is-invalid", empty);
});

$('textarea[required]').on('keyup', function () {
    const empty = $(this).val() === "";
    $(this).toggleClass("is-invalid", empty);
});

$('select[required]').on('change', function () {
    const empty = $(this).val() === "";
    if ($(this).attr('id') === '-records[operationCountry]') {
        checkBlacklistedStep5($(this).val());
    }
    // Used for select2 selectors (country select)
    $(`span[aria-labelledby='select2-${$(this).attr('id').replace('[', '').replace(']', '')}-container']`).toggleClass("is-invalid", empty);
    $(this).toggleClass("is-invalid", empty);
});

$("input[type='radio'][required]").on('change', function () {
    const empty = $('input[name="' + this.name + '"]:checked').val() === "";
    $('input[name="' + this.name + '"]').toggleClass("is-invalid", empty);
});

$("input[type='checkbox'][required]").on('change', function () {
    const empty = !($('input[name="' + this.name + '"]:checked').val());
    $('input[name="' + this.name + '"]').toggleClass("is-invalid", empty);
});

const blacklist = ["Afghanistan", "Bosnia and Herzegovina", "Korea, Democratic People's Republic of", "Guyana", "Iran, Islamic Republic of", "Iraq", "Lao People's Democratic Republic", "Syrian Arab Republic", "Uganda", "Vanuatu", "Yemen"];
$('#-activityJurisdiction').change(function () {
    checkBlacklistedStep3();
});

$('#-taxResidencyJurisdiction').change(function () {
    checkBlacklistedStep3();
});

$('#-records[operationCountry]').change(function () {
    checkBlacklistedStep5();
});

function checkBlacklistedStep3() {
    let isBlacklisted = false;
    if (blacklist.find((country) => country === $('#-taxResidencyJurisdiction').val())) {
        isBlacklisted = true;
    }
    if (blacklist.find((country) => country === $('#-activityJurisdiction').val())) {
        isBlacklisted = true;
    }
    if (isBlacklisted) {
        $('#blacklistedCountryRowStep3').show(200);
        $('#step-three-next-btn').hide();
    } else {
        $('#blacklistedCountryRowStep3').hide(200);
        $('#step-three-next-btn').show();
    }
}

function checkBlacklistedStep5(value) {
    let isBlacklisted = false;
    if (blacklist.find((country) => country === value)) {
        isBlacklisted = true;
    }
    if (isBlacklisted) {
        $('#blacklistedCountryRowStep5').show(200);
        $('#step-five-next-btn').hide();
    } else {
        $('#blacklistedCountryRowStep5').hide(200);
        $('#step-five-next-btn').show();
    }
}

async function saveForm(isCompleted, currentStep, incorporationId) {
    let invalidRadios = false;
    let invalidFiles = false;
    if ($('#uploadStructureChartBtn').is(':visible') && $('#uploadStructureChartBtn').text().includes('Upload')) {
        invalidFiles = true;
        $('#missingFileRowStep1').show(200);
    } else {
        $('#missingFileRowStep1').hide(200);
    }
    $('#clientCorporationForm input[required]:visible').trigger('keyup');
    $('#clientCorporationForm textarea[required]:visible').trigger('keyup');
    $('#clientCorporationForm select[required]:visible').trigger('change');
    $("#clientCorporationForm input[type='checkbox'][required]:visible").each(function () {
        const val = $('input[name="' + this.name + '"]:checked').val();
        if (val === undefined) {
            $('input[name="' + this.name + '"]').toggleClass("is-invalid", true);
            invalidRadios = true;
        } else {
            $('input[name="' + this.name + '"]').toggleClass("is-invalid", false);
        }
    });
    let missingAsset = false;
    let missingFund = false;
    let missingRelation = false;
    if ($('#assetsAndFunds').is(':visible')) {
        missingAsset = true;
        missingFund = true;
        $('.asset-row').each(function () {
            missingAsset = false;
        });
        $('.fund-row').each(function () {
            missingFund = false;
        });
        if (missingAsset) {
            $('#missingAssetRowStep4').show(200);
        } else {
            $('#missingAssetRowStep4').hide(200);
        }
        if (missingFund) {
            $('#missingFundRowStep4').show(200);
        } else {
            $('#missingFundRowStep4').hide(200);
        }
    }
    if (currentStep === 2) {
        missingRelation = true;
        $('.relation-row').each(function () {
            missingRelation = false;
        });
        if (missingRelation) {
            $('#missingRelationRowStep2').show(200);
        } else {
            $('#missingRelationRowStep2').hide(200);
        }
    }

    $("#clientCorporationForm input[type='radio'][required]:visible").each(function () {
        const val = $('input[name="' + this.name + '"]:checked').val();
        if (val === undefined) {
            $('input[name="' + this.name + '"]').toggleClass("is-invalid", true);
            invalidRadios = true;
        } else {
            $('input[name="' + this.name + '"]').toggleClass("is-invalid", false);
        }
    });
    
    $('#principalBusinessActivityControl').change(function () {
        if ($(this).val() === '') {
            $('#requireSubstanceServiceRowStep3').hide(200);
            $('#principalBusinessActivityOtherRow').hide(200);
        } else if ($(this).val() === 'Other') {
            $('#principalBusinessActivityOtherRow').show(200);
            $('#requireSubstanceServiceRowStep3').hide(200);
        } else {
            $('#principalBusinessActivityOtherRow').hide(200);
            if ($(this).val() !== 'Holding Business (Pure Equity Holding entities)') {
                $('#economicSubstanceServices').prop('checked', true);
                $('#requireSubstanceServiceRowStep3').show(200);
            } else {
                $('#requireSubstanceServiceRowStep3').hide(200);
            }
        }
    });

    if ($(".is-invalid:visible").length === 0 && !invalidRadios && !invalidFiles && !missingAsset && !missingFund && !missingRelation) {
        const form = $("#clientCorporationForm").serializeJSON();
        form.isCompleted = isCompleted;
        form.additionalServices = [];
        form.currentStep = currentStep;

        $('input[name="additionalServices"]:checked').each(function () {
            form.additionalServices.push($(this).val());
        });

        $.ajax({
            type: "POST",
            url: "./" + incorporationId,
            data: JSON.stringify(form),
            contentType: "application/json; charset=utf-8",

            success: function (data) {
                if (data.status === 200) {
                    $('#step-' + (currentStep + 1)).show();
                    $('#step-' + currentStep).hide();
                } else if (data.status === 400 && data.errors && data.errors.length) {
                    for (let error of data.errors) {
                        toastr["warning"](error, 'Error!');
                    }
                } else {
                    toastr["warning"](data.message, 'Error!');
                }
            },
            error: function (err) {
                toastr["warning"]('Submission could not be saved, please try again later.', 'Error!');
            }
        });

    } else {
        return true;
    }
}

async function submitNameReservation(incorporationId) {
    $('#entityNameControl').trigger('keyup');
    $('#entityTypeControl').trigger('change');
    
    if ($(".is-invalid:visible").length === 0) {
        Swal.fire(
            {
                title: "Submit?",
                html: "Are you sure you want to reserve the name <br> <b>" + $('#entityNameControl').val() + "</b>",
                icon: "question",
                showCancelButton: true,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                cancelButtonText: "Cancel",
                confirmButtonText: "Submit"
            }).then(function (t) {
                if (t.value) {
                    const form = $("#clientCorporationForm").serializeJSON();
                    form.additionalServices = [];
                    form.nameReservationStatus = 'IN PROGRESS';
                    $('input[name="additionalServices"]:checked').each(function () {
                        form.additionalServices.push($(this).val());
                    });
                    $.ajax({
                        type: "POST",
                        url: "./" + incorporationId,
                        data: JSON.stringify(form),
                        contentType: "application/json; charset=utf-8",
                        success: function (data) {
                            if (data.status === 200) {
                                location.reload();
                            } else {
                                toastr["warning"](data.message, 'Error!');
                            }
                        },
                        error: function (err) {
                            toastr["warning"]('Submission could not be saved, please try again later.', 'Error!');
                        }
                    });
                }
            }
        );
    }
}