$(document).ready(function () {
    const arePhysicalOffices = $('input[name=arePhysicalOffices]:checked').val()

    if(arePhysicalOffices === "No"){
        $("#PremisesAddressLine1").val('None').prop('readonly', true);
        $("#PremisesAddressLine2").val('').prop('readonly', true);
        $("#PremisesCountry").val('Virgin Islands, British');
    }


    const hasError = $('#hasErrorVal').val();

    if (hasError === 'false') {
        window.parent.closePremiseIFrame();
    }


});

$('#arePhysicalOfficesYes').click(function () {
    onChangeNoneAddress()
})

$('#arePhysicalOfficesNo').click(function () {
    onChangeNoneAddress()
})

function onChangeNoneAddress() {
    const value = $('input[name=arePhysicalOffices]:checked').val()
    
    if (value === "No") {
        $("#PremisesAddressLine1").val('None').prop('readonly', true);
        $("#PremisesAddressLine2").val('').prop('readonly', true);
        $("#PremisesCountry").val('Virgin Islands, British');
    }else{
        $("#PremisesAddressLine1").val('').prop('readonly', false);
        $("#PremisesAddressLine2").prop('readonly', false);
    }
}