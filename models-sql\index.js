const { Sequelize } = require('sequelize');


Sequelize.DATE.prototype._stringify = function _stringify(date, options) {
    date = this._applyTimezone(date, options);
    return date.utc().format('YYYY-MM-DD HH:mm:ss.SSS');
};



// Database connection
const sequelize = new Sequelize(process.env.SQL_DB_NAME, process.env.SQL_DB_USER, process.env.SQL_DB_PASSWORD, {
    host: process.env.SQL_DB_HOST,
    port: process.env.SQL_DB_PORT,
    raw: true,
    dialect: 'mssql',
});

const db = {};

db.Sequelize = Sequelize;
db.sequelize = sequelize;

// Models/tables
db.VpDirectorInfo = require('./vpDirectorInfo.model.js')(sequelize, Sequelize);
db.VpDirectorInfoHistory = require('./vpDirectorInfoHistory.model.js')(sequelize, Sequelize);
db.VpDirectorInfoStatus = require('./vpDirectorInfoStatus.model.js')(sequelize, Sequelize);
db.mem_Directors = require('./mem_Directors.model.js')(sequelize, Sequelize);
db.mem_Shareholders = require('./mem_Shareholders.model.js')(sequelize, Sequelize);
db.mem_BeneficialOwners = require('./mem_BeneficialOwners.model.js')(sequelize, Sequelize);
db.mem_DirectorsHistory = require('./mem_DirectorsHistory.model.js')(sequelize, Sequelize);
db.mem_ShareholdersHistory = require('./mem_ShareholdersHistory.model.js')(sequelize, Sequelize);
db.mem_BeneficialOwnersHistory = require('./mem_BeneficialOwnersHistory.model.js')(sequelize, Sequelize);
db.mem_MemberProfiles = require('./mem_MemberProfiles.model.js')(sequelize, Sequelize);
db.mem_Entities = require('./mem_Entities.model.js')(sequelize, Sequelize);
db.mem_EntitiesMutualFundHistory = require('./mem_EntitiesMutualFundHistory.model.js')(sequelize, Sequelize);
db.mem_EntitiesStockHistory = require('./mem_EntitiesStockHistory.model.js')(sequelize, Sequelize);
db.mem_MemberProfilesHistory = require('./mem_MemberProfilesHistory.model.js')(sequelize, Sequelize);

// Associate existing relations
for (const model of Object.values(db)) {
    if (model.associate) {
        model.associate(db);
    }
}

module.exports = db;