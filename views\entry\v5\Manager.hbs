<form method="POST">
    <input type="text" hidden name="csrf-token" value="{{csrfToken}}">
    <input type="text" id="hasErrorVal" hidden readonly value="{{hasError}}">
    <div class="contour container-fluid pt-2">
        {{#if validationErrors }}
        {{#if hasError}}
        {{#each validationErrors }}
        {{renderValidationMessage this.msg this.field}}
        {{/each}}
        {{/if}}

        {{/if}}

        <div class="row">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="mb-2" for="is_corporate_director">Corporate Director? </label>
                </div>
            </div>
            <div class="col-md-8">
                <div class="radio form-check-inline">
                    <input type="radio" id="is_corporate_director_yes" name="is_corporate_director" value="Yes" {{#if
                        data.is_corporate_director}}checked{{/if}}>
                    <label for="is_corporate_director_yes">Yes</label>
                </div>
                <div class="radio form-check-inline">
                    <input type="radio" id="is_corporate_director_no" name="is_corporate_director" value="No" {{#unless
                        data.is_corporate_director}}checked{{/unless}}>
                    <label for="is_corporate_director_no">No</label>
                </div>
            </div>
        </div>

        <div class="row mt-2">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="mb-2" for="FirstNameDirector">First Name / Company Name (if corporate director)
                    </label>
                </div>
            </div>
            <div class="col-md-8">
                <div class="form-group mb-3">
                    <input type="text" name="FirstNameDirector" id="FirstNameDirector" class="form-control"
                        label="FirstNameDirector" value="{{data.first_name}}">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="mb-2" for="MiddleNameDirector">Middle Name</label>
                </div>
            </div>
            <div class="col-md-8">
                <div class="form-group mb-3">
                    <input type="text" name="MiddleNameDirector" id="MiddleNameDirector" class="form-control"
                        label="MiddleNameDirector" value="{{data.middle_name}}">
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="mb-2" for="LastNameDirector">Last Name</label>
                </div>
            </div>
            <div class="col-md-8">
                <div class="form-group mb-3">
                    <input type="text" name="LastNameDirector" id="LastNameDirector" class="form-control"
                        label="LastNameDirector" value="{{data.last_name}}">
                </div>
            </div>
        </div>


        <div class="row">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="mb-2" for="Name">Resident in the BVI?</label>
                </div>
            </div>
            <div class="col-md-8">
                <div class="form-group mb-3">
                    <div class="radio form-check-inline">

                        <input type="radio" id="ResidentinBVIYes" name="ResidentinBVIYesNo" value="Yes" {{#if
                            data.resident_in_bvi}}checked{{/if}}>
                        <label for="ResidentinBVIYes">Yes</label>
                    </div>
                    <div class="radio form-check-inline">
                        <input type="radio" id="ResidentinBVINo" name="ResidentinBVIYesNo" value="No" {{#unless
                            data.resident_in_bvi}}checked{{/unless}}>
                        <label for="ResidentinBVINo">No</label>

                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <label class="mb-2" for="PositionHeld">Relation to Entity</label>
                </div>
            </div>
            <div class="col-md-8">
                <div class="form-group">
                    <input type="text" name="PositionHeld" id="PositionHeld" class="form-control" label="PositionHeld"
                        value="{{data.position_held}}">
                </div>
            </div>
        </div>

        <div class="clearfix text-right ">
            <button type="submit" class="btn solid royal-blue"> <i class="mdi mdi-send mr-1"></i> Save</button>
        </div>


    </div>
</form>

<script type='text/javascript' src='/javascripts/form-advanced.init.js'></script>
<script type='text/javascript' src="/views-js/entry/v5/Manager.js"></script>
