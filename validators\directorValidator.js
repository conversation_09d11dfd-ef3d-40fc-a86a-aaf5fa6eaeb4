exports.validate = function(manager) {
    const errors = [];
    let minorDate = new Date();
    minorDate.setFullYear(minorDate.getFullYear() - 18);
    
    if (manager.first_name == undefined || manager.first_name.length == 0) {
        errors.push({msg: 'Provide the ' + (manager.is_corporate_director ? 'company name' : 'first name'), field: "FirstNameDirector"})     
    }

    if (manager.is_corporate_director == false && (manager.last_name == undefined || manager.last_name.length == 0)) {
        errors.push({ msg: 'Provide the last name', field: "LastNameDirector" })
    }


    
    if(manager.version < 5){

        if (manager.date_of_birth == undefined) {
            errors.push({ msg: 'Provide the ' + (manager.is_corporate_director ? 'Date of Incorporation' : 'date of birth'), field: "DateOfBirth" })
        } else {
            // check minor
            if (manager.is_corporate_director == false && manager.date_of_birth > minorDate) {
                errors.push({ msg: 'Director cannot be a minor', field: "DateOfBirth" })
            }
        }
        if (manager.address_line1 == undefined || manager.address_line1.length == 0) {
            errors.push({ msg: 'Provide the address', field: "StreetAndNumber" })
        }

        if (manager.position_held == undefined || manager.position_held.length == 0) {
            errors.push({ msg: 'Provide the position held', field: "PositionHeld" })
        }
    }else{
        if (manager.position_held == undefined || manager.position_held.length == 0) {
            errors.push({ msg: 'Provide relation to entity', field: "PositionHeld" })
        }
    }


    //
    return errors;
}