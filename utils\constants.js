exports.CURRENCIES = [
  {"cc":"AED","symbol":"\u062f.\u0625;","name":"UAE dirham"},
  {"cc":"AFN","symbol":"Afs","name":"Afghan afghani"},
  {"cc":"ALL","symbol":"L","name":"Albanian lek"},
  {"cc":"AMD","symbol":"AMD","name":"Armenian dram"},
  {"cc":"ANG","symbol":"NA\u0192","name":"Netherlands Antillean gulden"},
  {"cc":"AOA","symbol":"Kz","name":"Angolan kwanza"},
  {"cc":"ARS","symbol":"$","name":"Argentine peso"},
  {"cc":"AUD","symbol":"$","name":"Australian dollar"},
  {"cc":"AWG","symbol":"\u0192","name":"Aruban florin"},
  {"cc":"AZN","symbol":"AZN","name":"Azerbaijani manat"},
  {"cc":"BAM","symbol":"KM","name":"Bosnia and Herzegovina konvertibilna marka"},
  {"cc":"BBD","symbol":"Bds$","name":"Barbadian dollar"},
  {"cc":"BDT","symbol":"\u09f3","name":"Bangladeshi taka"},
  {"cc":"BGN","symbol":"BGN","name":"Bulgarian lev"},
  {"cc":"BHD","symbol":".\u062f.\u0628","name":"Bahraini dinar"},
  {"cc":"BIF","symbol":"FBu","name":"Burundi franc"},
  {"cc":"BMD","symbol":"BD$","name":"Bermudian dollar"},
  {"cc":"BND","symbol":"B$","name":"Brunei dollar"},
  {"cc":"BOB","symbol":"Bs.","name":"Bolivian boliviano"},
  {"cc":"BRL","symbol":"R$","name":"Brazilian real"},
  {"cc":"BSD","symbol":"B$","name":"Bahamian dollar"},
  {"cc":"BTN","symbol":"Nu.","name":"Bhutanese ngultrum"},
  {"cc":"BWP","symbol":"P","name":"Botswana pula"},
  {"cc":"BYR","symbol":"Br","name":"Belarusian ruble"},
  {"cc":"BZD","symbol":"BZ$","name":"Belize dollar"},
  {"cc":"CAD","symbol":"$","name":"Canadian dollar"},
  {"cc":"CDF","symbol":"F","name":"Congolese franc"},
  {"cc":"CHF","symbol":"Fr.","name":"Swiss franc"},
  {"cc":"CLP","symbol":"$","name":"Chilean peso"},
  {"cc":"CNY","symbol":"\u00a5","name":"Chinese/Yuan renminbi"},
  {"cc":"COP","symbol":"Col$","name":"Colombian peso"},
  {"cc":"CRC","symbol":"\u20a1","name":"Costa Rican colon"},
  {"cc":"CUC","symbol":"$","name":"Cuban peso"},
  {"cc":"CVE","symbol":"Esc","name":"Cape Verdean escudo"},
  {"cc":"CZK","symbol":"K\u010d","name":"Czech koruna"},
  {"cc":"DJF","symbol":"Fdj","name":"Djiboutian franc"},
  {"cc":"DKK","symbol":"Kr","name":"Danish krone"},
  {"cc":"DOP","symbol":"RD$","name":"Dominican peso"},
  {"cc":"DZD","symbol":"\u062f.\u062c","name":"Algerian dinar"},
  {"cc":"EEK","symbol":"KR","name":"Estonian kroon"},
  {"cc":"EGP","symbol":"\u00a3","name":"Egyptian pound"},
  {"cc":"ERN","symbol":"Nfa","name":"Eritrean nakfa"},
  {"cc":"ETB","symbol":"Br","name":"Ethiopian birr"},
  {"cc":"EUR","symbol":"\u20ac","name":"European Euro"},
  {"cc":"FJD","symbol":"FJ$","name":"Fijian dollar"},
  {"cc":"FKP","symbol":"\u00a3","name":"Falkland Islands pound"},
  {"cc":"GBP","symbol":"\u00a3","name":"British pound"},
  {"cc":"GEL","symbol":"GEL","name":"Georgian lari"},
  {"cc":"GHS","symbol":"GH\u20b5","name":"Ghanaian cedi"},
  {"cc":"GIP","symbol":"\u00a3","name":"Gibraltar pound"},
  {"cc":"GMD","symbol":"D","name":"Gambian dalasi"},
  {"cc":"GNF","symbol":"FG","name":"Guinean franc"},
  {"cc":"GQE","symbol":"CFA","name":"Central African CFA franc"},
  {"cc":"GTQ","symbol":"Q","name":"Guatemalan quetzal"},
  {"cc":"GYD","symbol":"GY$","name":"Guyanese dollar"},
  {"cc":"HKD","symbol":"HK$","name":"Hong Kong dollar"},
  {"cc":"HNL","symbol":"L","name":"Honduran lempira"},
  {"cc":"HRK","symbol":"kn","name":"Croatian kuna"},
  {"cc":"HTG","symbol":"G","name":"Haitian gourde"},
  {"cc":"HUF","symbol":"Ft","name":"Hungarian forint"},
  {"cc":"IDR","symbol":"Rp","name":"Indonesian rupiah"},
  {"cc":"ILS","symbol":"\u20aa","name":"Israeli new sheqel"},
  {"cc":"INR","symbol":"\u20B9","name":"Indian rupee"},
  {"cc":"IQD","symbol":"\u062f.\u0639","name":"Iraqi dinar"},
  {"cc":"IRR","symbol":"IRR","name":"Iranian rial"},
  {"cc":"ISK","symbol":"kr","name":"Icelandic kr\u00f3na"},
  {"cc":"JMD","symbol":"J$","name":"Jamaican dollar"},
  {"cc":"JOD","symbol":"JOD","name":"Jordanian dinar"},
  {"cc":"JPY","symbol":"\u00a5","name":"Japanese yen"},
  {"cc":"KES","symbol":"KSh","name":"Kenyan shilling"},
  {"cc":"KGS","symbol":"\u0441\u043e\u043c","name":"Kyrgyzstani som"},
  {"cc":"KHR","symbol":"\u17db","name":"Cambodian riel"},
  {"cc":"KMF","symbol":"KMF","name":"Comorian franc"},
  {"cc":"KPW","symbol":"W","name":"North Korean won"},
  {"cc":"KRW","symbol":"W","name":"South Korean won"},
  {"cc":"KWD","symbol":"KWD","name":"Kuwaiti dinar"},
  {"cc":"KYD","symbol":"KY$","name":"Cayman Islands dollar"},
  {"cc":"KZT","symbol":"T","name":"Kazakhstani tenge"},
  {"cc":"LAK","symbol":"KN","name":"Lao kip"},
  {"cc":"LBP","symbol":"\u00a3","name":"Lebanese lira"},
  {"cc":"LKR","symbol":"Rs","name":"Sri Lankan rupee"},
  {"cc":"LRD","symbol":"L$","name":"Liberian dollar"},
  {"cc":"LSL","symbol":"M","name":"Lesotho loti"},
  {"cc":"LTL","symbol":"Lt","name":"Lithuanian litas"},
  {"cc":"LVL","symbol":"Ls","name":"Latvian lats"},
  {"cc":"LYD","symbol":"LD","name":"Libyan dinar"},
  {"cc":"MAD","symbol":"MAD","name":"Moroccan dirham"},
  {"cc":"MDL","symbol":"MDL","name":"Moldovan leu"},
  {"cc":"MGA","symbol":"FMG","name":"Malagasy ariary"},
  {"cc":"MKD","symbol":"MKD","name":"Macedonian denar"},
  {"cc":"MMK","symbol":"K","name":"Myanma kyat"},
  {"cc":"MNT","symbol":"\u20ae","name":"Mongolian tugrik"},
  {"cc":"MOP","symbol":"P","name":"Macanese pataca"},
  {"cc":"MRO","symbol":"UM","name":"Mauritanian ouguiya"},
  {"cc":"MUR","symbol":"Rs","name":"Mauritian rupee"},
  {"cc":"MVR","symbol":"Rf","name":"Maldivian rufiyaa"},
  {"cc":"MWK","symbol":"MK","name":"Malawian kwacha"},
  {"cc":"MXN","symbol":"$","name":"Mexican peso"},
  {"cc":"MYR","symbol":"RM","name":"Malaysian ringgit"},
  {"cc":"MZM","symbol":"MTn","name":"Mozambican metical"},
  {"cc":"NAD","symbol":"N$","name":"Namibian dollar"},
  {"cc":"NGN","symbol":"\u20a6","name":"Nigerian naira"},
  {"cc":"NIO","symbol":"C$","name":"Nicaraguan c\u00f3rdoba"},
  {"cc":"NOK","symbol":"kr","name":"Norwegian krone"},
  {"cc":"NPR","symbol":"NRs","name":"Nepalese rupee"},
  {"cc":"NZD","symbol":"NZ$","name":"New Zealand dollar"},
  {"cc":"OMR","symbol":"OMR","name":"Omani rial"},
  {"cc":"PAB","symbol":"B./","name":"Panamanian balboa"},
  {"cc":"PEN","symbol":"S/.","name":"Peruvian nuevo sol"},
  {"cc":"PGK","symbol":"K","name":"Papua New Guinean kina"},
  {"cc":"PHP","symbol":"\u20b1","name":"Philippine peso"},
  {"cc":"PKR","symbol":"Rs.","name":"Pakistani rupee"},
  {"cc":"PLN","symbol":"z\u0142","name":"Polish zloty"},
  {"cc":"PYG","symbol":"\u20b2","name":"Paraguayan guarani"},
  {"cc":"QAR","symbol":"QR","name":"Qatari riyal"},
  {"cc":"RON","symbol":"L","name":"Romanian leu"},
  {"cc":"RSD","symbol":"din.","name":"Serbian dinar"},
  {"cc":"RUB","symbol":"R","name":"Russian ruble"},
  {"cc":"SAR","symbol":"SR","name":"Saudi riyal"},
  {"cc":"SBD","symbol":"SI$","name":"Solomon Islands dollar"},
  {"cc":"SCR","symbol":"SR","name":"Seychellois rupee"},
  {"cc":"SDG","symbol":"SDG","name":"Sudanese pound"},
  {"cc":"SEK","symbol":"kr","name":"Swedish krona"},
  {"cc":"SGD","symbol":"S$","name":"Singapore dollar"},
  {"cc":"SHP","symbol":"\u00a3","name":"Saint Helena pound"},
  {"cc":"SLL","symbol":"Le","name":"Sierra Leonean leone"},
  {"cc":"SOS","symbol":"Sh.","name":"Somali shilling"},
  {"cc":"SRD","symbol":"$","name":"Surinamese dollar"},
  {"cc":"SYP","symbol":"LS","name":"Syrian pound"},
  {"cc":"SZL","symbol":"E","name":"Swazi lilangeni"},
  {"cc":"THB","symbol":"\u0e3f","name":"Thai baht"},
  {"cc":"TJS","symbol":"TJS","name":"Tajikistani somoni"},
  {"cc":"TMT","symbol":"m","name":"Turkmen manat"},
  {"cc":"TND","symbol":"DT","name":"Tunisian dinar"},
  {"cc":"TRY","symbol":"TRY","name":"Turkish new lira"},
  {"cc":"TTD","symbol":"TT$","name":"Trinidad and Tobago dollar"},
  {"cc":"TWD","symbol":"NT$","name":"New Taiwan dollar"},
  {"cc":"TZS","symbol":"TZS","name":"Tanzanian shilling"},
  {"cc":"UAH","symbol":"UAH","name":"Ukrainian hryvnia"},
  {"cc":"UGX","symbol":"USh","name":"Ugandan shilling"},
  {"cc":"USD","symbol":"US$","name":"United States dollar"},
  {"cc":"UYU","symbol":"$U","name":"Uruguayan peso"},
  {"cc":"UZS","symbol":"UZS","name":"Uzbekistani som"},
  {"cc":"VEB","symbol":"Bs","name":"Venezuelan bolivar"},
  {"cc":"VND","symbol":"\u20ab","name":"Vietnamese dong"},
  {"cc":"VUV","symbol":"VT","name":"Vanuatu vatu"},
  {"cc":"WST","symbol":"WS$","name":"Samoan tala"},
  {"cc":"XAF","symbol":"CFA","name":"Central African CFA franc"},
  {"cc":"XCD","symbol":"EC$","name":"East Caribbean dollar"},
  {"cc":"XDR","symbol":"SDR","name":"Special Drawing Rights"},
  {"cc":"XOF","symbol":"CFA","name":"West African CFA franc"},
  {"cc":"XPF","symbol":"F","name":"CFP franc"},
  {"cc":"YER","symbol":"YER","name":"Yemeni rial"},
  {"cc":"ZAR","symbol":"R","name":"South African rand"},
  {"cc":"ZMK","symbol":"ZK","name":"Zambian kwacha"},
  {"cc":"ZWR","symbol":"Z$","name":"Zimbabwean dollar"}
];

exports.COUNTRIES = [
  {
    "num_code": "4",
    "alpha_2_code": "AF",
    "alpha_3_code": "AFG",
    "name": "Afghanistan",
    "nationality": ["afghan"]
  },
  {
    "num_code": "248",
    "alpha_2_code": "AX",
    "alpha_3_code": "ALA",
    "name": "Åland Islands",
    "nationality": ["aland island"]
  },
  {
    "num_code": "8",
    "alpha_2_code": "AL",
    "alpha_3_code": "ALB",
    "name": "Albania",
    "nationality": ["albanian"]
  },
  {
    "num_code": "12",
    "alpha_2_code": "DZ",
    "alpha_3_code": "DZA",
    "name": "Algeria",
    "nationality": ["algerian"]
  },
  {
    "num_code": "16",
    "alpha_2_code": "AS",
    "alpha_3_code": "ASM",
    "name": "American Samoa",
    "nationality": ["american samoan"]
  },
  {
    "num_code": "20",
    "alpha_2_code": "AD",
    "alpha_3_code": "AND",
    "name": "Andorra",
    "nationality": ["andorran"]
  },
  {
    "num_code": "24",
    "alpha_2_code": "AO",
    "alpha_3_code": "AGO",
    "name": "Angola",
    "nationality": ["angolan"]
  },
  {
    "num_code": "660",
    "alpha_2_code": "AI",
    "alpha_3_code": "AIA",
    "name": "Anguilla",
    "nationality": ["anguillan"]
  },
  {
    "num_code": "10",
    "alpha_2_code": "AQ",
    "alpha_3_code": "ATA",
    "name": "Antarctica",
    "nationality": ["antarctic"]
  },
  {
    "num_code": "28",
    "alpha_2_code": "AG",
    "alpha_3_code": "ATG",
    "name": "Antigua and Barbuda",
    "nationality": ["antiguan", "barbudan"]
  },
  {
    "num_code": "32",
    "alpha_2_code": "AR",
    "alpha_3_code": "ARG",
    "name": "Argentina",
    "nationality": ["argentinian"]
  },
  {
    "num_code": "51",
    "alpha_2_code": "AM",
    "alpha_3_code": "ARM",
    "name": "Armenia",
    "nationality": ["armenian"]
  },
  {
    "num_code": "533",
    "alpha_2_code": "AW",
    "alpha_3_code": "ABW",
    "name": "Aruba",
    "nationality": ["aruban"]
  },
  {
    "num_code": "36",
    "alpha_2_code": "AU",
    "alpha_3_code": "AUS",
    "name": "Australia",
    "nationality": ["australian"]
  },
  {
    "num_code": "40",
    "alpha_2_code": "AT",
    "alpha_3_code": "AUT",
    "name": "Austria",
    "nationality": ["austrian"]
  },
  {
    "num_code": "31",
    "alpha_2_code": "AZ",
    "alpha_3_code": "AZE",
    "name": "Azerbaijan",
    "nationality": ["azerbaijani", "azeri"]
  },
  {
    "num_code": "44",
    "alpha_2_code": "BS",
    "alpha_3_code": "BHS",
    "name": "Bahamas",
    "nationality": ["bahamian"]
  },
  {
    "num_code": "48",
    "alpha_2_code": "BH",
    "alpha_3_code": "BHR",
    "name": "Bahrain",
    "nationality": ["bahraini"]
  },
  {
    "num_code": "50",
    "alpha_2_code": "BD",
    "alpha_3_code": "BGD",
    "name": "Bangladesh",
    "nationality": ["bangladeshi"]
  },
  {
    "num_code": "52",
    "alpha_2_code": "BB",
    "alpha_3_code": "BRB",
    "name": "Barbados",
    "nationality": ["barbadian"]
  },
  {
    "num_code": "112",
    "alpha_2_code": "BY",
    "alpha_3_code": "BLR",
    "name": "Belarus",
    "nationality": ["belarusian"]
  },
  {
    "num_code": "56",
    "alpha_2_code": "BE",
    "alpha_3_code": "BEL",
    "name": "Belgium",
    "nationality": ["belgian"]
  },
  {
    "num_code": "84",
    "alpha_2_code": "BZ",
    "alpha_3_code": "BLZ",
    "name": "Belize",
    "nationality": ["belizean"]
  },
  {
    "num_code": "204",
    "alpha_2_code": "BJ",
    "alpha_3_code": "BEN",
    "name": "Benin",
    "nationality": ["beninese", "beninois"]
  },
  {
    "num_code": "60",
    "alpha_2_code": "BM",
    "alpha_3_code": "BMU",
    "name": "Bermuda",
    "nationality": ["bermudian", "bermudan"]
  },
  {
    "num_code": "64",
    "alpha_2_code": "BT",
    "alpha_3_code": "BTN",
    "name": "Bhutan",
    "nationality": ["bhutanese"]
  },
  {
    "num_code": "68",
    "alpha_2_code": "BO",
    "alpha_3_code": "BOL",
    "name": "Bolivia",
    "nationality": ["bolivian"]
  },
  {
    "num_code": "535",
    "alpha_2_code": "BQ",
    "alpha_3_code": "BES",
    "name": "Bonaire",
    "nationality": ["bonaire"]
  },
  {
    "num_code": "70",
    "alpha_2_code": "BA",
    "alpha_3_code": "BIH",
    "name": "Bosnia and Herzegovina",
    "nationality": ["bosnian", "herzegovinian"]
  },
  {
    "num_code": "72",
    "alpha_2_code": "BW",
    "alpha_3_code": "BWA",
    "name": "Botswana",
    "nationality": ["motswana", "botswanan"]
  },
  {
    "num_code": "74",
    "alpha_2_code": "BV",
    "alpha_3_code": "BVT",
    "name": "Bouvet Island",
    "nationality": ["bouvet island"]
  },
  {
    "num_code": "76",
    "alpha_2_code": "BR",
    "alpha_3_code": "BRA",
    "name": "Brazil",
    "nationality": ["brazilian"]
  },
  {
    "num_code": "86",
    "alpha_2_code": "IO",
    "alpha_3_code": "IOT",
    "name": "British Indian Ocean Territory",
    "nationality": ["biot"]
  },
  {
    "num_code": "96",
    "alpha_2_code": "BN",
    "alpha_3_code": "BRN",
    "name": "Brunei Darussalam",
    "nationality": ["bruneian"]
  },
  {
    "num_code": "100",
    "alpha_2_code": "BG",
    "alpha_3_code": "BGR",
    "name": "Bulgaria",
    "nationality": ["bulgarian"]
  },
  {
    "num_code": "854",
    "alpha_2_code": "BF",
    "alpha_3_code": "BFA",
    "name": "Burkina Faso",
    "nationality": ["burkinan"]
  },
  {
    "num_code": "108",
    "alpha_2_code": "BI",
    "alpha_3_code": "BDI",
    "name": "Burundi",
    "nationality": ["burundian"]
  },
  {
    "num_code": "132",
    "alpha_2_code": "CV",
    "alpha_3_code": "CPV",
    "name": "Cape Verde",
    "nationality": ["cabo verdean"]
  },
  {
    "num_code": "116",
    "alpha_2_code": "KH",
    "alpha_3_code": "KHM",
    "name": "Cambodia",
    "nationality": ["cambodian"]
  },
  {
    "num_code": "120",
    "alpha_2_code": "CM",
    "alpha_3_code": "CMR",
    "name": "Cameroon",
    "nationality": ["cameroonian"]
  },
  {
    "num_code": "124",
    "alpha_2_code": "CA",
    "alpha_3_code": "CAN",
    "name": "Canada",
    "nationality": ["canadian"]
  },
  {
    "num_code": "136",
    "alpha_2_code": "KY",
    "alpha_3_code": "CYM",
    "name": "Cayman Islands",
    "nationality": ["caymanian"]
  },
  {
    "num_code": "140",
    "alpha_2_code": "CF",
    "alpha_3_code": "CAF",
    "name": "Central African Republic",
    "nationality": ["central african"]
  },
  {
    "num_code": "148",
    "alpha_2_code": "TD",
    "alpha_3_code": "TCD",
    "name": "Chad",
    "nationality": ["chadian"]
  },
  {
    "num_code": "152",
    "alpha_2_code": "CL",
    "alpha_3_code": "CHL",
    "name": "Chile",
    "nationality": ["chilean"]
  },
  {
    "num_code": "156",
    "alpha_2_code": "CN",
    "alpha_3_code": "CHN",
    "name": "China",
    "nationality": ["chinese"]
  },
  {
    "num_code": "162",
    "alpha_2_code": "CX",
    "alpha_3_code": "CXR",
    "name": "Christmas Island",
    "nationality": ["christmas island"]
  },
  {
    "num_code": "166",
    "alpha_2_code": "CC",
    "alpha_3_code": "CCK",
    "name": "Cocos (Keeling) Islands",
    "nationality": ["cocos island"]
  },
  {
    "num_code": "170",
    "alpha_2_code": "CO",
    "alpha_3_code": "COL",
    "name": "Colombia",
    "nationality": ["colombian"]
  },
  {
    "num_code": "174",
    "alpha_2_code": "KM",
    "alpha_3_code": "COM",
    "name": "Comoros",
    "nationality": ["comoran", "comorian"]
  },
  {
    "num_code": "178",
    "alpha_2_code": "CG",
    "alpha_3_code": "COG",
    "name": "Congo",
    "nationality": ["congolese"]
  },
  {
    "num_code": "180",
    "alpha_2_code": "CD",
    "alpha_3_code": "COD",
    "name": "Congo, The Democratic Republic of The",
    "nationality": ["congolese"]
  },
  {
    "num_code": "184",
    "alpha_2_code": "CK",
    "alpha_3_code": "COK",
    "name": "Cook Islands",
    "nationality": ["cook island"]
  },
  {
    "num_code": "188",
    "alpha_2_code": "CR",
    "alpha_3_code": "CRI",
    "name": "Costa Rica",
    "nationality": ["costa rican"]
  },
  {
    "num_code": "384",
    "alpha_2_code": "CI",
    "alpha_3_code": "CIV",
    "name": "Cote d'Ivoire",
    "nationality": ["ivorian"]
  },
  {
    "num_code": "191",
    "alpha_2_code": "HR",
    "alpha_3_code": "HRV",
    "name": "Croatia",
    "nationality": ["croatian"]
  },
  {
    "num_code": "192",
    "alpha_2_code": "CU",
    "alpha_3_code": "CUB",
    "name": "Cuba",
    "nationality": ["cuban"]
  },
  {
    "num_code": "531",
    "alpha_2_code": "CW",
    "alpha_3_code": "CUW",
    "name": "Curacao",
    "nationality": ["curaçaoan"]
  },
  {
    "num_code": "196",
    "alpha_2_code": "CY",
    "alpha_3_code": "CYP",
    "name": "Cyprus",
    "nationality": ["cypriot"]
  },
  {
    "num_code": "203",
    "alpha_2_code": "CZ",
    "alpha_3_code": "CZE",
    "name": "Czech Republic",
    "nationality": ["czech"]
  },
  {
    "num_code": "208",
    "alpha_2_code": "DK",
    "alpha_3_code": "DNK",
    "name": "Denmark",
    "nationality": ["danish"]
  },
  {
    "num_code": "262",
    "alpha_2_code": "DJ",
    "alpha_3_code": "DJI",
    "name": "Djibouti",
    "nationality": ["djiboutian"]
  },
  {
    "num_code": "212",
    "alpha_2_code": "DM",
    "alpha_3_code": "DMA",
    "name": "Dominica",
    "nationality": ["dominican"]
  },
  {
    "num_code": "214",
    "alpha_2_code": "DO",
    "alpha_3_code": "DOM",
    "name": "Dominican Republic",
    "nationality": ["dominican"]
  },
  {
    "num_code": "218",
    "alpha_2_code": "EC",
    "alpha_3_code": "ECU",
    "name": "Ecuador",
    "nationality": ["ecuadorian"]
  },
  {
    "num_code": "818",
    "alpha_2_code": "EG",
    "alpha_3_code": "EGY",
    "name": "Egypt",
    "nationality": ["egyptian"]
  },
  {
    "num_code": "222",
    "alpha_2_code": "SV",
    "alpha_3_code": "SLV",
    "name": "El Salvador",
    "nationality": ["salvadoran"]
  },
  {
    "num_code": "226",
    "alpha_2_code": "GQ",
    "alpha_3_code": "GNQ",
    "name": "Equatorial Guinea",
    "nationality": ["equatorial guinean", "equatoguinean"]
  },
  {
    "num_code": "232",
    "alpha_2_code": "ER",
    "alpha_3_code": "ERI",
    "name": "Eritrea",
    "nationality": ["eritrean"]
  },
  {
    "num_code": "233",
    "alpha_2_code": "EE",
    "alpha_3_code": "EST",
    "name": "Estonia",
    "nationality": ["estonian"]
  },
  {
    "num_code": "231",
    "alpha_2_code": "ET",
    "alpha_3_code": "ETH",
    "name": "Ethiopia",
    "nationality": ["ethiopian"]
  },
  {
    "num_code": "238",
    "alpha_2_code": "FK",
    "alpha_3_code": "FLK",
    "name": "Falkland Islands (Malvinas)",
    "nationality": ["falkland island"]
  },
  {
    "num_code": "234",
    "alpha_2_code": "FO",
    "alpha_3_code": "FRO",
    "name": "Faroe Islands",
    "nationality": ["faroese"]
  },
  {
    "num_code": "242",
    "alpha_2_code": "FJ",
    "alpha_3_code": "FJI",
    "name": "Fiji",
    "nationality": ["fijian"]
  },
  {
    "num_code": "246",
    "alpha_2_code": "FI",
    "alpha_3_code": "FIN",
    "name": "Finland",
    "nationality": ["finnish"]
  },
  {
    "num_code": "250",
    "alpha_2_code": "FR",
    "alpha_3_code": "FRA",
    "name": "France",
    "nationality": ["french"]
  },
  {
    "num_code": "254",
    "alpha_2_code": "GF",
    "alpha_3_code": "GUF",
    "name": "French Guiana",
    "nationality": ["french guianese"]
  },
  {
    "num_code": "258",
    "alpha_2_code": "PF",
    "alpha_3_code": "PYF",
    "name": "French Polynesia",
    "nationality": ["french polynesian"]
  },
  {
    "num_code": "260",
    "alpha_2_code": "TF",
    "alpha_3_code": "ATF",
    "name": "French Southern Territories",
    "nationality": ["french southern territories"]
  },
  {
    "num_code": "266",
    "alpha_2_code": "GA",
    "alpha_3_code": "GAB",
    "name": "Gabon",
    "nationality": ["gabonese"]
  },
  {
    "num_code": "270",
    "alpha_2_code": "GM",
    "alpha_3_code": "GMB",
    "name": "Gambia",
    "nationality": ["gambian"]
  },
  {
    "num_code": "268",
    "alpha_2_code": "GE",
    "alpha_3_code": "GEO",
    "name": "Georgia",
    "nationality": ["georgian"]
  },
  {
    "num_code": "276",
    "alpha_2_code": "DE",
    "alpha_3_code": "DEU",
    "name": "Germany",
    "nationality": ["german"]
  },
  {
    "num_code": "288",
    "alpha_2_code": "GH",
    "alpha_3_code": "GHA",
    "name": "Ghana",
    "nationality": ["ghanaian"]
  },
  {
    "num_code": "292",
    "alpha_2_code": "GI",
    "alpha_3_code": "GIB",
    "name": "Gibraltar",
    "nationality": ["gibraltar"]
  },
  {
    "num_code": "300",
    "alpha_2_code": "GR",
    "alpha_3_code": "GRC",
    "name": "Greece",
    "nationality": ["greek", "hellenic"]
  },
  {
    "num_code": "304",
    "alpha_2_code": "GL",
    "alpha_3_code": "GRL",
    "name": "Greenland",
    "nationality": ["greenlandic"]
  },
  {
    "num_code": "308",
    "alpha_2_code": "GD",
    "alpha_3_code": "GRD",
    "name": "Grenada",
    "nationality": ["grenadian"]
  },
  {
    "num_code": "312",
    "alpha_2_code": "GP",
    "alpha_3_code": "GLP",
    "name": "Guadeloupe",
    "nationality": ["guadeloupe"]
  },
  {
    "num_code": "316",
    "alpha_2_code": "GU",
    "alpha_3_code": "GUM",
    "name": "Guam",
    "nationality": ["guamanian", "guambat"]
  },
  {
    "num_code": "320",
    "alpha_2_code": "GT",
    "alpha_3_code": "GTM",
    "name": "Guatemala",
    "nationality": ["guatemalan"]
  },
  {
    "num_code": "831",
    "alpha_2_code": "GG",
    "alpha_3_code": "GGY",
    "name": "Guernsey",
    "nationality": ["channel island"]
  },
  {
    "num_code": "324",
    "alpha_2_code": "GN",
    "alpha_3_code": "GIN",
    "name": "Guinea",
    "nationality": ["guinean"]
  },
  {
    "num_code": "624",
    "alpha_2_code": "GW",
    "alpha_3_code": "GNB",
    "name": "Guinea-bissau",
    "nationality": ["bissau-guinean"]
  },
  {
    "num_code": "328",
    "alpha_2_code": "GY",
    "alpha_3_code": "GUY",
    "name": "Guyana",
    "nationality": ["guyanese"]
  },
  {
    "num_code": "332",
    "alpha_2_code": "HT",
    "alpha_3_code": "HTI",
    "name": "Haiti",
    "nationality": ["haitian"]
  },
  {
    "num_code": "334",
    "alpha_2_code": "HM",
    "alpha_3_code": "HMD",
    "name": "Heard Island and McDonald Islands",
    "nationality": ["heard island", "mcDonald islands"]
  },
  {
    "num_code": "336",
    "alpha_2_code": "VA",
    "alpha_3_code": "VAT",
    "name": "Holy See (Vatican City State)",
    "nationality": ["vatican"]
  },
  {
    "num_code": "340",
    "alpha_2_code": "HN",
    "alpha_3_code": "HND",
    "name": "Honduras",
    "nationality": ["honduran"]
  },
  {
    "num_code": "344",
    "alpha_2_code": "HK",
    "alpha_3_code": "HKG",
    "name": "Hong Kong",
    "nationality": ["hong kong", "hong kongese"]
  },
  {
    "num_code": "348",
    "alpha_2_code": "HU",
    "alpha_3_code": "HUN",
    "name": "Hungary",
    "nationality": ["hungarian", "magyar"]
  },
  {
    "num_code": "352",
    "alpha_2_code": "IS",
    "alpha_3_code": "ISL",
    "name": "Iceland",
    "nationality": ["icelandic"]
  },
  {
    "num_code": "356",
    "alpha_2_code": "IN",
    "alpha_3_code": "IND",
    "name": "India",
    "nationality": ["indian"]
  },
  {
    "num_code": "360",
    "alpha_2_code": "ID",
    "alpha_3_code": "IDN",
    "name": "Indonesia",
    "nationality": ["indonesian"]
  },
  {
    "num_code": "364",
    "alpha_2_code": "IR",
    "alpha_3_code": "IRN",
    "name": "Iran, Islamic Republic of",
    "nationality": ["iranian", "persian"]
  },
  {
    "num_code": "368",
    "alpha_2_code": "IQ",
    "alpha_3_code": "IRQ",
    "name": "Iraq",
    "nationality": ["iraqi"]
  },
  {
    "num_code": "372",
    "alpha_2_code": "IE",
    "alpha_3_code": "IRL",
    "name": "Ireland",
    "nationality": ["irish"]
  },
  {
    "num_code": "833",
    "alpha_2_code": "IM",
    "alpha_3_code": "IMN",
    "name": "Isle of Man",
    "nationality": ["manx"]
  },
  {
    "num_code": "376",
    "alpha_2_code": "IL",
    "alpha_3_code": "ISR",
    "name": "Israel",
    "nationality": ["israeli"]
  },
  {
    "num_code": "380",
    "alpha_2_code": "IT",
    "alpha_3_code": "ITA",
    "name": "Italy",
    "nationality": ["italian"]
  },
  {
    "num_code": "388",
    "alpha_2_code": "JM",
    "alpha_3_code": "JAM",
    "name": "Jamaica",
    "nationality": ["jamaican"]
  },
  {
    "num_code": "392",
    "alpha_2_code": "JP",
    "alpha_3_code": "JPN",
    "name": "Japan",
    "nationality": ["japanese"]
  },
  {
    "num_code": "832",
    "alpha_2_code": "JE",
    "alpha_3_code": "JEY",
    "name": "Jersey",
    "nationality": ["channel island"]
  },
  {
    "num_code": "400",
    "alpha_2_code": "JO",
    "alpha_3_code": "JOR",
    "name": "Jordan",
    "nationality": ["jordanian"]
  },
  {
    "num_code": "398",
    "alpha_2_code": "KZ",
    "alpha_3_code": "KAZ",
    "name": "Kazakhstan",
    "nationality": ["kazakhstani", "kazakh"]
  },
  {
    "num_code": "404",
    "alpha_2_code": "KE",
    "alpha_3_code": "KEN",
    "name": "Kenya",
    "nationality": ["kenyan"]
  },
  {
    "num_code": "296",
    "alpha_2_code": "KI",
    "alpha_3_code": "KIR",
    "name": "Kiribati",
    "nationality": ["i-kiribati"]
  },
  {
    "num_code": "408",
    "alpha_2_code": "KP",
    "alpha_3_code": "PRK",
    "name": "Korea, Democratic People's Republic of",
    "nationality": ["north korean"]
  },
  {
    "num_code": "410",
    "alpha_2_code": "KR",
    "alpha_3_code": "KOR",
    "name": "Korea, Republic of",
    "nationality": ["south korean"]
  },
  {
    "num_code": "414",
    "alpha_2_code": "KW",
    "alpha_3_code": "KWT",
    "name": "Kuwait",
    "nationality": ["kuwaiti"]
  },
  {
    "num_code": "417",
    "alpha_2_code": "KG",
    "alpha_3_code": "KGZ",
    "name": "Kyrgyzstan",
    "nationality": ["kyrgyzstani", "kyrgyz", "kirgiz", "kirghiz"]
  },
  {
    "num_code": "418",
    "alpha_2_code": "LA",
    "alpha_3_code": "LAO",
    "name": "Lao People's Democratic Republic",
    "nationality": ["lao", "laotian"]
  },
  {
    "num_code": "428",
    "alpha_2_code": "LV",
    "alpha_3_code": "LVA",
    "name": "Latvia",
    "nationality": ["latvian"]
  },
  {
    "num_code": "422",
    "alpha_2_code": "LB",
    "alpha_3_code": "LBN",
    "name": "Lebanon",
    "nationality": ["lebanese"]
  },
  {
    "num_code": "426",
    "alpha_2_code": "LS",
    "alpha_3_code": "LSO",
    "name": "Lesotho",
    "nationality": ["basotho"]
  },
  {
    "num_code": "430",
    "alpha_2_code": "LR",
    "alpha_3_code": "LBR",
    "name": "Liberia",
    "nationality": ["liberian"]
  },
  {
    "num_code": "434",
    "alpha_2_code": "LY",
    "alpha_3_code": "LBY",
    "name": "Libyan Arab Jamahiriya",
    "nationality": ["libyan"]
  },
  {
    "num_code": "438",
    "alpha_2_code": "LI",
    "alpha_3_code": "LIE",
    "name": "Liechtenstein",
    "nationality": ["liechtenstein"]
  },
  {
    "num_code": "440",
    "alpha_2_code": "LT",
    "alpha_3_code": "LTU",
    "name": "Lithuania",
    "nationality": ["lithuanian"]
  },
  {
    "num_code": "442",
    "alpha_2_code": "LU",
    "alpha_3_code": "LUX",
    "name": "Luxembourg",
    "nationality": ["luxembourg", "luxembourgish"]
  },
  {
    "num_code": "446",
    "alpha_2_code": "MO",
    "alpha_3_code": "MAC",
    "name": "Macao",
    "nationality": ["macanese", "chinese"]
  },
  {
    "num_code": "807",
    "alpha_2_code": "MK",
    "alpha_3_code": "MKD",
    "name": "Macedonia, The Former Yugoslav Republic of",
    "nationality": ["macedonian"]
  },
  {
    "num_code": "450",
    "alpha_2_code": "MG",
    "alpha_3_code": "MDG",
    "name": "Madagascar",
    "nationality": ["malagasy"]
  },
  {
    "num_code": "454",
    "alpha_2_code": "MW",
    "alpha_3_code": "MWI",
    "name": "Malawi",
    "nationality": ["malawian"]
  },
  {
    "num_code": "458",
    "alpha_2_code": "MY",
    "alpha_3_code": "MYS",
    "name": "Malaysia",
    "nationality": ["malaysian"]
  },
  {
    "num_code": "462",
    "alpha_2_code": "MV",
    "alpha_3_code": "MDV",
    "name": "Maldives",
    "nationality": ["maldivian"]
  },
  {
    "num_code": "466",
    "alpha_2_code": "ML",
    "alpha_3_code": "MLI",
    "name": "Mali",
    "nationality": ["malian", "malinese"]
  },
  {
    "num_code": "470",
    "alpha_2_code": "MT",
    "alpha_3_code": "MLT",
    "name": "Malta",
    "nationality": ["maltese"]
  },
  {
    "num_code": "584",
    "alpha_2_code": "MH",
    "alpha_3_code": "MHL",
    "name": "Marshall Islands",
    "nationality": ["marshallese"]
  },
  {
    "num_code": "474",
    "alpha_2_code": "MQ",
    "alpha_3_code": "MTQ",
    "name": "Martinique",
    "nationality": ["martiniquais", "martinican"]
  },
  {
    "num_code": "478",
    "alpha_2_code": "MR",
    "alpha_3_code": "MRT",
    "name": "Mauritania",
    "nationality": ["mauritanian"]
  },
  {
    "num_code": "480",
    "alpha_2_code": "MU",
    "alpha_3_code": "MUS",
    "name": "Mauritius",
    "nationality": ["mauritian"]
  },
  {
    "num_code": "175",
    "alpha_2_code": "YT",
    "alpha_3_code": "MYT",
    "name": "Mayotte",
    "nationality": ["mahoran"]
  },
  {
    "num_code": "484",
    "alpha_2_code": "MX",
    "alpha_3_code": "MEX",
    "name": "Mexico",
    "nationality": ["mexican"]
  },
  {
    "num_code": "583",
    "alpha_2_code": "FM",
    "alpha_3_code": "FSM",
    "name": "Micronesia, Federated States of",
    "nationality": ["micronesian"]
  },
  {
    "num_code": "498",
    "alpha_2_code": "MD",
    "alpha_3_code": "MDA",
    "name": "Moldova, Republic of",
    "nationality": ["moldovan"]
  },
  {
    "num_code": "492",
    "alpha_2_code": "MC",
    "alpha_3_code": "MCO",
    "name": "Monaco",
    "nationality": ["monegasque", "monacan"]
  },
  {
    "num_code": "496",
    "alpha_2_code": "MN",
    "alpha_3_code": "MNG",
    "name": "Mongolia",
    "nationality": ["mongolian"]
  },
  {
    "num_code": "499",
    "alpha_2_code": "ME",
    "alpha_3_code": "MNE",
    "name": "Montenegro",
    "nationality": ["montenegrin"]
  },
  {
    "num_code": "500",
    "alpha_2_code": "MS",
    "alpha_3_code": "MSR",
    "name": "Montserrat",
    "nationality": ["montserratian"]
  },
  {
    "num_code": "504",
    "alpha_2_code": "MA",
    "alpha_3_code": "MAR",
    "name": "Morocco",
    "nationality": ["moroccan"]
  },
  {
    "num_code": "508",
    "alpha_2_code": "MZ",
    "alpha_3_code": "MOZ",
    "name": "Mozambique",
    "nationality": ["mozambican"]
  },
  {
    "num_code": "104",
    "alpha_2_code": "MM",
    "alpha_3_code": "MMR",
    "name": "Myanmar",
    "nationality": ["burmese"]
  },
  {
    "num_code": "516",
    "alpha_2_code": "NA",
    "alpha_3_code": "NAM",
    "name": "Namibia",
    "nationality": ["namibian"]
  },
  {
    "num_code": "520",
    "alpha_2_code": "NR",
    "alpha_3_code": "NRU",
    "name": "Nauru",
    "nationality": ["nauruan"]
  },
  {
    "num_code": "524",
    "alpha_2_code": "NP",
    "alpha_3_code": "NPL",
    "name": "Nepal",
    "nationality": ["nepali", "nepalese"]
  },
  {
    "num_code": "528",
    "alpha_2_code": "NL",
    "alpha_3_code": "NLD",
    "name": "Netherlands",
    "nationality": ["dutch", "netherlandic"]
  },
  {
    "num_code": "540",
    "alpha_2_code": "NC",
    "alpha_3_code": "NCL",
    "name": "New Caledonia",
    "nationality": ["new caledonian"]
  },
  {
    "num_code": "554",
    "alpha_2_code": "NZ",
    "alpha_3_code": "NZL",
    "name": "New Zealand",
    "nationality": ["new zealand", "nz"]
  },
  {
    "num_code": "558",
    "alpha_2_code": "NI",
    "alpha_3_code": "NIC",
    "name": "Nicaragua",
    "nationality": ["nicaraguan"]
  },
  {
    "num_code": "562",
    "alpha_2_code": "NE",
    "alpha_3_code": "NER",
    "name": "Niger",
    "nationality": ["nigerien"]
  },
  {
    "num_code": "566",
    "alpha_2_code": "NG",
    "alpha_3_code": "NGA",
    "name": "Nigeria",
    "nationality": ["nigerian"]
  },
  {
    "num_code": "570",
    "alpha_2_code": "NU",
    "alpha_3_code": "NIU",
    "name": "Niue",
    "nationality": ["niuean"]
  },
  {
    "num_code": "574",
    "alpha_2_code": "NF",
    "alpha_3_code": "NFK",
    "name": "Norfolk Island",
    "nationality": ["norfolk island"]
  },
  {
    "num_code": "580",
    "alpha_2_code": "MP",
    "alpha_3_code": "MNP",
    "name": "Northern Mariana Islands",
    "nationality": ["northern marianan"]
  },
  {
    "num_code": "578",
    "alpha_2_code": "NO",
    "alpha_3_code": "NOR",
    "name": "Norway",
    "nationality": ["norwegian"]
  },
  {
    "num_code": "512",
    "alpha_2_code": "OM",
    "alpha_3_code": "OMN",
    "name": "Oman",
    "nationality": ["omani"]
  },
  {
    "num_code": "586",
    "alpha_2_code": "PK",
    "alpha_3_code": "PAK",
    "name": "Pakistan",
    "nationality": ["pakistani"]
  },
  {
    "num_code": "585",
    "alpha_2_code": "PW",
    "alpha_3_code": "PLW",
    "name": "Palau",
    "nationality": ["palauan"]
  },
  {
    "num_code": "275",
    "alpha_2_code": "PS",
    "alpha_3_code": "PSE",
    "name": "Palestinian Territory, Occupied",
    "nationality": ["palestinian"]
  },
  {
    "num_code": "591",
    "alpha_2_code": "PA",
    "alpha_3_code": "PAN",
    "name": "Panama",
    "nationality": ["panamanian"]
  },
  {
    "num_code": "598",
    "alpha_2_code": "PG",
    "alpha_3_code": "PNG",
    "name": "Papua New Guinea",
    "nationality": ["papua new guinean", "papuan"]
  },
  {
    "num_code": "600",
    "alpha_2_code": "PY",
    "alpha_3_code": "PRY",
    "name": "Paraguay",
    "nationality": ["paraguayan"]
  },
  {
    "num_code": "604",
    "alpha_2_code": "PE",
    "alpha_3_code": "PER",
    "name": "Peru",
    "nationality": ["peruvian"]
  },
  {
    "num_code": "608",
    "alpha_2_code": "PH",
    "alpha_3_code": "PHL",
    "name": "Philippines",
    "nationality": ["philippine", "filipino"]
  },
  {
    "num_code": "612",
    "alpha_2_code": "PN",
    "alpha_3_code": "PCN",
    "name": "Pitcairn",
    "nationality": ["pitcairn island"]
  },
  {
    "num_code": "616",
    "alpha_2_code": "PL",
    "alpha_3_code": "POL",
    "name": "Poland",
    "nationality": ["polish"]
  },
  {
    "num_code": "620",
    "alpha_2_code": "PT",
    "alpha_3_code": "PRT",
    "name": "Portugal",
    "nationality": ["portuguese"]
  },
  {
    "num_code": "630",
    "alpha_2_code": "PR",
    "alpha_3_code": "PRI",
    "name": "Puerto Rico",
    "nationality": ["puerto rican"]
  },
  {
    "num_code": "634",
    "alpha_2_code": "QA",
    "alpha_3_code": "QAT",
    "name": "Qatar",
    "nationality": ["qatari"]
  },
  {
    "num_code": "638",
    "alpha_2_code": "RE",
    "alpha_3_code": "REU",
    "name": "Reunion",
    "nationality": ["reunionese"]
  },
  {
    "num_code": "642",
    "alpha_2_code": "RO",
    "alpha_3_code": "ROU",
    "name": "Romania",
    "nationality": ["romanian"]
  },
  {
    "num_code": "643",
    "alpha_2_code": "RU",
    "alpha_3_code": "RUS",
    "name": "Russian Federation",
    "nationality": ["russian"]
  },
  {
    "num_code": "646",
    "alpha_2_code": "RW",
    "alpha_3_code": "RWA",
    "name": "Rwanda",
    "nationality": ["rwandan"]
  },
  {
    "num_code": "652",
    "alpha_2_code": "BL",
    "alpha_3_code": "BLM",
    "name": "Saint Barthelemy",
    "nationality": ["barthelemois"]
  },
  {
    "num_code": "654",
    "alpha_2_code": "SH",
    "alpha_3_code": "SHN",
    "name": "Saint Helena",
    "nationality": ["saint helenian"]
  },
  {
    "num_code": "659",
    "alpha_2_code": "KN",
    "alpha_3_code": "KNA",
    "name": "Saint Kitts and Nevis",
    "nationality": ["kittitian", "nevisian"]
  },
  {
    "num_code": "662",
    "alpha_2_code": "LC",
    "alpha_3_code": "LCA",
    "name": "Saint Lucia",
    "nationality": ["saint lucian"]
  },
  {
    "num_code": "663",
    "alpha_2_code": "MF",
    "alpha_3_code": "MAF",
    "name": "Saint Martin (French part)",
    "nationality": ["saint-martinoise"]
  },
  {
    "num_code": "666",
    "alpha_2_code": "PM",
    "alpha_3_code": "SPM",
    "name": "Saint Pierre and Miquelon",
    "nationality": ["saint-pierrais", "miquelonnais"]
  },
  {
    "num_code": "670",
    "alpha_2_code": "VC",
    "alpha_3_code": "VCT",
    "name": "Saint Vincent and the Grenadines",
    "nationality": ["saint vincentian", "vincentian"]
  },
  {
    "num_code": "882",
    "alpha_2_code": "WS",
    "alpha_3_code": "WSM",
    "name": "Samoa",
    "nationality": ["samoan"]
  },
  {
    "num_code": "674",
    "alpha_2_code": "SM",
    "alpha_3_code": "SMR",
    "name": "San Marino",
    "nationality": ["sammarinese"]
  },
  {
    "num_code": "678",
    "alpha_2_code": "ST",
    "alpha_3_code": "STP",
    "name": "Sao Tome and Principe",
    "nationality": ["sao tomean"]
  },
  {
    "num_code": "682",
    "alpha_2_code": "SA",
    "alpha_3_code": "SAU",
    "name": "Saudi Arabia",
    "nationality": ["saudi", "saudi arabian"]
  },
  {
    "num_code": "686",
    "alpha_2_code": "SN",
    "alpha_3_code": "SEN",
    "name": "Senegal",
    "nationality": ["senegalese"]
  },
  {
    "num_code": "688",
    "alpha_2_code": "RS",
    "alpha_3_code": "SRB",
    "name": "Serbia",
    "nationality": ["serbian"]
  },
  {
    "num_code": "690",
    "alpha_2_code": "SC",
    "alpha_3_code": "SYC",
    "name": "Seychelles",
    "nationality": ["seychellois"]
  },
  {
    "num_code": "694",
    "alpha_2_code": "SL",
    "alpha_3_code": "SLE",
    "name": "Sierra Leone",
    "nationality": ["sierra leonean"]
  },
  {
    "num_code": "702",
    "alpha_2_code": "SG",
    "alpha_3_code": "SGP",
    "name": "Singapore",
    "nationality": ["singaporean"]
  },
  {
    "num_code": "534",
    "alpha_2_code": "SX",
    "alpha_3_code": "SXM",
    "name": "Sint Maarten (Dutch part)",
    "nationality": ["sint maarten"]
  },
  {
    "num_code": "703",
    "alpha_2_code": "SK",
    "alpha_3_code": "SVK",
    "name": "Slovakia",
    "nationality": ["slovak"]
  },
  {
    "num_code": "705",
    "alpha_2_code": "SI",
    "alpha_3_code": "SVN",
    "name": "Slovenia",
    "nationality": ["slovenian", "slovene"]
  },
  {
    "num_code": "90",
    "alpha_2_code": "SB",
    "alpha_3_code": "SLB",
    "name": "Solomon Islands",
    "nationality": ["solomon island"]
  },
  {
    "num_code": "706",
    "alpha_2_code": "SO",
    "alpha_3_code": "SOM",
    "name": "Somalia",
    "nationality": ["somali", "somalian"]
  },
  {
    "num_code": "710",
    "alpha_2_code": "ZA",
    "alpha_3_code": "ZAF",
    "name": "South Africa",
    "nationality": ["south african"]
  },
  {
    "num_code": "239",
    "alpha_2_code": "GS",
    "alpha_3_code": "SGS",
    "name": "South Georgia and the South Sandwich Islands",
    "nationality": ["south georgia", "south sandwich islands"]
  },
  {
    "num_code": "728",
    "alpha_2_code": "SS",
    "alpha_3_code": "SSD",
    "name": "South Sudan",
    "nationality": ["south sudanese"]
  },
  {
    "num_code": "724",
    "alpha_2_code": "ES",
    "alpha_3_code": "ESP",
    "name": "Spain",
    "nationality": ["spanish"]
  },
  {
    "num_code": "144",
    "alpha_2_code": "LK",
    "alpha_3_code": "LKA",
    "name": "Sri Lanka",
    "nationality": ["sri lankan"]
  },
  {
    "num_code": "729",
    "alpha_2_code": "SD",
    "alpha_3_code": "SDN",
    "name": "Sudan",
    "nationality": ["sudanese"]
  },
  {
    "num_code": "740",
    "alpha_2_code": "SR",
    "alpha_3_code": "SUR",
    "name": "Suriname",
    "nationality": ["surinamese"]
  },
  {
    "num_code": "744",
    "alpha_2_code": "SJ",
    "alpha_3_code": "SJM",
    "name": "Svalbard and Jan Mayen",
    "nationality": ["svalbard"]
  },
  {
    "num_code": "748",
    "alpha_2_code": "SZ",
    "alpha_3_code": "SWZ",
    "name": "Swaziland",
    "nationality": ["swazi"]
  },
  {
    "num_code": "752",
    "alpha_2_code": "SE",
    "alpha_3_code": "SWE",
    "name": "Sweden",
    "nationality": ["swedish"]
  },
  {
    "num_code": "756",
    "alpha_2_code": "CH",
    "alpha_3_code": "CHE",
    "name": "Switzerland",
    "nationality": ["swiss"]
  },
  {
    "num_code": "760",
    "alpha_2_code": "SY",
    "alpha_3_code": "SYR",
    "name": "Syrian Arab Republic",
    "nationality": ["syrian"]
  },
  {
    "num_code": "158",
    "alpha_2_code": "TW",
    "alpha_3_code": "TWN",
    "name": "Taiwan, Province of China",
    "nationality": ["taiwanese"]
  },
  {
    "num_code": "762",
    "alpha_2_code": "TJ",
    "alpha_3_code": "TJK",
    "name": "Tajikistan",
    "nationality": ["tajikistani"]
  },
  {
    "num_code": "834",
    "alpha_2_code": "TZ",
    "alpha_3_code": "TZA",
    "name": "Tanzania, United Republic of",
    "nationality": ["tanzanian"]
  },
  {
    "num_code": "764",
    "alpha_2_code": "TH",
    "alpha_3_code": "THA",
    "name": "Thailand",
    "nationality": ["thai"]
  },
  {
    "num_code": "626",
    "alpha_2_code": "TL",
    "alpha_3_code": "TLS",
    "name": "Timor-Leste",
    "nationality": ["timorese"]
  },
  {
    "num_code": "768",
    "alpha_2_code": "TG",
    "alpha_3_code": "TGO",
    "name": "Togo",
    "nationality": ["togolese"]
  },
  {
    "num_code": "772",
    "alpha_2_code": "TK",
    "alpha_3_code": "TKL",
    "name": "Tokelau",
    "nationality": ["tokelauan"]
  },
  {
    "num_code": "776",
    "alpha_2_code": "TO",
    "alpha_3_code": "TON",
    "name": "Tonga",
    "nationality": ["tongan"]
  },
  {
    "num_code": "780",
    "alpha_2_code": "TT",
    "alpha_3_code": "TTO",
    "name": "Trinidad and Tobago",
    "nationality": ["trinidadian", "tobagonian"]
  },
  {
    "num_code": "788",
    "alpha_2_code": "TN",
    "alpha_3_code": "TUN",
    "name": "Tunisia",
    "nationality": ["tunisian"]
  },
  {
    "num_code": "792",
    "alpha_2_code": "TR",
    "alpha_3_code": "TUR",
    "name": "Turkey",
    "nationality": ["turkish"]
  },
  {
    "num_code": "795",
    "alpha_2_code": "TM",
    "alpha_3_code": "TKM",
    "name": "Turkmenistan",
    "nationality": ["turkmen"]
  },
  {
    "num_code": "796",
    "alpha_2_code": "TC",
    "alpha_3_code": "TCA",
    "name": "Turks and Caicos Islands",
    "nationality": ["turks and caicos Island"]
  },
  {
    "num_code": "798",
    "alpha_2_code": "TV",
    "alpha_3_code": "TUV",
    "name": "Tuvalu",
    "nationality": ["tuvaluan"]
  },
  {
    "num_code": "800",
    "alpha_2_code": "UG",
    "alpha_3_code": "UGA",
    "name": "Uganda",
    "nationality": ["ugandan"]
  },
  {
    "num_code": "804",
    "alpha_2_code": "UA",
    "alpha_3_code": "UKR",
    "name": "Ukraine",
    "nationality": ["ukrainian"]
  },
  {
    "num_code": "784",
    "alpha_2_code": "AE",
    "alpha_3_code": "ARE",
    "name": "United Arab Emirates",
    "nationality": ["emirati", "emirian", "emiri"]
  },
  {
    "num_code": "826",
    "alpha_2_code": "GB",
    "alpha_3_code": "GBR",
    "name": "United Kingdom",
    "nationality": ["british", "uk"]
  },
  {
    "num_code": "581",
    "alpha_2_code": "UM",
    "alpha_3_code": "UMI",
    "name": "United States Minor Outlying Islands",
    "nationality": ["american islander"]
  },
  {
    "num_code": "840",
    "alpha_2_code": "US",
    "alpha_3_code": "USA",
    "name": "United States",
    "nationality": ["american"]
  },
  {
    "num_code": "858",
    "alpha_2_code": "UY",
    "alpha_3_code": "URY",
    "name": "Uruguay",
    "nationality": ["uruguayan"]
  },
  {
    "num_code": "860",
    "alpha_2_code": "UZ",
    "alpha_3_code": "UZB",
    "name": "Uzbekistan",
    "nationality": ["uzbekistani"]
  },
  {
    "num_code": "548",
    "alpha_2_code": "VU",
    "alpha_3_code": "VUT",
    "name": "Vanuatu",
    "nationality": ["vanuatuan"]
  },
  {
    "num_code": "862",
    "alpha_2_code": "VE",
    "alpha_3_code": "VEN",
    "name": "Venezuela",
    "nationality": ["venezuelan"]
  },
  {
    "num_code": "704",
    "alpha_2_code": "VN",
    "alpha_3_code": "VNM",
    "name": "Viet Nam",
    "nationality": ["vietnamese"]
  },
  {
    "num_code": "92",
    "alpha_2_code": "VG",
    "alpha_3_code": "VGB",
    "name": "Virgin Islands, British",
    "nationality": ["british virgin island"]
  },
  {
    "num_code": "850",
    "alpha_2_code": "VI",
    "alpha_3_code": "VIR",
    "name": "Virgin Islands, American",
    "nationality": ["u.s. virgin island"]
  },
  {
    "num_code": "876",
    "alpha_2_code": "WF",
    "alpha_3_code": "WLF",
    "name": "Wallis and Futuna",
    "nationality": ["wallis and futuna", "wallisian", "futunan"]
  },
  {
    "num_code": "732",
    "alpha_2_code": "EH",
    "alpha_3_code": "ESH",
    "name": "Western Sahara",
    "nationality": ["sahrawian"]
  },
  {
    "num_code": "887",
    "alpha_2_code": "YE",
    "alpha_3_code": "YEM",
    "name": "Yemen",
    "nationality": ["yemeni"]
  },
  {
    "num_code": "894",
    "alpha_2_code": "ZM",
    "alpha_3_code": "ZMB",
    "name": "Zambia",
    "nationality": ["zambian"]
  },
  {
    "num_code": "716",
    "alpha_2_code": "ZW",
    "alpha_3_code": "ZWE",
    "name": "Zimbabwe",
    "nationality": ["zimbabwean"]
  }
];

exports.CIGA_CODES = [
  {
    "code": "0.1",
    "type": "",
    "description": "No CIGA"
  },
  {
    "code": "0.2",
    "type": "",
    "description": "Other (Please specify)"
  },
  {
    "code": "1.1",
    "type": "BNK",
    "description": "Raising funds, managing risk including credit, currency and interest risk."
  },
  {
    "code": "1.2",
    "type": "BNK",
    "description": "Taking hedging positions."
  },
  {
    "code": "1.3",
    "type": "BNK",
    "description": "Providing loans, credit or other financial services to customers."
  },
  {
    "code": "1.4",
    "type": "BNK",
    "description": "Managing regulatory capital."
  },
  {
    "code": "1.5",
    "type": "BNK",
    "description": "Preparing regulatory reports and returns."
  },
  {
    "code": "2.1",
    "type": "INS",
    "description": "Predicting and calculating risk."
  },
  {
    "code": "2.2",
    "type": "INS",
    "description": "Insuring or re-insuring against risk."
  },
  {
    "code": "2.3",
    "type": "INS",
    "description": "Providing insurance business services to clients."
  },
  {
    "code": "3.1",
    "type": "FM",
    "description": "Taking decisions on the holding and selling of investments."
  },
  {
    "code": "3.2",
    "type": "FM",
    "description": "Calculating risk and reserves."
  },
  {
    "code": "3.3",
    "type": "FM",
    "description": "Taking decisions on currency or interest fluctuations and hedging positions."
  },
  {
    "code": "3.4",
    "type": "FM",
    "description": "Preparing relevant regulatory or other reports for government authorities and investors."
  },
  {
    "code": "4.1",
    "type": "FL",
    "description": "Agreeing funding terms."
  },
  {
    "code": "4.2",
    "type": "FL",
    "description": "Identifying and acquiring assets to be leased (in the case of leasing)."
  },
  {
    "code": "4.3",
    "type": "FL",
    "description": "Setting the terms and duration of any financing or leasing."
  },
  {
    "code": "4.4",
    "type": "FL",
    "description": "Monitoring and revising any agreements."
  },
  {
    "code": "4.5",
    "type": "FL",
    "description": "Managing any risks."
  },
  {
    "code": "5.1",
    "type": "HQ",
    "description": "Taking relevant management decisions."
  },
  {
    "code": "5.2",
    "type": "HQ",
    "description": "Incurring expenditures on behalf of affiliates."
  },
  {
    "code": "5.3",
    "type": "HQ",
    "description": "Coordinating group activities."
  },
  {
    "code": "6.1",
    "type": "SHP",
    "description": "Managing the crew (including hiring, paying and overseeing crewmembers)."
  },
  {
    "code": "6.2",
    "type": "SHP",
    "description": "Hauling and maintaining ships."
  },
  {
    "code": "6.3",
    "type": "SHP",
    "description": "Overseeing and tracking deliveries."
  },
  {
    "code": "6.4",
    "type": "SHP",
    "description": "Determining what goods to order and when to deliver them."
  },
  {
    "code": "6.5",
    "type": "SHP",
    "description": "Organising and overseeing voyages."
  },
  {
    "code": "8.1",
    "type": "IP",
    "description": "Business concerned with intellectual property assets such as patents, research and development."
  },
  {
    "code": "8.2",
    "type": "IP",
    "description": "Business concerned with non-trade intangible assets such as brand, trademark and customer data, marketing, branding and distribution."
  },
  {
    "code": "9.1",
    "type": "DS",
    "description": "Transporting and storing goods."
  },
  {
    "code": "9.2",
    "type": "DS",
    "description": "Managing stocks."
  },
  {
    "code": "9.3",
    "type": "DS",
    "description": "Taking orders."
  },
  {
    "code": "9.4",
    "type": "DS",
    "description": "Providing consulting or other administrative services."
  }
];


exports.DEFAULT_CURRENCY = { 
  "cc": "USD", 
  "symbol": "US$", 
  "name": "United States dollar" 
}

