{{#ifCond version '<' 5}} 
    {{#each data.premises}} <tr>
        <td>{{address_line1}}</td>
        <td>{{address_line2}}</td>
        <td>{{city}}</td>
        <td>{{country}}</td>
        <td>{{postalcode}}</td>
        <td class="text-center"><i class="fa fa-pencil btn btn-sm royal-blue solid editpremises" data-id="{{_id}}"></i></td>
        <td class="text-center"><i class="fa fa-times  btn btn-sm btn-danger deletepremises" data-id="{{_id}}"></i></td>
    {{/each}}
{{else}}
    {{#each data.premises}}
        <tr>
            <td>{{address_line1}}</td>
            <td>{{address_line2}}</td>
            <td>{{country}}</td>
            <td class="justify-content-center d-flex d-flex-inline">
                <button type="button" class="btn btn-sm royal-blue solid mr-1 editpremises" data-id="{{_id}}">
                    <i class="fa fa-pencil"></i>
                </button>
                <button type="button" class="btn btn-sm btn-danger deletepremises" data-id="{{_id}}">
                    <i class="fa fa-times"></i>
                </button>
            </td>
        </tr>
    {{else}}
        <tr>
            <td colspan="4">
                No premises found
            </td>
        </tr>>
    {{/each}}
{{/ifCond}}