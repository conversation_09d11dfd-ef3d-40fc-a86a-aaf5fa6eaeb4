(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['uploadrelationfiles'] = template({"1":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <div class=\"table-responsive\">\r\n        <table class=\"table table-striped mb-0\">\r\n            <thead>\r\n            <tr>\r\n                <th class=\"header-60-percent\">Uploaded Files</th>\r\n                <th class=\"header-20-percent\">Download</th>\r\n                <th class=\"header-40-percent\">Delete</th>\r\n            </tr>\r\n            </thead>\r\n            <tbody>\r\n"
    + ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"files") : depth0),{"name":"each","hash":{},"fn":container.program(2, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":12,"column":12},"end":{"line":29,"column":21}}})) != null ? stack1 : "")
    + "            </tbody>\r\n        </table>\r\n    </div>\r\n";
},"2":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, alias5=container.lambda, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                <tr>\r\n                    <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"originalName") || (depth0 != null ? lookupProperty(depth0,"originalName") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"originalName","hash":{},"data":data,"loc":{"start":{"line":14,"column":24},"end":{"line":14,"column":40}}}) : helper)))
    + "</td>\r\n                    <td class=\"pl-2 py-1 text-center align-middle\">\r\n                        <a target=\"_blank\" class=\"btn btn btn-xs solid royal-blue\"\r\n                           href=\"/masterclients/"
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"masterClientCode") : depths[1]), depth0))
    + "/incorporate-company/"
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"incorporationId") : depths[1]), depth0))
    + "/files/"
    + alias4(((helper = (helper = lookupProperty(helpers,"fileId") || (depth0 != null ? lookupProperty(depth0,"fileId") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"fileId","hash":{},"data":data,"loc":{"start":{"line":17,"column":121},"end":{"line":17,"column":131}}}) : helper)))
    + "/download?relation="
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"id") : depths[1]), depth0))
    + "&fileType="
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"fileTypeId") : depths[1]), depth0))
    + "&fileGroup="
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"group") : depths[1]), depth0))
    + "&row="
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"row") : depths[1]), depth0))
    + "\"\r\n                        >\r\n                        Download\r\n                        </a>\r\n                    </td>\r\n                    <td>\r\n                        <button class=\"demo-delete-row btn btn-danger btn-xs btn-icon deleteRelationFile\"\r\n                                data-mcc=\""
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"masterClientCode") : depths[1]), depth0))
    + "\" data-incorporation-id=\""
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"incorporationId") : depths[1]), depth0))
    + "\" data-id=\""
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"id") : depths[1]), depth0))
    + "\" data-group=\""
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"group") : depths[1]), depth0))
    + "\" data-field=\""
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"fileTypeId") : depths[1]), depth0))
    + "\" data-field-id=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"fileId") || (depth0 != null ? lookupProperty(depth0,"fileId") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"fileId","hash":{},"data":data,"loc":{"start":{"line":24,"column":206},"end":{"line":24,"column":216}}}) : helper)))
    + "\" data-blobName=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"blobName") || (depth0 != null ? lookupProperty(depth0,"blobName") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"blobName","hash":{},"data":data,"loc":{"start":{"line":24,"column":233},"end":{"line":24,"column":247}}}) : helper)))
    + "\">\r\n                            <i class=\"fa fa-times\"></i>\r\n                        </button>\r\n                    </td>\r\n                </tr>\r\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"files") : depth0),{"name":"if","hash":{},"fn":container.program(1, data, 0, blockParams, depths),"inverse":container.noop,"data":data,"loc":{"start":{"line":1,"column":0},"end":{"line":33,"column":7}}})) != null ? stack1 : "");
},"useData":true,"useDepths":true});
})();