<main class="">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="form-group">
                    </div>
                    <h4 class="mt-3">Entity Name: <B>{{entityName}}</B></h4>
                    <br>
                    {{#if directorsWithMissingValues}}
                        <input id="missingDirBoData" type="text" readonly hidden value="true">
                        {{> director-and-bo/missing-data-modal dataRecords=directorsWithMissingValues }}
                    {{/if}}

                    {{#if individualEntries}}
                    <h5>Individual Directors</h5>
                    <div class="table-responsive">
                        <table id="individual-director-table" class="table table-sm table-striped mb-0">
                            <thead>
                                <tr>
                                    <th>Director Type</th>
                                    <th class="header-10-percent min-width-100" >Name</th>
                                    <th>Former Name</th>
                                    <th class="header-10-percent min-width-100" >Appointment Date</th>
                                    <th class="header-10-percent min-width-100" >Cessation Date</th>
                                    <th>Service Address</th>
                                    <th>Residential Address</th>
                                    <th class="header-10-percent min-width-100" >Date of Birth</th>
                                    <th>Place of Birth</th>
                                    <th>Nationality</th>
                                    <th class="header-15-percent min-width-150" ></th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each individualEntries}}

                                {{#if oldData}}
                                <tr><td class="bg-white"  colspan="11"></td></tr> 
                                <tr class="bg-clear-gray font-italic" >
                                    <td>
                                        <small>
                                            {{oldData.OfficerType}}
                                            {{#if oldData.DirectorIsAlternateToId}}   
                                                   to {{oldData.DirectorIsAlternateToName}} 
                                            {{/if}}  
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.Name}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.FormerName}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.FromDate}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.ToDate}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{wrapText oldData.ServiceAddress}}
                                        </small>

                                    </td>
                                    <td>
                                        <small>
                                            {{wrapText oldData.ResidentialOrRegisteredAddress}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.DateOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.PlaceOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.Nationality}}
                                        </small>
                                    </td>
                                    <td class="text-center">
                                        Previous Record
                                    </td>
                                </tr>
                                {{/if}}
                                <!-- NEW VALUES -->
                                <tr>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{OfficerType}} 
                                            {{#if DirectorIsAlternateToId}}   
                                                   to {{DirectorIsAlternateToName}} 
                                            {{/if}} 
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{Name}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{FormerName}}
                                        </small>
                                    </td>
                                   
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{FromDate}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{ToDate}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{wrapText ServiceAddress}}
                                        </small>

                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{wrapText ResidentialOrRegisteredAddress}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{DateOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>

                                            {{PlaceOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{Nationality}}
                                        </small>
                                    </td>
                                    <td class="text-center">
                                        {{#ifCond showInfoQuestion '===' true}}
                                        {{#if oldData}} <small><b>Updated Record</b></small><br><br> {{/if}}
                                        <div id="showInfo-{{UniqueRelationId}}">
                                            <label>Is the information correct?</label>
                                            <div class="d-flex flex-row  justify-content-around">
                                                <div class="radio ">
                                                    <input type="radio" class="isCorrectRadioBtn"
                                                        id="isCorrectRadioBtnYes-{{UniqueRelationId}}"
                                                        name="isCorrectRadioBtn-{{UniqueRelationId}}" data-vpUniqueRelationId="{{UniqueRelationId}}">
                                                    <label for="isCorrectRadioBtnYes-{{UniqueRelationId}}">
                                                        <small>YES {{Status}}</small>
                                                    </label>
                                                </div>
                                                <div class="radio ">
                                                    <input type="radio" class="isCorrectRadioBtn"
                                                        id="isCorrectRadioBtnNo-{{UniqueRelationId}}"
                                                        name="isCorrectRadioBtn-{{UniqueRelationId}}" value="NO"
                                                        data-vpUniqueRelationId="{{UniqueRelationId}}">
                                                    <label for="isCorrectRadioBtnNo-{{UniqueRelationId}}">
                                                        <small>NO</small>
                                                    </label>
                                                </div>
                                            </div>

                                        </div>
                                        {{/ifCond}}

                                        {{#ifCond lastChange.status '===' 'CONFIRMED'}}
                                        <div>
                                            <span><small><b>CONFIRMED</b></small></span>
                                        </div>

                                        {{/ifCond}}

                                        {{#if canConfirm}}
                                        <button data-id="{{UniqueRelationId}}" id="confirmDataBtn-{{UniqueRelationId}}"
                                            class="btn btn-sm solid royal-blue width-md mb-1 confirm-btn confirmInformation {{#if
                                            showInfoQuestion}} hide-element {{/if}}">
                                            <small>Confirm</small>
                                        </button>
                                        {{/if}}

                                        {{#if canUpdate}}
                                        <button data-id="{{UniqueRelationId}}" data-type="{{../type}}"
                                            id="updateBtn-{{UniqueRelationId}}"
                                            class="btn btn-sm solid royal-blue width-md mb-1 request-update-btn requestUpdate {{#if
                                            showInfoQuestion}} hide-element {{/if}}"
                                            data-missing-values="{{hasMissingValues}}">
                                            <small>Request Update</small>
                                        </button>
                                        {{/if}}


                                        {{#if showHistory}}
                                        <button
                                           data-type="{{lastChange.changeType}}" data-reason="{{lastChange.changeReason}}"
                                            class="btn btn-sm solid width-md btn-secondary showLastChange">
                                            <small>View History</small>
                                        </button>
                                        {{/if}}
                                    </td>
                                </tr>
                                {{#if oldData}}
                                   <tr><td class="bg-white" colspan="11"></td></tr> 
                                {{/if}}

                                {{else}}
                                <tr>
                                    <td colspan="11">No records found </td>
                                </tr>
                                {{/each}}

                            </tbody>
                        </table>
                    </div>
                    <br>
                    {{/if}}


                    {{#if corporateEntries}}
                    <h5>Corporate Directors</h5>
                    <div class="table-responsive">
                        <table id="company-director-table" class="table table-sm table-striped mb-0">
                            <thead>
                                <tr>
                                    <th>Director Type</th>
                                    <th class="header-10-percent min-width-100" >Name</th>
                                    <th>Corporate Number</th>
                                    <th class="header-10-percent min-width-100" >Appointment Date</th>
                                    <th class="header-10-percent min-width-100" >Cessation Date</th>
                                    <th>Address</th>
                                    <th>Incorporation Place</th>
                                    <th class="header-10-percent min-width-100" >Incorporation Date</th>
                                    <th class="header-15-percent min-width-150" ></th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each corporateEntries}}
                                {{#if oldData}} 
                                <tr><td class="bg-white"  colspan="9"></td></tr> 
                                <tr class="bg-clear-gray font-italic" >
                                    <td>
                                        <small>
                                            {{oldData.OfficerType}}
                                            {{#if oldData.DirectorIsAlternateToId}}  
                                                   to {{oldData.DirectorIsAlternateToName}} 
                                            {{/if}} 
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.Name}}
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.BoDirIncorporationNumber}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.FromDate}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.ToDate}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{wrapText oldData.ResidentialOrRegisteredAddress}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.PlaceOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small>
                                            {{oldData.DateOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td class="text-center">
                                        Previous Record
                                    </td>
                                </tr>
                                {{/if}}
                                <tr class="bg-white" >
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{OfficerType}}
                                            {{#if DirectorIsAlternateToId}}   
                                                   to {{DirectorIsAlternateToName}} 
                                            {{/if}}  
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{Name}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{BoDirIncorporationNumber}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{FromDate}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{ToDate}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{wrapText ResidentialOrRegisteredAddress}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{PlaceOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td>
                                        <small {{#if oldData}} class="font-weight-bold" {{/if}}>
                                            {{DateOfBirthOrIncorp}}
                                        </small>
                                    </td>
                                    <td class="text-center">
                                        {{#ifCond showInfoQuestion '===' true}}
                                        {{#if oldData}} <small><b>Updated Record</b></small><br><br> {{/if}}
                                        <div id="showInfo-{{UniqueRelationId}}">
                                            <label>Is the information correct?</label>
                                            <div class="d-flex flex-row  justify-content-around">
                                                <div class="radio ">
                                                    <input type="radio" class="isCorrectRadioBtn"
                                                        id="isCorrectRadioBtnYes-{{UniqueRelationId}}"
                                                        name="isCorrectRadioBtn-{{UniqueRelationId}}" data-vpUniqueRelationId="{{UniqueRelationId}}">
                                                    <label for="isCorrectRadioBtnYes-{{UniqueRelationId}}">
                                                        <small>YES {{Status}}</small>
                                                    </label>
                                                </div>
                                                <div class="radio ">
                                                    <input type="radio" class="isCorrectRadioBtn"
                                                        id="isCorrectRadioBtnNo-{{UniqueRelationId}}"
                                                        name="isCorrectRadioBtn-{{UniqueRelationId}}" value="NO"
                                                        data-vpUniqueRelationId="{{UniqueRelationId}}">
                                                    <label for="isCorrectRadioBtnNo-{{UniqueRelationId}}">
                                                        <small>NO</small>
                                                    </label>
                                                </div>
                                            </div>

                                        </div>
                                        {{/ifCond}}

                                        {{#ifCond lastChange.status '===' 'CONFIRMED'}}
                                        <div>
                                            <span><small><b>CONFIRMED</b></small></span>
                                        </div>

                                        {{/ifCond}}

                                        {{#if canConfirm}}
                                        <button data-id="{{UniqueRelationId}}" id="confirmDataBtn-{{UniqueRelationId}}"
                                            class="btn btn-sm solid royal-blue width-md mb-1 confirm-btn confirmInformation {{#if
                                            showInfoQuestion}} hide-element {{/if}}">
                                            <small>Confirm</small>
                                        </button>
                                        {{/if}}

                                        {{#if canUpdate}}
                                        <button data-id="{{UniqueRelationId}}" data-type="{{../type}}"
                                            id="updateBtn-{{UniqueRelationId}}"
                                            class="btn btn-sm solid royal-blue width-md mb-1 request-update-btn requestUpdate {{#if
                                            showInfoQuestion}} hide-element {{/if}}"
                                            data-missing-values="{{hasMissingValues}}">
                                            <small>Request Update</small>
                                        </button>
                                        {{/if}}


                                        {{#if showHistory}}
                                        <button
                                            data-type="{{lastChange.changeType}}" data-reason="{{lastChange.changeReason}}"
                                            class="btn btn-sm solid width-md btn-secondary showLastChange">
                                            <small>View History</small>
                                        </button>
                                        {{/if}}
                                    </td>
                                </tr>
                                {{#if oldData}}
                                    <tr><td class="bg-white"  colspan="9"></td></tr> 
                                {{/if}}
                                
                                {{else}}
                                <tr>
                                    <td colspan="9">No records found </td>
                                </tr>
                                {{/each}}

                            </tbody>
                        </table>
                    </div>
                    <br>
                    {{/if}}

                    {{#unless individualEntries}}
                        {{#unless corporateEntries}}
                        <p>
                            Please contact us for further assistance.
                        </p>

                        {{/unless}}
                    {{/unless}}

                    <br>

                    <div>
                        <a href="/masterclients/{{masterClientCode}}/director-and-bo"
                            class="btn btn-secondary waves-effect waves-light width-xl">
                            Back
                        </a>

                        {{#unless individualEntries}}
                            {{#unless hasCorporateEntries}}
                            <button class="btn solid  btn-primary" id="requestAssistanceBtn">
                                Request Assistance
                            </button>
                            {{/unless}}
                        {{/unless}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script type="text/javascript" src="/templates/director-and-bo/requestupdatepopup.precompiled.js"></script>
<script type="text/javascript" src="/templates/director-and-bo/requestupdatelog.precompiled.js"></script>
<script type="text/javascript" src="/views-js/director-and-bo/director-forms.js"></script>