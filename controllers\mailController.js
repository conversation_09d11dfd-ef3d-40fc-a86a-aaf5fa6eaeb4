const nodemailer = require('nodemailer')

const transporter = nodemailer.createTransport(
    {
        host: process.env.SMTP_SERVER,
        port: process.env.SMTP_PORT ? process.env.SMTP_PORT :
            process.env.SMTP_SECURE === 'true' ? 465 : 587,
        secure: process.env.SMTP_SECURE === 'true',
        auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASSWORD
        } 
    });



exports.send = function(to, subject, text, html)
{
    const mailOptions = {
        from: process.env.SMTP_FROM, // sender address (who sends)
        to: to, // list of receivers (who receives)
        cc: process.env.SMTP_FROM,
        subject: subject, // Subject line
        text: text, // plaintext body
        html: html // html body
    };

    // send mail with defined transport object
    transporter.sendMail(mailOptions, function(error, info){
                console.log(error)
                console.log(info)
    });
}

exports.asyncSend = async function(to, subject, text, html, cc=[])
{
  let mailOptions = {
    from: process.env.SMTP_FROM, // sender address (who sends)
    to: to, // list of receivers (who receives)
    subject: subject, // Subject line
    text: text, // plaintext body
    html: html // html body
  };

  if(cc.length > 0){
    mailOptions["cc"] = cc
  }

  return await transporter.sendMail(mailOptions).catch(function (reason) {
    console.log("Error sending email: ", reason)
    return {"error": reason};
  });
};
