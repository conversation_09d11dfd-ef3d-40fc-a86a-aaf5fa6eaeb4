{{#each parentEntities}}
    <tr id="{{../type}}-parent-table-row-{{_id}}" class="{{../type}}-parent-row">
        <td> {{parentName}} </td>
        <td> {{alternativeName}} </td>
        <td>{{#if jurisdictionName}} {{jurisdictionName}} {{else}} {{getCountryName jurisdiction}} {{/if}} </td>
        <td> {{incorporationNumber}} </td>
        <td> {{TIN}} </td>
        <td class="justify-content-center d-flex d-flex-inline">
            <button type="button" class="btn btn-sm royal-blue solid mr-1" data-parent-type="{{../type}}"
                data-id="{{../entryId}}" data-parent-id="{{_id}}" data-toggle="modal" data-target="#entityParentModal">
                <i class="fa fa-pencil"></i>
            </button>
            <button type="button" class="btn btn-sm btn-danger deleteParentEntity"
                    data-type="{{../type}}" data-id="{{../entryId}}" data-parent-id="{{_id}}">
                <i class="fa fa-times"></i>
            </button>
        </td>
    </tr>
    {{else}}
        <tr>
            <td colspan="6">
                No {{type}} entity parents found
            </td>
        </tr>
{{/each}}


