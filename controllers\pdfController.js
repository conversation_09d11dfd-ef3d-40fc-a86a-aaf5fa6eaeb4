const PdfPrinter = require('pdfmake');
const moment = require('moment');
const { COUNTRIES, CIGA_CODES } = require('../utils/constants');
const settings = require('../settings');
const { ACCOUNTING_SERVICE_TYPES, REPORT_STATUS } = require('../utils/financialReportConstants');

exports.generatePdf = function (entry, res) {
  const printer = new PdfPrinter({
    Helvetica: {
      normal: 'Helvetica',
      bold: 'Helvetica-Bold',
      italics: 'Helvetica-Oblique',
      bolditalics: 'Helvetica-BoldOblique'
    }
  });

  const docDefinition = {
    watermark: {
      text: !entry.payment?.payment_received_at || entry.status === "SAVED" || (entry.status === "SUBMITTED" && !entry.reopened?.details) ?  "DRAFT" :
          (entry.status === "PAID" || entry.status === "SUBMITTED") && entry.reopened?.details?.length > 0 ? "RESUBMITTED" :
           entry.status === "PAID" ? "SUBMITTED" : entry.status,
      color: '#0081B4',
      opacity: 0.3,
      bold: true,
      italics: false,
    },
    info: {
      author: 'Trident Trust Company (BVI) Limited',
      creator: 'Trident Trust Company (BVI) Limited',
      producer: 'Trident Trust Company (BVI) Limited',
    },
    footer: function () {
      // you can apply any logic and return any valid pdfmake element

      return [

        {
          canvas: [
            {
              type: 'polyline',
              lineWidth: 2,
              color: "#0081B4",
              closePath: true,
              points: [{x: 0, y: 0}, {x: 800, y: 0}]
            },

          ]
        }, {
          text: ' ',
          fontSize: 6
        }, {
          text: 'Address: Trident Chambers, Wickhams Cay, PO Box 146, Road Town, Tortola, British Virgin Islands ',
          fontSize: 10,
          alignment: 'center'
        },
        {text: 'Telephone: ****** 494 2434  Website: www.tridenttrust.com', fontSize: 10, alignment: 'center'}
      ]
    },
    content: [
      {
        // under NodeJS (or in case you use virtual file system provided by pdfmake)
        // you can also pass file names here
        image: 'data:image/png;base64,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',
        width: 350,
      },
      {
        style: 'table',
        table: {
          widths: [300, '*'],
          body: createCompanyInfo(entry)
        },
        //pageBreak: "after"
      },
      {
        style: 'table',
        table: {
          widths: [300, '*'],
          body: createTableEntityDetails(entry)
        },
        //pageBreak: "after"
      },

    ],
    styles: {
      header: {
        fontSize: 18,
        bold: true,
        margin: [0, 0, 0, 10]
      },
      subheader: {
        fontSize: 16,
        bold: true,
        margin: [0, 10, 0, 5]
      },
      table: {
        margin: [0, 5, 0, 15]
      },
      tableHeader: {
        bold: true,
        color: 'black'
      }
    },
    defaultStyle: {
      font: 'Helvetica'
    }
  };
  
  const entryVersion = parseFloat(entry.version || 1);

  if (entryVersion && entryVersion >= 5){
    if(entry.entity_details.ultimateParents?.length > 0){
      createParentEntityTable(docDefinition, entry.entity_details.ultimateParents, 'Ultimate Parents' )
    }

    if (entry.entity_details.immediateParents?.length > 0) {
      createParentEntityTable(docDefinition, entry.entity_details.immediateParents, 'Immediate Parents')
    }
  }

  if (entryVersion && entryVersion >= 4){
    if (entry.relevant_activities){
      createTableRelevantActivities(docDefinition, entry);
    }

    if (entry.relevant_activities?.none.selected !== true){
      createTableTaxResidency(docDefinition, entry);
    }
  }else{
    createTableTaxResidency(docDefinition, entry);
  }

  if (entry.relevant_activities) {
    
    if (entryVersion && entryVersion < 4){
      createTableRelevantActivities(docDefinition, entry)
    }

    if (entry.tax_residency?.resident_in_BVI === true){
      if (entryVersion < 5) {
        if (entry.relevant_activities.banking_business.selected && entry.banking_business) {
          createTableBusiness(docDefinition, entry.banking_business, 'Banking Business');
        }
        if (entry.relevant_activities.insurance_business.selected && entry.insurance_business) {
          createTableBusiness(docDefinition, entry.insurance_business, 'Insurance Business');
        }
        if (entry.relevant_activities.fund_management_business.selected && entry.fund_management_business) {
          createTableBusiness(docDefinition, entry.fund_management_business, 'Fund Management Business',
            entry.version);
        }
        if (entry.relevant_activities.finance_leasing_business.selected && entry.finance_leasing_business) {
          createTableBusiness(docDefinition, entry.finance_leasing_business, 'Finance and Leasing Business',
            entry.version);
        }
        if (entry.relevant_activities.headquarters_business.selected && entry.headquarters_business) {
          createTableBusiness(docDefinition, entry.headquarters_business, 'Headquarters Business', entry.version);
        }
        if (entry.relevant_activities.shipping_business.selected && entry.shipping_business) {
          createTableBusiness(docDefinition, entry.shipping_business, 'Shipping Business', entry.version);
        }

        if (entry.relevant_activities.holding_business.selected && entry.holding_business) {
          createTableBusiness(docDefinition, entry.holding_business,
            'Holding Business (Pure Equity Holding entities)', entry.version);
        }
        if (entry.relevant_activities.intellectual_property_business.selected && entry.intellectual_property_business) {
          createTableBusiness(docDefinition, entry.intellectual_property_business,
            'Intellectual Property Business', entry.version);
        }
        if (entry.relevant_activities.service_centre_business.selected && entry.service_centre_business) {
          createTableBusiness(docDefinition, entry.service_centre_business,
            'Distribution and Service Centre Business', entry.version);
        }
      } else {
        const entryGeneralCurrency = entry.entity_details?.totalAnnualGrossCurrency || "USD";
        if (entry.relevant_activities.banking_business.selected && entry.banking_business) {
          createTableBusinessV5(docDefinition, entry.banking_business, entryGeneralCurrency, 
            settings.substance_business_types.BANKING, 'Banking Business');
        }
        if (entry.relevant_activities.insurance_business.selected && entry.insurance_business) {
          createTableBusinessV5(docDefinition, entry.insurance_business, entryGeneralCurrency,
            settings.substance_business_types.INSURANCE, 'Insurance Business');
        }
        if (entry.relevant_activities.fund_management_business.selected && entry.fund_management_business) {
          createTableBusinessV5(docDefinition, entry.fund_management_business, entryGeneralCurrency, 
            settings.substance_business_types.FUND_MANAGEMENT, 'Fund Management Business');
        }
        if (entry.relevant_activities.finance_leasing_business.selected && entry.finance_leasing_business) {
          createTableBusinessV5(docDefinition, entry.finance_leasing_business, entryGeneralCurrency, 
            settings.substance_business_types.FINANCE_LEASING, 'Finance and Leasing Business');
        }
        if (entry.relevant_activities.headquarters_business.selected && entry.headquarters_business) {
          createTableBusinessV5(docDefinition, entry.headquarters_business, entryGeneralCurrency, 
            settings.substance_business_types.HEADQUARTERS, 'Headquarters Business');
        }
        if (entry.relevant_activities.shipping_business.selected && entry.shipping_business) {
          createTableBusinessV5(docDefinition, entry.shipping_business, entryGeneralCurrency, 
            settings.substance_business_types.SHIPPING, 'Shipping Business');
        }

        if (entry.relevant_activities.holding_business.selected && entry.holding_business) {
          createTableBusinessV5(docDefinition, entry.holding_business, entryGeneralCurrency, 
            settings.substance_business_types.HOLDING, 'Holding Business (Pure Equity Holding entities)');
        }
        if (entry.relevant_activities.intellectual_property_business.selected && entry.intellectual_property_business) {
          createTableBusinessV5(docDefinition, entry.intellectual_property_business, entryGeneralCurrency,
            settings.substance_business_types.INTELLECTUAL_PROPERTY, 'Intellectual Property Business');
        }
        if (entry.relevant_activities.service_centre_business.selected && entry.service_centre_business) {
          createTableBusinessV5(docDefinition, entry.service_centre_business, entryGeneralCurrency,
             settings.substance_business_types.DISTRIBUTION_SERVICE_CENTRE,'Distribution and Service Centre Business');
        }
      }
    }



  }
  const supportingDetailsTable = [];
  supportingDetailsTable.push([{ text: 'Supporting Details', style: 'tableHeader', colSpan: 2 }, {}]);
  supportingDetailsTable.push(['Please provide any comment to support your Economic Substance Declaration', 
    entry.supporting_details?.support_comment || ""]);
    
  docDefinition.content.push({
    style: 'table',
    table: {
      headerRows: 0,
      widths: ['50%', '*'],

      body: supportingDetailsTable
    },
  });


  createTableConfirmation(docDefinition, entry);

  const pdfDoc = printer.createPdfKitDocument(docDefinition);

  const chunks = [];
  let result;

  pdfDoc.on('data', function (chunk) {
    chunks.push(chunk)
  });
  pdfDoc.on('end', function () {
    result = Buffer.concat(chunks)

    res.contentType('application/pdf');
    res.setHeader('Content-Disposition', 'inline; filename=SUB-' + entry.company_data.masterclientcode + '-' + entry.company_data.code + '-' + formatDate(entry.submitted_at, "YYYYMMDD") + '.pdf');
    res.send(result)
  });
  pdfDoc.end()
}

exports.generateInvoicePdf = function (entry, res) {
  const printer = new PdfPrinter({
    Helvetica: {
      normal: 'Helvetica',
      bold: 'Helvetica-Bold',
      italics: 'Helvetica-Oblique',
      bolditalics: 'Helvetica-BoldOblique'
    }
  });

  const financialPeriodEndYear = moment(entry.entity_details.financial_period_ends).utc().format('YYYY');
  let financialPeriodChanged = false;

  if ( entry.reopened && entry.reopened.details?.length > 0){
    financialPeriodChanged = entry.reopened.details.some((r) => r.change_financial_period_dates === true);
  }

  const docDefinition = {
    watermark: {
      text: Number(financialPeriodEndYear) < 2022 ||  ( Number(financialPeriodEndYear) >= 2022  && financialPeriodChanged  && entry.payment?.payment_received_at)  ?
        "" : "CANCELLED",
      color: '#0081B4',
      opacity: 0.3,
      bold: true,
      italics: false
    },
    info: {
      author: 'Trident Trust Company (BVI) Limited',
      creator: 'Trident Trust Company (BVI) Limited',
      producer: 'Trident Trust Company (BVI) Limited',
    },
    footer: function () {
      // you can apply any logic and return any valid pdfmake element
      return [
        {
          canvas: [
            {
              type: 'polyline',
              lineWidth: 2,
              color: "#0081B4",
              closePath: true,
              points: [{x: 0, y: 0}, {x: 800, y: 0}]
            },

          ]
        }, {
          text: ' ',
          fontSize: 6
        }, {
          text: 'PLEASE NOTE: KINDLY SEND A COPY OF THE REMITTANCE ADVICE OR DELIVERY NOTIFICATION BY EMAIL TO',
          fontSize: 8,
          bold: true,
          alignment: 'center'
        },
        {
          text: '<EMAIL>. FAILURE TO DO SO MAY RESULT IN THE WRONG ALLOCATION OF YOUR FUNDS',
          fontSize: 8,
          bold: true,
          alignment: 'center'
        }
      ]
    },
    content: [
      {
        columns: [
          {
            width: 200,
            image: 'data:image/png;base64,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',
          },
          {
            width: '*',
            text: ''
          },
          {
            width: '35%',
            margin: [0, 10, 0, 0],
            fontSize: 20,
            text: 'INVOICE'
          },
        ],
        columnGap: 10
      },
      '\n\n',
      {
        columns: [
          {
            width: '*',
            type: 'none',
            ol: [
              entry.company_data.name,
              '\n\n\n\n\n\n\n\n',
              formatDate(entry.submitted_at, "DD MMMM YYYY"),
              '\n',
              {
                columns: [
                  {
                    width: 'auto',
                    text: 'Invoice No: ',
                    bold: true
                  },
                  {
                    text: entry.invoice_number.toString()
                  }
                ],
                columnGap: 5
              },

            ]
          },
          {
            width: '35%',
            type: 'none',
            ol: [
              'Trident Trust Company (BVI) Ltd',
              'Trident Chambers',
              'Wickhams Cay',
              'PO Box 146',
              'Road Town, Tortola',
              'British Virgin Islands',
              'Tel **************',
              '<EMAIL>',
              'www.tridenttrust.com',
              '\n\n',
              'Our Ref:  TTRUM NA'
            ]
          }
        ]
      },
      '\n',
      {canvas: [{type: 'line', x1: 0, y1: 5, x2: 595 - 2 * 40, y2: 5, lineWidth: 1}]},
      '\n',
      {
        text: 'Fees',
        style: 'subheader'
      },
      {
        columns: [
          {
            width: '*',
            text: 'Submission of Economic Substance Annual Assessment to the ITA for the year ending ' + formatDate(entry.entity_details.financial_period_ends, "MMMM YYYY"),
          },
          {
            width: '20%',
            alignment: 'right',
            text: entry.company_data.amount ? entry.company_data.amount.toFixed(2).toString() : '0.00'
          }
        ],
        columnGap: 10
      },
      '\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nPayment is to be in US$ and due within 30 days from date of invoice.',
      '\n',
      {
        columns: [
          {
            width: 'auto',
            text: 'All bank charges of the originating bank and the intermediary banks are to be borne by the client so ' +
              'that Trident receives the total balance that is owed on the invoice.'
          },
          {
            width: '5%',
            bold: true,
            text: 'US$',
            alignment: "right"
          },
          {
            width: '20%',
            alignment: 'right',
            text: entry.company_data.amount ? entry.company_data.amount.toFixed(2).toString() : '0.00'
          }
        ],
        columnGap: 10
      },
      {
        canvas: [{type: 'line', x1: 0, y1: 5, x2: 595 - 2 * 40, y2: 5, lineWidth: 1,}],
        margin: [0, 0, 0, 10]

      },
      {
        columns: [
          {
            width: '65%',
            text: 'Please use the following link to make any payments by credit card: ',
            fontSize: 10,
            bold: true
          },
          {
            width: "*",
            fontSize: 10,
            text: '\thttps://tridenttrust.com/bvi-payments\t',
            color: "white",
            background: "#005C81",
          }
        ],
      },
      {
        style: 'table',
        table: {
          headerRows: 1,
          widths: ['35%', '35%', '15%', '15%'],
          body: [
            [{text: 'COMPANY NAME', style: 'tableHeader'},
              {text: 'REFERENCE', style: 'tableHeader'},
              {text: 'INVOICE DATE', style: 'tableHeader'}, {text: 'AMOUNT DUE', style: 'tableHeader'}],
            [
              {text: entry.company_data.name, style: 'tableText'},
              {text: entry.invoice_number.toString(), style: 'tableText'},
              {text: formatDate(entry.submitted_at, "DD/MM/YYYY").toString(), style: 'tableText'},
              {
                text: (entry.company_data.amount ? entry.company_data.amount.toFixed(2).toString() : '0.00'),
                style: 'tableText'
              }
            ]]
        },
        layout: {
          hLineWidth: function (i, node) {
            return (i === 0 || i === node.table.body.length) ? 1 : 0;
          },
          vLineWidth: function (i, node) {
            return (i === 0 || i === node.table.widths.length) ? 1 : 0;
          }
        }
      },
      {
        columns: [
          {
            text: ["Please make enquiries with your local Trident Office if you wish to pay by credit card or email ",
              {
                text: "<EMAIL>",
                italic: true, fontSize: 10, color: "blue",
                decoration: 'underline',
                decorationStyle: 'solid',
                decorationColor: 'blue'
              },
              " for details.\n"]
          },
          {
            text: "If making payment by wire transfer, please quote our invoice number, and wire to:\n\n" +
              "USA BANK details\n" +
              "Receiving Bank:\n" +
              "ABA: *********\n" +
              "CIBC Bank USA\n" +
              "120 South LaSalle Street" +
              "Chicago, IL 60603\n" +
              "USA\n\n" +
              "Swift Code: PVTBUS44" +
              "\n" +
              "Account Name: Trident Trust Company (BVI) Ltd\n" +
              "Account No: 2906031",
          }
        ],
        columnGap: 10
      }
    ],
    styles: {
      header: {
        fontSize: 18,
        bold: true,
        margin: [0, 0, 0, 10]
      },
      subheader: {
        fontSize: 14,
        bold: true,
        margin: [0, 10, 0, 5]
      },
      table: {
        margin: [0, 5, 0, 15]
      },
      tableHeader: {
        bold: true,
        color: 'black',
        alignment: 'center',
        fontSize: 10
      },
      tableText: {
        fontSize: 9,
        alignment: 'center'
      },
      feesText: {
        fonstSize: 5
      }
    },
    defaultStyle: {
      font: 'Helvetica',
      fontSize: 11,
      lineHeight: 1.1,
    }
  };

  const pdfDoc = printer.createPdfKitDocument(docDefinition);

  const chunks = [];
  let result;

  pdfDoc.on('data', function (chunk) {
    chunks.push(chunk)
  });
  pdfDoc.on('error', function (err) {
    console.log("pdf generation error ", err);
  });
  pdfDoc.on('end', function () {
    result = Buffer.concat(chunks);

    res.contentType('application/pdf');
    res.setHeader('Content-Disposition', 'inline; filename=SUB-' + entry.company_data.masterclientcode + '-' + entry.company_data.code + '-' + formatDate(entry.submitted_at, "YYYYMMDD") + '.pdf');
    res.send(result)
  });
  pdfDoc.end()
};

exports.generateIncorporationInvoicePdf = function (incorporation, res) {
  const printer = new PdfPrinter({
    Helvetica: {
      normal: 'Helvetica',
      bold: 'Helvetica-Bold',
      italics: 'Helvetica-Oblique',
      bolditalics: 'Helvetica-BoldOblique'
    }
  });

  const docDefinition = {
    footer: function () {
      // you can apply any logic and return any valid pdfmake element
      return [
        {
          canvas: [
            {
              type: 'polyline',
              lineWidth: 2,
              color: "#0081B4",
              closePath: true,
              points: [{x: 0, y: 0}, {x: 800, y: 0}]
            },

          ]
        }, {
          text: ' ',
          fontSize: 6
        }, {
          text: 'PLEASE NOTE: KINDLY SEND A COPY OF THE REMITTANCE ADVICE OR DELIVERY NOTIFICATION BY EMAIL TO',
          fontSize: 8,
          bold: true,
          alignment: 'center'
        },
        {
          text: '<EMAIL>. FAILURE TO DO SO MAY RESULT IN THE WRONG ALLOCATION OF YOUR FUNDS',
          fontSize: 8,
          bold: true,
          alignment: 'center'
        }
      ]
    },
    content: [
      {
        columns: [
          {
            width: 200,
            image: 'data:image/png;base64,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',
          },
          {
            width: '*',
            text: ''
          },
          {
            width: '35%',
            margin: [0, 10, 0, 0],
            fontSize: 20,
            text: incorporation.version === '2.0' ?  'DRAFT INVOICE' : 'INVOICE'
          },
        ],
        columnGap: 10
      },
      '\n\n',
      {
        columns: [
          {
            width: '*',
            type: 'none',
            ol: [
              incorporation.name,
              '\n\n\n\n\n\n\n\n',
              formatDate(incorporation.submittedAt, "DD MMMM YYYY"),
              '\n',
              {
                columns: [
                  {
                    width: 'auto',
                    text: 'Invoice No: ',
                    bold: true
                  },
                  {
                    text: incorporation.invoiceNumber.toString()
                  }
                ],
                columnGap: 5
              },

            ]
          },
          {
            width: '35%',
            type: 'none',
            ol: [
              'Trident Trust Company (BVI) Ltd',
              'Trident Chambers',
              'Wickhams Cay',
              'PO Box 146',
              'Road Town, Tortola',
              'British Virgin Islands',
              'Tel **************',
              '<EMAIL>',
              'www.tridenttrust.com',
              '\n\n',
              'Our Ref:  TTRUM ES'
            ]
          }
        ]
      },
      '\n',
      {canvas: [{type: 'line', x1: 0, y1: 5, x2: 595 - 2 * 40, y2: 5, lineWidth: 1}]},
      '\n',
      ...generateFeesAndDisbursements(incorporation),
      '\n\nPayment is to be in US$ and due within 30 days from date of invoice.',
      '\n',
      {
        columns: [
          {
            width: 'auto',
            text: 'All bank charges of the originating bank and the intermediary banks are to be borne by the client so ' +
              'that Trident receives the total balance that is owed on the invoice.'
          },
          {
            width: '5%',
            bold: true,
            text: 'US$',
            alignment: "right"
          },
          {
            width: '20%',
            alignment: 'right',
            text: incorporation.payment.total ? incorporation.payment.total.toFixed(2).toString() : '0.00'
          }
        ],
        columnGap: 10
      },
      {
        canvas: [{type: 'line', x1: 0, y1: 5, x2: 595 - 2 * 40, y2: 5, lineWidth: 1,}],
        margin: [0, 0, 0, 10]

      },
      {
        columns: [
          {
            width: '65%',
            text: 'Please use the following link to make any payments by credit card: ',
            fontSize: 10,
            bold: true
          },
          {
            width: "*",
            fontSize: 10,
            text: '\thttps://tridenttrust.com/bvi-payments\t',
            color: "white",
            background: "#005C81",
          }
        ],
      },
      {
        style: 'table',
        table: {
          headerRows: 1,
          widths: ['35%', '35%', '15%', '15%'],
          body: [
            [{text: 'COMPANY NAME', style: 'tableHeader'},
              {text: 'REFERENCE', style: 'tableHeader'},
              {text: 'INVOICE DATE', style: 'tableHeader'}, {text: 'AMOUNT DUE', style: 'tableHeader'}],
            [
              {text: incorporation.name, style: 'tableText'},
              {text: incorporation.invoiceNumber.toString(), style: 'tableText'},
              {text: formatDate(incorporation.submittedAt, "DD/MM/YYYY").toString(), style: 'tableText'},
              {
                text: (incorporation.payment.total ? incorporation.payment.total.toFixed(2).toString() : '0.00'),
                style: 'tableText'
              }
            ]]
        },
        layout: {
          hLineWidth: function (i, node) {
            return (i === 0 || i === node.table.body.length) ? 1 : 0;
          },
          vLineWidth: function (i, node) {
            return (i === 0 || i === node.table.widths.length) ? 1 : 0;
          }
        }
      },
      {
        columns: [
          {
            text: ["Please make enquiries with your local Trident Office if you wish to pay by credit card or email ",
              {
                text: "<EMAIL>",
                italic: true, fontSize: 10, color: "blue",
                decoration: 'underline',
                decorationStyle: 'solid',
                decorationColor: 'blue'
              },
              " for details.\n" +
              "Please send your remittance by way of a cheque of banker's draft drawn on a U.S. bank in favour of Trident Trust Company (BVI) Limited to the following address: \n" +
              "\n" +
              "TRIDENT TRUST COMPANY (BVI) LIMITED.\n" +
              "TRIDENT CHAMBERS, WICKHAMS CAY.\n" +
              "ROAD TOWN TORTOLA,\n" +
              "BRITISH VIRGIN ISLANDS\n" +
              "VG110"]
          },
          {
            text: "If making payment by wire transfer, please quote our invoice number, and wire to:\n\n" +
              "USA BANK details\n" +
              "Receiving Bank:\n" +
              "ABA: *********\n" +
              "CIBC Bank USA\n" +
              "120 South LaSalle Street" +
              "Chicago, IL 60603\n" +
              "USA\n\n" +
              "Swift Code: PVTBUS44" +
              "\n" +
              "Account Name: Trident Trust Company (BVI) Ltd\n" +
              "Account No: 2906031",
          }
        ],
        columnGap: 10
      }
    ],
    styles: {
      header: {
        fontSize: 18,
        bold: true,
        margin: [0, 0, 0, 10]
      },
      subheader: {
        fontSize: 14,
        bold: true,
        margin: [0, 10, 0, 5]
      },
      table: {
        margin: [0, 5, 0, 15]
      },
      tableHeader: {
        bold: true,
        color: 'black',
        alignment: 'center',
        fontSize: 10
      },
      tableText: {
        fontSize: 9,
        alignment: 'center'
      }
    },
    defaultStyle: {
      font: 'Helvetica',
      fontSize: 11,
      lineHeight: 1.1,
    },
    info: {
      author: 'Trident Trust Company (BVI) Limited',
      creator: 'Trident Trust Company (BVI) Limited',
      producer: 'Trident Trust Company (BVI) Limited',
    },
  };

  const pdfDoc = printer.createPdfKitDocument(docDefinition);

  const chunks = [];
  let result;

  pdfDoc.on('data', function (chunk) {
    chunks.push(chunk)
  });
  pdfDoc.on('error', function (err) {
    console.log("pdf generation error ", err);
  });
  pdfDoc.on('end', function () {
    result = Buffer.concat(chunks);

    res.contentType('application/pdf');
    res.setHeader('Content-Disposition', 'inline; filename=SUB-' + incorporation.masterClientCode + '-' + formatDate(incorporation.submittedAt, "YYYYMMDD") + '.pdf');
    res.send(result)
  });
  pdfDoc.end()
};

exports.generateIncorporationPdf = function (incorporation, res) {

  let printer = new PdfPrinter({
    Helvetica: {
      normal: 'Helvetica',
      bold: 'Helvetica-Bold',
      italics: 'Helvetica-Oblique',
      bolditalics: 'Helvetica-BoldOblique'
    }
  });

  const docDefinition = {
    watermark: {
      text: incorporation.status === 'IN PROGRESS' ? 'DRAFT' : incorporation.status,
      color: '#0081B4',
      opacity: 0.3,
      bold: true,
      italics: false
    },
    info: {
      author: 'Trident Trust Company (BVI) Limited',
      creator: 'Trident Trust Company (BVI) Limited',
      producer: 'Trident Trust Company (BVI) Limited',
    },
    footer: function () {
      // you can apply any logic and return any valid pdfmake element

      return [

        {
          canvas: [
            {
              type: 'polyline',
              lineWidth: 2,
              color: "#0081B4",
              closePath: true,
              points: [{x: 0, y: 0}, {x: 800, y: 0}]
            },

          ]
        }, {
          text: ' ',
          fontSize: 6
        }, {
          text: 'Address: Trident Chambers, Wickhams Cay, PO Box 146, Road Town, Tortola, British Virgin Islands ',
          fontSize: 10,
          alignment: 'center'
        },
        {text: 'Telephone: ****** 494 2434  Website: www.tridenttrust.com', fontSize: 10, alignment: 'center'}
      ]
    },
    content: [
      {
        // under NodeJS (or in case you use virtual file system provided by pdfmake)
        // you can also pass file names here
        image: 'data:image/png;base64,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',
        width: 350,
      },
      {
        style: 'table',
        table: {
          widths: [300, '*'],
          body: createIncorporationStep1Info(incorporation)
        },
      },
      ...createIncorporationStep2Info(incorporation),
      {
        style: 'table',
        table: {
          widths: [300, '*'],
          body: createIncorporationStep3Info(incorporation)
        },
      },
      ...createIncorporationStep4Info(incorporation),
      {
        style: 'table',
        table: {
          widths: [300, '*'],
          body: createIncorporationStep5Info(incorporation)
        },
      },
      {
        style: 'table',
        table: {
          widths: [300, '*'],
          body: createIncorporationStep6Info(incorporation)
        },
      },
      {
        style: 'table',
        table: {
          widths: [300, '*'],
          body: createIncorporationStep7Info(incorporation)
        },
      },
    ],
    styles: {
      header: {
        fontSize: 18,
        bold: true,
        margin: [0, 0, 0, 10]
      },
      subheader: {
        fontSize: 16,
        bold: true,
        margin: [0, 10, 0, 5]
      },
      table: {
        margin: [0, 5, 0, 15]
      },
      tableHeader: {
        bold: true,
        color: 'black'
      }
    },
    defaultStyle: {
      font: 'Helvetica'
    }
  };
  const pdfDoc = printer.createPdfKitDocument(docDefinition);

  const chunks = [];
  let result;

  pdfDoc.on('data', function (chunk) {
    chunks.push(chunk)
  });
  pdfDoc.on('end', function () {
    result = Buffer.concat(chunks)

    res.contentType('application/pdf');
    res.setHeader('Content-Disposition', 'inline; filename=incorporation.pdf');
    res.send(result)
  });
  pdfDoc.end()
};

exports.generateFinancialReportPdf = function (report, res) {
  const printer = new PdfPrinter({
    Helvetica: {
      normal: 'Helvetica',
      bold: 'Helvetica-Bold',
      italics: 'Helvetica-Oblique',
      bolditalics: 'Helvetica-BoldOblique'
    }
  });

  const STYLES = {
    header: {
      fontSize: 13,
      bold: true,
      margin: [0, 0, 0, 10],
      color: '#123863'
    },
    subheader: {
      fontSize: 12,
      bold: true,
      margin: [0, 10, 0, 5],
      color: '#123863'
    },
    table: {
      margin: [0, 3, 0, 15]
    },
    priceTable: {
      margin: [0, 0, 0, 0]
    },
    tableHeader: {
      bold: true,
      color: 'black',
      fontSize: 11,
    },
    tableText: {
      bold: false,
      color: 'black',
      fontSize: 11,
      lineHeight: 1.2,
      margin: [0, 2, 0, 0]
    },
    calculatedText: {
      bold: false,
      color: 'grey',
      fontSize: 11,
      lineHeight: 1.2,
      margin: [0, 2, 0, 0]
    },
    tablePrices: {
      alignment: 'right',
      margin: [0, 0, 15, 0]
    },
    tablePriceText: {
      margin: [0, 0, 15, 0],
      fontSize: 10
    }
  };

  const docDefinition = {
    watermark: {
      text: report.status === 'IN PROGRESS' || report.status === 'RE-OPEN' || report.status === 'SAVED' ? 'PREVIEW' : '',
      color: '#0081B4',
      opacity: 0.2,
      bold: true,
      italics: false
    },
    info: {
      author: 'Trident Trust Company (BVI) Limited',
      creator: 'Trident Trust Company (BVI) Limited',
      producer: 'Trident Trust Company (BVI) Limited',
    },
    footer: function (currentPage, pageCount) {
      const diffPages = pageCount > 3 ? pageCount - 3 : 1;

      if (currentPage > diffPages) {
        return [
          { text: 'Page ' + (currentPage - diffPages) + ' of ' + (pageCount - diffPages), alignment: 'center', fontSize: 10 }
        ];
      }
    },
    content: [
      {
        image: 'data:image/png;base64,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',
        width: 180,
        alignment: 'right',
        margin: [0, 0, 0, 20]
      },
      { text: 'BVI Business Companies - Annual Financial Return', style: 'header' },
      { text: 'Entity Details', style: 'subheader' },
      {
        style: 'table',
        table: {
          widths: ['50%', '25%', '25%'],
          body: createARCompanyInfoTable(report)
        },
        layout: {
          hLineWidth: function (i, node) {
            return (i === 0 || i === node.table.body.length) ? 1 : 0;
          },
          vLineWidth: function (i, node) {
            return (i === 0 || i === node.table.widths.length) ? 1 : 0;
          },
          hLineColor: function (i, node) {
            return i === 0 || i === node.table.body.length ? '#0070C0' : '';
          },
          vLineColor: function (i, node) {
            return i === 0 || i === node.table.widths.length ? '#0070C0' : '';
          },
        },
      },
      { text: 'Report Details', style: 'subheader' },
      {
        style: 'table',
        table: {
          widths: ['50%', '50%'],
          body: createARDetailsTable(report)
        },
        layout: {
          hLineWidth: (i, node) => (i === 0 || i === node.table.body.length) ? 1 : 1,
          vLineWidth: (i, node) => (i === 0 || i === node.table.widths.length) ? 1 : 0,
          hLineColor: () => '#0070C0',
          vLineColor: () => '#0070C0',
        },
      },
      { text: 'Declaration', style: 'subheader' },
      {
        style: 'table',
        table: {
          widths: ['*'],
          body: createARConfirmationTable(report)
        },
        layout: {
          hLineWidth: function (i, node) {
            return (i === 0 || i === node.table.body.length) ? 1 : 0;
          },
          vLineWidth: function (i, node) {
            return (i === 0 || i === node.table.widths.length) ? 1 : 0;
          },
          hLineColor: function (i, node) {
            return i === 0 || i === node.table.body.length ? '#0070C0' : '';
          },
          vLineColor: function (i, node) {
            return i === 0 || i === node.table.widths.length ? '#0070C0' : '';
          },
        }
      },
      {
        style: 'table',
        table: {
          widths: ['*'],
          body: [
            [{ text: [{ text: 'Name of the person stating the declaration: ', bold: true }, { text: report.declaration?.name ? report.declaration?.name : '' }], margin: [0, 10, 0, 10], style: 'tableText' }],
            [{ text: 'Relation to entity: ' + (report.declaration?.relation ? (report.declaration?.relation === 'Other' ? report.declaration?.relation + ' - ' + report.declaration?.relationOther :report.declaration?.relation ): ''), style: 'tableText' }],
            [{ text: 'Phone number: ' + (report.declaration?.phone ? report.declaration?.phone : ''), style: 'tableText' }],
            [{ text: 'Email: ' + (report.declaration?.email ? report.declaration?.email : ''), margin: [0, 5, 0, 5], style: 'tableText' }]
          ]
        },
        layout: {
          hLineWidth: (i, node) => (i === 0 || i === node.table.body.length) ? 1 : 0,
          vLineWidth: (i, node) => (i === 0 || i === node.table.widths.length) ? 1 : 0,
          hLineColor: () => '#0070C0',
          vLineColor: () => '#0070C0',
        }
      }
    ],
    styles: STYLES,
    defaultStyle: { font: 'Helvetica' }
  };


  if (report.reportDetails?.isExemptCompany !== true) {
    const isTridentServiceCompleted = (report.reportDetails.serviceType === ACCOUNTING_SERVICE_TYPES.TRIDENT_SERVICE_COMPLETE ||
      report.reportDetails.serviceType === ACCOUNTING_SERVICE_TYPES.TRIDENT_SERVICE_DROP) && report.status === REPORT_STATUS.HELP_COMPLETED;
   
    if (report.reportDetails.serviceType === ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_COMPLETE || isTridentServiceCompleted) {
      docDefinition.content.push({ text: 'ANNUAL FINANCIAL RETURN', style: 'header', pageBreak: 'before', alignment: 'center', margin: [0, 40, 0, 0] })

      docDefinition.content.push({ text: report.companyData.name, style: 'subheader', alignment: 'center', margin: [0, 40, 0, 40] })

      docDefinition.content.push({
        style: 'table',
        table: {
          widths: [350],
          body: [
            [{ text: 'FINANCIAL INFORMATION FOR YEAR ENDING', alignment: 'center', margin: [0, 5, 0, 0] }],
            [{ text: formatDate(report.financialPeriod.end, "YYYY-MM-DD"), alignment: 'center', margin: [0, 10, 0, 5] }]
          ]
        },
        layout: {
          hLineWidth: (i, node) => (i === 0 || i === node.table.body.length) ? 1 : 0,
          vLineWidth: (i, node) => (i === 0 || i === node.table.widths.length) ? 1 : 0,
          hLineColor: () => '#0070C0',
          vLineColor: () => '#0070C0',
        },
        margin: [80, 0, 20, 20]
      })

      docDefinition.content.push({
        style: 'table',
        table: {
          widths: [200, 142],
          body: createAssetsAndLiabilitiesTable(report)
        },
        layout: {
          hLineWidth: (i, node) => (i === 0 || i === node.table.body.length) ? 1 : 0,
          vLineWidth: (i, node) => (i === 0 || i === node.table.widths.length) ? 1 : 0,
          hLineColor: () => '#0070C0',
          vLineColor: () => '#0070C0',
        },
        margin: [80, 0, 20, 0],
        pageBreak: 'after'
      })

      docDefinition.content.push({
        style: 'table',
        table: {
          widths: [200, 142],
          body: createIncomeAndExpensesReport(report)
        },
        layout: {
          hLineWidth: (i, node) => (i === 0 || i === node.table.body.length) ? 1 : 0,
          vLineWidth: (i, node) => (i === 0 || i === node.table.widths.length) ? 1 : 0,
          hLineColor: () => '#0070C0',
          vLineColor: () => '#0070C0',
        },
        margin: [80, 0, 20, 0]
      })

      
    }
    if (report.reportDetails.serviceType === ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE) {

      const mappedReport = report.getMappedSchema();

      if(mappedReport){
        let mappedFieldsRows= []
        mappedReport.forEach((item) => {
          if(!item.hideFromDisplay) {
            if(item.isArray){
              mappedFieldsRows.push([{ text: item.label, style: 'tablePriceText' }, { text: `Total items  ${item.value.length}`, style: (item.isCalculated ? 'calculatedText' : 'tableText') }]);
              item.value.forEach((itemArrayValue) => {
                itemArrayValue.forEach((field) => {
                  mappedFieldsRows.push([{ text: "-" + field.label, style: 'tablePriceText' }, { text: field.value, style: (item.isCalculated ? 'calculatedText' : 'tableText') }]);
                })
              })
            }else{
              mappedFieldsRows.push([{ text: item.label, style: 'tablePriceText' }, { text: item.value, style: (item.isCalculated ? 'calculatedText' : 'tableText')}]);
            }
          }
        })
        docDefinition.content.push({ text: "Inputs Summary", style: 'subheader', alignment: 'center', margin: [0, 40, 0, 40] })
        docDefinition.content.push({
          style: 'table',
          table: {
            widths: ["70%", "30%"],
            body: mappedFieldsRows
          },
          layout: {
            hLineWidth: (i, node) => (i === 0 || i === node.table.body.length) ? 1 : 0,
            vLineWidth: (i, node) => (i === 0 || i === node.table.widths.length) ? 1 : 0,
            hLineColor: () => '#0070C0',
            vLineColor: () => '#0070C0',
          }
        })
      }

      docDefinition.content.push({ text: 'ANNUAL FINANCIAL RETURN', style: 'header', pageBreak: 'before', alignment: 'center', margin: [0, 40, 0, 0] })

      docDefinition.content.push({ text: report.companyData.name, style: 'subheader', alignment: 'center', margin: [0, 40, 0, 40] })

      docDefinition.content.push({
        style: 'table',
        table: {
          widths: [350],
          body: [
            [{ text: 'FINANCIAL INFORMATION FOR YEAR ENDING', alignment: 'center', margin: [0, 5, 0, 0] }],
            [{ text: formatDate(report.financialPeriod.end, "YYYY-MM-DD"), alignment: 'center', margin: [0, 10, 0, 5] }]
          ]
        },
        layout: {
          hLineWidth: (i, node) => (i === 0 || i === node.table.body.length) ? 1 : 0,
          vLineWidth: (i, node) => (i === 0 || i === node.table.widths.length) ? 1 : 0,
          hLineColor: () => '#0070C0',
          vLineColor: () => '#0070C0',
        },
        margin: [80, 0, 20, 20]
      })

      docDefinition.content.push({
        style: 'table',
        table: {
          widths: [200, 142],
          body: createAssetsAndLiabilitiesTable(report)
        },
        layout: {
          hLineWidth: (i, node) => (i === 0 || i === node.table.body.length) ? 1 : 0,
          vLineWidth: (i, node) => (i === 0 || i === node.table.widths.length) ? 1 : 0,
          hLineColor: () => '#0070C0',
          vLineColor: () => '#0070C0',
        },
        margin: [80, 0, 20, 0],
        pageBreak: 'after'
      })

      docDefinition.content.push({
        style: 'table',
        table: {
          widths: [200, 142],
          body: createIncomeAndExpensesReportForOption2(report)
        },
        layout: {
          hLineWidth: (i, node) => (i === 0 || i === node.table.body.length) ? 1 : 0,
          vLineWidth: (i, node) => (i === 0 || i === node.table.widths.length) ? 1 : 0,
          hLineColor: () => '#0070C0',
          vLineColor: () => '#0070C0',
        },
        margin: [80, 30, 20, 0],
        pageBreak: 'after'
      })  

      docDefinition.content.push({
        style: 'table',
        table: {
          widths: [200, 142],
          body: createIncomeAndExpensesReport(report)
        },
        layout: {
          hLineWidth: (i, node) => (i === 0 || i === node.table.body.length) ? 1 : 0,
          vLineWidth: (i, node) => (i === 0 || i === node.table.widths.length) ? 1 : 0,
          hLineColor: () => '#0070C0',
          vLineColor: () => '#0070C0',
        },
        margin: [80, 0, 20, 0]
      })

    }

  }

  let pdfDoc = printer.createPdfKitDocument(docDefinition);

  let chunks = [];
  let result;

  pdfDoc.on('data', function (chunk) {
    chunks.push(chunk)
  });
  pdfDoc.on('end', function () {
    result = Buffer.concat(chunks);
    res.contentType('application/pdf');
    res.setHeader('Content-Disposition', 'inline; filename=AR-' + report.companyData.masterclientcode + '-' + report.companyData.code + '-' + formatDate(report.submittedAt, "YYYYMMDD") + '.pdf');
    res.send(result)
  });
  pdfDoc.end()
}

// GENERATE PDF FOR FINANCIAL REPORT
exports.generateFinancialBSPLInvoicePdf = function (report, company, res) {
  const printer = new PdfPrinter({
    Helvetica: {
      normal: 'Helvetica',
      bold: 'Helvetica-Bold',
      italics: 'Helvetica-Oblique',
      bolditalics: 'Helvetica-BoldOblique'
    }
  });


  let addressInvoice = report.companyData.address;
  let companyCode = report.companyData.code;
  let companyName = report.companyData.name;

  let paymentText = "Financial Report " + (report.financialPeriod.start ? ("(" + (moment(report.financialPeriod.start).utc().format('YYYY/MM/DD') + " - " +
    moment(report.financialPeriod.end).utc().format('YYYY/MM/DD')) + ")") : "");
  let dueDate;

  let totalPaymentBeforeVAT = report.payment?.subtotal ? report.payment.subtotal : 0;
  let totalVat = report.payment.vat ? report.payment.vat : 0;
  let totalPayment = report.payment.total.toFixed(2).toString();

  totalPaymentBeforeVAT = totalPaymentBeforeVAT.toFixed(2).toString();
  totalVat = totalVat.toFixed(2).toString();

  if (report.submittedAt) {
    dueDate = new Date(report.submittedAt);
    dueDate.setDate(dueDate.getDate() + 30);
  }

  let addressHeader = [
    'TT & Asociados',
    'RUC 745091-1-21682',
    'P.H. Aseguradora Ancon Tower',
    'Office 19-N',
    'Centenario Avenue\nCosta del Este',
    'Panama City\nRepublic of Panama',
    'E: <EMAIL>',
    '\n',
    'www.tridenttrust.com',
  ]

  const docDefinition = {
    footer: function () {
      // you can apply any logic and return any valid pdfmake element
      return [
        {
          canvas: [
            {
              type: 'polyline',
              lineWidth: 2,
              color: "#0081B4",
              closePath: true,
              points: [{ x: 0, y: 0 }, { x: 800, y: 0 }]
            },

          ]
        }, {
          text: ' ',
          fontSize: 6
        }, {
          text: 'Address: Trident Chambers, Wickhams Cay, Road Town, Tortola, British Virgin Islands',
          fontSize: 6,
          bold: true,
          alignment: 'center',
          color: '#005C81'
        },
        {
          text: 'E-mail: <EMAIL>  Website: www.tridenttrust.com',
          fontSize: 6,
          bold: true,
          alignment: 'center',
          color: '#005C81'
        },
      ]
    },
    info: {
      author: 'Trident Trust Company (BVI) Limited',
      creator: 'Trident Trust Company (BVI) Limited',
      producer: 'Trident Trust Company (BVI) Limited',
    },
    content: [
      {
        columns: [
          {
            width: 200,
            image: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAbgAAABPCAYAAACZKtCmAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAABMaSURBVHhe7Z196F9VHcf9J4ggCIIIBEkKQzClEAxTkkgyRZIEMwaSWAtJElOQIJIkKEaioxwqOp9Sy7mp+dBUppJOp3t+1D3odJtNp3PPD27qbe/f7ul39tnnc+8595x7v3d37xe8cX6/95x7Hj/v+z3f8z2/YwpCCCFkgNDgCCGEDBIaHCGEkEFCgyOEEDJIaHCEEEIGCQ2OEELIIKHBEUIIGSQ0OEIIIYOEBkcIIWSQ0OAIIYQMEhocIYSQQUKDI4QQMkhocIQQQgYJDY4QQsggocERQggZJMEGt2f/J9m1de/H6uup2rVPfz1VhBBCjhyCDO6tbXuLYya9lFWn37e0mL5qs/peii56dGUxef5G9b0UXfXs2rI1wjj5gsm90LU3zCxLRMjowXjUxmnXOveXd5UlIkNmJAZ32r1Lis179hcz125R32+qCx9ZOVbe3AZ35aw4cwNf+f6kXugnv3mgLBEhowfjURunXevkH00uS0SGTOcGd+o9S4pNu/aN5ZvT4C54+PWxPEFOg7vimTfLXOPQJtUoRIMjfYIGR7qkU4P71t2Li407D5obyGVw509/rczxILkM7vKn3yhzjOeO6fPGNHfp+vKVMLQAEDsZN7y7rfH9CWkTjEc3NmOR8wKKzeex514bS4P/kuHTmcGdcuei4p0dH5U5HiSHwZ330OEDNYfBTXyqubmlkMPgCBkicl5ATYySHD10YnAnTV1UrNt+qLmBVIM7Z9qK4pNPy8w8Ug3usplrypy6hwZHiI6cFxANjlTRusGdeMfCYu3WvWVOh5JicGc/uLzYr7nbAVIM7tInR2dugAZHiI6cFxANjlTRqsGdcPvC4o0te8pcDqepwX3vH8uLvR/bv0tranCXPLG6zGF00OAI0ZHzAqLB5Wf5mveKOYvXHSK8diTSmsF97bYFxcoPbXMDTQzurAeWjf2Qu4omBjfh8VVl6tGS2+DWv7u1uOme2cXFVz/wf8n8oTMm3Dr23sTrHh67/qGnlhbbduifvENBPlIW0w7cD+/j/q6c+K2SVlbIlRdCuqdnrx6raw5cWXKpqlyufyDctw38+mhl8cvQtl5etK68azzaOEgxOFdvN44g7R5yrKXMDb8tnKrGRwyyH0NBXVAn1A+/EdTawAnXXD9lVqXh5Z4/VaqjFYM7/tb5xYoPdpepbWIN7sz7lxXbP/q4TG0Ta3AXP9YPcwO5DQ4BReYXIxhO06Ck5Weh1buJEIzqJmAducriVNV+sn/aMDm/PlpZUsdIjG68+8XyrvFo+aUYXGq9EeyfejEudmj5VI2PGGR9Qpg6Y16tqVlC/bWy554/Vaoju8Edd8v8Ytn7u8qU1cQYHE4+wdFeIcQYHE4+6RNdGNwPJ9419roUgsUvfj/jsOshDObYJ00tHwut3t+ZcMtYQJT6w82zxq5HPWQaX9YErCP3BK0qA96T1+deDvLro5VFK0NbQv81RcuvDYPD61JubnzjwFyU12OchfaZTAsh/xwgHz/fKvCpTVshwZzD/MKDlqs7TBz9Zs0LGRdyz58q1ZHV4I6dMq9YvCnM3ECowbmTT0IJNTh38kmf0AZHboPDParAgL160pOHpcOTXkzwlekhC63eIcEQExUTUCuvEz6FxiwpNSkL2sUFBCcECaSrejDQ+gftHPswUYVfH9xPgnuhnGhHv/wauM4vK2S1jZ+XC5JWviHI+0JtGFwVGEcwAJkGfRZSN5kOSmkTH1mfKqS5wbhDVg9Qf7Q5jBDpMO8kbtyjPE5W3PDL4KS1B+7r5+fuUUc2g/vyzXOLBe/tLFOEEWJw/sknoYQYnH/ySZ/og8E5tGCGiRxqFjItZNHU4HwsY4ZizDlHWULR+gdCAIox5SrqDC4GbUy01TYSeV+oa4NzIMDKdCEPJjINlNonDlkfC7SZfx0U8+AKnNGnPojJckC52gNkMbgv/W1uMXfjjvLqcOoMTp58EkqdwZ0/o7+nGPTJ4IBWHu2pTUOmgyxymgomq7aUFGpyOctShxVkIXzyzIFfn9TgQYMbR1vOx3JlFfJ6KFdAl/WxwDzwr4NRjQq/HE652gMkG9wX//pqMee/8eYGqgzulLsOP/kklCqD004+6RNacB2lwWFpSaaHQj5daOkscpsKjMwto/gK+QTaF4ODcgQfGpyO1fahYIxp6aseorTrcwV0WR8NbT7nun8TZFlylyfJ4L4w+dXipXe2l1fFYxncwZNPmi/PWAaHk08+1X8b3hv6ZnBApodCAouWzqINU7ECUN0noz4ZHBTy3UgVNDgdq+1j0FYKqtpDXgvlCuiyPhpa/+W6fxNkWXKXp7HBff6mV4oX1jc3N6AZXNXJJ6FoBld18kmf6KPBNQ34Mg1k0ZapaBMaqppEbZVFQ+sfTSFLqxZ+fVKDh9aebbWNRN4XGrXBaWMFr1nIa6HUPnHI+mho/Zfr/k2QZcldnkYG97kb5xTPr9tWvtscaXBfv31hsabi5JNQpMHVnXzSJ7QJ00eDC8lDpoEstHvkCpzW1m6LNssi8fsH963aJNP0C32/PqnBgwZ3KNpYwWsW8looV0CX9dHQ+i/0O/U2kGWBcrUHiDa4z94wp5j1dp4tzL7B4eSTVTUnn4TiG9zYySf7jwxzA9qEocGloW3rhizDaLMsEr9/XJtav+9rurPSr09q8KDBHYq20aRqbshroVwBXdZHA+3lX+OUskKQglaWXO0BogzuM395uXjmrTzmBpzB4eST1zbXn3wSijM4nHyyI+Dkkz6hBddRG5z2g9CQpz6ZBrLQ6p0rcMLIZN6QFRzbLIvE7x/XLzAxy+Sa7Kz065MaPGhwh6KNFdePGvJaKFdAl/XRsOYCVghylSMGrSw5yxFlcDPf3FK+kgcYXMzJJ6HA4GJOPukT2oQZpcEh2Mr0UEhg0dJZaPXOGTi1ZUqrHdoui4/fP3558EStlRmK3Vnp1yc1eNDgDkVuuYeq2kNeC+UK6LI+Ftr4dsIxd01WCZqilSFXe4Agg1v94Z5iysKNY0aUU3+es6GYsWqz+l6Krpy1tpi9Ybv6Xoq62KKiDb5RGpz2g1Yo5PsgLZ2FVu+cgVPLH+dWarRdFh+/f2S/aH3nFLOz0q9PavCgwY1jpe/zzwSAVW4nmHbIQcY50O6fqz1Ao00mOfTjR15Xd1Gm6tcHzE3bRZmqSa+8U7ZGu2jBdZQGBxOQ6fv+Q28NLTBDGm2XxcfvH61frAcMKPR7E78+qcGDBjcONirJtHVzS14P5Qrosj5VWPPBF+Y+/spAm2j3zdUeYCQG547Jym1wv3rmzbF8cxvcnw580uwKLbiOyuCuUXb04cfToUsYMi1kodU7Z+C0JrT2SbTtsvj4/WP1S+rOSr8+qcFDa8e22kYi7wuNyuC0jUtYUq7rD5kGyhXQZX3qsDZfSbVpdNr9crUH6Nzgzp8+fpJIToO7/Ok3ylzzGtwfX+7O3IAWXLs2OExS7ekUEzhmt5VMD1m0bSqWwWmTSSsLJjnaRJO2CSe07H7/VPWLViYoZGelnzY1eBztBoe2xkYfmSZ0bsh0UGqfOGR9QsDJJtqJP5ow1nKV1aHdJ+c9OjW4c8UxWbkMbuJT4+YGchnc9bPXlzl2hxbIujA4mBqe0rRPbRB29YV8WvDR8rHQ6t0ng4tVboNDYG26s9KvT2rwOBoNDuMef1AXc0PbVIJ+CX3wk2mh1D5xyPqEgrGFPrQ2NUnl3Iii5Z+rPUBnBveDaSsKeZBIDoO77N9rytzGyWFw172Yr5Fj0IJrboOLEZ7umh4VpeVnMSqD00xbKwvaAa/XyQWJ0LL7/YP0VSCIWkGoamcl8nXXpQYPrR1z9lMV8r5QGwYXKvRFbN21fHIFdFmfWGKMrulvMiVa3rnaA3RicGf/c3mxTzkmK9Xgfvbk4eYGUg3udy+MxtyAH4ycRmVwqYFLy9NCq3fOwIm8ZP6QRttl8fH7p87gQFV/Wg8iNDidlLnR9PQPLa/UPnHI+jTFGZ2fl6YcJqflm6s9QOsGh2Oy9hjHZKUY3CVPrC5zOZwUg/vtf94ucxkNXRgcllTwui9tHR4DOAWZH2TRtqloGzXwpKrRZ4MDsTsraXA6ftv7cnPCSRsP+E62SXCX+UC4Rw6Qj59vKljdsDY4OcX+JlOi5ZmrPUCrBvddHJO1zz4mq6nBTXh8VZmDTlODu/b50Zob0CZTF9/BaddBKcFLy89Cq3fOwKnlr7UDaLssPn67W+XRiNlZ6dcnNXgcDQYnwUODdl2T4K7lk9onDlmfXCDfqo0osd/N+2j55WoP0JrBnXH/0mJ7zTFZTQzu4n9VmxtoYnDXPPdWmXq0aMG1C4MD2rl6UNMBrOVl0bapyLyr8m+7LD5+/1j9YmH1l1w68uuTGjyORoMD1pb62PbUNgql1MFH1icnGE/WJqeU/tfySx2jPq0Y3Ol/X1ps2bu/TG0Ta3AXPbqyTFlNrMFd9ezaMuXo0YJrVwYHI9O+YMZW+CbIfCCLNk3FCmTWzrc2yyLxy2b1i0VV0PF3Vvr1SQ0eR6vBoa21uRG7jN/lOM8N2kD7JBc7bn1kXlDqGPXJbnCn3buk+GB3vbmBGIO78JEwcwMxBoeTT/qENgG6MjigBTCoSSDR8rFoc+Jry3mYqBZtlkXi90+TQGE9lEBuCc2vT2rw0MZHW20jkfeFujI4YH33GVP/2L8+EIPfN3jwaQOtzfB9ZFNkXlDqGPXJanCn3rOk2LRrX5mqnlCDcyefhBJqcFeUJ5/0CS24dmlwQHtKw3c7/rJXCDIPyEKrd47AiTJrv12q+ulDW2XR8PunaaCzviOCUE+/PqnBww+iTm21jUTeF+rS4IA2NqDQZXyt/TA+c+A/yOHfbaE9UDVF5gP10uC+effiYuPOcHMDIQbnn3wSSojB+Sef9AltAnVtcNbEj/1TLVoeFlq9cwROLaBUfXoDbZVFw2/run6pompnJZ6w3b9Tg4fWnm21jUTeF+ra4KyHidBlfOueqf0C/H6ueoBLRc6PlE+Lfj5OOdrCkcXgTr5zUbFhx0fl1eHUGdx54uSTUOoMTp580ie04Nq1wQFrAwOO9glFS2/RhqlYwaiuDm2UxcLvn5B+qcLaCOErNXgc7QYHrHYOLYu2QpL6iUvWJWVnYx1yfqSMWz8fp9Qx6pNscCdNXVS8feD9JlQZ3DnTVhSfNvz7NFUG9/OZ+o/D+4IWXEdhcNZ3OzG//5FpIQut3imBE2XEBgCZZ0ggyV2WKvz+SQkUDuvBxCk1eNDg7A0n2k80NLQ2hFJMCZ8gXT4YA20il/xT+sDPx6k3BnfiHQuLtVubmRuwDO7sB5cX+5WTT0KxDO5S4+STPtEXgwPWRAz9/Y+W1iKnqVjmFjrxj2SDQ92tnZUQDW6cpgYHrCXhkGV86+Gx6cEKsix1fZwyBrR6pxizzAtKHaM+jQ3uhNsXFmu27CmvaIZmcDj5ZK9x8kkomsFVnXzSJ/pkcEBbToFCBqGWziKXqeDAaG1TScwS0JFscMAKoFBq8KDBjaONEyhkGR/l1tLiQOcYsAzvj/e6h0+UDdfBiENXYhy43v+eD0pdWvXzckodoz6NDO6rty0oVn6YZm5AGtxZOPlkf5q5AWlwdSef9Alt0ozS4KwgELI1WEtnkWIqmHgwNn+ZxldsEEwpiw+MZs7idZVPuH77xvRLHdb3j6nBgwY3jtXGMJwQ84A5aOnxSa7uUxHyx6n+fjp8cq+6L97zzRD/xl/uDikr6ipXReruF4Kfn1PqGPWJNrjjb51frPhgd/lOGr7BnXn/smJHzcknofgG99PHjhxzA30zOGB9r1MX2LQ0Flq9YVgwLpiEFF7H5LRMDUKeTZZPtLLA0HEvKW0pVKqqndoyOKAtJ9Hgxkk1OGCZVOgyvpUewvjCGJ86Y97YmMe/rTEfYjaYC9byNT7Rufv4wmva/bBCANNLReYLjczgjrtlfrHs/V3lq+k4gzv9vqXF1r15zA04gws9+aRPaMF11AaHiSHzcKoa5Nr1Flq9mwgTD0EjZfLlKovTqAwOSEOiwY2Tw+BgKqnLwXgQsb4KCBHMNOaTFPrLKnOI8MCb+snNoeWfOkZ9gg3u2CnzisWb8pkbgMF9+94lxeY9YSefhAKDizn5pE/kNjgEeuTpK/Tp0geBROYDVQU37XoLlKlqc0SVkC/KEfMThipQFlnuFCGAWfj906RfQvDrk/rUjbq4vJyq6pcTre9TDE6bG1AsWptAsd9PuTmm1VMKhoj8m6xQOFy5tfw14X45zQf47eWUOkZ9gg1uwXs7y//LBwxu0+64H4eHAIMjRzaYSHUiZIjg0xHGNwwPD26+YEo5DcCB+8l7QShDG/friiCDI4QQQo40aHCEEEIGCQ2OEELIIKHBEUIIGSQ0OEIIIYOEBkcIIWSQ0OAIIYQMEhocIYSQQUKDI4QQMkhocIQQQgYJDY4QQsggocERQggZJDQ4Qgghg4QGRwghZJDQ4AghhAwSGhwhhJBBQoMjhBAySGhwhBBCBgkNjhBCyAApiv8B3YVweUT7DGsAAAAASUVORK5CYII=',
          },
          {
            width: '*',
            text: ''
          },
          {
            width: '60%',
            margin: [0, 10, 0, 0],
            fontSize: 14,
            bold: true,
            color: '#008AC6',
            text: 'Trident Corporate Services (Panama) SA',
            alignment: 'right'
          },
        ],
        columnGap: 10
      },
      '\n\n',
      {
        columns: [
          {
            width: '65%',
            stack: [
              {
                text: 'INVOICE',
                style: 'header'
              },
              {
                text: companyName + '\n' + addressInvoice,
                style: 'default'
              },
            ]
          },
          {
            width: '35%',
            type: 'none',
            ol: addressHeader
          }
        ]
      },
      '\n',
      { canvas: [{ type: 'line', x1: 0, y1: 5, x2: 595 - 2 * 40, y2: 5, lineWidth: 1 }] },
      '\n',

      {
        columns: [
          {
            width: '25%',
            text: '\n'
          },
          {
            width: '75%',
            alignment: 'right',
            style: 'table',
            table: {
              headerRows: 1,
              widths: ['25%', '25%', '25%', '25%'],
              body: [
                [{ text: 'CLIENT CODE', style: 'tableHeader', },
                { text: 'INVOICE DATE', style: 'tableHeader' },
                { text: 'DUE DATE', style: 'tableHeader' },
                { text: 'INVOICE NUMBER', style: 'tableHeader' }
                ],
                [
                  { text: companyCode, style: 'tableText' },
                  { text: formatDate(report.submittedAt, "DD/MM/YYYY").toString(), style: 'tableText' },
                  { text: formatDate(dueDate, "DD/MM/YYYY").toString(), style: 'tableText' },
                  { text: report.invoiceNumber?.toString(), style: 'tableText' }
                ]]
            },
            layout: {
              paddingLeft: function () {
                return 1;
              },
              paddingRight: function () {
                return 1;
              },
              paddingTop: function () {
                return 5;
              },
              paddingBottom: function () {
                return 5;
              },
            }
          }
        ],
        columnGap: 10
      },
      {
        text: '\n'
      },
      {
        style: 'table',
        table: {
          headerRows: 1,
          widths: ['20%', '20%', '15%', '15%', '15%', '15%'],
          body: [
            [

              {
                colSpan: 6,
                border: [true, true, true, true],
                text: companyName + " / " + report.invoiceNumber,
                style: 'tableHeaderLeft'
              },
              '', '', '', '', '',
            ],
            [
              {
                colSpan: 2,
                text: '\n',
              },
              '',
              {
                text: 'COST USD',
                style: 'tableHeaderRight'
              },
              {
                text: 'QUANTITY',
                style: 'tableHeaderRight'
              },
              {
                text: 'VAT RATE %',
                style: 'tableHeaderRight'
              },
              {
                text: 'VAT USD',
                style: 'tableHeaderRight'
              },
            ],
            [

              {
                colSpan: 6,
                border: [true, false, true, false],
                text: '\n' +
                  'Fees',
                bold: true,
                style: 'tableTextLeft',
                margin: [5, 0, 5, 0]
              },
              '', '', '', '', '',
            ],
            [
              {
                colSpan: 5,
                border: [true, false, false, false],
                text: paymentText,
                style: 'tableTextLeft',
                margin: [5, 0, 5, 0]
              },
              '', '', '', '',
              {
                border: [false, false, true, false],
                text: totalPaymentBeforeVAT,
                style: 'tableSmTextRight',
                margin: [5, 0, 5, 0]
              },
            ],
            [
              {
                colSpan: 3,
                border: [true, false, false, true],
                text: '\n',
              },
              '', '',
              {
                colSpan: 3,
                border: [false, false, true, true],
                table: {
                  widths: ['55%', '45%'],
                  body: [
                    [
                      {
                        border: [false, true, true, true],
                        text: 'Subtotal - Subject to VAT',
                        style: 'tableSmTextLeft',
                      },
                      {
                        text: totalPaymentBeforeVAT,
                        style: 'tableSmTextRight',
                      }
                    ],
                    [
                      {
                        border: [false, true, true, true],
                        text: 'Subtotal - Not Subject to VAT',
                        style: 'tableSmTextLeft',
                      },
                      {
                        text: '0.00',
                        style: 'tableSmTextRight',
                      }
                    ],
                    [
                      {
                        border: [false, true, true, true],
                        text: 'Total before VAT',
                        style: 'tableSmTextLeft',
                      },
                      {
                        text: totalPaymentBeforeVAT,
                        style: 'tableSmTextRight',
                      }
                    ],
                    [
                      {
                        border: [false, true, true, true],
                        text: 'VAT',
                        style: 'tableSmTextLeft',
                      },
                      {
                        text: totalVat,
                        style: 'tableSmTextRight',
                      }
                    ],
                  ]
                },
                layout: {
                  hLineWidth: function () {
                    return 0.5;
                  },
                  vLineWidth: function () {
                    return 0.5;
                  },
                  hLineStyle: function () {
                    return { dash: { length: 2, space: 4 } };
                  },
                  vLineStyle: function () {
                    return { dash: { length: 2 } };
                  },
                  paddingLeft: function () {
                    return 5;
                  },
                  paddingRight: function () {
                    return 5;
                  },
                  paddingTop: function () {
                    return 5;
                  },
                  paddingBottom: function () {
                    return 5;
                  },
                }
              },
            ],
            [
              {
                colSpan: 3,
                border: [false, false, true, false],
                text: '\n',
                style: 'tableSmTextLeft',
                margin: [5, 0, 5, 0]
              },
              '',
              '',
              {

                border: [true, true, true, true],
                text: 'TOTAL',
                style: 'tableHeaderRight',
                margin: [5, 0, 5, 0]
              },
              {
                colSpan: 2,
                border: [true, true, true, true],
                text: 'USD ' + totalPayment,
                style: 'tableHeaderRight',
                margin: [5, 0, 5, 0]
              },
              '',
            ]
          ]
        },
        layout: {
          paddingLeft: function () {
            return 5;
          },
          paddingRight: function () {
            return 5;
          },
          paddingTop: function () {
            return 5;
          },
          paddingBottom: function () {
            return 5;
          },
        }
      },
      // as
      {
        text: 'Note',
        fontSize: 9,
        decoration: 'underline',
        margin: [0, 5, 0, 5]

      },
      {
        table: {
          widths: ['100%'],
          body: [
            [
              {
                border: [true, true, true, true],
                ul: ['Bank Details are appended to this invoice. USD funds routed to our account using any other' +
                  ' correspondent banking details may incur processing delays, additional fees, or may be returned to sender'],
                style: 'tableTextLeft',
              },
            ],
            [
              {
                border: [true, true, true, true],
                ul: ['Trident does not accept any responsibility for any penalties that may accrue due to the late ' +
                  'payment of Government fees where an invoice is not settled within 30-days of the invoice date'],
                style: 'tableTextLeft',
              },
            ],
          ]
        },
        layout: {
          hLineWidth: function () {
            return 0.5;
          },
          vLineWidth: function () {
            return 0.5;
          },
          hLineStyle: function () {
            return { dash: { length: 2, space: 4 } };
          },
          vLineStyle: function () {
            return { dash: { length: 2 } };
          },
          paddingLeft: function () {
            return 5;
          },
          paddingRight: function () {
            return 5;
          },
          paddingTop: function () {
            return 5;
          },
          paddingBottom: function () {
            return 5;
          },
        }
      },
      {
        text: '\n',
        pageBreak: 'after'
      },
      {
        text: 'BANK DETAILS',
        style: 'header'
      },
      {
        text: 'Wire transfer instructions for the remittance of US Dollars to our account at:',
        style: 'tableTextLeft',
        margin: [0, 5, 0, 5]
      },
      {
        table: {
          widths: ['30%', '70%'],
          body: [
            [
              {
                colSpan: 2,
                text: 'Beneficiary Bank:',
                style: 'tableTextLeft',
                fillColor: '#dddddd',
              },
              '',
            ],
            [
              {
                text: 'Bank:',
                style: 'tableTextLeft',
              },
              {
                text: 'CIBC Bank USA',
                style: 'tableTextLeft',
              },
            ],
            [
              {
                text: 'Bank Address:',
                style: 'tableTextLeft',
              },
              {
                text: '120 South LaSalle Street, Chicago, Illinois 60603; United States',
                style: 'tableTextLeft',
              },
            ],
            [
              {
                text: 'ABA Number:',
                style: 'tableTextLeft',
              },
              {
                text: '0710-0648-6',
                style: 'tableTextLeft',
              },
            ],
            [
              {
                text: 'Swift BIC:',
                style: 'tableTextLeft',
              },
              {
                text: 'PVTBUS44',
                style: 'tableTextLeft',
              },
            ],
            [
              {
                colSpan: 2,
                text: 'For Final Credit to - Beneficiary Name:',
                style: 'tableTextLeft',
                fillColor: '#dddddd',
              },
              '',
            ],
            [
              {
                text: 'Beneficiary Name:',
                style: 'tableTextLeft',
              },
              {
                text: 'TT & Asociados\n' +
                  'P.H. Aseguradora Ancon Tower, Of 19-N, Av Centenario, Costa del Este, Panama City, Panama',
                style: 'tableTextLeft',
              },
            ],
            [
              {
                text: 'Account Number:',
                style: 'tableTextLeft',
              },
              {
                text: '2491087',
                style: 'tableTextLeft',
              },
            ],
            [
              {
                text: 'Reference:',
                style: 'tableTextLeft',
              },
              {
                text: '0',
                style: 'tableTextLeft',
              },
            ],
          ]
        },
        layout: {
          paddingLeft: function () {
            return 5;
          },
          paddingRight: function () {
            return 5;
          },
          paddingTop: function () {
            return 5;
          },
          paddingBottom: function () {
            return 5;
          },
        }
      },
    ],
    styles: {
      header: {
        fontSize: 14,
        bold: true,
        margin: [0, 0, 0, 10]
      },
      subheader: {
        fontSize: 12,
        bold: true,
        margin: [0, 10, 0, 5]
      },
      feesTitle: {
        margin: [0,10,0,10]
      }, 
      table: {
        margin: [0, 5, 0, 15]
      },
      tableHeader: {
        bold: true,
        color: 'black',
        alignment: 'center',
        fontSize: 9,
      },
      tableHeaderLeft: {
        bold: true,
        color: 'black',
        alignment: 'left',
        fontSize: 9,
      },
      tableHeaderRight: {
        bold: true,
        color: 'black',
        alignment: 'right',
        fontSize: 8,
      },
      tableText: {
        fontSize: 9,
        alignment: 'center'
      },
      tableTextLeft: {
        fontSize: 9,
        alignment: 'left'
      },
      tableSmTextLeft: {
        fontSize: 8,
        alignment: 'left'
      },
      tableSmTextRight: {
        fontSize: 8,
        alignment: 'right'
      }
    },
    defaultStyle: {
      font: 'Helvetica',
      fontSize: 11,
      lineHeight: 1.1,
    }
  };

  const pdfDoc = printer.createPdfKitDocument(docDefinition);
  const chunks = [];
  let result;

  pdfDoc.on('data', function (chunk) {
    chunks.push(chunk)
  });
  pdfDoc.on('error', function (err) {
    console.log("pdf generation error ", err);
  });
  pdfDoc.on('end', function () {
    result = Buffer.concat(chunks);

    res.contentType('application/pdf');
    res.setHeader('Content-Disposition', 'inline; filename=SUB-' + report.masterClientCode + '-' + formatDate(report.submittedAt, "YYYYMMDD") + '.pdf');
    res.send(result)
  });
  pdfDoc.end()
};

function createARCompanyInfoTable(report) {
  let tableBody = [];
  tableBody.push([{ text: ['Company Name: ', { text: report.companyData.name, bold: true }], style: 'tableText', margin: [0, 2, 0, 2] }, { text: 'Fiscal Period:', style: 'tableText', bold: true, alignment: 'right', margin: [0, 2, 5, 0] }, {}],);
  tableBody.push([{ text: ['Company Identity Code: ', { text: report.companyData.code, bold: true }], style: 'tableText', margin: [0, 2, 0, 2] }, { text: 'Start Date:', alignment: 'left', style: 'tableText', margin: [45, 2, 0, 0] }, { text: formatDate(report.financialPeriod.start, "DD MMMM YYYY"), alignment: 'left', style: 'tableText' }])
  tableBody.push([{ text: ['Master Client Code: ', { text: report.companyData.masterclientcode, bold: true }], style: 'tableText', margin: [0, 2, 0, 2] }, { text: 'End Date:', alignment: 'left', style: 'tableText', margin: [45 , 2, 0, 0] }, { text: formatDate(report.financialPeriod.end, "DD MMMM YYYY"), alignment: 'left', style: 'tableText' }]);
  tableBody.push([{ text: 'Registered Agent: ', style: 'tableText', margin: [0, 10, 0, 0] }, { text: 'Date submitted:', alignment: 'right', margin: [0, 10, 0, 0], style: 'tableText' }, { text: formatDate(report.initialSubmitDate ? report.initialSubmitDate : report.submittedAt, "DD MMMM YYYY"), alignment: 'left', bold: true, margin: [0, 10, 0, 0], style: 'tableText' }]);
  tableBody.push([{ text: 'Trident Trust Company (BVI) Limited', style: 'tableText', bold: true }, {}, {}]),
  tableBody.push([{}, { text: ['Status: ', { text: report.getStatusLabel(), bold: true }], alignment: 'left', colSpan: 2, margin: [45, 0, 0, 0], style: 'tableText' }, {}])


  if (report.reopened?.details?.length > 0) {
    let reopenedCount = 0;
    report.reopened.details.forEach((reopened) => {
      if (reopened.resubmittedAt) {
        reopenedCount++;
        tableBody.push([{text: `Date resubmitted (${reopenedCount}):`, style: 'tableText', margin:[0,3,0,0]}, {text: formatDate(reopened.resubmittedAt, "DD MMMM YYYY"), colSpan: 2, margin:[45,3,0,0], bold: true}, {}]);
      }
    });
  }
  return tableBody;
}

function createARDetailsTable(report) {
  let tableBody = [];

  const serviceTypes = {
    "self-service-complete": "Self Service: Completion of Company’s Annual Return in the prescribed format",
    "self-service-prepare": "Self Service: Preparation of accounting records thought Trident Accounting Tool and submission of Annual Return",
    "trident-service-complete": "Trident Service: Reformatting of existing accounting records to prescribed form",
    "trident-service-drop": "Trident Service:  Preparation of accounting records and submission of Annual Return"
  }
  const expemtTypes = {
    "in-liquidation": 'IN LIQUIDATION',
    "stock-exchange": 'LISTED COMPANY',
    "is-regulated": 'REGULATED COMPANY',
    "tax-return-filed": 'TAX RETURN WITH BVI INLAND REVENUE DEPT'
  }

  tableBody.push([{ text: 'Is your company an exempt company?', style: 'tableText' }, { text: formatBoolean(report.reportDetails.isExemptCompany), style: 'tableText' }]);

  if (report.reportDetails.isExemptCompany === true) {
    tableBody.push([{ text: 'Type of exemption', style: 'tableText' }, { text: expemtTypes[report.reportDetails.exemptCompanyType], style: 'tableText' }]);
    tableBody.push([{ text: 'Additional Remarks', style: 'tableText' }, { text: report.reportDetails.exemptCompanyExplanation, style: 'tableText' }]);
  } else {

    if (report.reportDetails.isFirstYearOperation !== true) {
      tableBody.push([{ text: "Is there a change of financial year?", style: 'tableText' }, { text: formatBoolean(report.reportDetails.isThereFinancialYearChange) }]);
    }

    if (report.reportDetails.serviceType === "trident-service-drop") {
      tableBody.push([{ text: "Specify assistance required", style: 'tableText' }, { text: report.reportDetails.assistanceRequired}]);
  
    }

    tableBody.push([{ text: "Is this the company's first year of operation?", style: 'tableText' }, { text: formatBoolean(report.reportDetails.isFirstYearOperation) }]);
    tableBody.push([{ text: 'Service requested', style: 'tableText', margin: [0, 15, 10, 10], alignment: 'justify' }, { text: serviceTypes[report.reportDetails.serviceType], margin: [0, 10, 10, 10] }]);
  }

  return tableBody;
}

// eslint-disable-next-line no-unused-vars
function createAssetsAndLiabilitiesTable(report) {

  const tableBody = []
  const assetsValues = report.completeDetails?.assets ?? {};
  const liabilitiesValues = report.completeDetails?.liabilities ?? {};
  const cashAndEquivalents = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? assetsValues.cashAmount : report.cashTransactions.totalBankAccounts
  const balanceOfLoans = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? assetsValues.loansAndReceivables : (report.assets.balanceOfLoansReceivables ? report.assets.balanceOfLoansReceivables : 0)
  const balanceOfInvestment = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? assetsValues.investmentsAssetsAmount : (report.assets.totalAmountOfInvestments ? report.assets.totalAmountOfInvestments : 0)
  const tangibleAssets = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? assetsValues.fixedAssetsAmount : 
    (report.assets.isFixedAssetsCotributed === true ? report.assets.valueTangibleAssetsEndPeriod : (report.assets.tangibleAssetsEndPeriod ? report.assets.tangibleAssetsEndPeriod : 0))
  const intangibleAssets = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? assetsValues.intangibleAssetsAmount : 
    (report.assets.isIntagibleAssetsContributed === true ? report.assets.intangibleAssetsEndFinancialPeriod : (report.assets.intangibleAssetsEndReportPeriod ? report.assets.intangibleAssetsEndReportPeriod : 0))

  // Assets Table
  tableBody.push([{ text: 'BALANCE SHEET/STATEMENT OF FINANCIAL POSITION', style: 'tableHeader', colSpan: 2, margin: [0, 10, 0, 10] }, {}])
  tableBody.push([{ text: 'ASSETS', style: 'tableHeader' }, {}])
  tableBody.push([{ text: 'Cash and Cash Equivalents', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(cashAndEquivalents, ''), style: 'tablePrices' }]);
  tableBody.push([{ text: 'Loans and Receivables', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(balanceOfLoans, ''), style: 'tablePrices' }]);
  tableBody.push([{ text: 'Investments and Other Financial Assets', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(balanceOfInvestment, ''), style: 'tablePrices' }]);
  tableBody.push([{ text: 'Tangible fixed Assets', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(tangibleAssets, ''), style: 'tablePrices' }]);
  tableBody.push([{ text: 'Intangible Assets', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(intangibleAssets, ''), style: 'tablePrices' }]);


  const valueofOtherAssets = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? assetsValues.totalOtherAssets : (report.assets.valueOfOtherAssetsEndPeriod ? report.assets.valueOfOtherAssetsEndPeriod : 0)
  const totalAssetsValue = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? assetsValues.total : report.calculatedValues?.totalAssets ?? 0
  tableBody.push([{ text: `Other Assets`, style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(valueofOtherAssets, ''), style: 'tablePrices' }]);

  tableBody.push([{ text: 'TOTAL ASSETS', style: 'tablePriceText', margin: [0, 15, 0, 0], bold: true }, { text: [report.currency + ' ', { text: formatToCurrencyLocaleNumber(totalAssetsValue, ''), style: 'tablePrices' }], alignment: 'right', margin: [0, 15, 15, 0] }]);
  // liabilities table 

  const accountsPayableValue = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? liabilitiesValues.accountsPayable : 
    (report.liabilities.accountsPayable ? report.liabilities.accountsPayable : 0)
  const longTermsDebtsValue = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? liabilitiesValues.longTermDebts : 
    (report.liabilities.ltDebtsClosingBalance ? report.liabilities.ltDebtsClosingBalance : 0)


  tableBody.push([{ text: 'LIABILITIES', colSpan: 2, style: 'tableHeader', margin: [0, 15, 0, 10] }, {}]);
  tableBody.push([{ text: 'Accounts Payable', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(accountsPayableValue, ''), style: 'tablePrices' }]);
  tableBody.push([{ text: 'Long-term Debts', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(longTermsDebtsValue, ''), style: 'tablePrices' }]);

  const otherLiabilitiesValue = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? 
    liabilitiesValues.totalOtherLiabilities : (report.liabilities.valueOfOtherLiabilitiesEndPeriod ? report.liabilities.valueOfOtherLiabilitiesEndPeriod : 0)
  const totalLiabilitiesValue = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? 
    liabilitiesValues.total : report.liabilities.totalLiabilities
  const shareholdersValue = report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE ? 
    (report.completeDetails?.shareholderEquity ?? 0) : (report.calculatedValues?.shareholderEquality ?? 0);
  tableBody.push([{ text: `Other Liabilities`, style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(otherLiabilitiesValue, ''), style: 'tablePrices' }]);

  tableBody.push([{ text: 'TOTAL LIABILITIES', style: 'tablePriceText', margin: [0, 15, 0, 15], bold: true }, { text: formatToCurrencyLocaleNumber(totalLiabilitiesValue, report.currency), style: 'tablePrices', margin: [0, 15, 15, 15] }]);
  tableBody.push([{ text: "SHAREHOLDER'S EQUITY", style: 'tablePriceText', margin: [0, 15, 0, 15], bold: true },
  { text: formatToCurrencyLocaleNumber(shareholdersValue, report.currency), style: 'tablePrices', margin: [0, 15, 15, 15] }]);
  return tableBody

}

function createIncomeAndExpensesReport(report) {
  const tableBody = []
  if (report.reportDetails.serviceType !== ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE) {
    const incomeValues = report.completeDetails?.income ?? {};
    const expensesValues = report.completeDetails?.expenses ?? {};

    tableBody.push([{ text: 'INCOME STATEMENT', colSpan: 2, style: 'tableHeader', margin: [0, 10, 0, 10] }, {}]);
    tableBody.push([{ text: 'REVENUE', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(incomeValues.total, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'COST OF SALES', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(Math.abs(incomeValues.costOfSales) * -1, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'GROSS PROFIT', style: 'tablePriceText', bold: true, margin: [0, 15, 0, 0] }, { text: formatToCurrencyLocaleNumber(report.completeDetails?.grossProfit?? 0, report.currency), style: 'tablePrices', margin: [0, 15, 15, 0] }]);

    //expenses table
    tableBody.push([{ text: 'EXPENSES', colSpan: 2, style: 'tableHeader', margin: [0, 10, 0, 10], bold: true }, {}]);
    tableBody.push([{ text: 'Operating expenses', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(Math.abs(expensesValues.operatingExpenses) * -1, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: `Other Expenses`, style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(Math.abs(expensesValues.totalOtherExpenses) *-1, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Income tax expense', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(Math.abs(expensesValues.incomeTax) *-1, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'TOTAL EXPENSES', style: 'tablePriceText', bold: true, margin: [0, 15, 0, 0] }, { text: formatToCurrencyLocaleNumber(Math.abs(expensesValues.totalOfExpenses) * -1, report.currency), style: 'tablePrices', margin: [0, 15, 15, 0] }]);
    tableBody.push([{ text: 'NET INCOME', style: 'tablePriceText', bold: true, margin: [0, 15, 0, 10] }, { text: formatToCurrencyLocaleNumber(report.completeDetails?.netIncome??0, report.currency), style: 'tablePrices', margin: [0, 15, 15, 10] }]);
  } else {
    tableBody.push([{ text: 'INCOME ', colSpan: 2, style: 'tableHeader', margin: [0, 10, 0, 10] }, {}]);
    tableBody.push([{ text: 'Divided Income', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.calculatedValues?.dividedIncome?? 0, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Loan Interest Income', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber((report.assets?.interestReceivableOnTheLoan ? report.assets.interestReceivableOnTheLoan : 0), ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Net Gain on Investment', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.calculatedValues?.netGainInvestment??0, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Other income', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.calculatedValues?.otherIncome??0, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Sale of Goods/Services', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.assets?.invoicesIssued?? 0, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'TOTAL INCOME', style: 'tablePriceText', bold: true, margin: [0, 15, 0, 0] }, { text: formatToCurrencyLocaleNumber(report.calculatedValues?.totalIncome??0, report.currency), style: 'tablePrices', margin: [0, 15, 15, 0] }]);

    tableBody.push([{ text: 'EXPENSES ', colSpan: 2, style: 'tableHeader', margin: [0, 10, 0, 10] }, {}]);
    tableBody.push([{ text: 'Cost of Goods/Services acquired', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.liabilities?.costOfGoods??0, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Net Loss on investment', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.calculatedValues?.netLossInvestment?? 0, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Company Administration fees', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.liabilities.compAdminFees, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Portfolio management fees and Related Services', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.calculatedValues?.portfolioFees?? 0, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Bank Fees', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.calculatedValues?.bankFees??0, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Loan interest expense', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.liabilities.loanInterestAccrued, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Income tax expenses', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.calculatedValues?.incomeTaxExpenses??0, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Other Expenses', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.calculatedValues?.otherExpenses??0, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'TOTAL EXPENSES', style: 'tablePriceText', bold: true, margin: [0, 15, 0, 0] }, { text: formatToCurrencyLocaleNumber(report.calculatedValues?.totalExpenses??0, report.currency), style: 'tablePrices', margin: [0, 15, 15, 0] }]);
    tableBody.push([{ text: 'NET PROFIT (LOSS)', style: 'tablePriceText', bold: true, margin: [0, 15, 0, 0] }, { text: formatToCurrencyLocaleNumber(report.calculatedValues?.netProfit??0, report.currency), style: 'tablePrices', margin: [0, 15, 15, 0] }]);

  }


  return tableBody
}

function createIncomeAndExpensesReportForOption2(report) {
  const tableBody = []
    tableBody.push([{ text: 'INCOME STATEMENT', colSpan: 2, style: 'tableHeader', margin: [0, 10, 0, 10] }, {}]);
    tableBody.push([{ text: 'REVENUE', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(report.calculatedValues._revenue, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'COST OF SALES', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(Math.abs(report.calculatedValues._costOfSales) * -1, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'GROSS PROFIT', style: 'tablePriceText', bold: true, margin: [0, 15, 0, 0] }, { text: formatToCurrencyLocaleNumber(report.calculatedValues._profit, report.currency), style: 'tablePrices', margin: [0, 15, 15, 0] }]);

    //expenses table
    tableBody.push([{ text: 'EXPENSES', colSpan: 2, style: 'tableHeader', margin: [0, 10, 0, 10], bold: true }, {}]);
    tableBody.push([{ text: 'Operating expenses', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(Math.abs(report.calculatedValues._operationExpenses) * -1, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: `Other Expenses`, style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(Math.abs(report.calculatedValues._otherExpenses) * -1, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'Income tax expense', style: 'tablePriceText' }, { text: formatToCurrencyLocaleNumber(Math.abs(report.calculatedValues.incomeTaxExpenses) *-1, ''), style: 'tablePrices' }]);
    tableBody.push([{ text: 'TOTAL EXPENSES', style: 'tablePriceText', bold: true, margin: [0, 15, 0, 0] }, { text: formatToCurrencyLocaleNumber(Math.abs(report.calculatedValues._totalExpenses) * -1, report.currency), style: 'tablePrices', margin: [0, 15, 15, 0] }]);
    tableBody.push([{ text: 'NET INCOME', style: 'tablePriceText', bold: true, margin: [0, 15, 0, 10] }, { text: formatToCurrencyLocaleNumber(report.calculatedValues._netIncome, report.currency), style: 'tablePrices', margin: [0, 15, 15, 10] }]);
  return tableBody
}


function createARConfirmationTable(report) {
  let tableBody = [];
  tableBody.push([{ text: 'I confirm that:', styles: 'tableText', fontSize: 10, margin: [0, 3, 0, 0] }])

  let total = report.payment?.total || 0;

  if (!total && report.reportDetails.isExemptCompany !== true){
    if (report.reportDetails?.serviceType === ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_COMPLETE) {
      total = report.companyData?.accountingRecordsModule?.selfServiceCompleteAnnualReturnAmount;
    }

    if (report.reportDetails?.serviceType === ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE) {
      total = report.companyData?.accountingRecordsModule?.selfServicePrepareAnnualReturnAmount;
    }
    if (report.reportDetails?.serviceType === ACCOUNTING_SERVICE_TYPES.TRIDENT_SERVICE_COMPLETE) {
      total = report.companyData?.accountingRecordsModule?.tridentServiceCompleteAnnualReturnAmount;
    }
    if (report.reportDetails?.serviceType === ACCOUNTING_SERVICE_TYPES.TRIDENT_SERVICE_DROP) {
      total = report.companyData?.accountingRecordsModule?.tridentServiceDropAccountingRecordsAmount;
    }
  } else {
    total = 190
  }

  const declarationOptions = [];

  if(parseFloat(report.version) > 1 ){
    declarationOptions.push({ text: 'I have the authority to act on behalf of the company and further confirm that the details presented in this portal, along with any document(s) submitted while completing this accounting return, have been provided to me by the Company’s Director and to the best of the Company’s Directors knowledge and belief, are true and accurate.', margin: [0, 2] })
    declarationOptions.push({ text: 'The Registered Agent has a legitimate interest in processing any personal data provided above to satisfy the Entity’s compliance with relevant BVI law. I further acknowledge that the processing of such personal data may include its transfer to BVI competent authorities, and that the Registered Agent’s processing of any personal data will be done in accordance with Trident Trust Data Privacy Policy, which I have read and understood.', margin: [0, 2] })
  }else{
    declarationOptions.push({ text: 'The details presented in this portal, along with any document(s) submitted while completing this accounting return, are, to the best of my knowledge and belief, accurate and true.', margin: [0, 2] })

    if (report.reportDetails.isExemptCompany !== true) {
      declarationOptions.push({ text: 'Except for the assets and liabilities explicitly outlined in the form, the Company, to the best of my knowledge, does not possess any additional assets or have any other liabilities.', margin: [0, 2] });
    }
    declarationOptions.push({ text: `The assets allocated to the Company and the funds utilized for all Trident's services originate from lawful sources. Additionally, the submission fee of US$${total} must be paid to fulfill the submission process.`, margin: [0, 2] })
    declarationOptions.push({ text: 'I have the authority to act on behalf of the company.', margin: [0, 2] })
    declarationOptions.push({ text: 'The Registered Agent has a legitimate interest in processing any personal data provided above to satisfy the Entity’s compliance with relevant BVI law. I further acknowledge that the processing of such personal data may include its transfer to BVI competent authorities, and that the Registered Agent’s processing of any personal data will be done in accordance with Trident Trust Data Privacy Policy, which I have read and understood.', margin: [0, 2] })
  }


  tableBody.push([{
    ul: declarationOptions, 
    margin: [20, 0, 0, 0], fontSize: 10, lineHeight: 1.3
  }])
  return tableBody;
}

function generateFeesAndDisbursements(incorporation) {
  const fees = [{
    text: incorporation.version >= '2.0' ? 'FORMATION FEES (STANDARD BVI COMPANY)' : 'Fees',
    style: 'subheader'
  }];
  const disbursements = [];
  
  for (let fee of incorporation.payment.fees) {
    if (incorporation.version === '2.0') {
      fees.push({
        columns:[
          { width: '100%',
            alignment: 'left',
            text: fee.title,
            fontSize: 9
          }],
          columnGap: 10
        },)
    }
    fees.push(
      {
        columns: [
          {
            width: '*',
            text: fee.description,
            fontSize: incorporation.version === '2.0' ? 8 : 11
          },
          {
            width: '20%',
            alignment: 'right',
            text: fee.value ? fee.value.toFixed(2).toString() : '0.00'
          }
        ],
        columnGap: 10
      }, '\n')
  }
  
  if (incorporation.version === '1.0') {
    disbursements.push({
      text: 'Disbursements',
      style: 'subheader'
    })
  }
  for (let disbursement of incorporation.payment.disbursements) {
    if (incorporation.version === '2.0') {
      disbursements.push({
        columns:[
          { width: '100%',
            alignment: 'left',
            text: disbursement.title,
            fontSize: 9
          }]},)
    }
    disbursements.push(
      {
        columns: [
          {
            width: '*',
            text: disbursement.description,
            fontSize: incorporation.version === '2.0' ? 8 : 11
          },
          {
            width: '20%',
            alignment: 'right',
            text: disbursement.value ? disbursement.value.toFixed(2).toString() : '0.00'
          }
        ],
        columnGap: 10
      }, '\n')
  }
  return [
    ...fees, ...disbursements
  ]
}

function createIncorporationStep1Info(incorporation) {
  let tableBody = [];

  tableBody.push([{text: 'Entity Name', style: 'tableHeader'}, {text: incorporation.name}]);
  tableBody.push([{text: 'Type of entity', style: 'tableHeader'}, {text: incorporation.type}]);
  if (incorporation.type === 'Special Instructions') {
    tableBody.push([{
      text: 'Special Instructions',
      style: 'tableHeader'
    }, {text: incorporation.typeSpecialInstructions}]);
  }
  tableBody.push([{
    text: 'Is the entity part of a group/structure?',
    style: 'tableHeader'
  }, {text: formatBoolean(incorporation.partOfGroup)}]);
  tableBody.push([{
    text: 'Date submitted',
    style: 'tableHeader'
  }, {text: formatDate(incorporation.submittedAt, "YYYY-MM-DD")}]);

  return tableBody;
}

function createIncorporationStep2Info(incorporation) {
  const tables = [];
  for (const relation of incorporation.relations) {
    const relationTable = {
      style: 'table',
      table: {
        widths: [300, '*'],
        body: [],
      },
    };
    const relationsTypes = {
      natural: 'Natural',
      corporate: 'Corporate',
      foundation: 'Foundation',
      trust: 'Trust',
      limited: 'Limited Partnership',
    };

    relationTable.table.body.push([
      {text: 'Is the company owned by a Natural person or a Corporate entity?', style: 'tableHeader'},
      {text: relation.type === "natural" ? 'Natural' : 'Corporate'}]);
    relationTable.table.body.push([{
      text: 'Type of relation',
      style: 'tableHeader'
    }, {text: relation.groups.join(', ')}]);


    if (relation.type === 'natural') {
      relationTable.table.body.push(...createNaturalRelation(relation));
    } else {
      relationTable.table.body.push([{
        text: 'Type of corporation',
        style: 'tableHeader'
      }, {text: relationsTypes[relation.ownerShip]}]);
      if (relation.type === 'corporate') {
        relationTable.table.body.push(...createCorporateRelation(relation));
      } else if (relation.type === 'foundation') {
        relationTable.table.body.push(...createFoundationRelation(relation));
      } else if (relation.type === 'trust') {
        relationTable.table.body.push(...createTrustRelation(relation));
      } else if (relation.type === 'limited') {
        relationTable.table.body.push(...createLimitedPartnershipRelation(relation));
      }
    }
    if (relation.groups.includes('Shareholder')) {
      relationTable.table.body.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);

      relationTable.table.body.push([{text: 'ADDITIONAL SHAREHOLDER DETAILS', style: 'tableHeader', colSpan: 2}, {}]);
      relationTable.table.body.push([{text: 'Share Percentage', style: 'tableHeader'}, {text: relation.percentage}]);
    }
    tables.push(relationTable);
  }
  return tables;
}

function createNaturalRelation(relation) {
  const table = [];
  // Relation details
  table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);
  table.push([{text: 'RELATION DETAILS', style: 'tableHeader', colSpan: 2}, {}]);
  table.push([{text: 'First name', style: 'tableHeader'}, {text: relation.details.firstName}]);
  table.push([{text: 'Middlename/Initial', style: 'tableHeader'}, {text: relation.details.middleName}]);
  table.push([{text: 'Last Name', style: 'tableHeader'}, {text: relation.details.lastName}]);
  table.push([{text: 'Occupation', style: 'tableHeader'}, {text: relation.details.occupation}]);
  table.push([{text: 'Source of Income', style: 'tableHeader'}, {text: relation.details.source_of_income}]);
  table.push([{
    text: 'Date of Birth',
    style: 'tableHeader'
  }, {text: formatDate(relation.details.birthDate, 'YYYY-MM-DD')}]);
  table.push([{text: 'Nationality', style: 'tableHeader'}, {text: relation.details.nationality}]);
  table.push([{text: 'Country of birth', style: 'tableHeader'}, {text: relation.details.countryBirth}]);
  // Identification
  table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);

  table.push([{text: 'IDENTIFICATION', style: 'tableHeader', colSpan: 2}, {}]);
  table.push([{text: 'Electronic ID', style: 'tableHeader'}, {
    text: relation.electronicIdInfo &&
    relation.electronicIdInfo.isElectronicId === true ? 'Yes' : 'No'
  }]);
  table.push([{
    text: 'Type of Identification',
    style: 'tableHeader'
  }, {text: relation.identification.identificationType}]);
  table.push([{text: 'Country of Issue', style: 'tableHeader'}, {text: relation.identification.issueCountry}]);
  table.push([{
    text: 'Expiry Date',
    style: 'tableHeader'
  }, {text: formatDate(relation.identification.expiryDate, 'YYYY-MM-DD')}]);
  table.push([{
    text: 'Valid Identification',
    style: 'tableHeader'
  }, {text: formatBoolean(relation.identification.valid)}]);
  // Principal Address
  table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);

  table.push([{text: 'PRINCIPAL ADDRESS', style: 'tableHeader', colSpan: 2}, {}]);
  table.push(...createAddressTable(relation.principalAddress));
  // Mailing Address
  table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);

  table.push([{text: 'MAILING ADDRESS', style: 'tableHeader', colSpan: 2}, {}]);
  table.push([{
    text: 'Is mailing address the same as Principal address?',
    style: 'tableHeader'
  }, {text: relation.isSamePrincipalAddress}]);
  table.push(...createAddressTable(relation.mailingAddress));
  // Tax Advice
  table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);

  table.push([{text: 'TAX ADVICE', style: 'tableHeader', colSpan: 2}, {}]);
  table.push([{
    text: 'Confirmation Regarding Legal / Tax Advice',
    style: 'tableHeader'
  }, {text: formatBoolean(relation.taxResidence.confirmation)}]);
  table.push([{text: 'Tax Residence', style: 'tableHeader'}, {text: relation.taxResidence.taxResidence}]);
  if (relation.taxResidence.confirmation) {
    // Advisor Details
    table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);

    table.push([{text: 'ADVISOR DETAILS', style: 'tableHeader', colSpan: 2}, {}]);
    table.push([{text: 'First Name', style: 'tableHeader'}, {text: relation.advisorDetails.firstName}]);
    table.push([{text: 'Middle Name', style: 'tableHeader'}, {text: relation.advisorDetails.middleName}]);
    table.push([{text: 'Last Name', style: 'tableHeader'}, {text: relation.advisorDetails.lastName}]);
    table.push([{text: 'Name of Firm', style: 'tableHeader'}, {text: relation.advisorDetails.firmName}]);
    table.push([{text: 'Phone', style: 'tableHeader'}, {text: relation.advisorDetails.phone}]);
    table.push([{text: 'E-mail', style: 'tableHeader'}, {text: relation.advisorDetails.email}]);
    table.push([{text: 'Nationality', style: 'tableHeader'}, {text: relation.advisorDetails.nationality}]);
    table.push([{
      text: 'Country of Incorporation',
      style: 'tableHeader'
    }, {text: relation.advisorDetails.incorporationCountry}]);

    // Principal Advisor Address
    table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);

    table.push([{text: 'PRINCIPAL ADVISOR ADDRESS', style: 'tableHeader', colSpan: 2}, {}]);
    table.push(...createAddressTable(relation.principalAddress));
  }

  // PEP Information
  table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);

  table.push([{text: 'Is this relation a PEP?', style: 'tableHeader'}, {text: formatBoolean(relation.pep)}]);
  return table;
}

function createCorporateRelation(relation) {
  const table = [];
  // Relation details
  table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);
  table.push([{text: 'RELATION DETAILS', style: 'tableHeader', colSpan: 2}, {}]);
  table.push([{text: 'Is the company already a TridentTrust client?', style: 'tableHeader'},
    {text: formatBoolean(relation.details.isTridentClient)}]);
  table.push([{text: 'Organization name', style: 'tableHeader'}, {text: relation.details.organizationName}]);
  table.push([{
    text: 'Incorporation / Formation Number',
    style: 'tableHeader'
  }, {text: relation.details.incorporationNumber}]);
  if (!relation.details.isTridentClient) {
    table.push([{text: 'Tax Residence', style: 'tableHeader'}, {text: relation.details.taxResidence}]);
    table.push([{
      text: 'Business Registration Number (if applicable)',
      style: 'tableHeader'
    }, {text: relation.details.businessNumber}]);
    table.push([{
      text: 'Date of Incorporation',
      style: 'tableHeader'
    }, {text: formatDate(relation.details.incorporationDate, 'YYYY-MM-DD')}]);
    table.push([{
      text: 'Country of Incorporation',
      style: 'tableHeader'
    }, {text: relation.details.incorporationCountry}]);
  }

  // Principal Address
  table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);

  table.push([{text: 'PRINCIPAL ADDRESS', style: 'tableHeader', colSpan: 2}, {}]);
  table.push(...createAddressTable(relation.principalAddress));
  // Mailing Address
  table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);

  table.push([{text: 'MAILING ADDRESS', style: 'tableHeader', colSpan: 2}, {}]);
  table.push([{
    text: 'Is mailing address the same as Principal address?',
    style: 'tableHeader'
  }, {text: relation.isSamePrincipalAddress}]);
  table.push(...createAddressTable(relation.mailingAddress));

  table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);

  table.push([{
    text: 'Listed Company Details',
    style: 'tableHeader'
  }, {text: formatBoolean(relation.listedCompanyDetails.active)}]);
  table.push([{
    text: 'Regulated Limited Company Details',
    style: 'tableHeader'
  }, {text: formatBoolean(relation.limitedCompanyDetails.active)}]);
  table.push([{text: 'Fund Details', style: 'tableHeader'}, {text: formatBoolean(relation.mutualFundDetails.active)}]);

  return table;
}

function createFoundationRelation(relation) {
  const table = [];
  // Relation details
  table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);
  table.push([{text: 'RELATION DETAILS', style: 'tableHeader', colSpan: 2}, {}]);
  table.push([{text: 'Is the company already a TridentTrust client?', style: 'tableHeader'},
    {text: formatBoolean(relation.details.isTridentClient)}]);
  table.push([{text: 'Organization name', style: 'tableHeader'}, {text: relation.details.organizationName}]);
  table.push([{
    text: 'Incorporation / Formation Number',
    style: 'tableHeader'
  }, {text: relation.details.incorporationNumber}]);
  if (!relation.details.isTridentClient) {
    table.push([{text: 'Tax Residence', style: 'tableHeader'}, {text: relation.details.taxResidence}]);
    table.push([{
      text: 'Business Registration Number (if applicable)',
      style: 'tableHeader'
    }, {text: relation.details.businessNumber}]);
    table.push([{
      text: 'Date of Incorporation',
      style: 'tableHeader'
    }, {text: formatDate(relation.details.incorporationDate, 'YYYY-MM-DD')}]);
    table.push([{
      text: 'Country of Incorporation',
      style: 'tableHeader'
    }, {text: relation.details.incorporationCountry}]);
  }


  // Principal Address
  table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);

  table.push([{text: 'PRINCIPAL ADDRESS', style: 'tableHeader', colSpan: 2}, {}]);
  table.push(...createAddressTable(relation.principalAddress));
  // Mailing Address
  table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);

  table.push([{text: 'MAILING ADDRESS', style: 'tableHeader', colSpan: 2}, {}]);
  table.push([{
    text: 'Is mailing address the same as Principal address?',
    style: 'tableHeader'
  }, {text: relation.isSamePrincipalAddress}]);
  table.push(...createAddressTable(relation.mailingAddress));

  table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);

  table.push([{
    text: 'Listed Company Details',
    style: 'tableHeader'
  }, {text: formatBoolean(relation.listedCompanyDetails.active)}]);
  table.push([{
    text: 'Regulated Limited Company Details',
    style: 'tableHeader'
  }, {text: formatBoolean(relation.limitedCompanyDetails.active)}]);
  table.push([{text: 'Foundation Details', style: 'tableHeader'}, {text: formatBoolean(relation.foundation.active)}]);
  table.push([{text: 'Fund Details', style: 'tableHeader'}, {text: formatBoolean(relation.mutualFundDetails.active)}]);

  return table;
}

function createTrustRelation(relation) {
  const table = [];
  // Relation details
  table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);
  table.push([{text: 'RELATION DETAILS', style: 'tableHeader', colSpan: 2}, {}]);
  table.push([{text: 'Is the company already a TridentTrust client?', style: 'tableHeader'},
    {text: formatBoolean(relation.details.isTridentClient)}]);
  table.push([{text: 'Trust Name', style: 'tableHeader'}, {text: relation.details.organizationName}]);
  table.push([{text: 'Trustee Name', style: 'tableHeader'}, {text: relation.details.incorporationNumber}]);
  if (!relation.details.isTridentClient) {
    table.push([{text: 'Tax Residence', style: 'tableHeader'}, {text: relation.details.taxResidence}]);
    table.push([{
      text: 'Business Registration Number (if applicable)',
      style: 'tableHeader'
    }, {text: relation.details.businessNumber}]);
    table.push([{
      text: 'Date of Trust Establishment',
      style: 'tableHeader'
    }, {text: formatDate(relation.details.incorporationDate, 'YYYY-MM-DD')}]);
    table.push([{
      text: 'Country of Incorporation',
      style: 'tableHeader'
    }, {text: relation.details.incorporationCountry}]);
  }

  // Principal Address
  table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);

  table.push([{text: 'PRINCIPAL ADDRESS', style: 'tableHeader', colSpan: 2}, {}]);
  table.push(...createAddressTable(relation.principalAddress));
  // Mailing Address
  table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);

  table.push([{text: 'MAILING ADDRESS', style: 'tableHeader', colSpan: 2}, {}]);
  table.push([{
    text: 'Is mailing address the same as Principal address?',
    style: 'tableHeader'
  }, {text: relation.isSamePrincipalAddress}]);
  table.push(...createAddressTable(relation.mailingAddress));

  table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);

  table.push([{
    text: 'Regulated Limited Company Details',
    style: 'tableHeader'
  }, {text: formatBoolean(relation.limitedCompanyDetails.active)}]);

  return table;
}

function createLimitedPartnershipRelation(relation) {
  const table = [];
  // Relation details
  table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);
  table.push([{text: 'RELATION DETAILS', style: 'tableHeader', colSpan: 2}, {}]);
  table.push([{text: 'Is the company already a TridentTrust client?', style: 'tableHeader'},
    {text: formatBoolean(relation.details.isTridentClient)}]);
  table.push([{text: 'Organization name', style: 'tableHeader'}, {text: relation.details.organizationName}]);
  table.push([{
    text: 'Incorporation / Formation Number',
    style: 'tableHeader'
  }, {text: relation.details.incorporationNumber}]);
  if (!relation.details.isTridentClient) {
    table.push([{text: 'Tax Residence', style: 'tableHeader'}, {text: relation.details.taxResidence}]);
    table.push([{
      text: 'Business Registration Number (if applicable)',
      style: 'tableHeader'
    }, {text: relation.details.businessNumber}]);
    table.push([{
      text: 'Date of Incorporation',
      style: 'tableHeader'
    }, {text: formatDate(relation.details.incorporationDate, 'YYYY-MM-DD')}]);
    table.push([{
      text: 'Country of Incorporation',
      style: 'tableHeader'
    }, {text: relation.details.incorporationCountry}]);
  }

  // Principal Address
  table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);

  table.push([{text: 'PRINCIPAL ADDRESS', style: 'tableHeader', colSpan: 2}, {}]);
  table.push(...createAddressTable(relation.principalAddress));
  // Mailing Address
  table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);

  table.push([{text: 'MAILING ADDRESS', style: 'tableHeader', colSpan: 2}, {}]);
  table.push([{
    text: 'Is mailing address the same as Principal address?',
    style: 'tableHeader'
  }, {text: relation.isSamePrincipalAddress}]);
  table.push(...createAddressTable(relation.mailingAddress));

  table.push([{text: " ", style: 'tableHeader', colSpan: 2}, {}]);

  table.push([{
    text: 'Listed Company Details',
    style: 'tableHeader'
  }, {text: formatBoolean(relation.listedCompanyDetails.active)}]);
  table.push([{
    text: 'Regulated Limited Company Details',
    style: 'tableHeader'
  }, {text: formatBoolean(relation.limitedCompanyDetails.active)}]);
  table.push([{text: 'Fund Details', style: 'tableHeader'}, {text: formatBoolean(relation.mutualFundDetails.active)}]);

  return table;
}

function createAddressTable(address) {
  const table = [];
  table.push([{text: 'Address - 1st Line', style: 'tableHeader'}, {text: address.primaryAddress}]);
  table.push([{text: 'Address - 2nd Line', style: 'tableHeader'}, {text: address.secondaryAddress}]);
  table.push([{text: 'Country', style: 'tableHeader'}, {text: address.country}]);
  table.push([{text: 'State/Province', style: 'tableHeader'}, {text: address.state}]);
  table.push([{text: 'City', style: 'tableHeader'}, {text: address.postalCode}]);
  table.push([{text: 'Postal Code', style: 'tableHeader'}, {text: address.city}]);
  return table;
}

function createIncorporationStep3Info(incorporation) {
  let tableBody = [];

  tableBody.push([{
    text: 'Principal business activities',
    style: 'tableHeader'
  }, {text: incorporation.principalBusinessActivity}]);
  if (incorporation.principalBusinessActivity === 'Other') {
    tableBody.push([{
      text: 'Principal business activities',
      style: 'tableHeader'
    }, {text: incorporation.principalBusinessActivityOther}]);
  }
  tableBody.push([{
    text: 'Jurisdiction where activities take place',
    style: 'tableHeader'
  }, {text: incorporation.activityJurisdiction}]);
  tableBody.push([{text: 'Tax Residence', style: 'tableHeader'}, {text: incorporation.taxResidence}]);
  if (incorporation.taxResidence == 'Foreign') {
    tableBody.push([{
      text: 'Jurisdiction of Tax Residency',
      style: 'tableHeader'
    }, {text: incorporation.taxResidencyJurisdiction}]);
  }

  return tableBody;
}

function createIncorporationStep4Info(incorporation) {
  let tables = [];
  const firstQuestion = {
    style: 'table',
    table: {
      widths: [300, '*'],
      body: [],
    },
  };
  firstQuestion.table.body.push([{
    text: 'Will the company own any assets legally or beneficially?',
    style: 'tableHeader'
  }, {text: formatBoolean(incorporation.ownAssets)}]);
  tables.push(firstQuestion);
  if (incorporation.ownAssets) {
    const assetsTable = {
      style: 'table',
      table: {
        widths: [300, '*'],
        body: [],
      },
    };
    assetsTable.table.body.push([{text: 'ASSETS', style: 'tableHeader', colSpan: 2}, {}]);
    assetsTable.table.body.push([{text: 'Asset Type', style: 'tableHeader'}, {
      text: 'Information',
      style: 'tableHeader'
    }]);
    for (const asset of incorporation.assets) {
      let informationColumn = '';
      if (asset.type === 'Aircraft' || asset.type === 'Vessel (ship/yacht)') {
        informationColumn = `Registration Number: ${asset.registrationNumber}\nJurisdiction of Registration: ${asset.jurisdictionOfRegistration}`;
      } else if (asset.type === 'Intellectual property rights'
        || asset.type === 'Shares/equity participations'
        || asset.type === 'Debt'
        || asset.type === 'Other') {
        informationColumn = `Details: ${asset.details}`;
      } else if (asset.type === 'Investment portfolio') {
        informationColumn = `Name of institution: ${asset.nameOfInstitution}\nAddress of institution: ${asset.addressOfInstitution}`;
      } else if (asset.type === 'Bank account') {
        informationColumn = `Name of bank: ${asset.nameOfBank} \nAddress of bank: ${asset.addressOfBank}`;
      } else if (asset.type === 'Trust assets') {
        informationColumn = `Name of trust: ${asset.nameOfTrust}`;
      } else if (asset.type === 'Real estate') {
        informationColumn = `Type: ${asset.realEstateType}\nLocation: ${asset.location}`;
      }
      assetsTable.table.body.push([{text: asset.type}, {text: informationColumn}]);
    }
    assetsTable.table.body.push([{text: 'Estimated value of assets:', style: 'tableHeader'}, {
      text: formatToCurrencyLocaleNumber(incorporation.estimated_value_of_assets, '')}]);
    

    tables.push(assetsTable);
    
  }
  const fundsTable = {
    style: 'table',
    table: {
      widths: [300, '*'],
      body: [],
    },
  };
  fundsTable.table.body.push([{text: 'SOURCE OF FUNDS', style: 'tableHeader', colSpan: 2}, {}]);
  fundsTable.table.body.push([{text: 'Payment for Trident Services will be made from bank account owned by'}, {text: incorporation.bankAccountOwner}]);
  fundsTable.table.body.push([{text: 'Bank name'}, {text: incorporation.bankName}]);
  fundsTable.table.body.push([{text: 'Bank address'}, {text: incorporation.bankAddress}]);
  fundsTable.table.body.push([{text: 'FUNDS', style: 'tableHeader', colSpan: 2}, {}]);
  fundsTable.table.body.push([{text: 'Source Type', style: 'tableHeader'}, {
    text: 'Information',
    style: 'tableHeader'
  }]);
  for (const fund of incorporation.funds) {
    let informationColumn = '';
    if (fund.type === 'Loan') {
      informationColumn = `Name of Financial Institution: ${fund.nameOfFinancialInstitution}`;
    } else if (fund.type === 'Sale of assets' || fund.type === 'Other') {
      informationColumn = `Details: ${fund.details}`;
    } else if (fund.type === 'Business income') {
      informationColumn = `Average annual turnover/profit: ${fund.profit}`;
    } else if (fund.type === 'Dividend from subsidiary') {
      informationColumn = `Name of subsidiary: ${fund.nameOfSubsidiary}\nJurisdiction of operation: ${fund.jurisdictionOfOperation}`;
    }
    fundsTable.table.body.push([{text: fund.type}, {text: informationColumn}]);
  }
  tables.push(fundsTable);
  return tables;
}

function createIncorporationStep5Info(incorporation) {
  let tableBody = [];

  tableBody.push([{text: 'Record Keeping Details', style: 'tableHeader', colSpan: 2}, {}]);
  tableBody.push([{text: 'Record Holder'}, {text: incorporation.records.recordHolder}]);
  tableBody.push([{text: 'Address - 1st Line'}, {text: incorporation.records.primaryAddress}]);
  tableBody.push([{text: 'Address - 2st Line'}, {text: incorporation.records.secondaryAddress}]);
  tableBody.push([{text: 'State'}, {text: incorporation.records.state}]);
  tableBody.push([{text: 'City'}, {text: incorporation.records.city}]);
  tableBody.push([{text: 'Postal Code'}, {text: incorporation.records.postalCode}]);
  tableBody.push([{text: 'E-mail'}, {text: incorporation.records.email}]);
  tableBody.push([{text: 'Country of Operation'}, {text: incorporation.records.operationCountry}]);

  return tableBody;
}

function createIncorporationStep6Info(incorporation) {
  let tableBody = [];

  tableBody.push([{
    text: 'Would you like to request additional services?',
    style: 'tableHeader'
  }, {text: formatBoolean(incorporation.requestAdditionalServices)}]);
  if (incorporation.requestAdditionalServices) {
    tableBody.push([{
      text: 'Additional services requested',
      style: 'tableHeader'
    }, {text: incorporation.additionalServices.join(', ')}]);
    if (incorporation.additionalServices.includes('SIBA licence')) {
      tableBody.push([{text: 'SIBA licence type', style: 'tableHeader'}, {text: incorporation.sibaLicence}]);
    }
    if (incorporation.additionalServices.includes('Trust licence')) {
      tableBody.push([{text: 'Trust licence type', style: 'tableHeader'}, {text: incorporation.trustLicence}]);
    }
    if (incorporation.additionalServices.includes('Other')) {
      tableBody.push([{text: 'Other services requested', style: 'tableHeader'}, {text: incorporation.otherServices}]);
    }
  }

  return tableBody;
}

function createIncorporationStep7Info(incorporation) {
  let tableBody = [];
  if (incorporation.status !== 'IN PROGRESS') {

    tableBody.push([{text: 'Declaration', style: 'tableHeader', colSpan: 2}, {}]);
    tableBody.push([{text: 'The information provided in this and any form provided to Trident in conjunction with this form is, to the best of my knowledge and belief, true and correct.'}, {text: formatBoolean(incorporation.declaration.information)}]);
    tableBody.push([{text: 'The assets to be introduced into the entity/structure and the funds used to pay for Trident’s services in relation thereto are from lawful sources.'}, {text: formatBoolean(incorporation.declaration.assets)}]);
    tableBody.push([{text: 'I have read, understood and accept Trident’s Terms of Business and agree to comply with all of the terms and conditions outlined therein.'}, {text: formatBoolean(incorporation.declaration.termsAndConditions)}]);
    tableBody.push([{text: 'Name'}, {text: incorporation.declaration.name}]);
    tableBody.push([{text: 'Date'}, {text: formatDate(incorporation.declaration.date, 'YYYY-MM-DD')}]);
    tableBody.push([{text: 'Relation to entity'}, {text: incorporation.declaration.relationToEntity}]);
    if (incorporation.declaration.relationToEntity === 'Other') {
      tableBody.push([{text: 'Other relation to entity'}, {text: incorporation.declaration.relationToEntityOther}]);
    }
    tableBody.push([{text: 'Phone'}, {text: incorporation.declaration.phone}]);
  } else {
    tableBody.push([]);
  }


  return tableBody;
}

function formatDate(date, format) {
  if (date) {
    if(typeof(date) === "string"){
      return moment(date).format(format);
    }
    else{
      date = new Date(date.getTime() +  ( date.getTimezoneOffset() * 60000 ));
      return moment(date).format(format);
    }
  } else {
    return '';
  }
}

function formatBoolean(boolValue) {
  if (boolValue == null || boolValue == undefined) {
    return "";
  }
  return (boolValue ? "Yes" : "No")
}

function formatBusinessAddress(businessObj) {
  let addressComponent = [];
  addressComponent.push(businessObj.address_line1);
  addressComponent.push(businessObj.address_line2);
  addressComponent.push(businessObj.city);
  addressComponent.push(businessObj.country);
  addressComponent.push(businessObj.postalcode);

  return addressComponent.filter(function (val) {
    return val;
  }).join(', ');
}


function formatToCurrencyLocaleNumber(number, currency) {
  let val = number && Number(number) ? number : 0;
  return currency + " " + Number(val).toLocaleString('en', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

function createCompanyInfo(entry) {
  let tableBody = [];

  tableBody.push([{text: 'Client Company Name', style: 'tableHeader'}, {text: entry.company_data.name}]);
  tableBody.push([{text: 'Client Company Number', style: 'tableHeader'}, {text: entry.company_data.code}]);
  tableBody.push([{text: 'Registered Agent', style: 'tableHeader'}, {text: "Trident Trust Company (BVI) Limited"}]);
  tableBody.push([{text: 'Master Client Code', style: 'tableHeader'}, {text: entry.company_data.masterclientcode}]);
  tableBody.push([{
    text: 'Date submitted',
    style: 'tableHeader'
  }, {text: formatDate(entry.initial_submit_date ? entry.initial_submit_date : entry.submitted_at, "YYYY-MM-DD")}]);

  if (entry.reopened?.details?.length > 0){
    let reopenedCount = 0;
    entry.reopened.details.forEach((reopened) =>{
      if (reopened.resubmitted_at){
        reopenedCount++;
        tableBody.push([{
          text: `Date resubmitted (${reopenedCount})`,
          style: 'tableHeader'
        }, {text: formatDate(reopened.resubmitted_at, "YYYY-MM-DD")}]);
      }
    });
  }

  return tableBody;
}

function createTableEntityDetails(entry) {
  let tableBody = [];

  const entryVersion = entry.version ? parseFloat(entry.version) : 0;

  if(entryVersion >= 4){
    tableBody.push([{
      text: 'Has an application been made and confirmed with ITA to change your financial period?',
      style: 'tableHeader'
    }, formatBoolean(entry.entity_details.financial_period_changed)]);
  }
  tableBody.push([{
    text: 'Financial period begins',
    style: 'tableHeader'
  }, {text: formatDate(entry.entity_details.financial_period_begins, "YYYY-MM-DD")}]);

  tableBody.push([{
    text: 'Financial period ends',
    style: 'tableHeader'
  }, formatDate(entry.entity_details.financial_period_ends, "YYYY-MM-DD")]);

  if (entryVersion <4){
    tableBody.push([{
      text: 'Has the financial period changed',
      style: 'tableHeader'
    }, formatBoolean(entry.entity_details.financial_period_changed)]);
  }

  if (entry.entity_details.financial_period_changed) {
    tableBody.push([{
      text: 'Previous end date',
      style: 'tableHeader'
    }, formatDate(entry.entity_details.previous_financial_period_ends, "YYYY-MM-DD")]);
    tableBody.push([{
      text: 'Date of application to ITA under Rule 13, 15 or 16',
      style: 'tableHeader'
    }, formatDate(entry.entity_details.date_of_application_ITA, "YYYY-MM-DD")]);
  }

  if (entryVersion >= 5){
    tableBody.push([{
      text: 'TIN',
      style: 'tableHeader'
    }, entry.entity_details.TIN ?? ""]);

    tableBody.push([{
      text: 'Gross total annual income of the entity',
      style: 'tableHeader'
    }, (entry.entity_details.totalAnnualGrossCurrency || 'USD') + " " + (entry.entity_details.totalAnnualGross ?? "")]);

    tableBody.push([{
      text: 'Business address is same as registered address?',
      style: 'tableHeader'
    }, formatBoolean(entry.entity_details.isSameBusinessAddress)]);

    tableBody.push([{
      text: 'Business Address Line 1 (street #, name & city)',
      style: 'tableHeader'
    }, entry.entity_details.businessAddress.address_line1]);

    tableBody.push([{
      text: 'Business Address Line 2',
      style: 'tableHeader'
    }, entry.entity_details.businessAddress.address_line2]);

    const businessCountry = COUNTRIES.find((c) => c.alpha_3_code === entry.entity_details.businessAddress?.country )
    tableBody.push([{
      text: 'Business Address Country',
      style: 'tableHeader'
    }, businessCountry?.name || ""]);

    tableBody.push([{
      text: 'Name of the MNE Group (if applicable)',
      style: 'tableHeader'
    }, entry.entity_details.nameOfMNEGroup || ""]);


    tableBody.push([{
      text: 'Does entity have an ultimate parent entity?',
      style: 'tableHeader'
    }, formatBoolean(entry.entity_details.hasUltimateParents)]);

    tableBody.push([{
      text: 'Does entity have an immediate parent entity?',
      style: 'tableHeader'
    }, formatBoolean(entry.entity_details.hasImmediateParents)]);
    

  }

  return tableBody;
}

function createTableTaxResidency(docDefinition, entry) {

  let tableBody = [];
  tableBody.push([{text: 'Tax Residency', style: 'tableHeader', colSpan: 2}, {}]);

  if (entry.version && parseFloat(entry.version) < 4){
    tableBody.push(['Are you a resident in the BVI?', formatBoolean(entry.tax_residency.resident_in_BVI)])
    if (!entry.tax_residency.resident_in_BVI) {
      tableBody.push(['Entity’s jurisdiction of tax residency', entry.tax_residency.entity_jurisdiction])
      tableBody.push(['Foreign Tax ID Number', entry.tax_residency.foreign_tax_id_number])


      tableBody.push(['Attachments submitted with Substance Filing', ((entry.tax_residency.evidence_non_residency && entry.tax_residency.evidence_non_residency.length > 0) ||
      (entry.tax_residency.evidence_provisional_treatment_non_residency != null && entry.tax_residency.evidence_provisional_treatment_non_residency.length > 0) ? "Yes" : "No")]);

      if (entry.version && parseFloat(entry.version) >= 3) {

        tableBody.push(['Does the entity have a parent entity?', entry.tax_residency.have_parent_entity_not_answered ?
          'Not Answered' : formatBoolean(entry.tax_residency.have_parent_entity)]);
        if (entry.tax_residency.have_parent_entity && !entry.tax_residency.have_parent_entity_not_answered) {
          tableBody.push(['Parent entity name', (entry.tax_residency.parent_entity_name && entry.tax_residency.parent_entity_name.length > 0 ? entry.tax_residency.parent_entity_name : "N/A")]);
          tableBody.push(['Parent entity alternative name', (entry.tax_residency.parent_entity_alternative_name && entry.tax_residency.parent_entity_alternative_name.length > 0 ? entry.tax_residency.parent_entity_alternative_name : "N/A")]);
          tableBody.push(["Parent Entity's jurisdiction of formation", (entry.tax_residency.parent_entity_jurisdiction && entry.tax_residency.parent_entity_jurisdiction.length > 0 ? entry.tax_residency.parent_entity_jurisdiction : "N/A")]);
          tableBody.push(["Parent Entity’s Incorporation/ Formation Number", (entry.tax_residency.parent_entity_incorporation_number && entry.tax_residency.parent_entity_incorporation_number.length > 0 ? entry.tax_residency.parent_entity_incorporation_number : "N/A")]);
        }
      }
    }
  }else{
    tableBody.push(['Does the entity intend to make a claim of tax residency outside the Virgin Islands under rule 2?',
      formatBoolean(!entry.tax_residency.resident_in_BVI)]);
    if (!entry.tax_residency.resident_in_BVI) {
      tableBody.push(['Entity’s jurisdiction of tax residency', entry.tax_residency.entity_jurisdiction]);
      tableBody.push(['Taxpayer identification number (“TIN”) or other identification reference number', entry.tax_residency.foreign_tax_id_number]);

      if (parseFloat(entry.version) < 5){
        tableBody.push(['Name of MNE group', entry.tax_residency.MNE_group_name]);

        tableBody.push(['Does the entity have a parent entity?', entry.tax_residency.have_parent_entity_not_answered ?
          'Not Answered' : formatBoolean(entry.tax_residency.have_parent_entity)]);
        if (entry.tax_residency.have_parent_entity && !entry.tax_residency.have_parent_entity_not_answered) {
          tableBody.push(['Parent entity name', (entry.tax_residency.parent_entity_name && entry.tax_residency.parent_entity_name.length > 0 ? entry.tax_residency.parent_entity_name : "N/A")]);
          tableBody.push(['Parent entity alternative name', (entry.tax_residency.parent_entity_alternative_name && entry.tax_residency.parent_entity_alternative_name.length > 0 ? entry.tax_residency.parent_entity_alternative_name : "N/A")]);
          tableBody.push(["Parent Entity's jurisdiction of formation", (entry.tax_residency.parent_entity_jurisdiction && entry.tax_residency.parent_entity_jurisdiction.length > 0 ? entry.tax_residency.parent_entity_jurisdiction : "N/A")]);
          tableBody.push(["Parent Entity’s Incorporation/ Formation Number", (entry.tax_residency.parent_entity_incorporation_number && entry.tax_residency.parent_entity_incorporation_number.length > 0 ? entry.tax_residency.parent_entity_incorporation_number : "N/A")]);
        }
      }

      tableBody.push(['Evidence type', entry.tax_residency.evidence_type === "non residency" ?
        "Evidence of Tax Residency in another jurisdiction which meets ITA Rule 3" :
        "Provisional treatment as non-resident under ITA Rule 6 which meets conditions in Rule 10"
      ]);
      tableBody.push(['Attachments submitted with Substance Filing', ((entry.tax_residency.evidence_non_residency && entry.tax_residency.evidence_non_residency.length > 0) ||
      (entry.tax_residency.evidence_provisional_treatment_non_residency != null && entry.tax_residency.evidence_provisional_treatment_non_residency.length > 0) ? "Yes" : "No")]);

    }
  }

  docDefinition.content.push(
    {
      style: 'table',
      table: {
        widths: [300, '*'],
        body: tableBody
      }
    });

  return tableBody;
}

function createTableRelevantActivities(docDefinition, entry) {

  if (entry.relevant_activities) {

    let tableBody = [];
    tableBody.push([{text: 'Relevant Activities', style: 'tableHeader', colSpan: 5}, {}, {}, {}, {}]);
    tableBody.push([{text: 'Relevant Activity Period'}, {
      text: 'Start date',
      style: 'tableHeader'
    }, {text: formatDate(entry.entity_details.financial_period_begins, "YYYY-MM-DD")}, {
      text: 'End date',
      style: 'tableHeader'
    }, {text: formatDate(entry.entity_details.financial_period_ends, "YYYY-MM-DD")}]);
    tableBody.push([{text: 'Relevant Activity or Activities Conducted', colSpan: 5}, {}, {}, {}, {}]);
    if (entry.relevant_activities.banking_business.selected) {
      tableBody.push([{
        text: 'Banking Business',
        style: 'tableHeader',
        colSpan: 2
      }, {}, formatDate(entry.relevant_activities.banking_business.financial_periods[0].financial_period_begins, "YYYY-MM-DD"), {}, {text: formatDate(entry.relevant_activities.banking_business.financial_periods[0].financial_period_ends, "YYYY-MM-DD")}]);
      //addFinancialPeriods(tableBody, entry.relevant_activities.banking_business.financial_periods)
    }

    if (entry.relevant_activities.insurance_business.selected) {
      tableBody.push([{
        text: 'Insurance Business',
        style: 'tableHeader',
        colSpan: 2
      }, {}, formatDate(entry.relevant_activities.insurance_business.financial_periods[0].financial_period_begins, "YYYY-MM-DD"), {}, {text: formatDate(entry.relevant_activities.insurance_business.financial_periods[0].financial_period_ends, "YYYY-MM-DD")}]);
      addFinancialPeriods(tableBody, entry.relevant_activities.insurance_business.financial_periods)
    }
    if (entry.relevant_activities.fund_management_business.selected) {
      tableBody.push([{
        text: 'Fund Management Business',
        style: 'tableHeader',
        colSpan: 2
      }, {}, formatDate(entry.relevant_activities.fund_management_business.financial_periods[0].financial_period_begins, "YYYY-MM-DD"), {}, {text: formatDate(entry.relevant_activities.fund_management_business.financial_periods[0].financial_period_ends, "YYYY-MM-DD")}]);
      addFinancialPeriods(tableBody, entry.relevant_activities.fund_management_business.financial_periods)
    }
    if (entry.relevant_activities.finance_leasing_business.selected) {
      tableBody.push([{
        text: 'Finance and Leasing Business',
        style: 'tableHeader',
        colSpan: 2
      }, {}, formatDate(entry.relevant_activities.finance_leasing_business.financial_periods[0].financial_period_begins, "YYYY-MM-DD"), {}, {text: formatDate(entry.relevant_activities.finance_leasing_business.financial_periods[0].financial_period_ends, "YYYY-MM-DD")}]);
      addFinancialPeriods(tableBody, entry.relevant_activities.finance_leasing_business.financial_periods)
    }
    if (entry.relevant_activities.headquarters_business.selected) {
      tableBody.push([{
        text: 'Headquarters business',
        style: 'tableHeader',
        colSpan: 2
      }, {}, formatDate(entry.relevant_activities.headquarters_business.financial_periods[0].financial_period_begins, "YYYY-MM-DD"), {}, {text: formatDate(entry.relevant_activities.headquarters_business.financial_periods[0].financial_period_ends, "YYYY-MM-DD")}]);
      addFinancialPeriods(tableBody, entry.relevant_activities.headquarters_business.financial_periods)
    }
    if (entry.relevant_activities.shipping_business.selected) {
      tableBody.push([{
        text: 'Shipping Business',
        style: 'tableHeader',
        colSpan: 2
      }, {}, formatDate(entry.relevant_activities.shipping_business.financial_periods[0].financial_period_begins, "YYYY-MM-DD"), {}, {text: formatDate(entry.relevant_activities.shipping_business.financial_periods[0].financial_period_ends, "YYYY-MM-DD")}]);
      addFinancialPeriods(tableBody, entry.relevant_activities.shipping_business.financial_periods)
    }
    if (entry.relevant_activities.holding_business.selected) {
      tableBody.push([{
        text: 'Holding Business (Pure Equity Holding entities)',
        style: 'tableHeader',
        colSpan: 2
      }, {}, formatDate(entry.relevant_activities.holding_business.financial_periods[0].financial_period_begins, "YYYY-MM-DD"), {}, {text: formatDate(entry.relevant_activities.holding_business.financial_periods[0].financial_period_ends, "YYYY-MM-DD")}]);
      addFinancialPeriods(tableBody, entry.relevant_activities.holding_business.financial_periods)
    }
    if (entry.relevant_activities.intellectual_property_business.selected) {
      tableBody.push([{
        text: 'Intellectual Property Business',
        style: 'tableHeader',
        colSpan: 2
      }, {}, formatDate(entry.relevant_activities.intellectual_property_business.financial_periods[0].financial_period_begins, "YYYY-MM-DD"), {}, {text: formatDate(entry.relevant_activities.intellectual_property_business.financial_periods[0].financial_period_ends, "YYYY-MM-DD")}]);
      addFinancialPeriods(tableBody, entry.relevant_activities.intellectual_property_business.financial_periods)
    }
    if (entry.relevant_activities.service_centre_business.selected) {
      tableBody.push([{
        text: 'Distribution and Service Centre Business',
        style: 'tableHeader',
        colSpan: 2
      }, {}, formatDate(entry.relevant_activities.service_centre_business.financial_periods[0].financial_period_begins, "YYYY-MM-DD"), {}, {text: formatDate(entry.relevant_activities.service_centre_business.financial_periods[0].financial_period_ends, "YYYY-MM-DD")}]);
      addFinancialPeriods(tableBody, entry.relevant_activities.service_centre_business.financial_periods)
    }
    if (entry.relevant_activities.none.selected) {
      tableBody.push([{
        text: 'None',
        style: 'tableHeader',
        colSpan: 2
      }, {}, formatDate(entry.relevant_activities.none.financial_periods[0].financial_period_begins, "YYYY-MM-DD"), {}, {text: formatDate(entry.relevant_activities.none.financial_periods[0].financial_period_ends, "YYYY-MM-DD")}]);
      addFinancialPeriods(tableBody, entry.relevant_activities.none.financial_periods)
      if (entry.relevant_activities.none_remarks && entry.relevant_activities.none_remarks.length > 0) {
        tableBody.push([{text: entry.relevant_activities.none_remarks, colSpan: 5}, {}, {}, {}, {}]);
      }
    }

    docDefinition.content.push(
      {
        style: 'table',
        table: {
          widths: [200, '*', '*', '*', '*'],
          body: tableBody
        }
      });

    return tableBody;
  }
}

function addFinancialPeriods(tableBody, financial_periods) {
  if (financial_periods.length > 1) {
    for (let index = 1; index < financial_periods.length; index++) {
      tableBody.push([{
        text: '',
        colspan: 2
      }, {}, {text: formatDate(financial_periods[index].financial_period_begins, "YYYY-MM-DD")}, {}, {text: formatDate(financial_periods[index].financial_period_ends, "YYYY-MM-DD")}]);
    }
  }
}

function createTableBusiness(docDefinition, businessObj, businessTitle, entryVersion) {

  let tableBody = [];
  if (businessTitle == 'Intellectual Property Business') {
    tableBody.push(['Is the entity high risk?', formatBoolean(businessObj.high_risk_ip)]);
    tableBody.push(['Can the company provide evidence that would allow it to contest  the  rebuttable  presumption  introduced  by ESA section  9(2)(b)?', formatBoolean(businessObj.evidence_high_risk_ip)]);
    tableBody.push(['Attachments submitted with Substance Filing', (businessObj.high_risk_ip_evidence && businessObj.high_risk_ip_evidence.length > 0 ? "Yes" : "No")]);
  }
  if (businessTitle != 'Holding Business (Pure Equity Holding entities)') {
    tableBody.push(['Is the relevant activity directed and managed in the BVI?', formatBoolean(businessObj.management_in_bvi)]);
    tableBody.push(['Number of board meetings the entity held during the financial period', businessObj.number_of_board_meetings]);
    tableBody.push(['How many board meetings were held outside of the BVI', businessObj.number_or_board_meetings_outside_bvi]);
    tableBody.push(['Total turnover for the relevant activity during the financial period', businessObj.total_turnover]);
    tableBody.push(['Total expenditure incurred on the relevant activity during the financial period', businessObj.total_expenditure]);
    tableBody.push(['Total expenditure incurred in the BVI on the relevant activity during the financial period', businessObj.total_expenditure_bvi]);
  } else {
    tableBody.push(['Is the entity in compliance with its statutory obligations?', formatBoolean(businessObj.compliant_with_statutory_obligations)]);
    tableBody.push(['Does the entity manage its equity participations?', formatBoolean(businessObj.manage_equity_participations)]);
  }
  if (entryVersion && parseFloat(entryVersion) >= 3) {
    if ((businessTitle != 'Holding Business (Pure Equity Holding entities)' && businessTitle != "Intellectual Property Business") ||
      (businessTitle == "Intellectual Property Business" && (businessObj.high_risk_ip == false || businessObj.evidence_high_risk_ip == true)) ||
      (businessTitle == 'Holding Business (Pure Equity Holding entities)' && businessObj.manage_equity_participations)) {
      tableBody.push(['Total number of employees', businessObj.full_total_employees]);
      tableBody.push(['Total number of suitably qualified employees engaged in the relevant activity in the BVI', businessObj.total_employees]);
      tableBody.push(['Is the total number suitably qualified employees engaged in the relevant activity in the BVI adequate?', formatBoolean(businessObj.total_employees_adequate)]);
    }
  }
  if (businessTitle != 'Holding Business (Pure Equity Holding entities)' || businessObj.manage_equity_participations) {
    if (entryVersion && parseFloat(entryVersion) < 3) {
      tableBody.push(['Total number of suitably qualified employees engaged in the relevant activity in the BVI', businessObj.total_employees]);
      tableBody.push(['Is this number adequate?', formatBoolean(businessObj.total_employees_adequate)]);
    }
    if (businessTitle != 'Holding Business (Pure Equity Holding entities)') {
      tableBody.push(['Are premises appropriate for income generating activities in the BVI?', formatBoolean(businessObj.income_generating_premises)]);
    } else {
      tableBody.push(['Are premises appropriate for carrying out the management of the entity’s equity participations?', formatBoolean(businessObj.income_generating_premises)]);
    }
    if (businessTitle == 'Intellectual Property Business') {
      tableBody.push(['Is any equipment located within the BVI that is used in connection with the relevant activity?', formatBoolean(businessObj.equipment_in_bvi)]);
      tableBody.push(['Attachments submitted with Substance Filing', (businessObj.evidence_equipment && businessObj.evidence_equipment.length > 0 ? "Yes" : "No")]);
    }

    docDefinition.content.push({text: businessTitle, fontSize: 15, margin: [0, 20, 0, 20]});
    docDefinition.content.push(
      {
        style: 'table', // optional
        table: {
          headerRows: 0,
          widths: [400, '*'],

          body: tableBody
        },
        //pageBreak: "after"
      });
    if (businessTitle != 'Holding Business (Pure Equity Holding entities)') {
      let tableDirectors = [];
      tableDirectors.push([{text: 'Name', style: 'tableHeader'},
        {text: 'Date of Birth / Date of Incorporation', style: 'tableHeader'},
        {text: 'Address', style: 'tableHeader'},
        {text: 'Resident in BVI', style: 'tableHeader'},
        {text: 'Position held', style: 'tableHeader'}]);

      for (let manager of businessObj.managers) {
        tableDirectors.push([manager.first_name + "  " + (manager.middle_name ? manager.middle_name + " " : "") + manager.last_name,
          formatDate(manager.date_of_birth, "YYYY-MM-DD"),
          formatBusinessAddress(manager),
          formatBoolean(manager.resident_in_bvi),
          manager.position_held
        ]);
      }

      docDefinition.content.push({ text: "Direction and management", fontSize: 12, margin: [0, 20, 0, 20]});

      docDefinition.content.push(
        {
          style: 'table', // optional
          table: {
            headerRows: 0,
            widths: ['*', '*', '*', '*', '*'],

            body: tableDirectors
          },
          //pageBreak: "after"
        });
    }
    let tablePremises = [];
    tablePremises.push(['Address line 1', 'Address line 2', 'Island', 'Country', 'Postalcode']);

    for (let premises of businessObj.premises) {
      tablePremises.push([premises.address_line1,
        premises.address_line2,
        premises.city || "",
        premises.country,
        premises.postalcode || ""
      ]);
    }

    docDefinition.content.push({text: "Premises", fontSize: 12, margin: [0, 20, 0, 20]});

    docDefinition.content.push(
      {
        style: 'table', // optional
        table: {
          headerRows: 0,
          widths: ['*', '*', '*', '*', '*'],

          body: tablePremises
        },
        //pageBreak: "after"
      });
  } else {
    docDefinition.content.push({text: businessTitle, fontSize: 15, margin: [0, 20, 0, 20]});
    docDefinition.content.push(
      {
        style: 'table', // optional
        table: {
          headerRows: 0,
          widths: [400, '*'],

          body: tableBody
        },
        //pageBreak: "after"
      });
  }

  if (businessTitle != 'Holding Business (Pure Equity Holding entities)') {
    let tableOutsourcing = [];
    docDefinition.content.push({text: "Outsourcing", fontSize: 12, margin: [0, 20, 0, 20]});
    tableOutsourcing.push(['Has any core income generating activity (CIGA) been outsourced?', formatBoolean(businessObj.core_income_generating_outsourced)])
    if (businessObj.core_income_generating_outsourced) {
      tableOutsourcing.push(['Has the outsourced activity been undertaken in the BVI?', formatBoolean(businessObj.outsourced_activity_undertaken_in_BVI)])
      if (!businessObj.outsourced_activity_undertaken_in_BVI) {

        tableOutsourcing.push([{text: 'Explanation', colSpan: 2}, {}]);
        tableOutsourcing.push([{text: businessObj.explanation_outsourced_activity_undertaken_in_BVI, colSpan: 2}, {}]);

      }
      tableOutsourcing.push(['Is the legal entity able to demonstrate that it is monitoring the outsourced activity?', formatBoolean(businessObj.demonstrate_monitoring_outsourced_activity)])
      if (businessObj.demonstrate_monitoring_outsourced_activity) {
        tableOutsourcing.push([{text: 'Explanation', colSpan: 2}, {}]);
        tableOutsourcing.push([{
          text: businessObj.explanation_demonstrate_monitoring_outsourced_activity,
          colSpan: 2
        }, {}]);
      }
      if (businessObj.outsourcing_evidence != null && businessObj.outsourcing_evidence.length > 0) {
        tableOutsourcing.push(['Attachments submitted with Substance Filing', (businessObj.outsourcing_evidence && businessObj.outsourcing_evidence.length > 0 ? "Yes" : "No")]);

      }
    }


    docDefinition.content.push(
      {
        style: 'table',
        table: {
          widths: [400, '*'],
          body: tableOutsourcing
        }
      });
  }
}

function createTableBusinessV5(docDefinition, businessObj, entryCurrency, activityType, title){
  let tableBody = [];
  tableBody.push(['Total Gross Income for the relevant activity during the financial period', 
    entryCurrency + " " + businessObj.gross_income_total]);
  tableBody.push(['Type of gross income in relation to the relevant activity', businessObj.gross_income_type]);

  // COMMON FIELDS FOR ACTIVITIES != HOLDING 
  if (activityType !== settings.substance_business_types.HOLDING){
    tableBody.push(['Amount and type of assets and premises held in the course of carrying out the relevant activity',
      `${businessObj.activity_assets_amount} ${businessObj.activity_assets_type}`]);
    tableBody.push(['Net book values of tangible assets held in the course of carrying out the relevant activity',
      businessObj.activity_netbook_values]);
    
    //direction and management
    tableBody.push(['Is the relevant activity directed and managed in the Virgin Islands?', formatBoolean(businessObj.management_in_bvi)]);

    tableBody.push(['Number of board meetings the entity held during the financial period', businessObj.number_of_board_meetings]);
    tableBody.push(['Of those board meetings, how many were held in the Virgin Islands where a quorum of directors was physically present?',
     businessObj.number_of_board_meetings_in_bvi]); 
    if (businessObj.number_of_board_meetings_in_bvi > 0){
      tableBody.push(['Are the minutes for these board meetings being held in the Virgin Islands?', formatBoolean(businessObj.are_minutes_for_board_meetings)]);
    }


    tableBody.push(['Quorum of board meetings', businessObj.quorum_of_board_meetings]);
    tableBody.push(['Quorum of directors physically present in the Virgin Islands?', formatBoolean(businessObj.are_quorum_of_directors)]);
    tableBody.push(['Total expenditure incurred on the relevant activity during the financial period',
      entryCurrency + " " + businessObj.total_expenditure]);
    tableBody.push(['Total expenditure incurred in the BVI on the relevant activity during the financial period',
      entryCurrency + " " + businessObj.total_expenditure_bvi]);
    tableBody.push(['Total number of employees', businessObj.full_total_employees]);
    tableBody.push(['Total number of employees engaged in the relevant activity', businessObj.total_employees_engaged]);
    tableBody.push(['Total number of suitably qualified employees engaged in the relevant activity in the BVI', businessObj.total_employees]);
  }
  else{
    //  FIELDS FOR HOLDING ACTIVITY
    tableBody.push(['Total expenditure incurred on the relevant activity during the financial period',
      entryCurrency + " " + businessObj.total_expenditure]);
    tableBody.push(['Total expenditure incurred in the BVI on the relevant activity during the financial period',
      entryCurrency + " " + businessObj.total_expenditure_bvi]);
    tableBody.push(['Does the entity actively manage its equity participation?', formatBoolean(businessObj.manage_equity_participations)]);

    if (businessObj.manage_equity_participations === true){
      tableBody.push(['Total number of employees', businessObj.full_total_employees]);
      tableBody.push(['Total number of employees engaged in the relevant activity', businessObj.total_employees_engaged]);
      tableBody.push(['Total number of suitably qualified employees engaged in the relevant activity in the BVI', businessObj.total_employees]);
    }else{
      tableBody.push(['Does the entity comply with its statutory obligations under the BVI Business Companies Act, 2004 or the Limited Partnership Act, 2017(whichever is relevant)?', 
        formatBoolean(businessObj.compliant_with_statutory_obligations)]);
      
    }
  }

  // FIELDS FOR OTHER TYPES OF ACTIVITIES 
  if (activityType !== settings.substance_business_types.HOLDING && activityType !== settings.substance_business_types.INTELLECTUAL_PROPERTY){
    const activityCore = CIGA_CODES.find((cigaCode) => cigaCode.code === businessObj.activity_ciga_core );

    tableBody.push(['Core Income Generating Activities conducted carried out in the Virgin Islands for the relevant activity',
      activityCore?.description || businessObj.activity_ciga_core]);
    if (businessObj.activity_ciga_core == '0.2'){
      tableBody.push(['Core Income Other',
        businessObj.activity_ciga_core_other]);
    }

    tableBody.push(['Has any core income generating activity(CIGA) been outsourced to another entity?', 
      formatBoolean(businessObj.core_income_generating_outsourced)]);
    tableBody.push(['Total expenditure incurred on outsourcing in the Virgin Islands during the financial period?', 
      entryCurrency + " " + businessObj.outsourcing_total_expenditure]);
  }

  // FIELDS FOR INTELLECTUAL PROPERTY
  if (activityType === settings.substance_business_types.INTELLECTUAL_PROPERTY){
    tableBody.push(['Is the entity high risk?', formatBoolean(businessObj.high_risk_ip)]);
    tableBody.push(['Does the entity wish to provide evidence to rebut the presumption as set out in ESA section 9(4)?', formatBoolean(businessObj.evidence_high_risk_ip)]);
    tableBody.push(['Attachments submitted with Substance Filing', (businessObj.high_risk_ip_evidence && businessObj.high_risk_ip_evidence.length > 0 ? "Yes" : "No")]);

    if (businessObj.high_risk_ip === true){
      tableBody.push(['Gross income through Royalties, if applicable',
        entryCurrency +  " " + businessObj.high_risk_gross_income_total]);
      tableBody.push(['Gross income through Gains from sale of IP Assets, if applicable',
        entryCurrency + " " + businessObj.high_risk_gross_income_assets]);
      tableBody.push(['Gross income through others', 
        entryCurrency + " " +businessObj.high_risk_gross_income_others]);
      tableBody.push(['The relevant tangible asset which the corporate and legal entity holds', businessObj.tangible_assets_name]);
      tableBody.push(['Explanation of how that tangible asset is being used to generate income', businessObj.tangible_assets_explanation]);
      tableBody.push(['Identify the decisions for which each employee is responsible for in respect of the generation of income from the intangible asset',
        businessObj.intangible_assets_decisions]);
      tableBody.push(['The nature and history of strategic decisions (if any) taken by the entity in the Virgin Islands',
        businessObj.intangible_assets_nature]);
      tableBody.push(['The nature and history of the trading activities (if any carried out in the Virgin Islands by which) the intangible assets is exploited for the purpose of generating income from third parties', 
        businessObj.intangible_assets_trading_nature]);
    }else{
      tableBody.push(['Does the legal entity conduct CIGA other than those outlined in section 7(h) of the ESA?', 
        formatBoolean(businessObj.is_other_ciga_legal_entity)]);
      if (businessObj.is_other_ciga_legal_entity === true){
        tableBody.push(['Does the entity wish to provide evidence to rebut the presumption as set out in ESA section 9(3)?',
          formatBoolean(businessObj.has_other_ciga_evidences)]);
        tableBody.push(['Identify the relevant IP asset which it holds', businessObj.other_ciga_ip_asset]);
        tableBody.push(['Provide the detail business plans which explain the commercial rationale of holding the intellectual property assets in the Virgin Islands',
          businessObj.other_ciga_business_details]);
        tableBody.push(['Identify the decisions for which each employee is responsible in respect of the generation of income from the intangible asset', 
          businessObj.other_ciga_decisions]);
        tableBody.push(['Provide concrete evidence that decision making is taking place within the Virgin Islands, including but not limited to, minutes of meetings which have taken place in the Virgin Islands',
          businessObj.other_ciga_evidence_details]);
      }else{
        const activityCore = CIGA_CODES.find((cigaCode) => cigaCode.code === businessObj.activity_ciga_core);
        tableBody.push(['Core Income Generating Activities conducted carried out in the Virgin Islands for the relevant activity',
          activityCore?.description || businessObj.activity_ciga_core]);
        if (businessObj.activity_ciga_core == '0.2') {
          tableBody.push(['Core Income Other',
            businessObj.activity_ciga_core_other]);
        }
      }
    }

    tableBody.push(['Has any core income generating activity(CIGA) been outsourced to another entity?',
      formatBoolean(businessObj.core_income_generating_outsourced)]);
    tableBody.push(['Total expenditure incurred on outsourcing in the Virgin Islands during the financial period?',
      entryCurrency + " " + businessObj.outsourcing_total_expenditure]);
    
    tableBody.push(['Description of the nature of any equipment located within the Virgin Islands used in connection with the relevant activity',
      businessObj.equipment_nature_description]);     

  }
  docDefinition.content.push({ text: title, fontSize: 15, margin: [0, 10, 0, 10] });
  docDefinition.content.push({
    style: 'table', 
    table: {
      headerRows: 0,
      widths: [400, '*'],

      body: tableBody
    },
  });


  
  if (activityType !== settings.substance_business_types.HOLDING){
    // DIRECTORS TABLE
    let tableDirectors = [];
    tableDirectors.push([{ text: 'Name', style: 'tableHeader' },
    { text: 'Corporate director?', style: 'tableHeader'},
    { text: 'Resident in Virgin Islands', style: 'tableHeader' },
    { text: 'Relation to Entity', style: 'tableHeader' }]);

    if (businessObj.managers.length > 0) {
      for (let manager of businessObj.managers) {
        tableDirectors.push([
          manager.first_name + "  " + (manager.middle_name ? manager.middle_name + " " : "") + manager.last_name,
          formatBoolean(manager.is_corporate_director),
          formatBoolean(manager.resident_in_bvi),
          manager.position_held
        ]);
      }
    }
    docDefinition.content.push({ text: "Direction and management", fontSize: 12, margin: [0, 10, 0, 10] });

    docDefinition.content.push({
      style: 'table', 
      table: {
        headerRows: 0,
        widths: ['*', '*', '*', '*'],
        body: tableDirectors
      },

    });

    // BOARD MEETINGS
    if (businessObj.number_of_board_meetings_in_bvi > 0 && businessObj.board_meetings?.length > 0) {
      let tableBoardMeetings = [];
      tableBoardMeetings.push([
        { text: 'Meeting #', style: 'tableHeader'},
        { text: 'Name', style: 'tableHeader' },
        { text: 'Physically Present', style: 'tableHeader' },
        { text: 'Relation to Entity', style: 'tableHeader' },
        { text: 'Qualification', style: 'tableHeader' }
      ]);

      for (let boardMeeting of businessObj.board_meetings) {
        tableBoardMeetings.push([
          boardMeeting.meeting_number,
          boardMeeting.name,
          formatBoolean(boardMeeting.physically_present),
          boardMeeting.relation_to_entity,
          boardMeeting.qualification
        ]);
      }

      docDefinition.content.push({ text: "Board Meetings", fontSize: 12, margin: [0, 10, 0, 10] });
      docDefinition.content.push({
        style: 'table',
        table: {
          headerRows: 0,
          widths: ['15%', '*', '15%', '*', '*'],

          body: tableBoardMeetings
        },
      });
    }
  }

  // EMPLOYEES TABLE
  if (businessObj.total_employees > 0 && businessObj.employees?.length > 0) {
    let tableEmployees = [];
    tableEmployees.push([
      { text: 'Name', style: 'tableHeader' },
      { text: 'Qualification', style: 'tableHeader' },
      { text: 'Years of relevant experience', style: 'tableHeader' }
    ]);

    for (let employee of businessObj.employees) {
      tableEmployees.push([
        employee.name,
        employee.qualification,
        employee.experience_years
      ]);
    }

    docDefinition.content.push({ text: "Employees", fontSize: 12, margin: [0, 10, 0, 10] });
    docDefinition.content.push({
      style: 'table',
      table: {
        headerRows: 0,
        widths: ['*', '*', '*'],

        body: tableEmployees
      },
    });
  }

  // PREMISES TABLE
  let tablePremises = [];
  tablePremises.push([
    { text: 'Address line 1', style: 'tableHeader' },
    { text: 'Address line 2', style: 'tableHeader' },
    { text: 'Country', style: 'tableHeader' }
  ]);
  
  if (businessObj.premises?.length > 0){
    for (let premises of businessObj.premises) {
      const country = COUNTRIES.find((c) => c.alpha_3_code === premises.country);
      tablePremises.push([premises.address_line1,
      premises.address_line2,
      (country ? country.name : premises.country)
      ]);
    }
  }

  docDefinition.content.push({ text: "Premises", fontSize: 12, margin: [0, 10, 0, 10] });
  docDefinition.content.push({
    style: 'table',
    table: {
      headerRows: 0,
      widths: ['*', '*', '*',],

      body: tablePremises
    },
  });

  if (businessObj.core_income_generating_outsourced === true){
    let tableOutsourcing = [];
    tableOutsourcing.push([
      { text: 'Entity Name', style: 'tableHeader' },
      { text: 'Details of Resources', style: 'tableHeader' },
      { text: 'Number of staff', style: 'tableHeader' },
      { text: 'Hours per month', style: 'tableHeader' },
      { text: 'Monitoring and Control', style: 'tableHeader' },
    ]);

    if (businessObj.outsourcing_providers?.length > 0) {
      for (let provider of businessObj.outsourcing_providers) {
        tableOutsourcing.push([
          provider.entity_name,
          provider.resource_details,
          provider.staff_count,
          provider.hours_per_month,
          formatBoolean(provider.monitoring_control)
        ]);
      }
    }

    docDefinition.content.push({ text: "Outsourcing", fontSize: 12, margin: [0, 10, 0, 10] });
    docDefinition.content.push({
      style: 'table',
      table: {
        headerRows: 0,
        widths: ['*', '30%', '15%', '15%', '15%'],

        body: tableOutsourcing
      },
    });
  }

}

function createTableConfirmation(docDefinition, entry) {
  if (entry.confirmation) {
    let tableBody = [];
    tableBody.push([{text: 'Confirmation', colSpan: 2, style: 'tableHeader'}, {}]);
    tableBody.push(['I confirm that the information provided above is true and accurate to the best of my knowledge and belief and that by submission of this information to the Registered Agent, I have provided all the information required to complete the economic substance self-assessment.', formatBoolean(entry.confirmation.confirmed)])
    tableBody.push(['Please confirm you have the authority to act on behalf of the company.', formatBoolean(entry.confirmation.confirmed_authority)])
    tableBody.push(['I confirm and acknowledge that the Registered Agent has a legitimate interest for processing any personal data provided above; specifically, in order to ensure the Registered Agent’s and the Entity’s compliance with relevant BVI law. I further acknowledge that the processing of such personal data may include its transfer to BVI competent authorities and that the Registered Agent’s processing of any personal data will be done in accordance with the https://tridenttrust.com/legal-pages/data-protection/ (Trident Trust Data Privacy Policy), which I have read and understood', formatBoolean(entry.confirmation.confirmed_conditions)])
    if (entry.company_data.amount && entry.company_data.amount > 0) {
      tableBody.push(['I confirm and acknowledge that the submission fee in the amount of US$' + entry.company_data.amount + ' is due and payable in order to complete the submission process', formatBoolean(entry.confirmation.confirmed_payment)])
    }
    tableBody.push(['Relation to entity', entry.confirmation.relation_to_entity]);
    tableBody.push(['Relation to entity (other)', (entry.confirmation.relation_to_entity_other && entry.confirmation.relation_to_entity_other.length > 0 ? entry.confirmation.relation_to_entity_other : "N/A")]);
    tableBody.push(['Name of the person making the declaration', entry.confirmation.user_fullname]);
    tableBody.push(['Phone number', entry.confirmation.user_phonenumber]);
    if (!entry.version || (entry.version && parseFloat(entry.version) < 3)) {
      tableBody.push(['Ultimate parent entity name', (entry.confirmation.ultimate_parent_entity_name && entry.confirmation.ultimate_parent_entity_name.length > 0 ? entry.confirmation.ultimate_parent_entity_name : "N/A")]);
      tableBody.push(['Ultimate parent entity address', (entry.confirmation.ultimate_parent_entity_address && entry.confirmation.ultimate_parent_entity_address.length > 0 ? entry.confirmation.ultimate_parent_entity_address : "N/A")]);
      tableBody.push(['Entity jurisdiction', (entry.confirmation.entity_jurisdiction && entry.confirmation.entity_jurisdiction.length > 0 ? entry.confirmation.entity_jurisdiction : "N/A")]);
      tableBody.push(['Incorporation number', (entry.confirmation.incorporation_number && entry.confirmation.incorporation_number.length > 0 ? entry.confirmation.incorporation_number : "N/A")]);
    }


    docDefinition.content.push(
      {
        style: 'table',
        table: {
          widths: [300, '*'],
          body: tableBody
        }
      });
  }
}

function createParentEntityTable(docDefinition, parents, title){
  let tableParents = [];
  tableParents.push(['Name', 'Alternative Name', 'Jurisdiction of Formation', 'Incorporation Number', 'TIN or other number']);

  for (let parent of parents) {
    const jurisdiction = COUNTRIES.find((c) => c.alpha_3_code === parent.jurisdiction)
    tableParents.push([
      parent.parentName,
      parent.alternativeName,
      jurisdiction.name || "",
      parent.incorporationNumber,
      parent.TIN
    ]);
  }

  docDefinition.content.push({ text: title, fontSize: 12, margin: [0, 20, 0, 20] });

  docDefinition.content.push({
      style: 'table', 
      table: {
        headerRows: 0,
        widths: ['20%', '20%', '20%', '20%', '20%'],

        body: tableParents
      },
  });
}