
$(document).ready(function () {

    $("#missing-dirbo-data-table").DataTable({
        "searching": false,   
        "ordering": false,   
        "info": true,  
        "pageLength": 5,
        "lengthChange": false,
        "scrollX": false,
        "language": {
            paginate: { previous: "<i class='mdi mdi-chevron-left'>", next: "<i class='mdi mdi-chevron-right'>" }
        },
        drawCallback: function () { $(".dataTables_paginate > .pagination").addClass("pagination-rounded") }
    })
});
