const express = require('express');
const router = express.Router({ mergeParams: true });

// Require controller modules.
const financialReportController = require('../controllers/financialReportController');
const companyController = require('../controllers/companyController');
const downloadController = require('../controllers/downloadController');
const uploadController = require('../controllers/uploadController');


router.get('/', ensureAuthenticated, companyController.validateMasterClient, financialReportController.getDashboard);


router.get('/companies/:companyCode/', ensureAuthenticated, financialReportController.validateMccAndCompany, financialReportController.getCompanyForms);

router.post('/companies/:companyCode/create', ensureAuthenticated, financialReportController.validateMccAndCompany, financialReportController.createNewFinancialReport); // jquery / ajax

router.get('/companies/:companyCode/:reportId', ensureAuthenticated, financialReportController.validateMccAndCompany,
  financialReportController.validateFinancialReport, financialReportController.loadFinancialReportForm);

router.post('/companies/:companyCode/:reportId',ensureAuthenticated, companyController.validateMasterClient,
  financialReportController.validateFinancialReport, financialReportController.saveFinancialReportForm); // jquery / ajax

router.delete('/companies/:companyCode/:reportId',ensureAuthenticated, companyController.validateMasterClient,
  financialReportController.validateFinancialReport,  financialReportController.deleteFinancialReport); // jquery / ajax

// files
router.post('/companies/:companyCode/:reportId/upload-files', ensureAuthenticated, companyController.validateMasterClient,
  financialReportController.validateFinancialReport,
  uploadController.uploadErrorHandler(uploadController.uploadFinancialReportsFile.fields([{ name: 'fileUploaded', maxCount: 5 }])),
  financialReportController.saveFinancialReportFiles 
);
router.get('/companies/:companyCode/:reportId/search-files', ensureAuthenticated, companyController.validateMasterClient,
  financialReportController.validateFinancialReport, financialReportController.getFinancialReportFiles 
); // jquery / ajax

router.delete('/companies/:companyCode/:reportId/files', ensureAuthenticated, companyController.validateMasterClient,
  financialReportController.validateFinancialReport, financialReportController.deleteFinancialReportFiles
);
router.get('/companies/:companyCode/:reportId/files/:fileId/download', ensureAuthenticated, companyController.validateMasterClient,
  financialReportController.validateFinancialReport, downloadController.downloadFinancialReportFiles
); 


// bank account Financial
router.get('/companies/:companyCode/:reportId/bank-accounts/', ensureAuthenticated, companyController.validateMasterClient,
  financialReportController.validateFinancialReport, financialReportController.getBankAccounts); // jquery / ajax

router.post('/companies/:companyCode/:reportId/bank-accounts/', ensureAuthenticated, companyController.validateMasterClient,
  financialReportController.validateFinancialReport, financialReportController.createBankAccount); // jquery / ajax
router.post('/companies/:companyCode/:reportId/bank-accounts/invalidate', ensureAuthenticated, companyController.validateMasterClient,
  financialReportController.validateFinancialReport, financialReportController.invalidateBankAccounts); // jquery / ajax
router.put('/companies/:companyCode/:reportId/bank-accounts/:bankAccountId', ensureAuthenticated, companyController.validateMasterClient,
  financialReportController.validateFinancialReport, financialReportController.editBankAccount); // jquery / ajax
router.delete('/companies/:companyCode/:reportId/bank-accounts/:bankAccountId', ensureAuthenticated, companyController.validateMasterClient,
  financialReportController.validateFinancialReport, financialReportController.deleteBankAccount); // jquery / ajax
router.delete('/companies/:companyCode/:reportId/bank-accounts', ensureAuthenticated, companyController.validateMasterClient,
  financialReportController.validateFinancialReport, financialReportController.deleteAllBankAccounts); // jquery / ajax

// PROPERTY Financial

router.get('/companies/:companyCode/:reportId/report.pdf', ensureAuthenticated, companyController.validateMasterClient,
  financialReportController.validateFinancialReport, financialReportController.downloadSummaryPdf);

router.get('/companies/:companyCode/:reportId/invoice.pdf', ensureAuthenticated, companyController.validateMasterClient,
  financialReportController.validateFinancialReport, financialReportController.downloadInvoicePdf);


router.get('/companies/:companyCode/:reportId/reopened-info/:reopenedId', ensureAuthenticated, companyController.validateMasterClient,
  financialReportController.validateFinancialReport, financialReportController.getFinancialReportReopenedInfo); // jquery / ajax

function ensureAuthenticated(req, res, next) {
  //only authenticated when the user is logged in and allowed to open the substance entry
  
  if (!req.user || req.session.id != req.user.sessionId) {
    req.logout(function (err) {
      if (err) { return next(err) }
      req.session.destroy(function () {
        // cannot access session here
        res.redirect("/");
      });
    });
  } else if ((req.user && req.session.id == req.user.sessionId) && !req.session.auth2fa) {
    if (req.user.secret_2fa) {
      res.redirect('/users/2fa-code');
    } else {
      res.redirect('/users/2fa-setup');
    }
  } else {    

      next();
  } 
}

module.exports = router;

