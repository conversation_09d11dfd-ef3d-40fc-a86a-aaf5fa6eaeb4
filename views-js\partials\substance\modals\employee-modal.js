let employeeId = '';
let isEditEmployee = false;
let resetEmployeeForm = false;

$(document).on('click', '.deleteEmployee', async function () {
    await deleteEmployee($(this).attr('data-id'),$(this).attr('data-activity-type'),$(this).attr('data-employee-id'))
})

$('#employeeModal').on('show.bs.modal', function (event) {
    $('#loadingEmployee').hide();
    const button = $(event.relatedTarget); // Button that triggered the modal
    const activityType = button.data('activity-type');
    const entityId = button.data('id');

    employeeId = button.data('employee-id');
    resetEmployeeForm = false;
    $("#activityType").val(activityType);

    if (employeeId) {
        isEditEmployee = true;

        const queryString = $.param({
            type: activityType
        });

        $.ajax({
            type: "GET",
            url: `/substance/entry/${entityId}/activity-business?${queryString}`,
            success: (response) => {
                if (response.status === 200) {
                    const employees = response.data?.employees || [];
                    if (employees.length > 0) {
                        const employee = employees.find((e) => e._id === employeeId);
                        if (employee) {
                            $("#employeeName").val(employee.name);
                            $("#employeeQualification").val(employee.qualification);
                            $("#yearsOfExperience").val(employee.experience_years);
                        }
                    }
                } else {
                    Swal.fire('Error', 'There was an error getting the information.', 'error').then(() => {
                        $('#employeeModal').modal('hide');
                    });
                }
            },
            error: (err) => {
                Swal.fire('Error', 'There was an error getting the information.', 'error').then(() => {
                    $('#employeeModal').modal('hide');
                });
            },
        });
    } else {
        isEditEmployee = false;
    }

    if (isEditEmployee) {
        $("#employeeModalLbl").html('Edit Employee');
    } else {
        $("#employeeModalLbl").html('New Employee');
    }
});

$('#employeeModal').on('hidden.bs.modal', function (event) {
    resetEmployeeForm = true;
    $("#submitEmployee").prop('disabled', false);
    $("#employeeForm")[0].reset();
    $("#yearsOfExperience").val('')
});

$('#employeeForm').on('submit', async function (event) {
    event.preventDefault();
    $("#submitEmployee").prop('disabled', true);

    if (!this.checkValidity()) {
        this.classList.add('was-validated');
        $("#submitEmployee").prop('disabled', false);
        return false;
    }

    $('#submitEmployee').hide();
    $('#loadingEmployee').show();

    const responseSave = await saveEmployee();

    $('#submitEmployee').show();
    $('#loadingEmployee').hide();

    if (responseSave) {
        refreshEmployeeTable(responseSave.entryId, responseSave.activityType);

        Swal.fire('Success', 'Employee saved successfully.', 'success').then(() => {
            $('#employeeModal').modal('hide');
        });
    }
    $("#submitEmployee").prop('disabled', false);
});

async function saveEmployee() {
    try {
        const employee = {
            type: $("#activityType").val(),
            name: $("#employeeName").val(),
            qualification: $("#employeeQualification").val(),
            experienceYears: $("#yearsOfExperience").val(),
        };
        
        const response = await $.ajax({
            type: isEditEmployee ? "PUT" : "POST",
            url: `./employees${isEditEmployee ? `/${employeeId}` : ''}`,
            data: JSON.stringify(employee),
            dataType: "json",
            contentType: "application/json; charset=utf-8"
        });

        if (response.status === 200) {
            return response
        } else {
            const errorMessage = response.message || 'There was an error saving the employee.';
            Swal.fire('Error', errorMessage, 'error');
            return null;
        }
    } catch (err) {
        Swal.fire('Error', 'There was an error saving the employee.', 'error');
        return null;
    }
}

async function deleteEmployee(entryId, activityType, employeeId) {
    const result = await Swal.fire({
        icon: 'warning',
        title: 'Are you sure?',
        showCancelButton: true,
        confirmButtonText: 'Yes, delete it',
        reverseButtons: true,
        showLoaderOnConfirm: true,
        allowOutsideClick: () => !Swal.isLoading(),
        backdrop: true,
        preConfirm: async () => {
            try {
                
                const response = await $.ajax({
                    type: 'DELETE',
                    url: `/substance/entry/${entryId}/employees/${activityType}/${employeeId}`,
                    dataType: 'json'
                });

                if (response.status === 200) {
                    return response;
                } else {
                    Swal.fire('Error', response.error ? response.error : 'Error deleting the Employee', 'error');
                    return null;
                }
            } catch (error) {
                Swal.fire('Error', 'An error occurred while deleting the Employee', 'error');
                return null;
            }
        }
    });

    if (result.isConfirmed && result.value) {
        refreshEmployeeTable(entryId, activityType);
        Swal.fire('Success', result.value?.message ? result.value.message : 'Employee deleted successfully.', 'success');
    }
}

function refreshEmployeeTable(entryId, activityType) {
    let template = Handlebars.templates.employees;
    const queryString = $.param({ type: activityType });

    $.ajax({
        type: "GET",
        url: `/substance/entry/${entryId}/activity-business?${queryString}`,
        success: (response) => {
            if (response.status === 200) {
                let rows = template({
                    employees: response.data?.employees || [],
                    entryId: entryId,
                    activityType: activityType
                });

                $("#employeesTableBody").html(rows);
            } else {
                toastr["error"]('There was an error getting the Employees.', 'Error');
            }
        },
        error: (err) => {
            toastr["error"]('There was an error getting the Employees.', 'Error');
        },
    });
}