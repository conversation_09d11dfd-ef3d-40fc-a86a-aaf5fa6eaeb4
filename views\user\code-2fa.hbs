<main class="">
    <div class="container">
        <div class="row">
            <div class="col-lg-2"></div>
            <div class="col-sm-12 col-lg-8">
                <div class='contour'>
                    <div class="header-title-container">
                        <h1>{{title}}</h1>
                    </div>
                    {{#if activated}}
                        <p class="alert alert-success">{{ message }}</p>
                        <p>Click <a href="/">here</a> to login to the portal</p>
                    {{else}}
                        
                        {{#if message }}
                            <p class="alert alert-danger">{{ message }}</p>
                        {{/if}}
                        <form class='enquiry' method="POST" action="/users/2fa-code" autocomplete="off">
                            <input type="text" hidden name="csrf-token" value="{{csrfToken}}">
                            <p>{{dialog}}</p>
                            <div class='row'>
                                <div class='col-lg-6' align='center'>
                                    <div class="input-group">
                                        <input id="code" name="code" type="text" class="form-control">
                                        <input type="text" name="method" class="hidden" value="{{method}}">
                                        <div class="input-group-append">
                                            <button id="submitButton" class="btn btn-primary waves-effect waves-light"
                                                type="submit">Validate</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <br>
                            {{#ifCond method '===' 'email'}}
                            <div class="row mt-2">
                                <div class="col-12">
                                    <span>If you haven't received the verification code, please check your inbox and ensure that you have
                                        correctly reviewed your spam folder. If you still haven't received the code, you can click on the resend
                                        button below.
                                    </span>
                                    <br>
                                    <button class="btn btn-primary mt-2" type="button" id="sendEmailCodeBtn">Resend code</button>
                                </div>

                            </div>
                            {{/ifCond}}

                        </form>
                    {{/if}}



                </div>
            </div>
        </div>
    </div>
</main>
<script type='text/javascript' src="/views-js/user/code-2fa.js"></script>