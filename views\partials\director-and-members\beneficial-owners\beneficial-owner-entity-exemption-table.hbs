<div class="table-responsive">
  <table id="exempt-entity-table" class="table table-sm table-striped mb-0">
    <thead>
      <tr>
        {{#ifEquals exemptionType "Listed Company"}}
        <th>Stock Exchange Name</th>
        <th>Ticker Symbol</th>
        <th>Jurisdiction of Stock Exchange</th>
        <th>Name of Stock Exchange Regulator</th>
        <th>Date of Listing</th>
        {{else ifEquals exemptionType "Subsidiaries"}}
        <th>Company/ Limited Partnership Name</th>
        <th>Company/ Limited Partnership No.</th>
        <th>Date of Incorporation/ Registration</th>
        {{else ifEquals exemptionType "Licensed Trustee"}}
        <th>Name of Licensed Trustee</th>
        <th>License No.</th>
        <th>License Type</th>
        <th>License Grant Date</th>
        {{else ifEquals exemptionType "Foreign Fund"}}
        <th>Entity Name</th>
        <th>Incorporation/ Registration/ Formation No.</th>
        <th>Date of Incorporation/ Registration/ Formation</th>
        <th>Place of Incorporation/ Registration/ Formation</th>
        <th>Address</th>
        <th>Equivalent Exemption Criteria</th>
        <th>Disclosure and Transparency Rules</th>
        <th>International Standards</th>
        {{else ifEquals exemptionType "Specified Fund"}}
        <th>License No.</th>
        <th>License Type</th>
        <th>License Grant Date</th>
        {{/ifEquals}}
        <th>Status</th>
        <th class="min-width-150">Is this information correct?</th>
        <th class="text-right"></th>
      </tr>
    </thead>
    <tbody>
      <tr>
        {{#ifEquals exemptionType "Listed Company"}}
        <td>{{exemptEntity.BOSTXRecognisedName}}</td>
        <td>{{exemptEntity.BOSTXTickerID}}</td>
        <td>{{exemptEntity.BOSTXJurisdiction}}</td>
        <td>{{exemptEntity.BOSTXRegulator}}</td>
        <td>{{formatDate exemptEntity.BOSTXListingDate "YYYY-MM-DD"}}</td>
        {{else ifEquals exemptionType "Subsidiaries"}}
        <td>{{exemptEntity.BOJointOwnerName}}</td>
        <td>{{exemptEntity.BOIncorpNr}}</td>
        <td>{{formatDate exemptEntity.BOIncorpDate "YYYY-MM-DD"}}</td>
        {{else ifEquals exemptionType "Licensed Trustee"}}
        <td>{{exemptEntity.BOJointOwnerName}}</td>
        <td>{{exemptEntity.BOFundLicenceNr}}</td>
        <td>{{exemptEntity.BOFundLicenceType}}</td>
        <td>{{formatDate exemptEntity.BOFundLicenceGrantDate "YYYY-MM-DD"}}</td>
        {{else ifEquals exemptionType "Foreign Fund"}}
        <td>{{exemptEntity.BOName}}</td>
        <td>{{exemptEntity.BOIncorpNr}}</td>
        <td>{{formatDate exemptEntity.BOIncorpDate "YYYY-MM-DD"}}</td>
        <td>{{exemptEntity.BOIncorpCountry}}</td>
        <td>{{exemptEntity.BOForeignFundAddress}}</td>
        <td>{{exemptEntity.BOForeignFundExemption}}</td>
        <td>{{exemptEntity.BOForeignFundDislosureRules}}</td>
        <td>{{exemptEntity.BOForeignFundIntlStd}}</td>
        {{else ifEquals exemptionType "Specified Fund"}}
        <td>{{exemptEntity.BOFundLicenceNr}}</td>
        <td>{{exemptEntity.BOFundLicenceType}}</td>
        <td>{{formatDate exemptEntity.BOFundLicenceGrantDate "YYYY-MM-DD"}}</td>
        {{/ifEquals}}
        <td>
          {{#if beneficialOwnersWithMissingValues}}
          MISSING INFORMATION
          {{else if exemptEntity.isConfirmed}}
          CONFIRMED
          {{else if exemptEntity.lastChange.status}}
          {{exemptEntity.lastChange.status}}
          {{else}}
          CONFIRMATION REQUIRED
          {{/if}}
        </td>
        <td>
          {{#unless beneficialOwnersWithMissingValues}}
          <div class="custom-control custom-radio custom-control-inline">
            <input type="radio" id="entity-info-yes-{{exemptEntity.BOCode}}"
              name="entity-info-correct-{{exemptEntity.BOCode}}" class="custom-control-input entity-radio" value="yes">
            <label class="custom-control-label" for="entity-info-yes-{{exemptEntity.BOCode}}">Yes</label>
          </div>
          <div class="custom-control custom-radio custom-control-inline">
            <input type="radio" id="entity-info-no-{{exemptEntity.BOCode}}"
              name="entity-info-correct-{{exemptEntity.BOCode}}" class="custom-control-input entity-radio" value="no">
            <label class="custom-control-label" for="entity-info-no-{{exemptEntity.BOCode}}">No</label>
          </div>
          {{/unless}}
        </td>
        <td class="text-right">
          <button type="button" class="btn solid royal-blue btn-sm hide-element confirm-entity"
            data-id="{{exemptEntity.BOCode}}">Confirm</button>
          <button type="button" class="btn solid royal-blue btn-sm hide-element request-entity-update"
            data-id="{{exemptEntity.BOCode}}"
            data-missing-values="{{#if beneficialOwnersWithMissingValues}}true{{else}}false{{/if}}"
            data-is-confirmed="{{#if exemptEntity.isConfirmed}}true{{else}}false{{/if}}">Request
            Update</button>
        </td>
      </tr>
    </tbody>
  </table>
</div>
