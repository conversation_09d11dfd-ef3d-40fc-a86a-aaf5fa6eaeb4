$('input[name="partOfGroupControl"]').change(function () {
    if ($(this).val() === 'Yes') {
        $('#structureRow').show(200).css("display", "flex");
    } else {
        $('#structureRow').hide(200);
    }
});
$('#entityTypeControl').change(function () {
    if ($(this).val() === 'Special Instructions') {
        $('#specialInstructionsRow').css('display', 'flex');
    } else {
        $('#specialInstructionsRow').hide(200);
    }
});

$('#enableNameChange').click(function () {
    enableNameChange()
})

function enableNameChange() {
    incorporationId = window.location.pathname.split('/')[4]
     Swal.fire(
            {
                title: "Are you sure you want to update the entity name?",
                text: "You will have to submit the name reservation again",
                icon: "question",
                showCancelButton: true,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                cancelButtonText: "Cancel",
                confirmButtonText: "Confirm"
            }).then(async function (t) {
                if (t.value) {
                    const form = $("#clientCorporationForm").serializeJSON();
                    form.additionalServices = [];
                    form.approvedName = false;
                    form.nameReservationStatus = 'NOT STARTED';
                    $('input[name="additionalServices"]:checked').each(function () {
                        form.additionalServices.push($(this).val());
                    });
                    
                    $.ajax({
                        type: "POST",
                        url: "./" + incorporationId,
                        data: JSON.stringify(form),
                        contentType: "application/json; charset=utf-8",
                        success: function (data) {
                            if (data.status === 200) {
                                location.reload();
                            } else {
                                toastr["warning"](data.message, 'Error!');
                            }
                        },
                        error: function (err) {
                            toastr["warning"]('Submission could not be saved, please try again later.', 'Error!');
                        }
                    });
                }
            });
}