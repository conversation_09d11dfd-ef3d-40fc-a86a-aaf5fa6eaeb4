<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <h4 class="mt-3">Master Client Code: {{masterclientcode}}</h4>
                    <br>
                    <h6>What would you like to do?</h6>
                    <div class="row mt-3">
                        {{#if hasSubstanceCompanies}}
                        <div class="col-md-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5 class="card-title text-white">Economic Substance</h5>
                                    <a href="/masterclients/{{masterclientcode}}/substance/companies" class="btn btn-light btn-sm waves-effect">File submission</a>
                                </div>
                            </div>
                        </div>
                        {{/if}}
                        
                        {{#if hasFinancialReport}}
                        <div class="col-md-4" >
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5 class="card-title text-white">Basic Financial Report</h5>
                                    <a href="/masterclients/{{masterclientcode}}/financial-reports" class="btn btn-light btn-sm waves-effect">Prepare basic financial report</a>
                                </div>
                            </div>
                        </div>
                        {{/if}}
                        <div class="col-md-4" hidden>
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5 class="card-title text-white">Submit Request</h5>
                                    <a href="/" class="btn btn-light btn-sm waves-effect">Submit request</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5 class="card-title text-white">Company Incorporation</h5>
                                    <a href="/masterclients/{{masterclientcode}}/incorporate-company" class="btn btn-light btn-sm waves-effect">Incorporate a Company</a>
                                </div>
                            </div>
                        </div>

                        {{#if hasDirectors}}
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-12">
                                                <h5 class="card-title text-white">Beneficial Owner</h5>
                                            </div>
                                        </div>
                                        <a href="/masterclients/{{masterclientcode}}/director-and-bo"
                                            class="btn btn-light btn-sm waves-effect">Manage Beneficial Owner
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {{/if}}
                        {{#if hasMemDirectors}}
                            <div class="col-md-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-10">
                                                <h5 class="card-title text-white">Company Information</h5>
                                            </div>
                                            {{#if hasUnconfirmedData}}
                                            <div class="col-2">
                                                <span class="btn-circle " data-count="&nbsp;">
                                                </span>
                                            </div>
                                            {{/if}}
                                        </div>
                                        <a href="/masterclients/{{masterclientcode}}/director-and-members"
                                            class="btn btn-light btn-sm waves-effect">Manage company information
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {{/if}}
                        {{#if pendingRequestFilesCount}}
                            <div class="col-md-4" >
                                <div class="card bg-primary text-white">
                                    <div class="card-body"  >
                                        <div class="row">
                                            <div class="col-10">
                                                <h5 class="card-title text-white">Requests for Information</h5>
                                            </div>
                                            <div class="col-2">
                                                <span class="btn-circle " data-count="{{pendingRequestFilesCount}}">
                                                </span>
                                            </div>
                                        </div>
                                        <a href="/masterclients/{{masterclientcode}}/company-files" class="btn btn-light btn-sm waves-effect">
                                            Update My File
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {{/if}}

                    </div>
                    <a href="/masterclients/" class="btn btn-secondary waves-effect waves-light width-xl">Back</a>
                </div>
            </div>
        </div>
    </div>
</main>
