$(document).ready(function () {
  // Initially hide both buttons until user makes a selection for all questions
  $('#confirmBtn').hide();
  $('#requestUpdateBtn').hide();
  $('#is-correct-row').hide();


  // Wire up radio changes
  $('input[type="radio"]').on('change', handleInputChanges);

  // Prefill visibility based on flags
  const hasMissingValues = $('#requestUpdateBtn').attr('data-missing-values') === 'true';
  const isConfirmed = $('#requestUpdateBtn').attr('data-is-confirmed') === 'true';

  if (!hasMissingValues && !isConfirmed) {
    $('#is-correct-row').show();
  }
  if (hasMissingValues || isConfirmed) {
    $('#requestUpdateBtn').show();
  }

  // Buttons
  $('#confirmBtn').on('click', async function () {
    await confirmInformation($(this).attr('data-id'));
  });

  $('#requestUpdateBtn').on('click', async function () {
    await requestUpdate($(this).attr('data-id'), $(this).attr('data-missing-values'));
  });

  $('.showLastChange').on('click', function () {
    showLastChange($(this).attr('data-type'), $(this).attr('data-reason'));
  });

});

function handleInputChanges() {
  const infoCorrect = $('input[name="information-correct"]:checked').val();
  const $confirm = $('#confirmBtn');
  const $request = $('#requestUpdateBtn');
  const hasMissingValues = $request.attr('data-missing-values') === 'true';


  $('#is-correct-row').show();
  $confirm.hide();
  $request.hide();

  if (hasMissingValues) {
    $request.show();
    return;
  }

  if (infoCorrect === 'yes') {
    $confirm.show();
    $request.hide();
  } else if (infoCorrect === 'no') {
    $confirm.hide();
    $request.show();
  }

}

function showLastChange(type, reason) {
  let template = Handlebars.templates.requestupdatelog;
  let d = {
    changeType: type,
    changeReason: reason
  };
  let html = template(d);


  Swal.fire({
    title: "Last request for update",
    icon: "info",
    html: html
  })

}

// Extract masterClientCode and companyCode from URL path
function extractCodes() {
  const urlParts = window.location.pathname.split('/');
  return {
    masterClientCode: urlParts[2],
    companyCode: urlParts[4]
  };
}

async function requestUpdate(id, missingValues) {
  let template = Handlebars.templates.requestupdatepopup;
  let d = {
    type: "beneficial owner",
    hasMissingData: (missingValues === "true")
  };
  let html = template(d);


  const { masterClientCode, companyCode } = extractCodes();
  const requestUrl = `/masterclients/${masterClientCode}/director-and-members/${companyCode}/beneficial-owners/${id}/request-update`;
  const redirectUrl = `/masterclients/${masterClientCode}/director-and-members/${companyCode}/beneficial-owners`;

  Swal.fire(
    {
      title: "Are you sure you want to request an update?",
      icon: "warning",
      html: html,
      showCancelButton: !0,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Submit",
      focusConfirm: false,
      showLoaderOnConfirm: true,
      reverseButtons: true,
      preConfirm: async () => {
        const changeType = $('#changeType').val();
        const changeReason = $('#changeReason').val();
        if (!changeType || changeType === "") {
          Swal.showValidationMessage('Please select an option')
        }
        return axios.post(requestUrl,
          JSON.stringify({
            changeType: changeType,
            changeReason: changeReason,
          }),
          {
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
            },
          }
        ).then(response => {
          try {
            return response.data
          } catch (e) {
            throw new Error(response.statusText)
          }
        }).catch(error => {
          if (error?.response?.data) {
            return error.response.data
          }
          return { status: error.status || 500, error: error }

        });

      },
      allowOutsideClick: () => !Swal.isLoading()
    }).then(function (result) {
      if (result.isConfirmed) {
        swal.showLoading();
        if (result.value.status === 200) {
          Swal.fire({
            title: 'Success',
            html: 'We have received your request to update the beneficial owner information. A Trident Trust Representative will be in touch shortly.',
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#3085d6'
          }).then(() => {
            window.location.href = redirectUrl;
          });
        } else if (result.value.status === 400 || result.value.status === 404) {
          Swal.fire('Error', result.value.error, 'error');
        } else {
          Swal.fire('Error', 'There was an error generating the request', 'error');
        }
      }
    })

}

async function confirmInformation(id) {
  const { masterClientCode, companyCode } = extractCodes();
  const confirmUrl = `/masterclients/${masterClientCode}/director-and-members/${companyCode} /beneficial-owners/${id}/confirm`;
  const redirectUrl = `/masterclients/${masterClientCode}/director-and-members/${companyCode}/beneficial-owners`;


  Swal.fire({
    title: '<h4><strong>Confirm</strong></h4>',
    html: 'Are you sure you want to confirm the information? <br><br> By confirming the information, please note that you have consented to us processing your data for the BVI Registry pursuant to the law requirements.',
    icon: "info",
    iconColor: '#e6b800',
    customClass: {
      icon: 'swal-icon-small',
      padding: '20px',
    },
    showCancelButton: true,
    confirmButtonText: 'Submit',
    confirmButtonColor: "#3085d6",
    cancelButtonColor: "#d33",
    focusConfirm: false,
    showLoaderOnConfirm: true,
    reverseButtons: true,
    preConfirm: async () => {
      return axios.post(confirmUrl,
        {},
        {
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        }).then(response => {
          try {
            return response.data
          } catch (e) {
            throw new Error(response.statusText)
          }
        }).catch(error => {
          if (error?.response?.data) {
            return error.response.data
          }
          return { status: error.status || 500, error: error }

        });
    },
    allowOutsideClick: () => !Swal.isLoading()
  }).then(function (result) {
    if (result.isConfirmed) {
      swal.showLoading();
      if (result.value.status === 200) {
        Swal.fire('Success', result.value.message, 'success').then(() => {
          window.location.href = redirectUrl;
        });
      } else if (result.value.status === 400 || result.value.status === 404) {
        Swal.fire('Error', result.value.error, 'error');
      } else {
        Swal.fire('Error', 'There was an error with the confirmation request', 'error');
      }
    }
  })

}
