<div class="col-lg-12">
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-12 hide-element" id="blacklistedCountryRowStep5">
                    <div class="alert alert-warning" role="alert">
                        <i class="fa fa-warning mr-2"></i><b>This country is blacklisted
                            and cannot be accepted.</b> Please contact us at <a
                            href="mailto:<EMAIL>"><EMAIL></a>
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <h4>Record Keeping Details</h4>
                <!-- RECORD HOLDER AND PRIMARY ADDRESS -->
                <div class="row mt-2">
                    <div class="col-2 py-1">
                        <label for="records-holder" data-toggle="tooltip" data-placement="right" title="Name of the person keeping the records of the company including the accounting records
                                            ">Record Holder*</label>
                    </div>
                    <div class="col-4 py-1">
                        <input id="records-holder" name="records[recordHolder]" type="text" class="form-control"
                            required value="{{incorporation.records.recordHolder}}"/>
                    </div>

                    <div class="col-2 py-1">
                        <label for="records-primary-address">Address - 1st Line*</label>
                    </div>
                    <div class="col-4 py-1">
                        <input id="records-primary-address" name="records[primaryAddress]" type="text"
                            class="form-control" value="{{incorporation.records.primaryAddress}}" required/>
                    </div>
                </div>
                <!-- SECONDARY ADDRESS AND EMAIL -->
                <div class="row mt-2">
                    <div class="col-2 py-1">
                        <label for="records-secondary-address">Address - 2nd Line</label>
                    </div>
                    <div class="col-4 py-1">
                        <input id="records-secondary-address" name="records[secondaryAddress]" type="text"
                            class="form-control" value="{{incorporation.records.secondaryAddress}}"/>
                    </div>
                    <div class="col-2 py-1">
                        <label for="records-email">E-mail*</label>
                    </div>
                    <div class="col-4 py-1">
                        <input id="records-email" name="records[email]" type="text" class="form-control"
                            value="{{incorporation.records.email}}"required />
                    </div>
                </div>
                <!-- STATE AND CITY -->
                <div class="row mt-2">
                    <div class="col-2 py-1">
                        <label for="records-state">State</label>
                    </div>
                    <div class="col-4 py-1">
                        <input id="records-state" name="records[state]" type="text" class="form-control"
                            value="{{incorporation.records.state}}"/>
                    </div>

                    <div class="col-2 py-1">
                        <label for="records-city">City*</label>
                    </div>
                    <div class="col-4 py-1">
                        <input id="records-city" name="records[city]" type="text" class="form-control"
                            value="{{incorporation.records.city}}" required/>
                    </div>
                </div>
                <!-- POSTAL CODE AND COUNTRY OF OPERATION -->
                <div class="row mt-2">
                    <div class="col-2 py-1">
                        <label for="records-postal-code">Postal Code*</label>
                    </div>
                    <div class="col-4 py-1">
                        <input id="records-postal-code" name="records[postalCode]" type="text" class="form-control"
                            value="{{incorporation.records.postalCode}}" required/>
                    </div>

                    <div class="col-2">
                        <label for="records[operationCountry]">Country of Operation*</label>
                    </div>
                    <div class="col-4">
                        {{>file-reviewer/shared/select-country selectId="records[operationCountry]" required="true" value=incorporation.records.operationCountry}}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="progress">
                        <div class="progress-bar width-62" role="progressbar" aria-valuenow="5"
                            aria-valuemin="0" aria-valuemax="8">5 of 8
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>