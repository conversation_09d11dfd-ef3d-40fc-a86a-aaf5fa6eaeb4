(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['outsourcingproviders'] = template({"1":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, alias5=container.lambda, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <tr id=\"outsourcing-providers-table-row-"
    + alias4(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":2,"column":44},"end":{"line":2,"column":51}}}) : helper)))
    + "\" class=\"outsourcing-row\">\r\n        <td> "
    + alias4(((helper = (helper = lookupProperty(helpers,"entity_name") || (depth0 != null ? lookupProperty(depth0,"entity_name") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"entity_name","hash":{},"data":data,"loc":{"start":{"line":3,"column":13},"end":{"line":3,"column":28}}}) : helper)))
    + " </td>\r\n        <td class=\"text-truncate header-max-width-150\" > "
    + alias4(((helper = (helper = lookupProperty(helpers,"resource_details") || (depth0 != null ? lookupProperty(depth0,"resource_details") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"resource_details","hash":{},"data":data,"loc":{"start":{"line":4,"column":57},"end":{"line":4,"column":77}}}) : helper)))
    + " </td>\r\n        <td> "
    + alias4(((helper = (helper = lookupProperty(helpers,"staff_count") || (depth0 != null ? lookupProperty(depth0,"staff_count") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"staff_count","hash":{},"data":data,"loc":{"start":{"line":5,"column":13},"end":{"line":5,"column":28}}}) : helper)))
    + " </td>\r\n        <td> "
    + alias4(((helper = (helper = lookupProperty(helpers,"hours_per_month") || (depth0 != null ? lookupProperty(depth0,"hours_per_month") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"hours_per_month","hash":{},"data":data,"loc":{"start":{"line":6,"column":13},"end":{"line":6,"column":32}}}) : helper)))
    + " </td>\r\n        <td>"
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,(depth0 != null ? lookupProperty(depth0,"monitoring_control") : depth0),{"name":"if","hash":{},"fn":container.program(2, data, 0, blockParams, depths),"inverse":container.program(4, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":7,"column":12},"end":{"line":7,"column":62}}})) != null ? stack1 : "")
    + "</td>\r\n        <td class=\"justify-content-center d-flex d-flex-inline\">\r\n            <button type=\"button\" class=\"btn btn-sm royal-blue solid mr-1\" data-activity-type=\""
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"activityType") : depths[1]), depth0))
    + "\"\r\n                data-id=\""
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"entryId") : depths[1]), depth0))
    + "\" data-provider-id=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":10,"column":59},"end":{"line":10,"column":66}}}) : helper)))
    + "\" data-toggle=\"modal\"\r\n                data-target=\"#outsourcingProviderModal\">\r\n                <i class=\"fa fa-pencil\"></i>\r\n            </button>\r\n            <button type=\"button\" class=\"btn btn-sm btn-danger deleteOutsourcingProvider\"\r\n                    data-activity-type=\""
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"activityType") : depths[1]), depth0))
    + "\"\r\n                    data-entry-id=\""
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"entryId") : depths[1]), depth0))
    + "\" data-id=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":16,"column":60},"end":{"line":16,"column":67}}}) : helper)))
    + "\">\r\n                <i class=\"fa fa-times\"></i>\r\n            </button>\r\n        </td>\r\n    </tr>\r\n";
},"2":function(container,depth0,helpers,partials,data) {
    return " Yes ";
},"4":function(container,depth0,helpers,partials,data) {
    return " No ";
},"6":function(container,depth0,helpers,partials,data) {
    return "    <tr>\r\n        <td colspan=\"6\">\r\n            No outsourcing providers found\r\n        </td>\r\n    </tr>\r\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"outsourcingProviders") : depth0),{"name":"each","hash":{},"fn":container.program(1, data, 0, blockParams, depths),"inverse":container.program(6, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":1,"column":0},"end":{"line":27,"column":9}}})) != null ? stack1 : "")
    + "\r\n\r\n";
},"useData":true,"useDepths":true});
})();