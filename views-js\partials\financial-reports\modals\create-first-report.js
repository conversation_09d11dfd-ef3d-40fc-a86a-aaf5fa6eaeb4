$('#firstFinancialReportPeriodModal').on('show.bs.modal', function (event) {
    $("input[type=date]").flatpickr({
        dateFormat: "Y-m-d",
    });
    const incorporationDate = moment($('#companyIncorporationDate').val())
    if(incorporationDate.isSameOrAfter('2023-01-01')) {
        let incorMaxDate = incorporationDate.add(18, 'months')
        incorMaxDate = incorMaxDate.subtract(1, "days").format('YYYY-MM-DD')
        $('#financialPeriodStart').val($('#companyIncorporationDate').val())
        $('#financialPeriodStart').prop('disabled', true)
        $("#financialPeriodEnd").flatpickr({
            dateFormat: "Y-m-d",
            minDate: incorporationDate.format('YYYY-MM-DD'),
            maxDate: incorMaxDate
        });
    } else {
        $('#financialPeriodEnd').prop('disabled', true)
        $("#financialPeriodStart").flatpickr({
            dateFormat: "Y-m-d",
            minDate: moment('2023-01-01').format('YYYY-MM-DD'),
            maxDate: moment('2023-12-31').format('YYYY-MM-DD'),
            defaultDate: "2023-01-01",
            onReady: function(selectedDates, dateStr, instance) {
                instance.clear(); // Clears the default date 
            }
        });
    }

});


$('#firstFinancialReportPeriodModal').on('hidden.bs.modal', function (event) {
    $("#submitFirstFinancialReport").prop('disabled', false);
    $("#firstFinancialReportForm")[0].reset();
    $(".new-financial-report").prop('disabled', false);
});

$("#financialPeriodStart").on('change', function(){
    const val = $(this).val();

    const newEndPeriod = moment(val).add(1, 'years').subtract(1,'days').format('YYYY-MM-DD');

    $("#financialPeriodEnd").val(newEndPeriod).trigger('change');
})



$('#firstFinancialReportForm').on('submit', async function (event) {
    console.log('hola')
    event.preventDefault();
    $("#submitFirstFinancialReport").prop('disabled', true);

    if (!this.checkValidity()) {
        this.classList.add('was-validated');
        $("#submitFirstFinancialReport").prop('disabled', false);
        return false;
    }

    $('#submitFirstFinancialReport').hide();
    $('#loadingFirstFinancialReport').show();

    const mcc = $("#newFinancialReportMCC").val();
    const company = $("#newFinancialReportCode").val();
    const data = {
        financialPeriodStart: $("#financialPeriodStart").val(),
        financialPeriodEnd: $("#financialPeriodEnd").val(),
        isFirstReport: true,
    };
    const responseSave = await createNewFinancialReport(mcc, company, data);

    $('#submitFirstFinancialReport').show();
    $('#loadingFirstFinancialReport').hide();

    if (responseSave && responseSave.reportId) {
       
        Swal.fire('Success', responseSave.message, 'success').then(() => {
            document.location = document.location.href;
        });
    }else{
        $("#submitFirstFinancialReport").prop('disabled', false);
    }
   
});

$(document).ready(function () {
    $('#loadingFirstFinancialReport').hide()
})
