<div id="organizationForm">
    <!-- CORPORATE DETAILS -->
    <div id="corporateDetails">
        <h4>Details</h4>
        <div class="row mt-2">
            <div class="col-6 d-flex justify-content-between">
                <label>Is this company related to a TridentTrust client?*</label>
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" class="custom-control-input" id="corporate-is-already-client-yes"
                           name="corporate[isTridentClient]"  required {{#if relation.details.isTridentClient }}
                           checked {{/if}} value="YES"/>
                    <label class="custom-control-label" for="corporate-is-already-client-yes">Yes</label>
                </div>
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" class="custom-control-input" id="corporate-is-already-client-no"
                           name="corporate[isTridentClient]" {{#unless relation.details.isTridentClient }}
                           checked {{/unless}} value="NO"/>
                    <label class="custom-control-label" for="corporate-is-already-client-no">No</label>
                </div>
            </div>
            <div class="col-6">
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-2">
                <label for="corporate-details-organization-name">Company Name*</label>
            </div>
            <div class="col-4">
                <input type="text" class="form-control" id="corporate-details-organization-name"
                       name="details[organizationName]"
                       value="{{relation.details.organizationName}}" required/>
            </div>
            <div class="col-2">
                <label for="corporate-details-incorporation-number">Incorporation / Formation Number*</label>
            </div>
            <div class="col-4">
                <input id="corporate-details-incorporation-number" class="form-control" type="text"
                       name="details[incorporationNumber]" required
                       value="{{relation.details.incorporationNumber}}"/>
            </div>
        </div>


        <div class="row mt-2 isTridentClient {{#if relation.details.isTridentClient}}
             hide-element {{/if}}">
            <div class="col-2">
                <label for="corporate-details-tax-residence">Tax Residence*</label>
            </div>
            <div class="col-4">
                {{>file-reviewer/shared/select-country selectId="details[taxResidence]" group="corporate" required="true"
                        value=relation.details.taxResidence}}
            </div>
            <div class="col-2">
                <label for="corporate-details-registration-number"
                >Business Registration Number (if applicable)*</label
                >
            </div>
            <div class="col-4">
                <input class="form-control" type="text" id="corporate-details-registration-number"
                       name="details[businessNumber]" required
                       value="{{relation.details.businessNumber}}"/>
            </div>
        </div>

        <div class="row mt-2 isTridentClient {{#if relation.details.isTridentClient}}
             hide-element {{/if}}">
            <div class="col-2">
                <label for="corporate-details-incorporation-date">Date of Incorporation*</label>
            </div>
            <div class="col-4">
                <input class="form-control datepicker" type="date" id="corporate-details-incorporation-date" placeholder="mm/dd/yyyy"
                       name="details[incorporationDate]" required
                       value="{{#formatDate relation.details.incorporationDate "YYYY-MM-DD"}} {{/formatDate }}"/>
            </div>
            <div class="col-2">
                <label for="details[incorporationCountry]">Country of Incorporation*</label>
            </div>
            <div class="col-4">
                {{>file-reviewer/shared/select-country selectId="details[incorporationCountry]" group="corporate" required="true"
                        value=relation.details.incorporationCountry}}
            </div>
        </div>

        <div class="isTridentClient {{#if relation.details.isTridentClient}}
             hide-element {{/if}}">
            <!-- DETAILS TABLE -->
            {{>file-reviewer/shared/relation-file-table tableId="detailsTable"  name="details" group="corporate"
                    files=(ternary newRelation relation.corporateFiles.details relation.details.files) relationId=relation._id}}

            <!-- DETAILS PARTNER TABLE -->
            {{>file-reviewer/shared/certificate-partner-table group="corporate"
                    partnerFiles=(ternary newRelation relation.corporateFiles.detailsPartner relation.detailsPartner.files)
                    relationId=relation._id}}
        </div>

    </div>

    <div class="isTridentClient {{#if relation.details.isTridentClient}}
         hide-element {{/if}}">
        <hr class="mt-2"/>
        <!-- PRINCIPAL ADDRESS DETAILS -->
        <div id="principalAddressDetails">
            <h4>Principal Address</h4>
            {{>file-reviewer/relations/sections/address-details-form group="corporate"
                    principalAddress=relation.principalAddress formType="principalAddress"}}
        </div>
        <hr class="mt-2"/>

        <div class="row mt-2">
            <div class="col-6">
                <h4>Is mailing address the same as Principal address?* {{relation.isSamePrincipalAddress}}</h4>
            </div>
            <div class="col-6 d-flex justify-content-end">
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" class="custom-control-input" id="corporate-is-same-address-yes"
                           name="corporate[isSamePrincipalAddress]" value="YES" required {{#if relation.isSamePrincipalAddress }} checked {{/if}} >
                    <label class="custom-control-label" for="corporate-is-same-address-yes" >Yes</label>
                </div>
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" class="custom-control-input" id="corporate-is-same-address-no"
                           name="corporate[isSamePrincipalAddress]" {{#unless relation.isSamePrincipalAddress }}  checked {{/unless}}
                           value="NO">
                    <label class="custom-control-label" for="corporate-is-same-address-no">No</label>
                </div>
            </div>
        </div>
        <br>

        <!-- MAILING ADDRESS DETAILS -->
        <div id="mailingAddressDetails  {{#if relation.isSamePrincipalAddress}} hide-element {{/if}}">
            <h4>Mailing Address</h4>
            {{>file-reviewer/relations/sections/address-details-form group="corporate"
                    principalAddress=relation.mailingAddress formType="mailingAddress"}}
        </div>
        <hr class="mt-2"/>

        <!-- LISTED COMPANY DETAILS -->
        <div class="listedCompanyDetails">
            {{>file-reviewer/relations/sections/listed-company-details-form group="corporate"}}
        </div>
        <hr class="mt-2"/>

        <!-- LIMITED COMPANY DETAILS -->
        <div class="limitedCompanyDetails">
            {{>file-reviewer/relations/sections/limited-company-details-form group="corporate"}}
        </div>
        <hr class="mt-2"/>

        <!-- MUTUAL FUND DETAILS -->
        <div class="mutualFundDetails">
            {{>file-reviewer/relations/sections/mutual-fund-details-form group="corporate"
                    mutualFund=(ternary newRelation relation.corporateFiles.mutualFund relation.mutualFundDetails)}}
        </div>
    </div>


</div>

<script type="text/javascript" src="/views-js/partials/file-reviewer/relations/corporate-form-component.js"></script>
