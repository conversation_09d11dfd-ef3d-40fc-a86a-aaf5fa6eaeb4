<div class="col-lg-12">
    <div class="card">
        <div class="card-body">

            <div class="row">
                <div class="col-md-12">
                    <h4>
                        <label class="mb-2 lbl-read-only color-black cursor-default" for="authShares">
                            4. Liabilities:
                        </label>
                    </h4>
                </div>
            </div>
            <!--4.1-->
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group">
                        <label class="lbl-read-only">4.1. Balances at the beginning of the financial period</label>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group ml-3">
                        <label class="lbl-read-only">4.1.1. Is this the Company's first year of operation?</label>
                    </div>
                </div>
                <div class="col-md-4 text-right">
                    <div class="custom-control custom-radio custom-control-inline">
                        <input type="radio" class="custom-control-input" id="companyFirstOperationLiabilityYes" disabled
                               name="companyFirstOperationLiability" {{#ifEquals report.reportDetails.isFirstYearOperation true}} checked {{/ifEquals}}  value="YES">
                        <label class="custom-control-label" for="companyFirstOperationLiabilityYes">Yes</label>
                    </div>
                    <div class="custom-control custom-radio custom-control-inline">
                        <input type="radio" class="custom-control-input" id="companyFirstOperationLiabilityNo" disabled
                               name="companyFirstOperationLiability"  {{#ifEquals report.reportDetails.isFirstYearOperation false}} checked {{/ifEquals}}  value="NO">
                        <label class="custom-control-label" for="companyFirstOperationLiabilityNo">No</label>
                    </div>
                </div>
            </div>

            <!--companyFirstOperation IF YES-->
            <div id="companyFirstOperationYesRows" {{#ifCond report.reportDetails.isFirstYearOperation '!==' false}} class="hide-element" {{/ifCond}} >
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group ml-3">
                            <label class="lbl-read-only">4.1.2. Please indicate the balances of the liabilities as of the beginning of
                                the financial period:</label>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group ml-5">
                            <label class="lbl-read-only">4.1.2.1. Accounts payable</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text prepend-currency"> {{report.currency}}</span>
                            </div>
                            <input type="text" id="accountsPayable" name="accountsPayable"  class="liabilityInput form-control autonumber-pos text-right cashPaidExp"
                                value="{{report.liabilities.accountsPayable}}" 
                                data-a-sep=",">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group ml-5">
                            <label class="lbl-read-only">4.1.2.2. Long-term debts</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text prepend-currency"> {{report.currency}}</span>
                            </div>
                            <input type="text" id="longTermDebts" name="longTermDebts" class="liabilityInput form-control autonumber-pos text-right liabilities-long-term"
                                value="{{report.liabilities.longTermDebts}}"
                                data-a-sep=",">
                        </div>
                    </div>
                </div>

                
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group ml-5">
                            <label class="lbl-read-only" for="valueOfOtherLiabilitiesStartPeriod">4.1.2.3. Other Liabilities</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text prepend-currency"> {{report.currency}}</span>
                            </div>
                            <input type="text" id="valueOfOtherLiabilitiesStartPeriod"  name="valueOfOtherLiabilitiesStartPeriod" class="liabilityInput form-control autonumber-pos text-right cashPaidExp"
                                value="{{report.liabilities.valueOfOtherLiabilitiesStartPeriod}}"
                                data-a-sep=",">
                        </div>
                    </div>
                </div>

                <div class="row hidden">
                    <div class="col-md-8">
                    </div>
                    <div class="col-md-4">
                        <div class="form-group input-group">
                            <button type="button" class="btn solid royal-blue cc addOtherLiabilityBtn" data-type="LiabilityStartPeriod">
                                <i class="fa fa-plus pr-2"></i>Add New Liability
                            </button>
                        </div>
                    </div>
                </div>

                <div id="LiabilityStartPeriodTableContainer" class="table-responsive pl-5 {{#ifCond report.liabilities.otherLiabilitiesStartPeriod.length '>' 0}} d-block {{else}} hidden{{/ifCond}}">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th class="header-30-percent">Description</th>
                                <th class="header-30-percent">Value</th>
                                <th class="header-20-percent"></th>
                                <th class="header-20-percent"></th>
                            </tr>
                        </thead>
                        <tbody id="LiabilityStartPeriodTable">
                            {{#each report.liabilities.otherLiabilitiesStartPeriod}}
                                <tr id="liability-table-row-{{_id}}-LiabilityStartPeriod" class="liability-row">
                                    <td>{{description}}</td>
                                    <td>{{decimalValue value}}</td>
                                    <td class="text-right">
                                        <button type="button" class="btn btn-outline-secondary openEditLiability"
                                                data-id="{{_id}}" data-type="LiabilityStartPeriod"> 
                                            <i class="fa fa-pencil"></i>
                                        </button>
                                    </td>
                                    <td class="text-left">
                                        <button type="button" class="delete btn btn-danger deleteLiability"
                                                data-id="{{_id}}" data-type="LiabilityStartPeriod">
                                            <i class="fa fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            {{/each}}
                        </tbody>
                    </table>
                </div>
            </div>

            <!--4.2-->
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group ">
                        <label class="lbl-read-only">4.2. Accounts Payable</label>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="form-group ml-3">
                        <label class="lbl-read-only">4.2.1. Did the Company have any accounts payable at the end of the financial period?</label>
                    </div>
                </div>
                <div class="col-md-4 text-right">
                    <div class="custom-control custom-radio custom-control-inline">
                        <input type="radio" class="custom-control-input" id="companyAccPayableYes"
                               name="anyCompanyAccPayable"   value="YES" {{#ifEquals report.liabilities.anyCompanyAccPayable true}} checked {{/ifEquals}} >
                        <label class="custom-control-label" for="companyAccPayableYes">Yes</label>
                    </div>
                    <div class="custom-control custom-radio custom-control-inline">
                        <input type="radio" class="custom-control-input" id="companyAccPayableNo"
                               name="anyCompanyAccPayable"   value="NO" {{#ifEquals report.liabilities.anyCompanyAccPayable false}} checked {{/ifEquals}} >
                        <label class="custom-control-label" for="companyAccPayableNo">No</label>
                    </div>
                </div>
            </div>

            <!-- if yes 4.2.1 -->
            <div id="haveAccountsPayableYesRows" {{#ifCond report.liabilities.anyCompanyAccPayable '!==' true}} class="hide-element"  {{/ifCond}}>
                <div class="row">
                                    <div class="col-md-8">
                            <div class="form-group ml-5">
                                <label class="lbl-read-only">4.2.1.1. Please indicate the total accounts payable balance at the end of the
                                    financial period:</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text prepend-currency"> {{report.currency}}</span>
                                </div>
                                <input type="text" id="accountsPayableBalance" name="accountsPayableBalance" 
                                    class="liabilityInput form-control autonumber text-right cashPaidExp"
                                    value="{{report.liabilities.accountsPayableBalance}}"
                                    data-a-sep=",">
                            </div>
                        </div>
                </div>
            </div>
            

                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group ml-3">
                            <label class="lbl-read-only">4.2.2. Please indicate the total amount of invoices issued to the Company for
                                services received or goods acquired in the <strong>current</strong> financial period:</label>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group ml-5">
                            <label class="lbl-read-only">4.2.2.1. Company Administration fees</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text prepend-currency"> {{report.currency}}</span>
                            </div>
                            <input type="text" id="liabilitiesAdminFees" name="liabilitiesAdminFees" 
                                class="liabilityInput form-control autonumber text-right cashPaidExp"
                                value="{{report.liabilities.compAdminFees}}"
                                data-a-sep=",">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group ml-5">
                            <label class="lbl-read-only">4.2.2.2. Other Expenses</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text prepend-currency"> {{report.currency}}</span>
                            </div>
                            <input type="text" id="liabilitiesOtherExpenses" name="liabilitiesOtherExpenses" 
                                value="{{report.liabilities.otherExpenses}}"
                                class="liabilityInput form-control autonumber text-right cashPaidExp" data-a-sep=","
                                
                            >
                        </div>
                    </div>
                </div>

                 <div class="row">
                    <div class="col-md-8">
                        <div class="form-group ml-5">
                            <label class="lbl-read-only" for="liabilitiesCostOfGoods">4.2.2.3. Cost of Goods / Services Acquired</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text prepend-currency"> {{report.currency}}</span>
                            </div>
                            <input type="text" id="liabilitiesCostOfGoods" name="liabilitiesCostOfGoods" 
                                value="{{report.liabilities.costOfGoods}}"
                                class="form-control autonumber text-right cashPaidExp" data-a-sep=","
                                
                            >
                        </div>
                    </div>
                </div>
            <!--4.3-->
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group">
                        <label class="lbl-read-only">4.3. Long-term debts</label>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="form-group ml-3">
                        <label class="lbl-read-only">4.3.1. Did the Company owe any Long-term debts at the <strong>end</strong> of the financial period?</label>
                    </div>
                </div>
                <div class="col-md-4 text-right">
                    <div class="custom-control custom-radio custom-control-inline">
                        <input type="radio" class="custom-control-input" id="LongTermDebtsYes"
                               name="didCompanyOweLongTermDebts"   value="YES"  {{#ifEquals report.liabilities.didCompanyOweLongTermDebts true}} checked {{/ifEquals}}>
                        <label class="custom-control-label" for="LongTermDebtsYes">Yes</label>
                    </div>
                    <div class="custom-control custom-radio custom-control-inline">
                        <input type="radio" class="custom-control-input" id="LongTermDebtsNo"
                               name="didCompanyOweLongTermDebts"   value="NO"  {{#ifEquals report.liabilities.didCompanyOweLongTermDebts false}} checked {{/ifEquals}}>
                        <label class="custom-control-label" for="LongTermDebtsNo">No</label>
                    </div>
                </div>
            </div>

            <!-- if no 4.3.1-->

            <div id="companyOwnLongTermNoRows" class="row {{#ifEquals report.liabilities.didCompanyOweLongTermDebts  false }} d-flex {{else}} hide-element  {{/ifEquals}} ">

                <div class="col-md-8">
                    <div class="form-group ml-3">
                        <label class="lbl-read-only">4.3.2. Please indicate the total amount of loan interest expense accrued for
                            the period</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text prepend-currency"> {{report.currency}}</span>
                        </div>
                        <input type="text" id="loanInterestAccrued" name="loanInterestAccrued" class="liabilityInput form-control autonumber text-right liabilities-long-term"
                            value="{{report.liabilities.loanInterestAccrued}}"
                            data-a-sep=",">
                    </div>
                </div>
            </div>


            <!-- if yes 4.3.1-->
            <div id="companyOwnLongTermYesRows"
                    {{#ifEquals report.liabilities.didCompanyOweLongTermDebts  true }} class="d-block" {{else}}class="hide-element"  {{/ifEquals}} >

                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group ml-3">
                            <label class="lbl-read-only">4.3.3. Please indicate if any Long-term debts amounts were received by the
                                shareholder or another party on behalf of the Company</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text prepend-currency"> {{report.currency}}</span>
                            </div>
                            <input type="text" id="ltDebtsReceivedByShareholder" name="ltDebtsReceivedByShareholder" class="liabilityInput form-control autonumber text-right liabilities-long-term"
                                value="{{report.liabilities.ltDebtsReceived}}"
                                data-a-sep=",">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group ml-3">
                            <label class="lbl-read-only">4.3.4. Please indicate if the shareholder or another party made repayments on
                                behalf of the Company in relation to the Long-term debts</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text prepend-currency"> {{report.currency}}</span>
                            </div>
                            <input type="text" id="ltDebtsRepayments" name="ltDebtsRepayments" class="liabilityInput form-control autonumber-neg text-right liabilities-long-term"
                                value="{{report.liabilities.ltDebtsRepayments}}"
                                data-a-sep=",">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group ml-3">
                            <label class="lbl-read-only">4.3.5. Closing balance of Long-term debts</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text prepend-currency"> {{report.currency}}</span>
                            </div>
                            <input type="text" id="ltDebtsClosingBalance" name="ltDebtsClosingBalance" class="liabilityInput form-control autonumber text-right cashPaidExp" readonly
                                value="{{report.liabilities.ltDebtsClosingBalance}}"
                                data-a-sep=",">
                        </div>
                    </div>
                </div>
            </div>

            <!--4.4-->
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group ">
                        <label class="lbl-read-only">4.4. Other Liabilities</label>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-8">
                    <div class="form-group ml-3">
                        <label class="lbl-read-only">4.4.1. Did the company owe any Other liabilities at the
                            end of the financial period?</label>
                    </div>
                </div>
                <div class="col-md-4 text-right">
                    <div class="custom-control custom-radio custom-control-inline">
                        <input type="radio" class="custom-control-input" id="otherLiabilitiesYes"
                            name="otherLiabilitiesOwed" value="YES" {{#ifEquals report.liabilities.otherLiabilitiesOwed true}} checked {{/ifEquals}}>
                        <label class="custom-control-label" for="otherLiabilitiesYes">Yes</label>
                    </div>
                    <div class="custom-control custom-radio custom-control-inline">
                        <input type="radio" class="custom-control-input" id="otherLiabilitiesNo"
                            name="otherLiabilitiesOwed" value="NO" {{#ifEquals report.liabilities.otherLiabilitiesOwed false}} checked {{/ifEquals}}>
                        <label class="custom-control-label" for="otherLiabilitiesNo">No</label>
                    </div>
                </div>
            </div>
            
            <!-- if yes -->
            <div id="longTermOtherLiabilitiesYesRows"
                {{#ifEquals report.liabilities.otherLiabilitiesOwed true}} class="d-block" {{else}}class="hide-element" {{/ifEquals}}
            >
                <div class="row mt-2">
                    <div class="col-md-8">
                        <div class="form-group ml-5">
                            <label class="lbl-read-only">4.4.1.1.</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group input-group">
                            <button type="button" class="btn solid royal-blue cc addOtherLiabilityBtn"
                                data-type="LiabilityEndPeriod">
                                <i class="fa fa-plus pr-2"></i>Add New Liability
                            </button>
                        </div>
                    </div>
                </div>
            
                <div id="LiabilityEndPeriodTableContainer" class="table-responsive pl-5 {{#ifCond report.liabilities.otherLiabilitiesEndPeriod.length '>' 0}} d-block {{else}} hide-element {{/ifCond}}">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th class="header-30-percent">Description</th>
                                <th class="header-30-percent">Value</th>
                                <th class="header-20-percent"></th>
                                <th class="header-20-percent"></th>
                            </tr>
                        </thead>
                        <tbody id="LiabilityEndPeriodTable">
                            {{#each report.liabilities.otherLiabilitiesEndPeriod}}
                                <tr id="liability-table-row-{{_id}}-LiabilityEndPeriod" class="liability-row">
                                    <td>{{description}}</td>
                                    <td class="otherLiabilityValue">{{decimalValue value}}</td>
                                    <td class="text-right">
                                        <button type="button" class="btn btn-outline-secondary openEditLiability"
                                                data-id="{{_id}}" data-type="LiabilityEndPeriod"> 
                                            <i class="fa fa-pencil"></i>
                                        </button>
                                    </td>
                                    <td class="text-left">
                                        <button type="button" class="delete btn btn-danger deleteLiability"
                                                data-id="{{_id}}" data-type="LiabilityEndPeriod">
                                            <i class="fa fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            {{/each}}
                        </tbody>
                    </table>
                </div>
            

                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group ml-3">
                            <label class="lbl-read-only" for="valueOfOtherLiabilitiesEndPeriod">4.4.2. Value of other liabilities at the end od the financial period:</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text prepend-currency"> {{report.currency}}</span>
                            </div>
                            <input type="text" id="valueOfOtherLiabilitiesEndPeriod" name="valueOfOtherLiabilitiesEndPeriod" class="liabilityInput form-control autonumber text-right cashPaidExp"
                                value="{{report.liabilities.valueOfOtherLiabilitiesEndPeriod}}"
                                data-a-sep=",">
                        </div>
                    </div>
                </div>
            
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group ml-3">
                            <label class="lbl-read-only">4.4.3. Please indicate the total balance of the invoices issued to the
                                Company
                                during the financial period in relation to other expenses (which was not indicated in 4.2.2.2 section). Please include also any accrued expenses
                                for
                                services received during the reporting period but not invoiced in the current financial
                                period:</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text prepend-currency"> {{report.currency}}</span>
                            </div>
                            <input type="text" id="totalInvoicesAndAccruedExpenses" name="totalInvoicesAndAccruedExpenses" class="liabilityInput form-control autonumber text-right cashPaidExp"
                                value="{{report.liabilities.totalInvoicesAndAccruedExpenses}}"
                                data-a-sep=",">
                        </div>
                    </div>
                </div>
            </div>


            <div class="row">
                <div class="col-md-12">
                    <div class="progress">
                        <div class="progress-bar width-57" role="progressbar" aria-valuenow="4"
                             aria-valuemin="0" aria-valuemax="7">4 of 7
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script type="text/javascript" src="/views-js/partials/financial-reports/liabilities.js"></script>