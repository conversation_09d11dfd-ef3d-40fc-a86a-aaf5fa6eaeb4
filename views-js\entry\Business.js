function refreshUploadedFiles() {
    const entryId = window.location.pathname.split('/')[3]

    $.get('/substance/entry/'+entryId+'/uploaded-files', {}, function (data) {

        var template = Handlebars.templates.uploadedfiles;

        var equipment_files = [];
        var outsourcing_files = [];
        var highrisk_ip_files = [];
        var support_files = [];
        switch (urlPart) {
            case "banking-business":
                equipment_files = data.evidence_equipment_banking_business;
                outsourcing_files = data.evidence_outsourcing_banking_business;
                support_files = data.support_documents_banking_business;
                break;
            case "insurance-business":
                equipment_files = data.evidence_equipment_insurance_business;
                outsourcing_files = data.evidence_outsourcing_insurance_business;
                support_files = data.support_documents_insurance_business;
                break;
            case "fund-management-business":
                equipment_files = data.evidence_equipment_fund_management_business;
                outsourcing_files = data.evidence_outsourcing_fund_management_business;
                support_files = data.support_documents_fund_management_business;
                break;
            case "finance-leasing-business":
                equipment_files = data.evidence_equipment_finance_leasing_business;
                outsourcing_files = data.evidence_outsourcing_finance_leasing_business;
                support_files = data.support_documents_finance_leasing_business;
                break;
            case "headquarters-business":
                equipment_files = data.evidence_equipment_headquarters_business;
                outsourcing_files = data.evidence_outsourcing_headquarters_business;
                support_files = data.support_documents_headquarters_business;
                break;
            case "shipping-business":
                equipment_files = data.evidence_equipment_shipping_business;
                outsourcing_files = data.evidence_outsourcing_shipping_business;
                support_files = data.support_documents_shipping_business;
                break;
            case "intellectual-property-business":
                equipment_files = data.evidence_equipment_intellectual_property_business;
                outsourcing_files = data.evidence_outsourcing_intellectual_property_business;
                support_files = data.support_documents_intellectual_property_business;
                highrisk_ip_files = data.evidence_high_risk_intellectual_property_business;
                break;
            case "holding-business":
                equipment_files = data.evidence_equipment_holding_business;
                outsourcing_files = data.evidence_outsourcing_holding_business;
                support_files = data.support_documents_holding_business;
                break;
            case "service-centre-business":
                equipment_files = data.evidence_equipment_service_centre_business;
                outsourcing_files = data.evidence_outsourcing_service_centre_business;
                support_files = data.support_documents_service_centre_business;
                break;

            default:
                equipment_files = [];
                outsourcing_files = [];
                highrisk_ip_files = [];
                support_files = [];
                break;
        }

        var equipment_data = {
            title: "",
            files: equipment_files,
            field: "EvidenceEquipment-" + urlPart
        }
        var equipment_html = template(equipment_data);

        $("#equipment_uploaded_files").html(equipment_html);

        var outsourcing_data = {
            title: "",
            files: outsourcing_files,
            field: "EvidenceOutsourcing-" + urlPart
        }
        var outsourcing_html = template(outsourcing_data);

        $("#outsourcing_uploaded_files").html(outsourcing_html);

        var highrisk_ip_data = {
            title: "",
            files: highrisk_ip_files,
            field: "EvidenceHighRiskIp-" + urlPart
        }
        var highrisk_ip_html = template(highrisk_ip_data);

        $("#high_risk_ip_uploaded_files").html(highrisk_ip_html);

        var support_data = {
            title: "",
            files: support_files,
            field: "SupportDocuments-" + urlPart
        }
        var support_html = template(support_data);

        $("#support_documents_uploaded_files").html(support_html);
    });

}

function refreshManagers() {
    $("#iframeDirector").prop('src', 'about:blank');
    const entryId = window.location.pathname.split('/')[3]
    $.get('/substance/entry/'+entryId+'/managers', {}, function (data) {
        Handlebars.registerHelper('formatDate', function (strDate) {
            var formattedDate = "";
            if (strDate) {
                var date = new Date(strDate)
                date = new Date(date.getTime() + (date.getTimezoneOffset() * 60000))
                return ((date.getMonth() + 1) + "/" + date.getDate() + "/" + date.getFullYear());
            }
        });
        Handlebars.registerHelper('formatAddress', function (manager) {
            let addressComponent = [];
            addressComponent.push(manager.address_line1);
            addressComponent.push(manager.address_line2);
            addressComponent.push(manager.city);
            addressComponent.push(manager.country);
            addressComponent.push(manager.postalcode);
            return addressComponent.filter(function (val) { return val; }).join(', ');
        });

        var template = Handlebars.templates.managers;
        //get part of url to define which managers should be shown
        var managers = [];
        switch (urlPart) {
            case "banking-business":
                managers = data.banking_business_managers;
                break;
            case "insurance-business":
                managers = data.insurance_business_managers;
                break;
            case "fund-management-business":
                managers = data.fund_management_business_managers;
                break;
            case "finance-leasing-business":
                managers = data.finance_leasing_business_managers;
                break;
            case "headquarters-business":
                managers = data.headquarters_business_managers;
                break;
            case "shipping-business":
                managers = data.shipping_business_managers;
                break;
            case "intellectual-property-business":
                managers = data.intellectual_property_business_managers;
                break;
            case "holding-business":
                managers = data.holding_business_managers;
                break;
            case "service-centre-business":
                managers = data.service_centre_business_managers;
                break;

            default:
                managers = [];
                break;
        }

        var d = {
            title: "Managers",
            version: data.entryVersion,
            data: {
                managers: managers
            }
        }
        var html = template(d);

        $("#tbl_managers").html(html);

        $('.editmanager').click(function () {
            //oad manager data and prefille
            var id = $(this).data('id')
            editManager(id);
        });

        $('.deletemanager').click(async function () {
            //oad manager data and prefille
            var id = $(this).data('id')
            await deleteManager(id);
        });
    });

}
function refreshPremises() {
    const entryId = window.location.pathname.split('/')[3]
    $.get('/substance/entry/'+entryId+'/premises', {}, function (data) {
        var template = Handlebars.templates.premises;
        //get part of url to define which managers should be shown
        var premises = [];
        switch (urlPart) {
            case "banking-business":
                premises = data.banking_business_premises;
                break;
            case "insurance-business":
                premises = data.insurance_business_premises;
                break;
            case "fund-management-business":
                premises = data.fund_management_business_premises;
                break;
            case "finance-leasing-business":
                premises = data.finance_leasing_business_premises;
                break;
            case "headquarters-business":
                premises = data.headquarters_business_premises;
                break;
            case "shipping-business":
                premises = data.shipping_business_premises;
                break;
            case "intellectual-property-business":
                premises = data.intellectual_property_business_premises;
                break;
            case "holding-business":
                premises = data.holding_business_premises;
                break;
            case "service-centre-business":
                premises = data.service_centre_business_premises;
                break;

            default:
                premises = [];
                break;
        }

        var d = {
            title: "Premises",
            version: data.entryVersion,
            data: {
                premises: premises
            }
        }
        var html = template(d);

        $("#tbl_premises").html(html);
        $('.editpremises').click(function () {
            //oad manager data and prefille
            var id = $(this).data('id')
            editPremises(id);
        });

        $('.deletepremises').click(async function () {
            //oad manager data and prefille
            var id = $(this).data('id')
            await deletePremises(id);
        });
    });

}

function ShowHideOutsourcing() {
    var CoreIncomeGeneratingOutsourcedYes = document.getElementById("CoreIncomeGeneratingOutsourcedYes");

    if (CoreIncomeGeneratingOutsourcedYes.checked)
        $("#Outsourcing").show()
    else
        $("#Outsourcing").hide()
}

function ShowHideActivity() {
    var OutsourcedActivityundertakenInBVINo = document.getElementById("OutsourcedActivityundertakenInBVINo");
    if (OutsourcedActivityundertakenInBVINo.checked)
        $("#ShowActivity").show()
    else
        $("#ShowActivity").hide()
}
function ShowHideActivity2() {
    var LegalEntityDemonstrateMonitoringYes = document.getElementById("LegalEntityDemonstrateMonitoringYes");
    if (LegalEntityDemonstrateMonitoringYes.checked)
        $("#ShowActivity2").show()
    else
        $("#ShowActivity2").hide()
}
function ShowHideActivity3() {
    var manage_equity_participations_yes = document.getElementById("manage_equity_participations_yes");
    if (manage_equity_participations_yes.checked)
        $("#ShowActivity3").show()
    else
        $("#ShowActivity3").hide()
}

function ShowHideHighRiskIp() {
    var high_risk_ip_yes = document.getElementById("entity_high_risk_yes");
    if (high_risk_ip_yes.checked)
        $(".show-high-risk-ip").removeClass("hidden")
    else
        $(".show-high-risk-ip").addClass("hidden")



    var high_risk_ip_no = document.getElementById("entity_high_risk_no");
    if (high_risk_ip_no.checked) {
        $("#evidence_high_risk_ip_no").prop('checked', true);
        ShowHideHighRiskIpEvidence();
        $(".hide-for-high-risk-ip-with-evidence").removeClass("hidden")
    } else {
        $(".hide-for-high-risk-ip-with-evidence").addClass("hidden");
        ShowHideHighRiskIpEvidence();
    }

}

function ShowHideHighRiskIpEvidence() {
    var high_risk_ip_evidence_yes = document.getElementById("evidence_high_risk_ip_yes");
    if (high_risk_ip_evidence_yes.checked) {
        $(".show-high-risk-ip-evidence").removeClass("hidden");
        $(".hide-for-high-risk-ip-with-evidence").removeClass("hidden");
    } else {
        $(".show-high-risk-ip-evidence").addClass("hidden");

        $(".hide-for-high-risk-ip-with-evidence").addClass("hidden");
    }
}

$('#entity_high_risk_yes').click(function () {
    ShowHideHighRiskIp()
})

$('#entity_high_risk_no').click(function () {
    ShowHideHighRiskIp()
})

$('#evidence_high_risk_ip_yes').click(function () {
    ShowHideHighRiskIpEvidence()
})

$('#evidence_high_risk_ip_np').click(function () {
    ShowHideHighRiskIpEvidence()
})

$('#LegalEntityDemonstrateMonitoringYes').click(function () {
    ShowHideActivity2()
})

$('#LegalEntityDemonstrateMonitoringNo').click(function () {
    ShowHideActivity2()
})

$('#manage_equity_participations_yes').click(function () {
    ShowHideActivity3()
})

$('#manage_equity_participations_no').click(function () {
    ShowHideActivity3()
})

$('#CoreIncomeGeneratingOutsourcedYes').click(function () {
    ShowHideOutsourcing()
})

$('#CoreIncomeGeneratingOutsourcedNo').click(function () {
    ShowHideOutsourcing()
})

$('#OutsourcedActivityundertakenInBVIYes').click(function () {
    ShowHideActivity()
})

$('#OutsourcedActivityundertakenInBVINo').click(function () {
    ShowHideActivity()
})


$(document).ready(function () {

    //show equipment if intellectual business
    if (document.URL.endsWith('intellectual-property-business')) {
        $("#equipment").removeClass('hidden');
        $(".show-intellectual-property-business").removeClass('hidden');
        ShowHideHighRiskIp();
    };

    if (document.URL.endsWith('holding-business')) {
        $(".hidden-holding-business").addClass('hidden');
        $(".show-holding-business").removeClass('hidden');
        ShowHideActivity3();
    } else {
        ShowHideOutsourcing();
        ShowHideActivity();
        ShowHideActivity2();
    }


    refreshManagers();
    refreshPremises();
    refreshUploadedFiles();

    $('#AddPremises').on('hide.bs.modal', function (event) {
        refreshPremises();
    });
    $('#AddPerson').on('hide.bs.modal', function (event) {

        refreshManagers();
    });

});