const moment = require('moment');

module.exports = (sequelize, Sequelize) => {
    const mem_Directors = sequelize.define('mem_Directors', {
        UniqueRelationID: {
            type: Sequelize.STRING(32),
            allowNull: false,
            primaryKey: true
        },
        ClientCode: Sequelize.STRING(10),
        ClientName: Sequelize.STRING(356),
        ClientUniqueNr: { type: Sequelize.INTEGER, allowNull: false },
        EntityCode: { type: Sequelize.STRING(10), allowNull: false },
        EntityName: Sequelize.STRING(356),
        EntityUniqueNr: { type: Sequelize.INTEGER, allowNull: false },
        EntityLegacyID: Sequelize.STRING(30),
        DirCode: Sequelize.STRING(10),
        DirName: Sequelize.STRING(356),
        DirUniqueNr: { type: Sequelize.INTEGER, allowNull: false },
        DirFileType: Sequelize.STRING(50),
        RelationType: Sequelize.STRING(150),
        DirOfficerType: Sequelize.STRING(150),
        DirFromDate: {
            type: Sequelize.DATE,
            allowNull: true,
            get() {
                const date = this.getDataValue('DirFromDate');
                return date ? moment.utc(date).format('YYYY-MM-DD') : null;
            }
        },
        DirToDate: {
            type: Sequelize.DATE,
            allowNull: true,
            get() {
                const date = this.getDataValue('DirToDate');
                return date ? moment.utc(date).format('YYYY-MM-DD') : null;
            }
        },
        DirStatus: { type: Sequelize.STRING(9), allowNull: false },
        LicenseeEntityCode: Sequelize.STRING(10),
        LicenseeEntityName: Sequelize.STRING(356),
        DirCapacityCode: Sequelize.STRING(20),
        DirCapacity: Sequelize.STRING(255),
        DirID: Sequelize.STRING(100),
        PreviousUniqueRelationId: {
            type: Sequelize.STRING(21),
            allowNull: false
        }
    }, {
        sequelize,
        tableName: 'mem_Directors',
        schema: 'dbo',
        timestamps: false
    });

    mem_Directors.associate = (models) => {
        // Define association between mem_Directors and mem_MemberProfiles
        mem_Directors.belongsTo(models.mem_MemberProfiles, {
            foreignKey: 'DirUniqueNr',  // Foreign key in mem_Directors
            targetKey: 'MFUniqueNr',    // Target key in mem_MemberProfiles
            as: 'directorProfile'       // Alias for the association
        });

        mem_Directors.belongsTo(models.mem_DirectorsHistory, {
            foreignKey: 'UniqueRelationID',
            targetKey: 'UniqueRelationID',
            as: 'directorHistory'
        });

        mem_Directors.belongsTo(models.mem_Entities, {
            foreignKey: 'EntityUniqueNr',
            targetKey: 'EntityUniqueNr',
            as: 'entity'
        });
    };

    return mem_Directors;
};
