let evidenceRadioSelected;
let evidencesUploadedCount = 0;

function refreshUploadedFiles() {
    const entryId = window.location.pathname.split('/')[3]

    $.get('/substance/entry/'+entryId+'/uploaded-files', {}, function (data) {

        const template = Handlebars.templates.uploadedfiles;

        const files = [...data?.evidence_non_residency || [], ...data?.evidence_provisional_treatment_non_residency || []];
        evidencesUploadedCount = files.length;
        const hasNonResidencyEvidences = $("#evidenceTypeNonResidency").is(':checked');
        const hasProvisionalTreatmentEvidences = $("#evidenceTypeProvisionalTreatment").is(':checked');


        let d = {
            title: hasNonResidencyEvidences ? "Evidence of tax residency in another jurisdiction" : hasProvisionalTreatmentEvidences ?
                    "Evidence of provisional treatment as non-resident" : "Evidence Files",
            files: hasNonResidencyEvidences || hasProvisionalTreatmentEvidences ? files : [],
            field: "EvidenceNonResidency-" + urlPart
        };
        const html1 = template(d);

        $("#uploaded_files_evidence_tax_residency").html(html1);
    });

}


function ShowHideDiv() {
    const residencyOutSideBVIYes = document.getElementById("ResidencyOutsideBVIYes");
    const showRest = document.getElementById("Showrest");
    showRest.style.display = residencyOutSideBVIYes.checked ? "block" : "none";

    const hasNonResidencyEvidences = $("#evidenceTypeNonResidency").is(':checked');
    if (hasNonResidencyEvidences) {
        evidenceRadioSelected = $("#evidenceTypeNonResidency").val();
        $("#nonResidencyUploadBtn").prop('disabled', false);
        $("#provisionalTreatmentUploadBtn").prop('disabled', true);
        $("#uploaded_files_evidence_tax_residency").show();
    }

    const hasProvisionalTreatmentEvidences = $("#evidenceTypeProvisionalTreatment").is(':checked');

    if (hasProvisionalTreatmentEvidences) {
        evidenceRadioSelected =  $("#evidenceTypeProvisionalTreatment").val();
        $("#provisionalTreatmentUploadBtn").prop('disabled', false);
        $("#nonResidencyUploadBtn").prop('disabled', true);
        $("#uploaded_files_evidence_tax_residency").show();
    }


}

const Blacklist = new Set(["Afghanistan", "Bosnia and Herzegovina", "Korea, Democratic People's Republic of", "Guyana", "Iran, Islamic Republic of", "Iraq", "Lao People's Democratic Republic", "Syrian Arab Republic", "Uganda", "Vanuatu", "Yemen"]);

function ShowHideAlert() {
    var ShowAlert = document.getElementById("ShowAlert");

    if (Blacklist.has($("#select2-EntityJurisdiction-container").text()))
        ShowAlert.style.display = "block";
    else
        ShowAlert.style.display = "none";

}

async function  showChangeEvidenceTypeModal() {
    return await Swal.fire({
        title: 'Are you sure?',
        icon: 'warning',
        text: "Please note that you are changing your choice of evidence. Do you want to keep or replace your previously uploaded document?",
        backdrop: true,
        showCancelButton: true,
        cancelButtonColor: "#6c757d",
        cancelButtonText: 'Cancel',
        showDenyButton: true,
        denyButtonColor: "#0081B4",
        denyButtonText: 'Keep',
        confirmButtonColor: "#0081B4",
        confirmButtonText: 'Replace',
        reverseButtons: true,
        showLoaderOnDeny: true,
        showLoaderOnConfirm: true,
        preConfirm(inputValue) {
            return axios.delete(window.location.href + "/evidences")
            .then(response => {
                try {
                    return response.data
                } catch (e) {
                    throw new Error(response.statusText)
                }

            })
            .catch(error => {
                if (error?.response?.data) {
                    return error.response.data
                }
                return {status: 500, error: error}

            });
        },
        allowOutsideClick: () => !Swal.isLoading()
    }).then((result) => {
        if (result.isDismissed) {
            return false;
        }
        if (result.isDenied) {
            return true;
        }

        if (result.isConfirmed) {
            swal.showLoading();
            if (result.value.status === 200) {
                return true;
            } else if (result.value?.status !== 200 &&  result.value?.error) {
                Swal.fire('Error', result.value.error, 'error');
            } else {
                Swal.fire('Error', 'There was an error removing all the evidences, please try again.', 'error');
            }

        }

    });
}


$("input[name='evidenceType']").on('change', function () {
    const evidenceType = $("input[name='evidenceType']:checked").val();


    if (evidenceType === "non residency") {
        $("#nonResidencyUploadBtn").prop('disabled', false);
        $("#provisionalTreatmentUploadBtn").prop('disabled', true);
        $("#uploaded_files_evidence_tax_residency").show();
    }
    else {
        $("#provisionalTreatmentUploadBtn").prop('disabled', false);
        $("#nonResidencyUploadBtn").prop('disabled', true);
        $("#uploaded_files_evidence_tax_residency").show();
    }
    refreshUploadedFiles();
});

$("input[name='evidenceType']").on('click', async function (e) {
    const evidenceType = $("input[name='evidenceType']:checked").val();

    if(!evidenceRadioSelected){
        evidenceRadioSelected = evidenceType;

    }else{

      if(evidenceType === evidenceRadioSelected) {
          return false;
      }
      if(evidencesUploadedCount === 0){
          evidenceRadioSelected = evidenceType;
          return false;
      }

      e.preventDefault();
      let allowChangeEvidenceType = await showChangeEvidenceTypeModal();

      if(!allowChangeEvidenceType){
        return false;
      }
      evidenceRadioSelected = evidenceType;
      $("input[name='evidenceType']").val([evidenceType]).trigger('change');

    }

});




$(document).ready(function () {
    $('#submitForm').on('submit', function (e) {
        if (Blacklist.has($("#select2-EntityJurisdiction-container").text())) {
            event.preventDefault();
            var form = this;
            Swal.fire(
                {
                    title: "The selected country is blacklisted.",
                    text: "The submission will be cancelled. Please contact <NAME_EMAIL>",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    cancelButtonText: "Cancel",
                    confirmButtonText: "Ok"
                }).then(function (t) {
                    if (t.value) {
                        document.location = '/'
                    }
                })
        };
    });

    ShowHideDiv();
    const jurisdiction = $("#EntityJurisdiction").attr('data-value')
    $("#EntityJurisdiction").val(jurisdiction);
    $("#select2-EntityJurisdiction-container").text(jurisdiction);
    $("#select2-EntityJurisdiction-container").attr('title', jurisdiction);
    ShowHideAlert();
    refreshUploadedFiles();
});


$("input[name='ResidencyOutsideBVIYesOrNo']").on('change', function (e) {
    const isOutsideBvi = $("input[name='ResidencyOutsideBVIYesOrNo']:checked").val();
    ShowHideDiv()
    if (isOutsideBvi === "No") {
        $("#EntityJurisdiction").val('');
        $("#select2-EntityJurisdiction-container").text('');
        $("#select2-EntityJurisdiction-container").attr('title', '');
        ShowHideAlert();
    }

})