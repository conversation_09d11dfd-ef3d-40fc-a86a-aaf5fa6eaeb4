Dropzone.autoDiscover = false;

let mcc 
let company 
let entryId

$(async function () {
    const pathname = window.location.pathname
    mcc = pathname.split('/')[2]
    company = pathname.split('/')[5]
    entryId = pathname.split('/')[6]

    const url = window.location.pathname.substring(0, window.location.pathname.lastIndexOf("/"));
    const csrfToken = $("input[name='csrf-token']").val()
    const myDropZone = new Dropzone("#frmUploadModal", {
        url: "/",
        acceptedFiles: 'application/pdf',
        autoProcessQueue: true,
        parallelUploads: 1,
        maxFiles: 10,
        maxFilesize: 5,
        addRemoveLinks: false,
        paramName: function () {
            return 'fileUploaded';
        },
        uploadMultiple: true,
        headers: {
            'x-csrf-token': csrfToken
        },
        init: function () {
            this.on("processing", function (file) {
                this.options.url = window.location.pathname + "/upload-files";
            })
            this.on("success", function () {
                refreshUploadedFiles();
            })
            this.on("sending", function (file, xhr, formData) {
                $("#btnSubmit").prop('disabled', true);
                if (!formData.has('filetype')) {
                    formData.append("filetype", '');
                }
                formData.append('csrf-token', csrfToken);

            })

            this.on("error", function () {
                $("#btnSubmit").prop('disabled', false);
            })

            this.on('resetFiles', function () {
                if (this.files && this.files.length) {
                    for (let file of this.files) {
                        file.previewElement.remove();
                    }
                    this.files.length = 0;
                }
            });

            this.on("removedfile", function (file) {
                var name = file.name;
                $.ajax({
                    type: 'POST',
                    url: window.location.pathname + "/delete-files",
                    data: { name: name },
                    success: function (data) {

                    }
                });
            });
        }
    })
    refreshUploadedFiles();
})

$('[data-toggle="tooltip"]').tooltip({
    container: 'body',
    boundary: 'window'
});

$(document).on('click', '.deleteFile',async  function (event) {
    event.preventDefault()
    await deleteFile($(this).attr('data-field'), $(this).attr('data-field-id'), $(this).attr('data-name'))
})

async function deleteFile(field, fileId, name ) {
    console.log(name)
    $.ajax({
        type: "DELETE",
        url: "/masterclients/"+mcc+"/company-files/financial-reports/"+company+"/"+entryId+"/files/" + fileId,

        success: function (data) {
            if (data.status === 200) {
                refreshUploadedFiles();
                const objDZ = Dropzone.forElement('#frmUploadModal');
                console.log(objDZ)
                const file = objDZ.files.find((file) => file.name === name);
                if (file){
                    file.previewElement.remove();
                    objDZ.files = objDZ.files.filter((fl) => fl.upload.uuid !== file.upload.uuid);
                }
            }
        },
        dataType: "json"
    });
    return false;
}

function refreshUploadedFiles() {

    $.ajax({
        type: 'GET',
        url: "/masterclients/"+mcc+"/company-files/financial-reports/"+company+"/"+entryId+"/files",
        timeout: 5000,
        success: function (data) {
            if (data.result === true) {
                let template = Handlebars.templates.uploadedfiles;
                let d = {
                    files: data.files ? data.files.map(f => { return {...f, _id: f.fileId }}) : [],
                    field: '',
                    title: 'Uploaded files',
                };
                let html = template(d);
                $('#uploadedRequestedFiles').html(html);
            } else {
                Swal.fire('Error', data.error ? data.error : 'There was an error getting files', 'error');
            }
        },
        error: function (res) {
            Swal.fire('Error', 'There was an error getting files', 'error');
        },
    });

}

$("#submit-requested-files-button").on('click',async  function () {
    const comment = $("#comment").val();

    if(!comment || comment === ""){
        toastr["warning"]('Please provide the comment', 'Error!');
        return false;
    }
    Swal.fire({
        title: 'Are you sure?',
        text: "You are about to submit the information.",
        showCancelButton: true,
        icon: 'info',
        backdrop: true,
        cancelButtonColor: "#6c757d",
        confirmButtonColor: "#0081B4",
        confirmButtonText: 'Submit',
        reverseButtons: true,
        showLoaderOnConfirm: true,
        preConfirm() {
            let data = {
                comment: comment,
                status: 'CONFIRMED'
            };
            return axios.put(window.location.href,
                JSON.stringify(data),
                {
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                }
            ).then(response => {
                try {
                    return response.data
                } catch (e) {
                    throw new Error(response.statusText)
                }

            }).catch(error => {
                if (error?.response?.data) {
                    return error.response.data
                }
                return { status: 500, error: error }
            });
        },
        allowOutsideClick: () => !Swal.isLoading()
    }).then((result) => {
        if (result.value) {
            swal.showLoading();
            if (result.value.status === 200) {
                Swal.fire({
                    title: 'Success',
                    text: 'Files have been submitted to Trident successfully.',
                    icon: 'success',
                    confirmButtonColor: "#0081B4",
                }).then(() => {
                    location.href = '/masterclients/'+mcc+'/company-files';
                });
            } else if (result.value.status === 400) {
                Swal.fire({
                    title: 'Error',
                    text: result.value.message,
                    icon: 'error',
                    confirmButtonColor: "#0081B4",
                });
            } else {
                Swal.fire({
                    title: 'Error',
                    text: result.value.message ? result.value.message : 'There was an error updating the information.',
                    icon: 'error',
                    confirmButtonColor: "#0081B4",
                });
            }
        }

    })
});
