const express = require('express');
const router = express.Router();

// Require controller modules.
const messageController = require('../controllers/messageController');
const downloadController = require("../controllers/downloadController");

router.get('/', messageController.ensureAuthenticated, messageController.getMessages);
router.post('/:id', messageController.ensureAuthenticated, messageController.getMessageContent); // jquery / ajax
router.get('/:id/files/:fileId', messageController.ensureAuthenticated, downloadController.downloadMessageFile);

module.exports = router;

