$(function () {
    $('[data-toggle="tooltip"]').tooltip({ container: 'body' });
})

$(document).ready(function () {
    let table = $("#companies-table").DataTable({
        scrollX: !0,
        searching: false,
        info: false,
        language: {
            paginate: {
                previous: "<i class='mdi mdi-chevron-left'>",
                next: "<i class='mdi mdi-chevron-right'>"
            }
        },
        drawCallback: function () {
            $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
        }
    });
});

$(".new-financial-report").on('click', async function(e){
    e.preventDefault();
    $(".new-financial-report").prop('disabled', true);

    const hasReports = $(this).data('has-reports');

    const mcc = $(this).data('mcc');
    const company = $(this).data('company');
    const incorporationDate = $(this).data('incorporation')
    
    if(hasReports === true){
        const lastPeriod = $(this).data('last-end-period');

        const newStartPeriod = moment(lastPeriod).utc().add(1, 'days').format('YYYY/MM/DD');
        const newEndPeriod = moment(newStartPeriod, 'YYYY/MM/DD').utc().add(1, 'years').subtract(1, "days").format('YYYY/MM/DD');

        const response = await createNewFinancialReport(mcc, company, { isFirstReport: false })
        if(response !== false) {
            document.location = document.location.href+ `/companies/${response.companyId}/${response.reportId}`;
        } 
    }else{
        $('#firstFinancialReportForm #companyIncorporationDate').val(incorporationDate)
        $("#firstFinancialReportForm #newFinancialReportMCC").val(mcc);
        $("#firstFinancialReportForm #newFinancialReportCode").val(company);
        $("#firstFinancialReportPeriodModal").modal('show');
        const firstDate = moment(incorporationDate)
      
        let maxDate = firstDate.add(18, 'months');
        maxDate = maxDate.subtract(1, "days").format('YYYY-MM-DD')

        $('#financialPeriodEnd').flatpickr({
            dateFormat: "Y-m-d",
            maxDate: maxDate
        })

    }
    
})

$('.information-request').click(function () {
    const url = document.URL.split('/').splice(5)
    console.log(url)
})

$(document).on('click', '.showReopenContinueModal', function (event) {
    event.stopPropagation();
    showReopenFinancialReportModal($(this).attr('data-mcc'), $(this).attr('data-code'), $(this).attr('data-id'), $(this).attr('data-reopened-id'))
})

async function createNewFinancialReport(mcc, company, data) {
    try {
        // REMOVE LATER WITH THE MERGE OF CSRF-CHANGE
        let csrfToken = $("#firstFinancialReportForm input[name='csrf-token']").val();
        const response = await $.ajax({
            type: "POST",
            url: `/masterclients/${mcc}/financial-reports/companies/${company}/create`,
            data: JSON.stringify(data),
            dataType: "json",
            contentType: "application/json; charset=utf-8",
            headers: {
                'x-csrf-token': csrfToken
            },
        });

        if (response.status === 200) {
            return response
        } else {
            const error = response.error || 'New financial report could not be created, please try again later.';
            toastr["warning"](error, 'Error!');
            $(".new-financial-report").prop('disabled', false);
            return false;
        }
    } catch (error) {
        $(".new-financial-report").prop('disabled', false);
        toastr["warning"](error.responseJSON.error || 'New financial report could not be created, please try again later.', 'Error!');
        return false;
    }
}


function showReopenFinancialReportModal(mcc, company, reportId, reopenedId) {
    $(".showReopenContinueModal").prop('disabled', true);
    $.ajax({
        type: 'GET',
        url: `./financial-reports/companies/${company}/${reportId}/reopened-info/${reopenedId}`,
        timeout: 5000,
        success: function (data) {
            if (data.status === 200) {
                Swal.fire({
                    title: "Re-opened financial report",
                    icon: "warning",
                    backdrop: true,
                    showCancelButton: true,
                    confirmButtonColor: "#005C81",
                    confirmButtonText: 'Continue',
                    reverseButtons: true,
                    allowOutsideClick: false,
                    html: "<div class='text-justify'> Please note the following message from your Trident Trust Officer: " + data.reopenedData.reason + "</div> " +
                        (data.reopenedData.changeFinancialPeriodDates === true ?
                            "<hr> " +
                            "<div class='text-justify'> <h5>  Note that your Financial Period has been changed by a " +
                            "Trident Officer according to the ITA guidelines. Please make sure the information on " +
                            "the remaining pages of this submission is correct and in accordance with the amended dates.  </h5> </div>" :
                            ""),
                }).then((result) => {

                    if (result.isConfirmed) {
                        
                        window.location.href = `/masterclients/${mcc}/financial-reports/companies/${company}/${reportId}`
                    } else {
                        $(".showReopenContinueModal").prop('disabled', false);
                    }
                });
            } else {
                console.log("ERROR", data)
                Swal.fire('Error', 'There was an error opening the re-open submission... Please try again later.', 'error').then(() => {
                    $(".showReopenContinueModal").prop('disabled', false);
                });
            }

        },
        error: function (err) {
            console.log(err);
            Swal.fire('Error', 'There was an error opening the re-open submission... Please try again later.', 'error').then(() => {
                $(".showReopenContinueModal").prop('disabled', false);
            });
        },
    });


}
