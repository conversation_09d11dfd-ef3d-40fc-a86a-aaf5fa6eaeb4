const settings = {
	next_page_action: 'Save & next page',
	save_and_exit_action: 'Save & exit',
	previous_page_action: 'Previous page'
};

exports.settings = settings;

exports.subtitle_banking_business = "Banking Business means the business of receiving (other than from a bank or trust company) and holding on current, savings, deposit or similar account money that is repayable by cheque or order and is capable of being invested by way of advances to customers or otherwise but does not include the receiving on savings, deposit or similar account money which is paid by one company to another at a time when one is the subsidiary of the other or both are subsidiaries of another company"


const substance_form_steps = {
	FINANCIAL_PERIOD_FORM: "financial-period",
	ENTITY_DETAILS_FORM: "entity-details",
	TAX_RESIDENCY_FORM: "tax-residency",
	RELEVANT_ACTIVITIES_FORM: "relevant-activities",
	BANKING_BUSINESS_FORM: "banking-business",
	INSURANCE_BUSINESS_FORM: "insurance-business",
	FUND_MANAGEMENT_BUSINESS_FORM: "fund-management-business",
	FINANCE_LEASING_BUSINESS_FORM: "finance-leasing-business",
	HEADQUARTERS_BUSINESS_FORM: "headquarters-business",
	SHIPPING_BUSINESS_FORM: "shipping-business",
	INTELLECTUAL_PROPERTY_BUSINESS_FORM: "intellectual-property-business",
	HOLDING_BUSINESS_FORM: "holding-business",
	SERVICE_CENTRE_BUSINESS_FORM: "service-centre-business",
	MANAGERS_FORM: "managers",
	PREMISES_FORM: "premises",
	SUPPORTING_DETAILS_FORM: "supporting-details",
	CONFIRMATION_FORM: "confirmation",
};
exports.substance_form_steps = substance_form_steps;


const substance_business_types = {
	BANKING: "BNK",
	INSURANCE: "INS",
	FUND_MANAGEMENT: "FM",
	FINANCE_LEASING: "FL",
	HEADQUARTERS: "HQ",
	SHIPPING: "SHP",
	INTELLECTUAL_PROPERTY: "IP",
	HOLDING: "HLD",
	DISTRIBUTION_SERVICE_CENTRE: "DS",
}

exports.substance_business_types = substance_business_types;

exports.validateSubstanceStep = function (step) {
	let isValidStep = false;
	Object.keys(substance_form_steps).forEach(function(key) {
		if (substance_form_steps[key] === step) {
			isValidStep = true;
		}
	});

	return isValidStep;
};
