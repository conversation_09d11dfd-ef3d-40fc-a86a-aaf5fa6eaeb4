$("#registerBtn").on('click', async function (e) {
    e.preventDefault();
    $("#registerBtn").prop('disabled', true);

    try {
        const recaptcha_site = $("#registerBtn").data('sitekey');
        const isTokenSet = await setCaptchaToken(recaptcha_site);
        if (isTokenSet) {
            $("#registerForm").submit();
        } else {
            $("#registerBtn").prop('disabled', false);
        }
    }
    catch (e) {
        $("#registerBtn").prop('disabled', false);
    }
});