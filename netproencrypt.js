 
const crypto = require('crypto');
const algorithm = 'aes-256-cbc';

class netencrypt
{
 

    constructor()
    {
        this.key = process.env.ENCRYPT_KEY;
        this.iv = process.env.ENCRYPT_IV;
    }

     netencrypt(inputtext) {
        let cipher = crypto.createCipheriv(algorithm, Buffer.from(this.key,'hex'), Buffer.from(this.iv,'hex'));
        let encrypted = cipher.update(inputtext);
        encrypted = Buffer.concat([encrypted, cipher.final()]);
        return  encrypted.toString('hex');
       }
       
       netdecrypt(encryptedinput) {
        let encryptedText = Buffer.from(encryptedinput, 'hex');
        let decipher = crypto.createDecipheriv(algorithm, Buffer.from(this.key,'hex'), Buffer.from(this.iv,'hex'));
        let decrypted = decipher.update(encryptedText);
        decrypted = Buffer.concat([decrypted, decipher.final()]);
        return decrypted.toString();
       }
       
}


  
  module.exports = new netencrypt;