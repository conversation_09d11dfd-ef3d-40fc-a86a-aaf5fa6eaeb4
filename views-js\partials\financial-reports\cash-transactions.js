let companyOwnsCashOrEquivalentsSaved = $("input[name='companyOwnsCashOrEquivalents']:checked")
$("input[name='companyOwnsCashOrEquivalents']").on('change', function () {
    const val = $(this).val();
    if (companyOwnsCashOrEquivalentsSaved && companyOwnsCashOrEquivalentsSaved.length > 0 && val === 'NO') {
        Swal.fire({
            title: 'Are you sure?',
            text: "This will delete all the bank accounts created",
            showCancelButton: true,
            icon: 'info',
            backdrop: true,
            cancelButtonColor: "#6c757d",
            cancelButtonText: "No",
            confirmButtonColor: "#0081B4",
            confirmButtonText: 'Yes',
            reverseButtons: true,
            showLoaderOnConfirm: true,
            preConfirm: async () => {
                return axios.delete(`${window.location.href}/bank-accounts`, {},
                {
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                }).then(response => {
                    try {
                        return response.data
                    } catch (e) {
                        throw new Error(response.statusText)
                    }
                }).catch(error => {
                    if (error?.response?.data) {
                        return error.response.data
                    }
                    return { status: error.status || 500, error: error }

                });
            },
            allowOutsideClick: () => !Swal.isLoading()
        }).then(function (result) {
            if (result.isConfirmed) {
                swal.showLoading();
                if (result.value.status === 200) {
                    if (val === "YES") {
                        $("#showCashBankRows").show(200);
                    }
                    else {
                        $("#showCashBankRows").hide();
                        $("#showCashBankRows input[type='text']").val(null);
                        
                        if ($('#cashTransactionsTableBody tr').length > 0) {
                            $('#cashTransactionsTableBody tr').remove();
                            var newRow = $("<tr>")
                                .append("<td colspan='7'>No Cash/Bank Accounts found</td>");
                            $(`#cashTransactionsTableBody`).append(newRow)
                        }
                    }
                    
                } else if (result.value.status === 400 || result.value.status === 404) {
                    Swal.fire('Error', result.value.error, 'error');
                } else {
                    Swal.fire('Error', 'There was an error deleting the bank accounts', 'error');
                }
            
                companyOwnsCashOrEquivalentsSaved = $(this)
            } else {
                $("input[name='companyOwnsCashOrEquivalents'][value='" + companyOwnsCashOrEquivalentsSaved.val() + "']").prop('checked', true);
            }
        })
    } else {
        if (val === "YES") {
            $("#showCashBankRows").show(200);
        }
        else {
            $("#showCashBankRows").hide();
            $("#showCashBankRows input[type='text']").val(null);
        }
        companyOwnsCashOrEquivalentsSaved = $(this)
    }
   
});

$("#reportCurrency").on('change', async function(){
    const val = $(this).val();

    $("#cashATBankHeader").text(`Cash At Bank ${val}`)
    $(".prepend-currency").html(`${val}`)
    if (val === "USD") {
        $("#foreingExchangeRate").val('1.00');
    } else {
        $("#foreingExchangeRate").val('');
    }


    const bankAccountsTableRows = $("#cashTransactionsTableBody tr.bank-account-row").length;
    if (bankAccountsTableRows >= 0){
        await Swal.fire({
            icon: 'warning',
            text: 'The Reporting Currency has changed. The conversion value of the entered bank accounts are now invalid. Please update them.',
            confirmButtonText: 'Ok',
            showLoaderOnConfirm: true,
            allowOutsideClick: () => !Swal.isLoading(),
            backdrop: true,
            preConfirm: async () => {
                try {

                    const response = await $.ajax({
                        type: 'POST',
                        url: `${window.location.href}/bank-accounts/invalidate`,
                        dataType: 'json',
                        contentType: "application/json; charset=utf-8",
                        data: JSON.stringify({ newCurrency: val }),
                    });

                    if (response.status === 200) {
                        refreshBankAccountsTable();
                        return response;
                    } else {
                        Swal.fire('Error', response.error ? response.error : 'An error occurred while invalidate the  bank accounts conversions', 'error');
                        return false;
                    }
                } catch (error) {
                    Swal.fire('Error', 'An error occurred while invalidate the  bank accounts conversions', 'error');
                    return false;
                }
            }
        });


    }
})

$(document).on('click', '.deleteBankAccount', async function () {
    await deleteBankAccount($(this).attr('data-bank-account-id'))
})

async function deleteBankAccount(id) {
    const result = await Swal.fire({
        icon: 'warning',
        title: 'Are you sure?',
        showCancelButton: true,
        confirmButtonText: 'Yes, delete it',
        reverseButtons: true,
        showLoaderOnConfirm: true,
        allowOutsideClick: () => !Swal.isLoading(),
        backdrop: true,
        preConfirm: async () => {
            try {

                const response = await $.ajax({
                    type: 'DELETE',
                    url: `${window.location.href}/bank-accounts/${id}`,
                    dataType: 'json',
                });

                if (response.status === 200) {
                    return response;
                } else {
                    Swal.fire('Error', response.error ? response.error : 'Error deleting the cash transaction', 'error');
                    return false;
                }
            } catch (error) {
                Swal.fire('Error', 'An error occurred while deleting the cash transaction', 'error');
                return false;
            }
        }
    });

    if (result.isConfirmed && result.value) {
        refreshBankAccountsTable();
        Swal.fire('Success', result.value?.message ? result.value.message : 'Cash transaction deleted successfully.', 'success');
    }
}

function refreshBankAccountsTable() {
    let template = Handlebars.templates.createbankaccountrow;
    let bankaccountassetTemplate = Handlebars.templates.createbankaccountassetrow;

    $.ajax({
        type: "GET",
        url: `${window.location.href}/bank-accounts`,
        success: (response) => {
            if (response.status === 200) {
                let rows = template({
                    bankAccounts: response.data?.bankAccounts || [],
                    reportId: response.data.reportId
                });

                let bankAccountsAssets= bankaccountassetTemplate({
                    bankAccounts: response.data?.bankAccounts || [],
                })
        
                $("#cashTransactionsTableBody").html(rows);
                $("#rowsBankAccountsAssets").html(bankAccountsAssets);
                this.calculateClosingBalance()
                $("#totalBankAccounts").val(response.data.totalBankAccounts ? showDecimalValue(response.data.totalBankAccounts) : "")
                $("#lbtTotalLoansAmountReceived").val(response.data.totalLoansAmountReceived ? showDecimalValue(response.data.totalLoansAmountReceived) : "").trigger('keyup');
                $("#lbtTotalLoansAmountPaid").val(response.data.totalLoansAmountPaid ? showDecimalValue(response.data.totalLoansAmountPaid) : "").trigger('keyup');
            } else {
                toastr["error"]('There was an error getting the list of cash transactions.', 'Error');
            }
        },
        error: (err) => {
            toastr["error"]('There was an error getting the list of cash transactions.', 'Error');
        },
    });
}