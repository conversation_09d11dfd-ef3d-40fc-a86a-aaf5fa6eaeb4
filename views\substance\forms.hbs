<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="form-group">
                    </div>
                    <h4 class="mt-3">Overview of all submissions and latest status for entity: <B>{{company.name}}</B>
                        ({{company.code}})</h4>
                    <br>
                    <div class="table-responsive">
                        <table class="table table-striped mb-0">
                            <thead>
                                <tr>
                                    <th>Last Activity Date</th>
                                    <th>Financial Period</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                    <th>Download PDF</th>
                                    <th>Download Invoice</th>
                                    <th>Delete</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each forms}}
                                <tr>
                                    <td>{{formatDate lastUserActivityDate "MMMM D YYYY h:mm:ss a"}}</td>
                                    <td>{{formatDate entity_details.financial_period_begins "YYYY/MM/DD"}} -
                                        {{formatDate entity_details.financial_period_ends "YYYY/MM/DD"}} </td>
                                    <td>
                                        {{#ifEquals status 'SUBMITTED'}}
                                        PENDING PAYMENT
                                            {{#if pre2022Submission}}
                                                <span class="fa-stack tooltip-wrapper text-position" data-toggle="tooltip"
                                                    data-placement="right"
                                                    title="Please contact the ES support team for settlement of the outstanding payment">
                                                    <i class="fa fa-info-circle fa-stack text-danger fa-lg ml-1"></i>
                                                </span>
                                            {{else}}
                                                <span class="fa-stack tooltip-wrapper text-position" data-toggle="tooltip"
                                                      data-placement="right"
                                                      title="Pending annual license fee payment">
                                                    <i class="fa fa-info-circle fa-stack blue-icon fa-lg ml-1"></i>
                                                </span>
                                            {{/if}}
                                        {{else}}
                                            {{status}}
                                        {{/ifEquals}}
                                    </td>
                                    <td>
                                        {{#if allowContinue}}
                                            {{#ifEquals status 'RE-OPEN'}}
                                                <button data-id="{{id}}" data-reopen-id="{{reopenedId}}"
                                                        class="btn solid royal-blue width-xl open-reopen-reason-btn showReopenReason">
                                                    Continue Draft
                                                </button>
                                            {{else}}
                                                <a href="./forms/{{id}}/select" class="btn solid royal-blue width-xl">Continue Draft</a>
                                            {{/ifEquals}}

                                        {{/if}}
                                        {{#if allowEdit}}
                                            <button data-id="{{id}}"
                                                class="btn solid royal-blue width-xl reopenForm">Edit submission</button>
                                        {{/if}}
                                    </td>
                                    <td>
                                        {{#if allowDownload}}
                                        <a href="./forms/{{id}}/submission.pdf" target="_blank"
                                            class="btn solid royal-blue">Download</a>
                                        {{/if}}
                                    </td>
                                    <td>
                                        {{#if allowDownload}}
                                        {{#if allowInvoice}}
                                        <a href="./forms/{{id}}/invoice/invoice.pdf" target="_blank"
                                            class="btn btn-primary waves-effect waves-light">Download</a>
                                        {{/if}}
                                        {{/if}}
                                    </td>

                                    <td>
                                        {{#if allowDelete}}
                                        <form method="POST" class="deleteForm" action="./forms/delete">
                                            <input type="text" hidden name="csrf-token" value="{{../csrfToken}}">
                                            <input type="hidden" value={{id}} name="entryId" />
                                            <button class="demo-delete-row btn btn-danger btn-xs btn-icon"><i
                                                    class="fa fa-times"></i></button>
                                        </form>
                                        {{/if}}
                                    </td>
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                    </div> <!-- end .padding -->
                    <br>
                    <td><a href="/masterclients/{{company.masterclientcode}}/substance/companies"
                            class="btn btn-secondary waves-effect waves-light width-xl">Back</a></td>
                </div> <!-- end card-box-->
            </div> <!-- end col -->
        </div> <!-- end table-responsive-->
    </div> <!-- end card-box -->
    </div><!-- end col-->
</main>

<script type="text/javascript" src="/views-js/substance/forms.js"></script>