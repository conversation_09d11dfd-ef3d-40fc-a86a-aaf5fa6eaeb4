exports.TYPE_OF_DIRECTOR_DIR = "Director"
exports.TYPE_OF_DIRECTOR_BO = "Owner/Controller"
exports.TYPE_OF_MEMBER = "Member"

exports.DIRECTOR_REQUIRED_FIELDS = {
    "individual": [
        {
            field: "OfficerType",
            name: "Director Type"
        },
        {
            field: "Name",
            name: "Name"
        },
        {
            field: "FromDate",
            name: "Appointment Date"
        },
        {
            field: "ServiceAddress",
            name: "Service Address"
        },
        {
            field: "ResidentialOrRegisteredAddress",
            name: "Residential Address"
        },
        {
            field: "DateOfBirthOrIncorp",
            name: "Date of Birth"
        },
        {
            field: "PlaceOfBirthOrIncorp",
            name: "Place of Birth"
        },
        {
            field: "Nationality",
            name: "Nationality"
        }
    ],
    "company": [
        {
            field: "OfficerType",
            name: "Director Type"
        },
        {
            field: "Name",
            name: "Name"
        },
        {
            field: "BoDirIncorporationNumber",
            name: "Corporate Number"
        },
        {
            field: "FromDate",
            name: "Appointment Date"
        },
        {
            field: "ResidentialOrRegisteredAddress",
            name: "Address"
        },
        {
            field: "DateOfBirthOrIncorp",
            name: "Incorporation Date"
        },
        {
            field: "PlaceOfBirthOrIncorp",
            name: "Incorporation Place"
        }
    ],
}

exports.BO_REQUIRED_FIELDS = {
    "VGTP01": [
        {
            field: "Name",
            name: "Name"
        },
        {
            field: "DateOfBirthOrIncorp",
            name: "Date of Birth"
        },
        {
            field: "PlaceOfBirthOrIncorp",
            name: "Place of Birth"
        },
        {
            field: "Nationality",
            name: "Nationality"
        },
        {
            field: "ResidentialOrRegisteredAddress",
            name: "Residential Address"
        }
    ],
    "VGTP02": [
        {
            field: "Name",
            name: "Name"
        },
        {
            field: "BoDirIncorporationNumber",
            name: "Incorporation Number"
        },
        {
            field: "DateOfBirthOrIncorp",
            name: "Date of Incorporation"
        },
        {
            field: "ResidentialOrRegisteredAddress",
            name: "Address"
        },
        {
            field: "PlaceOfBirthOrIncorp",
            name: "Country of Formation"
        },
    ],
    "VGTP03": [
        {
            field: "Name",
            name: "Name"
        },
        {
            field: "BoDirIncorporationNumber",
            name: "Incorporation Number"
        },
        {
            field: "DateOfBirthOrIncorp",
            name: "Date of Incorporation"
        },
        {
            field: "ResidentialOrRegisteredAddress",
            name: "Address"
        },
        {
            field: "PlaceOfBirthOrIncorp",
            name: "Country of Formation"
        },
        {
            field: "NameOfRegulator",
            name: "Name of Regulator"
        },
        {
            field: "JurisdictionOfRegulationOrSovereignState",
            name: "Jurisdiction of Regulator"
        }
    ],
    "VGTP04": [
        {
            field: "Name",
            name: "Name"
        },
        {
            field: "BoDirIncorporationNumber",
            name: "Incorporation Number"
        },
        {
            field: "DateOfBirthOrIncorp",
            name: "Date of Incorporation"
        },
        {
            field: "ResidentialOrRegisteredAddress",
            name: "Address"
        },
        {
            field: "PlaceOfBirthOrIncorp",
            name: "Country of Formation"
        },
        {
            field: "JurisdictionOfRegulationOrSovereignState",
            name: "Sovereign State"
        }
    ],
    "VGTP05": [
        {
            field: "Name",
            name: "Name"
        },
        {
            field: "BoDirIncorporationNumber",
            name: "Incorporation Number"
        },
        {
            field: "DateOfBirthOrIncorp",
            name: "Date of Incorporation"
        },
        {
            field: "ResidentialOrRegisteredAddress",
            name: "Address"
        },
        {
            field: "PlaceOfBirthOrIncorp",
            name: "Country of Formation"
        },
        {
            field: "StockExchange",
            name: "Stock Exchange"
        },
        {
            field: "StockCode",
            name: "Stock Code"
        },
    ],
    "VGTP06": [
        {
            field: "Name",
            name: "Name"
        },
        {
            field: "BoDirIncorporationNumber",
            name: "Incorporation Number"
        },
        {
            field: "DateOfBirthOrIncorp",
            name: "Date of Incorporation"
        },
        {
            field: "ResidentialOrRegisteredAddress",
            name: "Address"
        },
        {
            field: "PlaceOfBirthOrIncorp",
            name: "Country of Formation"
        },
    ],
}

exports.DIRBO_STATUS = {
    INITIAL: "INITIAL",
    PENDING: "PENDING UPDATE REQUEST",
    RECEIVED: "VP DATA RECEIVED",
    CONFIRMED: "CONFIRMED",
    REFRESHED: "REFRESHED"
}
