$('#assetRegistrationNumberRow').hide();
$('#assetDetailsRow').hide();
$('#assetNameOfInstitutionRow').hide();
$('#assetAddressOfInstitutionRow').hide();
$('#assetNameOfBankRow').hide();
$('#assetAddressOfBankRow').hide();
$('#assetNameOfTrustRow').hide();
$('#assetRealEstateTypeRow').hide();
$('#assetLocationRow').hide();
$('#assetJurisdistionRow').hide();

$('#assetTypeControl').change(function () {
    $('#assetRegistrationNumberRow').hide();
    $('#assetDetailsRow').hide();
    $('#assetNameOfInstitutionRow').hide();
    $('#assetAddressOfInstitutionRow').hide();
    $('#assetNameOfBankRow').hide();
    $('#assetAddressOfBankRow').hide();
    $('#assetNameOfTrustRow').hide();
    $('#assetRealEstateTypeRow').hide();
    $('#assetLocationRow').hide();
    $('#assetJurisdistionRow').hide();

    if ($(this).val() === 'Aircraft') {
        $('#assetRegistrationNumberRow').show();
        $('#assetJurisdistionRow').show();
    } else if ($(this).val() === 'Vessel (ship/yacht)') {
        $('#assetRegistrationNumberRow').show();
        $('#assetJurisdistionRow').show();
    } else if ($(this).val() === 'Intellectual property rights') {
        $('#assetDetailsRow').show();
    } else if ($(this).val() === 'Investment portfolio') {
        $('#assetNameOfInstitutionRow').show();
        $('#assetAddressOfInstitutionRow').show();
    } else if ($(this).val() === 'Bank account') {
        $('#assetNameOfBankRow').show();
        $('#assetAddressOfBankRow').show();
    } else if ($(this).val() === 'Trust assets') {
        $('#assetNameOfTrustRow').show();
    } else if ($(this).val() === 'Shares/equity participations') {
        $('#assetDetailsRow').show();
    } else if ($(this).val() === 'Real estate') {
        $('#assetRealEstateTypeRow').show();
        $('#assetLocationRow').show();
    } else if ($(this).val() === 'Debt') {
        $('#assetDetailsRow').show();
    } else if ($(this).val() === 'Other') {
        $('#assetDetailsRow').show();
    }
});

$('#-assetJurisdistion').change(function () {
    checkBlacklistedAsset();
});

function checkBlacklistedAsset() {
    let isBlacklisted = false;
    if (blacklist.find((country) => country === $('#-assetJurisdistion').val())) {
        isBlacklisted = true;
    }
    if (isBlacklisted && $('#-assetJurisdistion').is(':visible')) {
        $('#blacklistedCountryRowAsset').show(200);
        $('#submitAssetBtn').hide();
    } else {
        $('#blacklistedCountryRowAsset').hide(200);
        $('#submitAssetBtn').show();
    }
}