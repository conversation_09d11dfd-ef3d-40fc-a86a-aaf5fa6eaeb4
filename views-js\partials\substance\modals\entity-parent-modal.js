let parentEntityId = '';
let isEditParentEntity = false;
let entityId = "";
let resetEntityParentForm = false;



$(document).ready(function () {
    $('#parentJurisdiction').select2({
        dropdownParent: $('#entityParentModal .modal-content')
    });
});


$('#entityParentModal').on('show.bs.modal', function (event) {
    let button = $(event.relatedTarget); // Button that triggered the modal
    $('#loadingParentEntity').hide()
    let parentType = button.data('parent-type');
    entityId = button.data('id');
    parentEntityId = button.data('parent-id');
    resetEntityParentForm = false;
    $("#parentEntityType").val(parentType);


    if (parentEntityId) {
        isEditParentEntity = true;
        const queryParams = {
            type: parentType,
            parentEntityId: parentEntityId,
        };

        const queryString = $.param(queryParams);

        $.ajax({
            type: "GET",
            url: `/substance/entry/${entityId}/parent-entities?${queryString}`,
            success: (response) => {
                if (response.status === 200) {
                    $("#parentName").val(response.data[0].parentName);
                    $("#parentAlternativeName").val(response.data[0].alternativeName);
                    $("#parentJurisdiction").val(response.data[0].jurisdiction).trigger('change');
                    $("#parentIncorporationNumber").val(response.data[0].incorporationNumber);
                    $("#parentTIN").val(response.data[0].TIN);
                } else {
                    Swal.fire('Error', 'There was an error getting the information.', 'error').then(() => {
                        $('#entityParentModal').modal('hide');
                    });
                }
            },
            error: (err) => {
                Swal.fire('Error', 'There was an error getting the information.', 'error').then(() => {
                    $('#entityParentModal').modal('hide');
                });
            },
        });
    } else {
        isEditParentEntity = false;
    }

    $("#parentEntityType").val(parentType);
    if (parentType === "ultimate") {
        $("#entitParentModalLbl").html(`${isEditParentEntity ? 'Edit' : 'New'} Ultimate Parent`);
    } else {
        $("#entitParentModalLbl").html(`${isEditParentEntity ? 'Edit' : 'New'} Immediate Parent`);
    }

});

$('#entityParentModal').on('hidden.bs.modal', function (event) {
    resetEntityParentForm = true;
    $("#submitParentEntity").prop('disabled', false);
    $("#parentJurisdiction").val(null).trigger('change');
    $("#parentEntityForm")[0].reset();
});

$(document).on('click', '.deleteParentEntity',async function () {
    await deleteParentEntity($(this).attr('data-id'), $(this).attr('data-type'),$(this).attr('data-parent-id'))
})


$('#parentEntityForm').on('submit', async function (event) {
    event.preventDefault();
    $("#submitParentEntity").prop('disabled', true);

    $("#parentJurisdiction").trigger('change');

    if (!this.checkValidity()) {
        this.classList.add('was-validated');
        $("#submitParentEntity").prop('disabled', false);
        return false;
    }

    const isValidForm = $("#parentEntityForm .is-invalid:visible").length;
    if (isValidForm > 0) {
        $("#submitParentEntity").prop('disabled', false);
        return false;
    }

    $('#submitParentEntity').hide();
    $('#loadingParentEntity').show();

    const responseSave = await saveEntityParent();

    $('#submitParentEntity').show();
    $('#loadingParentEntity').hide();

    if (responseSave) {
        refreshParentTable(entityId, responseSave.type);
        Swal.fire('Success', 'Entity parent saved successfully.', 'success').then(() => {
            $('#entityParentModal').modal('hide');
        });
    }
    $("#submitParentEntity").prop('disabled', false);
});

$("#parentJurisdiction").on("change", function () {
    const empty = $(this).val() === "";
    // Used for select2 selectors (country select)
    if(!resetEntityParentForm){
        $(`span[aria-labelledby='select2-${$(this).attr('id').replace('[', '').replace(']', '')}-container']`).toggleClass("is-invalid", empty);
        $(this).toggleClass("is-invalid", empty);
    }
    
});

async function saveEntityParent() {
    try {
        const parentEntity = {
            type: $("#parentEntityType").val(),
            parentName: $("#parentName").val(),
            alternativeName: $("#parentAlternativeName").val(),
            jurisdiction: $("#parentJurisdiction").val(),
            incorporationNumber: $("#parentIncorporationNumber").val(),
            TIN: $("#parentTIN").val(),
        };
        
        const response = await $.ajax({
            type: isEditParentEntity ? "PUT" : "POST",
            url: `./parent-entities/${isEditParentEntity ? `${parentEntityId}` : ''}`,
            data: JSON.stringify(parentEntity),
            dataType: "json",
            contentType: "application/json; charset=utf-8"
        });



        const status = response.status;
        if (status === 200) {
            return {type: response.type, parentEntity: response.parentEntity};
        } else {
            const error = data.error || 'Entity parent could not be saved, please try again later.';
            toastr["warning"](error, 'Error!');
            return false;
        }
    } catch (error) {
        toastr["warning"](error?.responseJSON?.error || 'Entity parent could not be saved, please try again later.', 'Error!');
        return false;

    }
}


async function deleteParentEntity(entryId, type, parentId) {
    
    $.ajax({
        type: "DELETE",
        url: `/substance/entry/${entryId}/parent-entities/${type}/${parentId}`,
        dataType: "json",
        success: function (response) {
            if (response.status === 200) {
                Swal.fire('Success', response.message ? response.message : 'Parent entity deleted successfully.', 'success');
                refreshParentTable(entryId, type);

            } else {
                Swal.fire('Error', response.error ? response.error : "Error deleting the parent entity", 'error');
            }
        },

    });
}

function  refreshParentTable(entryId, parentType){
    let template = Handlebars.templates.parententities;
    const queryString = $.param({ type: parentType });

    $.ajax({
        type: "GET",
        url: `/substance/entry/${entryId}/parent-entities?${queryString}`,
        success: (response) => {
            if (response.status === 200) {
                let rows = template({ 
                    entryId: entryId,
                    parentEntities: response.data, 
                    type: parentType
                });

                $(`#${parentType}ParentsTableBody`).html(rows);
            } else {
                toastr["error"]('There was an error getting the parent entities.', 'Error');
            }
        },
        error: (err) => {
            toastr["error"]('There was an error getting the parent entities.', 'Error');
        },
    });


}
