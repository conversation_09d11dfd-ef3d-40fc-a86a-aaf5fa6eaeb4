<table class="table table-striped" id="relationsTable">
    <thead>
        <tr>
            <th class="header-20-percent">Name</th>
            <th class="header-15-percent">Country</th>
            <th class="header-15-percent">Percentage</th>
            <th class="header-15-percent">Owned Type</th>
            <th class="header-25-percent">Relation type</th>
            <th class="header-5-percent"></th>
            <th class="header-5-percent"></th>
        </tr>
    </thead>
    <tbody>
        {{#each relations}}
            <tr class="relation-row">
                <td class="text-capitalize"> {{name}} </td>
                <td class="text-capitalize"> {{country}} </td>
                <td class="text-capitalize"> {{#if percentage}} {{percentage}}% {{else}} 0% {{/if}} </td>
                <td class="text-capitalize"> {{type}} </td>
                <td class="text-capitalize"> {{groups}} </td>
                <td>
                    <a href="/masterclients/{{../mcc}}/incorporate-company/{{../incorporationId}}/relations/{{relationId}}/edit"
                       class="btn btn-outline-secondary" id="edit-{{ relationId }}">
                        <i class="fa fa-pencil"></i>
                    </a>
                </td>
                <td>
                    <button type="button" class="delete btn btn-danger" id="delete-{{ relationId }}"
                            data-relation-id="{{ relationId }}" data-incorporation-id="{{ ../incorporationId  }}"
                            data-toggle="modal" data-target="#deleteRelationModal">
                        <i class="fa fa-trash"></i>
                    </button>
                </td>
            </tr>

        {{else}}
            <tr>
                <td colspan="9" class="text-center font-italic">
                    There are no relations
                </td>
            </tr>
        {{/each}}
    </tbody>
</table>

