const mongoose = require('mongoose');
const ObjectId = require('mongoose').Types.ObjectId;

const CompanyNameChangesSchema = new mongoose.Schema(
  {
    email: { type: String, required: true },
    originalName: { type: String, required: true },
    newName: { type: String, required: true },
    modulesChanged: [{ type: String, required: false }],
    changedAt: { type: Date, required: false },
  }
);
const CompanyCodeChangesSchema = new mongoose.Schema(
  {
    oldCode: { type: String, required: true },
    newCode: { type: String, required: true },
    changedAt: { type: Date, required: false },
  }
);

const substanceModuleSchema = new mongoose.Schema({
  active: { type: Boolean, required: true },
  hasITADate: { type: Boolean, required: false},
  approvedITAStartDate: { type: Date, required: false },
  approvedITAEndDate: { type: Date, required: false },
  applicationITADate: { type: Date, required: false },
  approval: {
    approved: { type: Boolean, required: false },
    date: { type: Date, required: false },
    by: { type: String, required: false },
  },

})

const reportedSchema = new mongoose.Schema({
  reportedBy: { type: String, required: false },
  isReported: { type: Boolean, required: false },
  reportedAt: { type: Date, required: false },
})

const deadlineRemindersSchema = new mongoose.Schema({
  reminderDay: { type: Number, required: false },
  description: { type: String, required: false },
  reminderDate: { type: Date, required: false },
  reminderDeadline: { type: Date, required: false },
  messageId: { type: ObjectId, required: false },
})


const accountingRecordsModuleSchema = new mongoose.Schema({
  active: { type: Boolean, required: false },
  selfServiceCompleteAnnualReturnAmount: { type: Number, required: false },
  selfServicePrepareAnnualReturnAmount: { type: Number, required: false },
  tridentServiceCompleteAnnualReturnAmount: { type: Number, required: false },
  tridentServiceDropAccountingRecordsAmount: { type: Number, required: false },
  firstFinancialPeriodStart: { type: Date, required: false },
  firstFinancialPeriodEnd: { type: Date, required: false },
  currentDeadline: { type: Date, required: false }, //used to set company IN PENALTY status
  currentFilingDeadline: { type: Date, required: false }, //used to send reminders on 90/60/30/-1 days interval
  compliantStatus: { type: String, required: false },
  isReported: { type: Boolean, required: false },
  deadlineReminders: [deadlineRemindersSchema],
  reportedHistory: [reportedSchema],
  inPenalty: { type: Boolean, required: false },
  currentPenaltyReport: { type: ObjectId, required: false },
  currentPenaltyAmount: { type: Number, required: false },
})

const dirboModuleSchema = new mongoose.Schema({
  active: { type: Boolean, required: true }
})

const CompanySchema = new mongoose.Schema({
  name: { type: String, required: true, max: 100 },
  address: { type: String, required: false, max: 100 },
  code: { type: String, required: true, max: 100 },
  incorporationcode: { type: String, required: true, max: 100 },
  incorporationdate: { type: Date, required: false },
  masterclientcode: { type: String, required: true, max: 100 },
  riskgroup: { type: String, required: false, max: 100 },
  filereviews: [{ type: mongoose.Schema.Types.ObjectId, ref: 'filereview' }],
  company_type: { type: String, required: false, max: 100 },
  referral_office: { type: String, required: true, max: 100 },
  amount: { type: Number, required: false },
  partitionkey: { type: String, required: true, default: "company" },
  // Deprecated, use module active flag instead (dirboModule, substanceModule, etc.)
  isDeleted: { type: Boolean, required: false, default: false },
  deletedAt: { type: Date, required: false },
  companyNameChanges: [CompanyNameChangesSchema],
  companyCodeChanges: [CompanyCodeChangesSchema],
  hasITADate: { type: Boolean, required: false, default: false },
  approvedITAStartDate: { type: Date, required: false },
  approvedITAEndDate: { type: Date, required: false },
  applicationITADate: { type: Date, required: false },
  paymentYears: [String],
  vpMasterFileCode: { type: String, required: false },
  entityStatus: { type: String, required: false },
  entitySubStatus: { type: String, required: false },
  entityStatusLabel: { type: String, required: false },
  productionOffice: { type: String, required: false },
  mccCpMasterFileCode: { type: String, required: false },
  substanceModule: substanceModuleSchema,
  accountingRecordsModule: accountingRecordsModuleSchema,
  dirboModule: dirboModuleSchema,

});


//Export model
module.exports.schema = mongoose.model('Company', CompanySchema);
module.exports.model = CompanySchema;
