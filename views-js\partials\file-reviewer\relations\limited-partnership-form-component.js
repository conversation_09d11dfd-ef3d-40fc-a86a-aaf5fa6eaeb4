$('input[name="limited[isSamePrincipalAddress]"]').on('change', function () {
    const value = $(this).val() === 'YES';
    if (value){
        $('#limitedPartnershipForm #mailingAddressDetails').hide(200);
    }
    else{
        $('#limitedPartnershipForm #mailingAddressDetails').show(200);
    }
});

$('input[name="limited[isTridentClient]"]').on('change', function () {
    const value = $(this).val() === 'YES';
    if (value){
        $('#limitedPartnershipForm .isTridentClient').hide(200);
        $('#shareholderAdditionalForm').hide();
    }
    else{
        $('#limitedPartnershipForm .isTridentClient').show(200);

        if ($("#shareholderType").is(':checked')){
            $('#shareholderAdditionalForm').show(200);
        }
    }
});