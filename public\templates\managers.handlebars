{{#ifCond version '<' 5}}

    {{#each data.managers}}
        <tr>
            <td>{{first_name}} {{middle_name}} {{last_name}}</td>
            <td>{{formatDate date_of_birth}}</td>
            <td>{{formatAddress this}}</td>
            <td>{{#if resident_in_bvi}}Yes{{else}}No{{/if}}</td>
            <td>{{position_held}}</td>
            <td class="text-center"><i class="fa fa-pencil  btn btn-sm royal-blue solid  editmanager" data-id="{{_id}}"></i></td>
            <td class="text-center"><i class="fa fa-times  btn btn-sm btn-danger deletemanager"  data-id="{{_id}}"></i></td>
        </tr>
    {{/each}}
{{else}}
    {{#each data.managers}}
    <tr>
        <td>{{first_name}} {{middle_name}} {{last_name}}</td>
        <td>{{#if is_corporate_director}}Yes {{else}} No {{/if}} </td>
        <td>{{#if resident_in_bvi}}Yes{{else}}No{{/if}}</td>
        <td>{{position_held}}</td>
        <td class="justify-content-center d-flex d-flex-inline">
            <button type="button" class="btn btn-sm royal-blue solid mr-1 editmanager" data-id="{{_id}}">
                <i class="fa fa-pencil"></i>
            </button>
            <button type="button" class="btn btn-sm btn-danger deletemanager"  data-id="{{_id}}">
                <i class="fa fa-times"></i>
            </button>
        </td>
    </tr>
    {{else}}
        <tr>
            <td colspan="5">
                No directors found
            </td>
        </tr>>
    {{/each}}
{{/ifCond}}
