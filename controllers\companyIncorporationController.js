const httpConstants = require('http2').constants;
const MasterClientCode = require('../models/masterClientCode');
const CompanyIncorporationModel = require('../models/clientIncorporation');
const NaturalRelationModel = require('../models/naturalperson');
const OrganizationRelationModel = require('../models/organization');
const ConfigModel = require('../models/config');
const ConfigurationModel = require('../models/configuration');
const pdfController = require('../controllers/pdfController');
const uploadController = require('../controllers/uploadController');
const idPalController = require('../controllers/idPalController');
const mailController = require('../controllers/mailController');
const mailFormatter = require('../controllers/mailFormatController');
const incorporationValidator = require('../validators/incorporationValidator').validateIncorporation;
const relationValidator = require('../validators/relationValidator').validateRelation;
const invoiceGenerator = require('../utils/invoiceGenerator');
const moment = require('moment');
const { v4: uuidv4 } = require('uuid');
const ObjectId = require('mongoose').Types.ObjectId;
const verifyExtFile = require('../utils/verifyExtensionFile');
const sessionUtils = require('../utils/sessionUtils');

exports.incorporate_company = async function (req, res, next) {
    try {
        let incorporations = await CompanyIncorporationModel.find({
            masterClientCode: req.params.masterclientcode,
        }, {
            '_id': 1,
            'name': 1,
            'status': 1,
            'updatedAt': 1,
            'incorporationStatus': 1,
            'nameReservationStatus': 1
        }).limit(1000);


        let notStarted = false;
        let pendingPayment = false;
        if (incorporations.length > 0) {
            incorporations.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
        }

        if (incorporations.find((incorporation) => incorporation.status === 'NOT STARTED')) {
            notStarted = true;
        }
        if (incorporations.find((incorporation) => incorporation.status === 'SUBMITTED')) {
            pendingPayment = true;
        }
        res.render('incorporate-company/dashboard', {
            title: 'Incorporate a new entity',
            user: req.user,
            messages: req.session.messages,
            masterclientcode: req.params.masterclientcode,
            incorporations,
            notStarted,
            pendingPayment
        });
    }catch(e){
        console.log(e);
        next(e);
    }

};

exports.incorporate_company_form = async function (req, res, next) {
    try {
        const newCompanyIncorporation = new CompanyIncorporationModel({
            masterClientCode: req.params.masterclientcode,
            status: "NOT STARTED",
            incorporationStatus: "NOT STARTED",
            createdAt: new Date(),
            createdBy: req.user.email,
            files: {
                structureChartFiles: [],
                passportFiles: [],
                addressProofFiles: [],
                otherDeclarationFiles: []
            }
        });
        await newCompanyIncorporation.save();
        res.redirect(`${newCompanyIncorporation._id}`);
    }catch(e){
        console.log(e);
        next(e);
    }

};

exports.load_incorporate_company_form = async function (req, res, next) {
    try {
        const incorporation = await CompanyIncorporationModel.findOne({ '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });

        const configTemplate = await ConfigModel.findOne({});
        if (incorporation.status === 'SUBMITTED' || incorporation.status === 'PAID' || incorporation.status === 'COMPLETED') {
            res.render('error', { message: 'This submission can not longer be edited' });
        } else {
            res.render('incorporate-company/form',
                {
                    title: 'Incorporate a new entity',
                    user: req.user,
                    messages: req.session.messages,
                    masterclientcode: req.params.masterclientcode,
                    incorporationId: req.params.incorporationId,
                    relation: configTemplate.relationFiles,
                    newRelation: true,
                    cantEditName: incorporation.nameReservationStatus === "IN PROGRESS" ||
                        incorporation.nameReservationStatus === "APPROVED" ||
                        incorporation.nameReservationStatus === "DECLINED" ||
                        incorporation.nameReservationStatus === "IN REVIEW",
                    showRelations: req.query.relations,
                    incorporation
                });
        }
    } catch (e) {
        console.log(e);
        next(e);
    }


};


exports.save_incorporate_form = async function (req, res) {

    try {
        let newValues;
        let updatedValues;
        const currentIncorporation = await CompanyIncorporationModel.findOne({ '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });
        const errors = incorporationValidator(req.body, currentIncorporation);

        if (errors.length) {
            return res.json({ status: httpConstants.HTTP_STATUS_BAD_REQUEST, errors });
        }
        newValues = {
            version: currentIncorporation.version === '1.0' ? '2.0': currentIncorporation.version,
            masterClientCode: req.params.masterclientcode,
            status: req.body.isCompleted ? "SUBMITTED" : "IN PROGRESS",
            submittedAt: req.body.isCompleted ? new Date() : null,
            // Step 1
            name: req.body.entityName,
            type: req.body.entityType,
            typeSpecialInstructions: req.body.specialInstructions,
            partOfGroup: req.body.partOfGroupControl === 'Yes',
            // Step 3
            principalBusinessActivity: req.body.principalBusinessActivity,
            principalBusinessActivityOther: req.body.principalBusinessActivityOther,
            activityJurisdiction: req.body.activityJurisdiction,
            taxResidence: req.body.taxResidenceControl,
            taxResidencyJurisdiction: req.body.taxResidencyJurisdiction,
            // Step 4
            ownAssets: req.body.companyOwnAssetsControl === 'Yes',
            estimated_value_of_assets : (req.body.EstimatedValueOfAssets ? req.body.EstimatedValueOfAssets.split(',').join('') : undefined),
            bankAccountOwner: req.body.bankAccountOwnerControl,
            bankName: req.body.bankName,
            bankAddress: req.body.bankAddress,
            // Step 5
            records: req.body.records,
            // Step 6
            requestAdditionalServices: req.body.additionalServicesControl === 'Yes',
            additionalServices: req.body.additionalServices,
            sibaLicence: req.body.sibaLicenceType,
            trustLicence: req.body.trustLicenceType,
            otherServices: req.body.otherServicesDetails,
            // Step 7
            declaration: {
                // Declarations 1,2 and 3
                information: req.body.declarationInformation === 'on',
                assets: req.body.declarationAssets === 'on',
                termsAndConditions: req.body.declarationTermsAndConditions === 'on',
                name: req.body.declarationName,
                date: req.body.declarationDate,
                relationToEntity: req.body.declarationRelation,
                relationToEntityOther: req.body.declarationRelationOther,
                phone: req.body.declarationPhoneNumber,
            },
        };

        if (req.body.approvedName === false) {
            newValues.approvedName = false;
        }

        if (req.body.nameReservationStatus) {
            newValues.nameReservationStatus = req.body.nameReservationStatus;
        }

        if (req.body.isCompleted) {
            newValues.submittedBy = req.user.email;
            const configuration = await ConfigurationModel.findOne({});
            const masterClient = await MasterClientCode.findOne({ code: req.params.masterclientcode });

            if (masterClient.customIncorporationPayment) {
                newValues.payment = {
                    ...newValues.payment,
                    fees: masterClient.incorporation.fees,
                    disbursements: masterClient.incorporation.disbursements,
                    total:  masterClient.incorporation.disbursements.reduce((prev, cur) => prev + (cur.value), 0) +
                      masterClient.incorporation.fees.reduce((prev, cur) => prev + (cur.value), 0)
                }
            } else {
                newValues.payment = {
                    ...newValues.payment,
                    fees: configuration.incorporation.fees,
                    disbursements: configuration.incorporation.disbursements,
                    total: configuration.incorporation.disbursements.reduce((prev, cur) => prev + (cur.value), 0) +
                      configuration.incorporation.fees.reduce((prev, cur) => prev + (cur.value), 0)
                };
            }
            newValues.invoiceNumber = await  invoiceGenerator.generateInvoiceNumber(configuration, 'incorporation');


            if(currentIncorporation.relations && currentIncorporation.relations.length > 0){
                let relations = currentIncorporation.relations;
                for (let i = 0; i < relations.length; i++) {
                    if (relations[i].type.toLowerCase() === "natural"){
                        const electronicIdInfo = relations[i].electronicIdInfo;
                        if (electronicIdInfo && electronicIdInfo.isElectronicId && !electronicIdInfo.electronicIdInvitationSent){
                            if(!electronicIdInfo.comments){
                                electronicIdInfo.comments = [];
                            }
                            if (electronicIdInfo.email){
                                const palResponse = await idPalController.generateUuid(relations[i]._id.toString());
                                if (palResponse && palResponse.status === 200){
                                    // send invitation
                                    const url = process.env.SUBSTANCE_APP_HOST + "/idpal?uuid=" + palResponse.uuid;
                                    let email = mailFormatter.generateIdPalEmail(url);
                                    let sentEmailResult = await mailController.asyncSend(
                                      electronicIdInfo.email,
                                      newValues.name + ' - ID Verification',
                                      email.textString,
                                      email.htmlString
                                    );
                                    if (electronicIdInfo.comments == null) {
                                        electronicIdInfo.comments = [];
                                    }
                                    if (!currentIncorporation.pendingElectronicIds) {
                                        currentIncorporation.pendingElectronicIds = true;
                                    }
                                    electronicIdInfo.uuid =palResponse.uuid;
                                    if (sentEmailResult.accepted) {

                                        electronicIdInfo.electronicIdInvitationSent = true;
                                        electronicIdInfo.status = "IN PROGRESS";
                                        electronicIdInfo.invitationDate = new Date();
                                    }
                                    else{
                                        electronicIdInfo.electronicIdInvitationSent = false;
                                        electronicIdInfo.status = "NOT SENT";
                                        electronicIdInfo.comments.push({
                                            username: req.user.email,
                                            comment: "Error sending email invitation",
                                            date: new Date(),
                                            status: electronicIdInfo.status,
                                        });
                                    }
                                }
                                else{
                                    electronicIdInfo.electronicIdInvitationSent = false;
                                    electronicIdInfo.status = "NOT SENT";
                                    electronicIdInfo.comments.push({
                                        username: req.user.email,
                                        comment: "Error sending invitation: " + (palResponse.message && palResponse.message.error ?
                                          palResponse.message.error : palResponse.message) ,
                                        date: new Date(),
                                        status: electronicIdInfo.status,
                                    });
                                }

                            }
                            else{
                                electronicIdInfo.electronicIdInvitationSent = false;
                                electronicIdInfo.status = "NOT SENT";
                                electronicIdInfo.comments.push({
                                    username: req.user.email,
                                    comment: "Email not found to send id-pal invitation",
                                    date: new Date(),
                                    status: electronicIdInfo.status,
                                });
                            }
                            relations[i].electronicIdInfo = electronicIdInfo;

                        }
                    }
                }
                currentIncorporation.relations = relations;
                currentIncorporation.markModified('relations');
                await currentIncorporation.save();
            }
        }

        updatedValues = await CompanyIncorporationModel.findOneAndUpdate({ '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode },
            newValues, { upsert: true, new: true, setDefaultsOnInsert: true })


        return res.json({ status: updatedValues ? httpConstants.HTTP_STATUS_OK : httpConstants.HTTP_STATUS_BAD_REQUEST });

    } catch (e) {
        console.log("E ", e);
        return res.json({ status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: "Internal Server Error" });
    }
};
exports.delete_incorporate_company = async function (req, res) {
    try {
        const incorporation = await CompanyIncorporationModel.findOne({ '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });
        if (!incorporation) {
            return res.json({ status: httpConstants.HTTP_STATUS_NOT_FOUND, message: "Submission not found" });
        }
        if (incorporation.status === 'NOT STARTED' || incorporation.status === 'IN PROGRESS') {
            await CompanyIncorporationModel.findByIdAndDelete(req.params.incorporationId);
            return res.json({ status: httpConstants.HTTP_STATUS_OK });
        } else {
            return res.json({ status: httpConstants.HTTP_STATUS_BAD_REQUEST, message: "This incorporation submission can't be deleted" });
        }
    } catch (e) {
        console.log("E ", e);
        return res.json({ status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: "Internal Server Error" });
    }
};

exports.get_incorporation_request_view = async function (req, res, next) {
    try {
        req.session.requestFiles = [];
        const incorporation = await CompanyIncorporationModel.findOne({
              '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode },
            { _id: 1, name: 1, requestInformation: 1, clientReturnedInformation: 1 });
        if (!incorporation) {
            const err = new Error('File Review not found');
            err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
            return next(err);
        }
        let requestedFiles = [];
        let providedFiles = [];
        let requestedQuestions = [];
        if (incorporation.requestInformation && incorporation.requestInformation.length > 0){
            incorporation.requestInformation.forEach((r) => {
                requestedQuestions.push({
                    requestedAt: r.requestedAt,
                    comment: r.comment,
                    answer: r.answer ? r.answer : ''
                });
                if (r.files && r.files.length > 0){
                    r.files.forEach((f) => {
                        f.requestId = r._id.toString();
                        f.requestedAt = r.requestedAt;
                        requestedFiles.push(f);
                    })
                }
            })
        }
        if (incorporation.clientReturnedInformation && incorporation.clientReturnedInformation.length > 0){
            incorporation.clientReturnedInformation.forEach((r) => {
                if (r.files && r.files.length > 0){
                    r.files.forEach((f) => {
                        f.fileTypeId = r._id.toString();
                        f.returnedAt = r.returnedAt;
                        providedFiles.push(f);
                    })
                }
            })
        }

        requestedQuestions.sort((a,b) => {
            return new Date(b.requestedAt) - new Date(a.requestedAt);
        });

        requestedFiles.sort((a,b) => {
            return new Date(b.requestedAt) - new Date(a.requestedAt);
        });

        providedFiles.sort((a,b) => {
            return new Date(b.returnedAt) - new Date(a.returnedAt);
        });

        res.render('incorporate-company/request-information-view',
            {
                title: 'Request information:',
                user: req.user,
                messages: req.session.messages,
                masterClientCode: req.params.masterclientcode,
                incorporationId: req.params.incorporationId,
                incorporationName: incorporation.name,
                requestInformation: requestedQuestions,
                requestedFiles: requestedFiles,
                providedFiles: providedFiles
            });


    } catch (error) {
        console.log("error: ", error);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
    }
};

exports.get_incorporation_approved_view = async function (req, res, next) {
    try {
        const incorporation = await CompanyIncorporationModel.findOne(
          {'_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode},
          { _id: 1, name: 1, companyCode: 1, incorporationNr:1, files:1 });

        if (!incorporation) {
            const err = new Error('File Review not found');
            err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
            return next(err);
        }
        res.render('incorporate-company/approved-incorporation-view',
          {
              title: 'Request information:',
              user: req.user,
              messages: req.session.messages,
              masterClientCode: req.params.masterclientcode,
              incorporation: incorporation
          });
    } catch (error) {
        console.log("error: ", error);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
    }
};

exports.submit_incorporation_request = async function (req, res, next) {
    try {
        const incorporation = await CompanyIncorporationModel.findOne({
            '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });
        if (!incorporation) {
            const err = new Error('File Review not found');
            err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
            return next(err);
        }
        let uploadedFiles = [];

        if (req.session.requestFiles && req.session.requestFiles.length > 0) {
            const fileTypeId = uuidv4();
            const fileName = "Client upload";

            uploadedFiles = req.session.requestFiles.map((file) => {
                uploadController.moveIncorporationUpload(req.params.incorporationId, file, fileName).catch((reason => {
                    if (reason) {
                        console.log(reason);
                        const err = new Error('Error uploading the document files');
                        err.status = httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR;
                        return next(err);
                    }
                }));
                return {
                    fileId: uuidv4(),
                    fileTypeId: fileTypeId,
                    fieldName: file.fieldname.replace(/fileUploaded/i, fileName),
                    blob: file.blob.replace(/fileUploaded/i, fileName),
                    blobName: file.blobName.replace(/fileUploaded/i, fileName),
                    url: file.url.replace(/fileUploaded/i, req.params.incorporationId + '/' + fileName),
                    originalName: file.originalname,
                    encoding: file.encoding,
                    mimeType: file.mimetype,
                    container: file.container,
                    blobType: file.blobType,
                    size: file.size,
                    etag: file.etag
                };
            });

        }

        incorporation.clientReturnedInformation.push({
            username: req.user.username,
            returnedAt: new Date(),
            comment: req.body.clientAnswer,
            files: uploadedFiles
        });

        const lastRequestInfo = incorporation.requestInformation[incorporation.requestInformation.length -1];
        if (lastRequestInfo){
            lastRequestInfo.answer = req.body.clientAnswer;
            incorporation.requestInformation[incorporation.requestInformation.length -1] = lastRequestInfo;
        }
        incorporation.incorporationStatus = "INFORMATION PROVIDED";
        incorporation.lastReturnedInformationDate = new Date();

        let email = mailFormatter.generateClientProvidedInformationEmail(incorporation.masterClientCode);
        await mailController.asyncSend(
          incorporation.user.username,
          'Trident Trust Client Notification',
          email.textString,
          email.htmlString
        );


        await incorporation.save();
        res.redirect('/masterclients/' + incorporation.masterClientCode + '/incorporate-company');

    } catch (error) {
        console.log("error: ", error);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
    }
};

exports.save_incorporation_files = async function (req, res, next) {
    try {
        const incorporation = await CompanyIncorporationModel.findOne({ '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });
        if (!incorporation) {
            console.log("error not found");
            const err = new Error('File Review not found');
            err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
            return next(err);
        }

        let uploadedFiles = req.files['fileUploaded'];

        if (uploadedFiles && uploadedFiles.length > 0) {
            uploadedFiles = uploadedFiles.map((itemToUpload) => {
                if (itemToUpload.mimetype !== 'application/pdf' ||
                    !verifyExtFile.isValidExtensionFile(itemToUpload.originalname) ||
                    !verifyExtFile.isValidExtensionFile(itemToUpload.blob) ||
                    !verifyExtFile.isValidExtensionFile(itemToUpload.blobName)
                ) {
                    console.log("Incorrect file type");
                    const err = new Error('Incorrect file type');
                    err.status = httpConstants.HTTP_STATUS_BAD_REQUEST;
                    return next(err);
                }
                uploadController.moveIncorporationUpload(req.params.incorporationId, itemToUpload, req.body.fieldName).catch((reason => {
                    if (reason) {
                        console.log(reason);
                        const err = new Error('Error uploading the document files');
                        err.status = httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR;
                        return next(err);
                    }
                }));
                return {
                    fileId: uuidv4(),
                    fieldName: itemToUpload.fieldname.replace(/fileUploaded/i, req.body.fieldName),
                    blob: itemToUpload.blob.replace(/fileUploaded/i, req.body.fieldName),
                    blobName: itemToUpload.blobName.replace(/fileUploaded/i, req.body.fieldName),
                    url: itemToUpload.url.replace(/fileUploaded/i, req.params.incorporationId + '/' + req.body.fieldName),
                    originalName: itemToUpload.originalname,
                    encoding: itemToUpload.encoding,
                    mimeType: itemToUpload.mimetype,
                    container: itemToUpload.container,
                    blobType: itemToUpload.blobType,
                    size: itemToUpload.size,
                    etag: itemToUpload.etag
                };
            });

            if (!incorporation.files) {
                incorporation.files = {
                    structureChartFiles: [],
                    passportFiles: [],
                    addressProofFiles: [],
                    otherDeclarationFiles: []
                }
            }

            if (req.body.fieldName === 'structureChartFiles') {
                incorporation.files.structureChartFiles = [...incorporation.files.structureChartFiles, ...uploadedFiles];
            }
            else if (req.body.fieldName === 'passportFiles') {
                incorporation.files.passportFiles = [...incorporation.files.passportFiles, ...uploadedFiles];
            }
            else if (req.body.fieldName === 'addressProofFiles') {
                incorporation.files.addressProofFiles = [...incorporation.files.addressProofFiles, ...uploadedFiles];
            }
            else if (req.body.fieldName === 'otherDeclarationFiles') {
                incorporation.files.otherDeclarationFiles = [...incorporation.files.otherDeclarationFiles, ...uploadedFiles];
            }
            await incorporation.save();
        }
        return res.json({ result: true });

    } catch (error) {
        console.log("error: ", error);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
    }
};

exports.get_incorporation_files = async function (req, res, next) {
    try {
        const incorporation = await CompanyIncorporationModel.findOne({ '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });
        if (!incorporation) {
            console.log("error not found");
            const err = new Error('File Review not found');
            err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
            return next(err);
        }
        let filesToReturn = [];

        if (incorporation.files) {
            const files = incorporation.files[req.query.fieldName];
            filesToReturn = files && files.length > 0 ? files : [];

        }
        return res.json({ result: true, files: filesToReturn });

    } catch (error) {
        console.log("error: ", error);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
    }
};


exports.get_name_reservation_info = async function (req, res) {
    try {
        const incorporation = await CompanyIncorporationModel.findOne({ '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode },
            { _id: 1, name: 1, nameReservationStatus: 1, nameReservationInfo: 1 });
        if (!incorporation) {
            console.log("error not found");
            return res.json({ status: httpConstants.HTTP_STATUS_NOT_FOUND, message: 'Company not found' });
        }
        return res.json({ status: httpConstants.HTTP_STATUS_OK, data: incorporation });

    } catch (error) {
        console.log("error: ", error);
        return res.json({ status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: 'Internal server error' });
    }
};

exports.update_name_reservation_info = async function (req, res) {
    try {
        const incorporation = await CompanyIncorporationModel.findOne({ '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode },
            { _id: 1, name: 1, nameReservationStatus: 1, nameReservationInfo: 1, incorporationStatus: 1 });
        if (!incorporation) {
            console.log("error not found");
            return res.json({ status: httpConstants.HTTP_STATUS_NOT_FOUND, message: 'Company not found' });
        }

        if (req.body.suggestedName !== "other") {
            incorporation.name = req.body.suggestedName;
            if (incorporation.incorporationStatus === "NAME DECLINED"){
                incorporation.incorporationStatus = "IN REVIEW";
            }
            incorporation.nameReservationStatus = "APPROVED";
            incorporation.nameReservationInfo = {
                approved: true,
                approvedName: req.body.suggestedName,
                approvedAt: new Date(),
            }
        }
        else {
            incorporation.name = req.body.otherName;
            incorporation.incorporationStatus = "NAME REVIEW";
            incorporation.nameReservationStatus = "IN PROGRESS";
        }
        await incorporation.save();
        return res.json({ status: httpConstants.HTTP_STATUS_OK, message: 'Success' });

    } catch (error) {
        console.log("error: ", error);
        return res.json({ status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: 'Internal server error' });
    }
};

exports.get_decline_reason_info = async function (req, res) {
    try {
        const incorporation = await CompanyIncorporationModel.findOne({'_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode},
          { _id: 1, name: 1, comments: 1});
        if (!incorporation) {
            console.log("error not found");
            return res.json({ status: httpConstants.HTTP_STATUS_NOT_FOUND, message: 'Company not found' });
        }

        let reasonComment;
        if (incorporation.comments && incorporation.comments.length > 0){
            const declinedComments = incorporation.comments.filter((c) => c.status === "DECLINED");

            if (declinedComments) {
                declinedComments.sort(function (a, b) {
                    return new Date(b.date) - new Date(a.date);
                });
                reasonComment = {
                    declinedAt: moment(declinedComments[0].date).format('YYYY-MM-DD'),
                    reason: declinedComments[0].comment
                }
            }
        }
        return res.json({ status: httpConstants.HTTP_STATUS_OK, data: reasonComment });

    } catch (error) {
        console.log("error: ", error);
        return res.json({ status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: 'Internal server error' });
    }
};


exports.delete_incorporation_files = async function (req, res, next) {
    try {
        const incorporation = await CompanyIncorporationModel.findOne({ '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });
        if (!incorporation) {
            const err = new Error('File Review not found');
            err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
            return next(err);
        }
        if (incorporation.files) {
            const files = incorporation.files[req.body.fieldName];
            if (files) {
                const fileIndex = files.findIndex((f) => f.fileId && f.fileId.toString() === req.body.fileId);
                if (fileIndex > -1) {
                    files.splice(fileIndex, 1);
                    incorporation.files[req.body.fieldName] = files;
                    await incorporation.save();
                }

            }
        }
        return res.json({ result: true });

    } catch (error) {
        console.log("error: ", error);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
    }
};

exports.downloadIncorporationPdf = async function (req, res) {
    try {
        const incorporation = await CompanyIncorporationModel.findOne({ '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });
        pdfController.generateIncorporationPdf(incorporation, res);
    } catch (error) {
        console.log("E ", error);
        return res.render('error', { status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: "Internal Server Error" });
    }
};

exports.downloadIncorporationInvoicePdf = async function (req, res) {
    try {
        const incorporation = await CompanyIncorporationModel.findOne({ '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });
        pdfController.generateIncorporationInvoicePdf(incorporation, res);
    } catch (error) {
        console.log("E ", error);
        return res.render('error', { status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: "Internal Server Error" });
    }
};

exports.incorporation_add_asset = async function (req, res) {
    try {
        const incorporation = await CompanyIncorporationModel.findOne({ '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });
        incorporation.assets.push(req.body);
        incorporation.ownAssets = true;
        await incorporation.save();
        return res.json({ status: httpConstants.HTTP_STATUS_OK, asset: incorporation.assets[incorporation.assets.length - 1] });
    } catch (error) {
        console.log("E ", error);
        return res.render('error', { status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: "Internal Server Error" });
    }
};

exports.incorporation_delete_asset = async function (req, res) {
    try {
        const incorporation = await CompanyIncorporationModel.findOne({ '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });
        incorporation.assets = incorporation.assets.filter((asset) => asset._id.toString() !== req.params.id);
        incorporation.ownAssets = true;
        await incorporation.save();
        return res.json({ status: httpConstants.HTTP_STATUS_OK });
    } catch (error) {
        console.log("E ", error);
        return res.render('error', { status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: "Internal Server Error" });
    }
};

exports.incorporation_get_asset = async function (req, res) {
    try {
        const incorporation = await CompanyIncorporationModel.findOne({ '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });
        const asset = incorporation.assets.find((asset) => asset._id.toString() == req.params.id);

        return res.json({ status: httpConstants.HTTP_STATUS_OK, asset });
    } catch (error) {
        console.log("E ", error);
        return res.render('error', { status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: "Internal Server Error" });
    }
};

exports.incorporation_edit_asset = async function (req, res) {
    try {
        const editedAsset = req.body;
        const incorporation = await CompanyIncorporationModel.findOne({ '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });
        incorporation.assets = incorporation.assets.map((asset) => {
            if (asset._id.toString() === req.params.id) {
                editedAsset._id = asset._id;
                return editedAsset;
            } else {
                return asset;
            }
        });
        incorporation.ownAssets = true;
        await incorporation.save();
        return res.json({ status: httpConstants.HTTP_STATUS_OK, asset: editedAsset });
    } catch (error) {
        console.log("E ", error);
        return res.render('error', { status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: "Internal Server Error" });
    }
};

exports.incorporation_add_fund = async function (req, res) {
    try {
        const incorporation = await CompanyIncorporationModel.findOne({ '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });
        incorporation.funds.push(req.body);
        incorporation.ownAssets = true;
        await incorporation.save();
        return res.json({ status: httpConstants.HTTP_STATUS_OK, fund: incorporation.funds[incorporation.funds.length - 1] });
    } catch (error) {
        console.log("E ", error);
        return res.render('error', { status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: "Internal Server Error" });
    }
};

exports.incorporation_delete_fund = async function (req, res) {
    try {
        const incorporation = await CompanyIncorporationModel.findOne({ '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });
        incorporation.funds = incorporation.funds.filter((fund) => fund._id.toString() !== req.params.id);
        incorporation.ownAssets = true;
        await incorporation.save();
        return res.json({ status: httpConstants.HTTP_STATUS_OK });
    } catch (error) {
        console.log("E ", error);
        return res.render('error', { status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: "Internal Server Error" });
    }
};

exports.incorporation_get_fund = async function (req, res) {
    try {
        const incorporation = await CompanyIncorporationModel.findOne({ '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });
        const fund = incorporation.funds.find((fund) => fund._id.toString() == req.params.id);

        return res.json({ status: httpConstants.HTTP_STATUS_OK, fund });
    } catch (error) {
        console.log("E ", error);
        return res.render('error', { status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: "Internal Server Error" });
    }
};

exports.incorporation_edit_fund = async function (req, res) {
    try {
        const editedFund = req.body;
        const incorporation = await CompanyIncorporationModel.findOne({ '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });
        incorporation.funds = incorporation.funds.map((fund) => {
            if (fund._id.toString() === req.params.id) {
                editedFund._id = fund._id;
                return editedFund;
            } else {
                return fund;
            }
        });
        incorporation.ownAssets = true;
        await incorporation.save();
        return res.json({ status: httpConstants.HTTP_STATUS_OK, fund: editedFund });
    } catch (error) {
        console.log("E ", error);
        return res.render('error', { status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: "Internal Server Error" });
    }
};


exports.ensureAuthenticated = function (req, res, next) {
    if ((req.user && req.session.id == req.user.sessionId) && req.session.auth2fa) {
        const sessData = req.session;
        //check if compancode in session is the same as the company code in the url
        if (!req.params.companyCode || req.params.companyCode == sessData.company.code) {
            next();
        } else {
            req.logout(function (err) {
                if (err) { return next(err) }
                req.session.destroy(function () {
                    // cannot access session here
                    sessionUtils.onSessionDestroyed(req, res);
                });
            });
        }
    } else if ((req.user && req.session.id == req.user.sessionId) && !req.session.auth2fa) {
        if (req.user.secret_2fa) {
            res.redirect('/users/2fa-code');
        } else {
            res.redirect('/users/2fa-setup');
        }
    } else {
        req.logout(function (err) {
            if (err) { return next(err) }
            req.session.destroy(function () {
                // cannot access session here
                sessionUtils.onSessionDestroyed(req, res);
            });
        });
    }
};


exports.validateMasterClient = async function (req, res, next) {
    try{
        const masterclient = await MasterClientCode.findOne({ 'code': req.params.masterclientcode });
        if (masterclient == null || masterclient.owners.indexOf(req.user.email.toLowerCase()) == -1) {
            let err = new Error('Masterclient not found');
            err.status = 404;
            return next(err);
        }
        return next();
    }catch(e){
        console.log(e);
        next(e);
    }

}

exports.get_relation = async function (req, res) {
    try {
        const incorporation = await CompanyIncorporationModel.findOne({ '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });

        if (!incorporation) {
            return res.json({ status: 404, message: "Company Incorporation not found" });
        }
        const relation = incorporation.relations.find((r) =>
            r && r._id.toString() === req.params.relationId);

        if (!relation) {
            return res.json({ status: 404, message: "Relation information not found" });
        }

        return res.json({
            status: 200, relation: relation,
            masterClientCode: incorporation.masterClientCode,
            relationInformation: relation,
            incorporationId: incorporation._id
        });

    } catch (e) {
        console.log("error getting relation ", e);
        return res.json({ status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: "Internal Server Error" });
    }
};


exports.get_edit_relation = async function (req, res) {
    try {
        clear_temp_files(req);
        const incorporation = await CompanyIncorporationModel.findOne({ '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });


        if (!incorporation) {
            return res.redirect('/');
        }

        const relation = incorporation.relations.find((r) => r._id && r._id.toString() === req.params.relationId);

        if (!relation) {
            return res.redirect('/');
        }

        res.render('incorporate-company/relation-view', {
            user: req.user,
            messages: req.session.messages,
            masterClientCode: incorporation.masterClientCode,
            incorporationId: incorporation._id,
            relation: relation,
            newRelation: false,
        });

    } catch (e) {
        console.log("error getting relation ", e);
        return res.redirect('/');
    }
};

exports.create_relation = async function (req, res) {
    try {
        const configTemplate = await ConfigModel.findOne({});
        const companyIncorporation = await CompanyIncorporationModel.findOne({ '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });
        if (!companyIncorporation) {
            return res.json({ status: httpConstants.HTTP_STATUS_NOT_FOUND, message: "Company incorporation not found" });
        }

        const errors = relationValidator(req.body);

        if (errors.length) {
            return res.json({ status: httpConstants.HTTP_STATUS_BAD_REQUEST, errors });
        }


        let relation;
        let type = req.body.relationType === "natural" ? 'natural' : req.body.ownerShip ? req.body.ownerShip : 'corporate';
        const fileGroupName = type + "Files";
        const fileGroupFiles = configTemplate.relationFiles[fileGroupName];
        if (type === "natural") {
            relation = new NaturalRelationModel({});
        } else {
            relation = new OrganizationRelationModel({});
        }
        if (relation) {
            const updatedRelation = set_relation_data(req.params.incorporationId, relation, type,
                fileGroupFiles, req.body, req.session);
            relation = Object.assign(relation, updatedRelation);
            companyIncorporation.relations.push(relation);
            await companyIncorporation.save();
            req.session.relationFiles = {};

            const relationsInfo = companyIncorporation.relations.map((r) => {
                return {
                    type: r.type,
                    groups: r.groups,
                    relationId: r._id,
                    percentage: r.percentage ? r.percentage : '',
                    name: r.type !== 'natural' ? r.details.organizationName :
                      r.details.middleName ?  r.details.firstName + " " + r.details.middleName + " "  +  r.details.lastName :
                        r.details.firstName + " "  +  r.details.lastName,
                    country: r.type === 'natural' ? r.details.countryBirth : r.details.incorporationCountry,
                }
            });
            return res.json({
                status: httpConstants.HTTP_STATUS_OK,
                message: "Relation created successfully",
                relations: relationsInfo,
                masterClientCode: companyIncorporation.masterClientCode,
                incorporationId: companyIncorporation._id
            });
        }
        return res.json({ status: httpConstants.HTTP_STATUS_BAD_REQUEST, message: "Error creating the relation." });

    } catch (e) {
        console.log("E ", e);
        return res.json({ status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: "Internal Server Error" });
    }
};


exports.update_relation = async function (req, res) {
    try {
        let relation;
        const companyIncorporation = await CompanyIncorporationModel.findOne(
          { '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });
        if (!companyIncorporation) {
            return res.json({ status: 404, message: "Company incorporation not found" });
        }

        const relationIndex = companyIncorporation.relations.findIndex((r) =>
            r._id && r._id.toString() === req.params.relationId);
        if (relationIndex <= -1) {
            return res.redirect('/');
        }
        relation = companyIncorporation.relations[relationIndex];


        req.body.relationType =relation.type.toLowerCase();
        if (relation.type !== 'natural'){
            req.body.ownerShip = relation.ownerShip;
        }

        const errors = relationValidator(req.body);

        if (errors.length) {
            return res.json({ status: httpConstants.HTTP_STATUS_BAD_REQUEST, errors });
        }



        let fileGroups;
        if (relation && relation.type.toLowerCase() === "natural") {
            fileGroups = {
                details: relation.details ? relation.details.files : [],
                identification: relation.identification ? relation.identification.files : [],
                pep: relation.pepDetails ? relation.pepDetails.files : [],
                worldCheck: relation.worldCheck ? relation.worldCheck.files : [],
            }
        }
        else {
            fileGroups = {
                details: relation.details.files ? relation.details.files : [],
                detailsPartner: relation.detailsPartner && relation.detailsPartner.files ? relation.detailsPartner.files : [],
                principalAddress: relation.principalAddress ? relation.principalAddress.files : [],
                limitedCompany: relation.limitedCompanyDetails ? relation.limitedCompanyDetails.files : [],
                foundation: relation.foundation ? relation.foundation.files : [],
                mutualFund: relation.mutualFundDetails ? relation.mutualFundDetails.files : [],
                worldCheck: relation.worldCheck ? relation.worldCheck.files : [],
            }
        }

        const updateData = set_relation_data(req.params.incorporationId, relation, relation.type, fileGroups,
            req.body, req.session);
        relation = Object.assign(relation, updateData);
        companyIncorporation.relations[relationIndex] = relation;

        companyIncorporation.markModified('relations');
        await companyIncorporation.save();
        req.session.relationFiles = {};
        return res.json({
            status: 200,
            message: "Relation updated successfully",
            masterClientCode: companyIncorporation.masterClientCode,
            incorporationId: companyIncorporation._id
        });
    } catch (e) {
        console.log("error ", e);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
            status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: "Internal Server Error" });
    }
};

exports.save_relation_files = async function (req, res, next) {
    try {
        const fileRow = req.body.row;

        let sessData = req.session;
        if (!sessData.relationFiles) {
            sessData.relationFiles = {}
        }

        const fileName = req.body.fileName.replace(/[^a-zA-Z0-9]/g, "");
        req.files.fileUploaded.forEach((file) => {
            if (file.mimetype !== 'application/pdf' ||
                !verifyExtFile.isValidExtensionFile(file.originalname) ||
                !verifyExtFile.isValidExtensionFile(file.blob) ||
                !verifyExtFile.isValidExtensionFile(file.blobName)
            ) {
                console.log("Incorrect file type");
                const err = new Error('Incorrect file type');
                err.status = httpConstants.HTTP_STATUS_BAD_REQUEST;
                return next(err);
            }
            file = {
                fileId: uuidv4(),
                fileTypeId: req.body.fileTypeId,
                fieldName: file.fieldname.replace(/fileUploaded/i, fileName),
                originalName: file.originalname,
                encoding: file.encoding,
                mimeType: file.mimetype,
                blobName: file.blobName,
                container: file.container,
                blob: file.blob.replace(/fileUploaded/i, fileName),
                blobType: file.blobType,
                size: file.size,
                etag: file.etag,
                url: file.url,
            };

            if (sessData.relationFiles && sessData.relationFiles[req.body.fileGroup]) {
                const tempFiles = sessData.relationFiles[req.body.fileGroup];
                if (tempFiles[fileRow]) {
                    tempFiles[fileRow].push(file);
                } else {
                    tempFiles[fileRow] = [file];
                }
                sessData.relationFiles[req.body.fileGroup] = tempFiles;
            } else {
                // save fieldname's files
                const tempFiles = {};
                tempFiles[fileRow] = [file];
                sessData.relationFiles[req.body.fileGroup] = tempFiles;
            }

        });

        return res.status(httpConstants.HTTP_STATUS_OK).end();
    } catch (e) {
        console.log(e);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
    }
};


exports.get_relation_files = async function (req, res) {
    try {
        let filesToReturn = [];
        let incorporation = await CompanyIncorporationModel.findOne(
          { '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });

        const relation = incorporation.relations.find((r) => r && r._id.toString() === req.query.relationId);

        if (relation) {
            if (relation.type === "natural") {
                let naturalFiles;
                const identificationFiles = relation.identification.files ? relation.identification.files : [];
                const pepFiles = relation.pepDetails.files ? relation.pepDetails.files : [];
                const worldCheckFiles = relation.worldCheck.files ? relation.worldCheck.files : [];
                const electronicIdFiles = relation.electronicIdInfo && relation.electronicIdInfo.files ? relation.electronicIdInfo.files : [];
                naturalFiles = [...identificationFiles, ...pepFiles, ...worldCheckFiles, ...electronicIdFiles];
                naturalFiles = naturalFiles.find((file) => file.id === req.query.fileTypeId);
                filesToReturn = naturalFiles && naturalFiles.uploadFiles ? naturalFiles.uploadFiles : [];
            } else {
                let organizationFiles;
                const detailsFiles = relation.details.files ? relation.details.files : [];
                const detailsPartnerFiles = relation.detailsPartner.files ? relation.detailsPartner.files : [];
                const limitedFiles = relation.limitedCompanyDetails.files ? relation.limitedCompanyDetails.files : [];
                const mutualFundFiles = relation.mutualFundDetails.files ? relation.mutualFundDetails.files : [];
                const foundationFiles = relation.foundation.files ? relation.foundation.files : [];
                const worldCheckFiles = relation.worldCheck.files ? relation.worldCheck.files : [];
                organizationFiles = [...detailsFiles, ...detailsPartnerFiles, ...limitedFiles, ...mutualFundFiles, ...foundationFiles,
                ...worldCheckFiles];

                organizationFiles = organizationFiles.find((file) => file.id === req.query.fileTypeId);
                filesToReturn = organizationFiles && organizationFiles.uploadFiles ? organizationFiles.uploadFiles : [];
            }
        }

        if (req.query.getTempFiles) {
            const tempFilesGroup = get_temp_uploaded_files(req.session, req.query.fileGroup);
            if (tempFilesGroup) {
                const tempFiles = tempFilesGroup[req.query.row] ? tempFilesGroup[req.query.row] : [];
                filesToReturn = [...filesToReturn, ...tempFiles];
            }

        }
        return res.json({ success: true, files: filesToReturn });
    } catch (error) {
        console.log(error);
        return res.status(500).end();
    }
};

exports.delete_relation_file = async function (req, res) {
    try {
        if (req.body.relationId) {
            const incorporation = await CompanyIncorporationModel.findOne(
              { '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });
            const relationIndex = incorporation.relations.findIndex((r) =>
                r._id.toString() === req.body.relationId);

            if (relationIndex > -1) {
                const relation = incorporation.relations[relationIndex];
                const selectedFileType = relation[req.body.group];
                if (selectedFileType) {
                    const fileIndex = selectedFileType.files.findIndex((file) => file.id === req.body.rowId);

                    if (fileIndex > -1) {
                        const fileToDelete = selectedFileType.files[fileIndex];
                        const indexFileToDelete = fileToDelete.uploadFiles.findIndex((f) => f.fileId === req.body.fileId);
                        if (indexFileToDelete > -1) {
                            fileToDelete.uploadFiles.splice(indexFileToDelete, 1);
                            relation[req.body.group].files[fileIndex] = fileToDelete;
                            incorporation.relations.set(relationIndex, relation);
                            await incorporation.save();
                        }
                    }
                }
            }
        }

        if (req.body.deleteTempFiles) {
            const tempFilesGroup = get_temp_uploaded_files(req.session, req.body.group);
            if (tempFilesGroup) {
                const tempFiles = tempFilesGroup[req.body.row] ? tempFilesGroup[req.body.row] : [];
                if (tempFiles.length) {
                    const fileIndex = tempFiles.findIndex((uploadFile) => uploadFile.fileId === req.body.fileId);
                    if (fileIndex !== -1) {
                        tempFiles.splice(fileIndex, 1);
                        tempFilesGroup[req.query.row] = tempFiles;

                        if (req.session && req.session.relationFiles && req.session.relationFiles[req.body.group]) {
                            req.session.relationFiles[req.body.group] = tempFilesGroup;
                        }
                    }
                }
            }
        }

        return res.json({ result: true });
    } catch (error) {
        console.log(error);
        return res.status(500).end();
    }
};

exports.clear_relation_temporal_files = async function (req, res) {
    try {
        clear_temp_files(req);
        return res.json({ result: true });
    } catch (error) {
        console.log(error);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
    }
};

exports.get_relation_template_files = async function (req, res) {
    try {
        const configTemplate = await ConfigModel.findOne({});
        if (configTemplate) {
            if (req.query.fieldToSearch) {
                const templateField = configTemplate[req.query.fieldToSearch];

                if (templateField.length === 0) {
                    return res.status(400).json({ "success": false })
                }
                const fileGroup = templateField[req.query.group + "Files"];
                if (!fileGroup || (fileGroup && fileGroup.length === 0)) {
                    return res.status(400).json({ "success": false })
                }
                const files = fileGroup[req.query.fileType];
                if (files) {
                    const file = files[req.query.row];
                    file["id"] = uuidv4();

                    if (req.query.newFile) {
                        req.session.extraDetailPartnerFiles.push(file);
                    }

                    return res.status(httpConstants.HTTP_STATUS_OK).json({ "success": true, "data": file })
                } else {
                    return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({ "success": false })
                }

            } else {
                return res.status(httpConstants.HTTP_STATUS_OK).json({ "success": true, "data": configTemplate })
            }
        }
        return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({ "success": false })
    } catch (e) {
        console.log(e);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
    }
};

exports.remove_incorporation_relation = async function (req, res) {
    try {

        const incorporation = await CompanyIncorporationModel.findOne(
          { '_id': req.params.incorporationId, 'masterClientCode': req.params.masterclientcode });

        if (incorporation) {
            const relationIndex = incorporation.relations.findIndex(r => r._id.toString() === req.params.relationId);
            if (relationIndex > -1) {
                incorporation.relations.splice(relationIndex, 1);
            }
            await incorporation.save();

            const relationsInfo = incorporation.relations.map((r) => {
                return {
                    type: r.type,
                    groups: r.groups,
                    relationId: r._id,
                    percentage: r.percentage ? r.percentage : '',
                    name: r.type !== 'natural' ? r.details.organizationName :
                      r.details.middleName ?  r.details.firstName + " " + r.details.middleName + " "  +  r.details.lastName :
                        r.details.firstName + " "  +  r.details.lastName,
                    country: r.type === 'natural' ? r.details.countryBirth : r.details.incorporationCountry,
                }
            });

            return res.json({
                status: 200, message: 'Delete relation successfully', relations: relationsInfo,
                incorporationId: incorporation._id,
                masterClientCode: incorporation.masterClientCode
            });
        }
        else {
            return res.json({ status: 404, message: 'Company incorporation not found' });
        }
    } catch (error) {
        console.log(error);
        return res.json({ status: 500, message: 'Internal Server Error' });
    }
};


function get_temp_uploaded_files(session, key) {
    if (key) {
        if (session.relationFiles && session.relationFiles[key]) {
            return session.relationFiles[key];
        } else {
            return null;
        }
    } else {
        return session.relationFiles;
    }
}

function clear_temp_files(req) {
    req.session.relationFiles = {};
    req.session.extraDetailPartnerFiles = [];
}

function set_relation_data(incorporationId, relation, type, fileGroupFiles, newValues, session) {
    let sections;
    let isSamePrincipalAddress;
    let isTridentClient;

    const relationSessionFiles = session.relationFiles

    switch (type) {
        case 'natural':
            isSamePrincipalAddress = newValues.natural && newValues.natural.isSamePrincipalAddress === "YES";
            break;
        case 'corporate':
            isSamePrincipalAddress = newValues.corporate && newValues.corporate.isSamePrincipalAddress === "YES";
            isTridentClient = newValues.corporate && newValues.corporate.isTridentClient === "YES";
            break;
        case 'foundation':
            isSamePrincipalAddress = newValues.foundation && newValues.foundation.isSamePrincipalAddress === "YES";
            isTridentClient = newValues.foundation && newValues.foundation.isTridentClient === "YES";
            break;
        case 'trust':
            isSamePrincipalAddress = newValues.trust && newValues.trust.isSamePrincipalAddress === "YES";
            isTridentClient = newValues.trust && newValues.trust.isTridentClient === "YES";
            break;
        case 'limited':
            isSamePrincipalAddress = newValues.limited && newValues.limited.isSamePrincipalAddress === "YES";
            isTridentClient = newValues.limited && newValues.limited.isTridentClient === "YES";
            break;
        default:
            isSamePrincipalAddress = false;
            isTridentClient = false;
            break;
    }

    if (type === "natural") {
        sections = ["details", "identification", "residentialAddress", "mailingAddress", "taxResidence", "advisorDetails",
            "principalAddress", "pepDetails", "worldCheck"];
        relation = {
            _id: relation._id,
            type: relation.type ? relation.type : newValues.relationType,
            groups: newValues.groups,
            percentage: newValues.additional && newValues.additional.percentage ? newValues.additional.percentage : '',
            unavailableToPick: true,
            pep: newValues['pepDetails'].confirmation === "YES",
            createdAt: relation.createdAt ? relation.createdAt : new Date(),
            updatedAt: new Date(),
            details: {
                files: fileGroupFiles.details ?
                    fileGroupFiles.details : [],
                firstName: newValues.details.firstName,
                middleName: newValues.details.middleName,
                lastName: newValues.details.lastName,
                occupation: newValues.details.occupation,
                source_of_income: newValues.details.source_of_income,
                birthDate: newValues.details &&  newValues.details.birthDate ? moment(newValues.details.birthDate).toDate() : undefined,
                nationality: newValues.details.nationality,
                countryBirth: newValues.details.countryBirth,
            },
            identification: {
                files: fileGroupFiles.identification ?
                    fileGroupFiles.identification : [],
                identificationType: newValues.identification && newValues.identification.identificationType ? newValues.identification.identificationType : '',
                issueCountry: newValues.identification &&  newValues.identification.issueCountry ?  newValues.identification.issueCountry : '',
                expiryDate: newValues.identification &&  newValues.identification.expiryDate ? moment(newValues.identification.expiryDate).toDate() : undefined,
                valid: false,
            },
            residentialAddress: {
                primaryAddress: newValues.residentialAddress ? newValues.residentialAddress.primaryAddress : '',
                secondaryAddress: newValues.residentialAddress ? newValues.residentialAddress.secondaryAddress : '',
                country: newValues.residentialAddress ? newValues.residentialAddress.country : '',
                state: newValues.residentialAddress ? newValues.residentialAddress.state : '',
                postalCode: newValues.residentialAddress ? newValues.residentialAddress.postalCode : '',
                city: newValues.residentialAddress ? newValues.residentialAddress.city : '',
            },
            taxResidence: {
                confirmation: !!(newValues.taxResidence && newValues.taxResidence.confirmation === "YES"),
                taxResidence: newValues.taxResidence.taxResidence,
            },
            advisorDetails: {
                firstName: newValues.advisorDetails ? newValues.advisorDetails.firstName : '',
                middleName: newValues.advisorDetails ? newValues.advisorDetails.middleName : '',
                lastName: newValues.advisorDetails ? newValues.advisorDetails.lastName : '',
                firmName: newValues.advisorDetails ? newValues.advisorDetails.firmName : '',
                phone: newValues.advisorDetails ? newValues.advisorDetails.phone : '',
                email: newValues.advisorDetails ? newValues.advisorDetails.email : '',
                nationality: newValues.advisorDetails ? newValues.advisorDetails.nationality : '',
                incorporationCountry: newValues.advisorDetails ? newValues.advisorDetails.incorporationCountry : '',
            },
            principalAddress: {
                primaryAddress: newValues.principalAddress ? newValues.principalAddress.primaryAddress : '',
                secondaryAddress: newValues.principalAddress ? newValues.principalAddress.secondaryAddress : '',
                country: newValues.principalAddress ? newValues.principalAddress.country : '',
                state: newValues.principalAddress ? newValues.principalAddress.state : '',
                postalCode: newValues.principalAddress ? newValues.principalAddress.postalCode : '',
                city: newValues.principalAddress ? newValues.principalAddress.city : '',
                files: fileGroupFiles.principalAddress ? fileGroupFiles.principalAddress : [],
            },
            isSamePrincipalAddress: isSamePrincipalAddress,
            pepDetails: {
                files: fileGroupFiles.pep ? fileGroupFiles.pep : [],
                information: newValues.pepDetails.information,
                additionalComments: newValues.pepDetails.additionalComments,
                confirmAdditionalComments: newValues['pepDetails'] && newValues['pepDetails'].confirmAdditionalComments === "YES",
            },
            worldCheck: {
                files: fileGroupFiles.worldCheck ? fileGroupFiles.worldCheck : [],
            },
            partitionkey: 'naturalperson',
        };

        const hasElectronicId = newValues.electronicIdInfo && newValues.electronicIdInfo.isElectronicId === "YES";
        let electronicIdInfo = relation.electronicIdInfo || {};

        if (hasElectronicId){
            electronicIdInfo.isElectronicId = true;
            electronicIdInfo.email =  newValues.electronicIdInfo && newValues.electronicIdInfo.email ? newValues.electronicIdInfo.email : '';
            electronicIdInfo.status = relation.electronicIdInfo && relation.electronicIdInfo.status ?
              relation.electronicIdInfo.status : 'PENDING INVITATION';
        }
        else{
            electronicIdInfo.isElectronicId = false;
            electronicIdInfo.status = "RECEIVED";
            electronicIdInfo.email = "";
        }
        if (!electronicIdInfo.files){
            electronicIdInfo.files = [
                {
                    present : false,
                    validated : false,
                    _id: new ObjectId(),
                    uploadFiles : [],
                    internal : "Electronic ID",
                    external : "Electronic ID",
                    fileGroup : "identification",
                    explanation : "",
                    comments : "",
                    id : uuidv4()

                }
            ];
        }
        relation.electronicIdInfo = electronicIdInfo;

    } else {
        sections = ["details", "detailsPartner", "principalAddress", "mailingAddress", "listedCompanyDetails",
            "limitedCompanyDetails", "mutualFundDetails", "foundation", "worldCheck"];
        relation = {
            _id: relation._id,
            type: relation.type ? relation.type : newValues.relationType,
            groups: newValues.groups,
            ownerShip: newValues.ownerShip ? newValues.ownerShip : relation.ownerShip,
            percentage: newValues.additional && newValues.additional.percentage ? newValues.additional.percentage : '',
            unavailableToPick: true,
            createdAt: relation.createdAt ? relation.createdAt : new Date(),
            updatedAt: new Date(),
            details: {
                isTridentClient: isTridentClient,
                organizationName: newValues.details.organizationName,
                incorporationNumber: newValues.details.incorporationNumber,
                taxResidence: newValues.details.taxResidence,
                businessNumber: newValues.details.businessNumber,
                incorporationDate: newValues.details && newValues.details.incorporationDate ?
                  moment(newValues.details.incorporationDate).toDate() : undefined,
                incorporationCountry: newValues.details.incorporationCountry,
                files: fileGroupFiles.details ? fileGroupFiles.details : [],
            },
            detailsPartner: {
                files: fileGroupFiles.detailsPartner ? fileGroupFiles.detailsPartner : [],
            },
            principalAddress: {
                primaryAddress: newValues.principalAddress ? newValues.principalAddress.primaryAddress : '',
                secondaryAddress: newValues.principalAddress ? newValues.principalAddress.secondaryAddress : '',
                country: newValues.principalAddress ? newValues.principalAddress.country : '',
                state: newValues.principalAddress ? newValues.principalAddress.state : '',
                postalCode: newValues.principalAddress ? newValues.principalAddress.postalCode : '',
                city: newValues.principalAddress ? newValues.principalAddress.city : '',
                files: fileGroupFiles.principalAddress ? fileGroupFiles.principalAddress : [],
            },
            isSamePrincipalAddress: isSamePrincipalAddress,
            listedCompanyDetails: {
                active: !!(newValues.listedCompanyDetails && newValues.listedCompanyDetails.active),
                stockCode: newValues.listedCompanyDetails ? newValues.listedCompanyDetails.stockCode : '',
            },
            limitedCompanyDetails: {
                files: fileGroupFiles.limitedCompany ? fileGroupFiles.limitedCompany : [],
                active: !!(newValues.limitedCompanyDetails && newValues.limitedCompanyDetails.active),
                registrationNumber: newValues.limitedCompanyDetails ? newValues.limitedCompanyDetails.registrationNumber : '',
                registrationDate: newValues.limitedCompanyDetails ? newValues.limitedCompanyDetails.registrationDate : null,
            },
            mutualFundDetails: {
                active: !!(newValues.mutualFundDetails && newValues.mutualFundDetails.active),
                files: fileGroupFiles.mutualFund ? fileGroupFiles.mutualFund : [],
            },
            foundation: {
                active: !!(newValues.foundation && newValues.foundation.active),
                isFoundation: newValues.relationType === "foundation",
                country: newValues.foundation ? newValues.foundation.country : '',
                files: fileGroupFiles.foundation ? fileGroupFiles.foundation : [],
            },
            worldCheck: {
                files: fileGroupFiles.worldCheck ? fileGroupFiles.worldCheck : [],
            },
            positions: [],
            partitionkey: 'organization',
        };


        if (session.extraDetailPartnerFiles?.length) {
            relation.detailsPartner.files = [...relation.detailsPartner.files, ...session.extraDetailPartnerFiles]
        }
    }

    if (isSamePrincipalAddress && newValues.principalAddress) {
        relation.mailingAddress = {
            primaryAddress: newValues.principalAddress.primaryAddress,
            secondaryAddress: newValues.principalAddress.secondaryAddress,
            country: newValues.principalAddress.country,
            state: newValues.principalAddress.state,
            postalCode: newValues.principalAddress.postalCode,
            city: newValues.principalAddress.city,
        };
    } else {
        relation.mailingAddress = {
            primaryAddress: newValues.mailingAddress ? newValues.mailingAddress.primaryAddress : '',
            secondaryAddress: newValues.mailingAddress ? newValues.mailingAddress.secondaryAddress : '',
            country: newValues.mailingAddress ? newValues.mailingAddress.country : '',
            state: newValues.mailingAddress ? newValues.mailingAddress.state : '',
            postalCode: newValues.mailingAddress ? newValues.mailingAddress.postalCode : '',
            city: newValues.mailingAddress ? newValues.mailingAddress.city : '',
        };
    }

    sections.forEach((sectionKey) => {
        const relationSection = relation[sectionKey] ? relation[sectionKey] : {};
        const formSection = newValues[sectionKey];
        let relationFiles = relationSection["files"];

        if (relationFiles) {
            if (formSection && formSection.files) {
                const fileKeys = Object.keys(formSection.files);
                fileKeys.forEach((fileKey) => {
                    if (relationFiles[fileKey]) {
                        relationFiles[fileKey].explanation = formSection.files[fileKey].explanation ?
                            formSection.files[fileKey].explanation : '';
                    }
                });
            }

            relationFiles.forEach((file, index) => {
                if (!file.id) {
                    file.id = uuidv4();
                }
                const tempFiles = relationSessionFiles ? relationSessionFiles[sectionKey] : {};

                if (tempFiles) {
                    let newFiles = tempFiles[index] ? tempFiles[index] : [];
                    if (newFiles.length) {

                        newFiles.forEach((newFile) => {
                            const pathName = "/relations/" + relation._id + "/" + newFile.fieldName;
                            uploadController.moveIncorporationUpload(incorporationId, newFile, pathName).catch((reason => {
                                if (reason) {
                                    console.log(reason);
                                    return { status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, error: "Error uploading the document files"};
                                }
                            }));
                            newFile.url = newFile.url.replace(/fileUploaded/i, incorporationId + pathName);
                            newFile.blobName = newFile.blobName.replace(/fileUploaded/i, newFile.fieldName);
                        });

                        file.uploadFiles = file.uploadFiles.concat(newFiles);
                        file.present = true;
                    }

                }

            });
            relationSection["files"] = relationFiles;
        }
        relation[sectionKey] = relationSection;

    });
    return relation;

}

exports.save_request_files = function (req, res, next) {
    let sessData = req.session;
    const uploadedFiles = req.files['fileUploaded'];
    if (uploadedFiles && uploadedFiles.length > 0) {
        uploadedFiles.forEach((itemToUpload) => {
            if (itemToUpload.mimetype !== 'application/pdf' ||
                !verifyExtFile.isValidExtensionFile(itemToUpload.originalname) ||
                !verifyExtFile.isValidExtensionFile(itemToUpload.blob) ||
                !verifyExtFile.isValidExtensionFile(itemToUpload.blobName)
            ) {
                console.log("Incorrect file type");
                const err = new Error('Incorrect file type');
                err.status = httpConstants.HTTP_STATUS_BAD_REQUEST;
                return next(err);
            }
        });
        if (sessData.requestFiles) {
            sessData.requestFiles = sessData.requestFiles.concat(uploadedFiles)
        } else {
            sessData.requestFiles = uploadedFiles
        }
        res.sendStatus( httpConstants.HTTP_STATUS_OK)
    }
};


exports.delete_request_files = function (req, res) {
    let sessData = req.session;
    let requestFiles = sessData.requestFiles;

    if (requestFiles.length) {
        const fileIndex = requestFiles.findIndex((rf) => rf.originalname === req.body.name);

        if (fileIndex > -1) {
            requestFiles.splice(fileIndex, 1);
        }
        sessData.requestFiles = requestFiles;
    }

    res.sendStatus(httpConstants.HTTP_STATUS_OK);
};
