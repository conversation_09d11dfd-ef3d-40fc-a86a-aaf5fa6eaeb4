<div class="col-lg-12">
    <div class="card">
        <div class="card-body">

            <div class="row">
                <div class="col-md-12">
                    <h4>5. Financial Reports</h4>
                </div>
            </div>

            <div class="row" id="previewReports">
                <div class="col-md-8">
                    <div class="form-group ml-1">
                        <label class="lbl-read-only">
                            5.1 Click to Preview the Financial Reports.
                        </label>
                    </div>
                </div>
                <div class="col-md-4 text-right">
                    <a href="/masterclients/{{masterClientCode}}/financial-reports/companies/{{companyCode}}/{{report._id}}/report.pdf"
                       class="btn solid royal-blue width-xl mb-2" target="_blank">Preview</a>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="form-group ml-1">
                        <label class="lbl-read-only">5.2 Are the Financial Reports accurate?</label>
                    </div>
                </div>
                <div class="col-md-4 text-right">
                    <div class="custom-control custom-radio custom-control-inline">
                        <input type="radio" class="custom-control-input" id="finReportAccurateYes"
                               name="finReportAccurate" required value="YES" required
                            {{#ifEquals report.finReportAccurate true}} checked {{/ifEquals}}>
                        <label class="custom-control-label" for="finReportAccurateYes">Yes</label>
                    </div>
                    <div class="custom-control custom-radio custom-control-inline">
                        <input type="radio" class="custom-control-input" id="finReportAccurateNo"
                               name="finReportAccurate" required value="NO"
                            {{#ifEquals report.finReportAccurate false}} checked {{/ifEquals}}>
                        <label class="custom-control-label" for="finReportAccurateNo">No</label>
                    </div>
                </div>
            </div>

            <div id="totalNonCurrentLoansNoRow" {{#ifEquals report.finReportAccurate false}} class="d-block"  {{else}}class="hide-element"  {{/ifEquals}}>
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group ml-3">
                            <label class="lbl-read-only">Please use the go back button, to return to the previous pages.</label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mt-2">
                <div class="col-md-12">
                    <div class="progress">
                        <div class="progress-bar width-71" role="progressbar"aria-valuenow="5"
                             aria-valuemin="0" aria-valuemax="7">5 of 7
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="/views-js/partials/financial-reports/report-summary.js"></script>