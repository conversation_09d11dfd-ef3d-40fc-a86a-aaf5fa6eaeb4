$(document).ready(function () {
    calculateCompleteTotalValues($('.complete-assets'), $('#completeDetailsAssetsTotal'));
    calculateCompleteTotalValues($('.complete-liabilities'), $('#completeDetailsLiabilitiesTotal'));
    calculateTotalShareholders();

});

$(document).on('change paste keyup', '.complete-assets', function () {
    calculateCompleteTotalValues($('.complete-assets'), $('#completeDetailsAssetsTotal'));
});

$(document).on('change paste keyup', '.complete-liabilities', function () {
    calculateCompleteTotalValues($('.complete-liabilities'), $('#completeDetailsLiabilitiesTotal'));
});


$('#completeDetailsAssetsTotal').on('keyup', function (e) {
    calculateTotalShareholders();
})

$('#completeDetailsLiabilitiesTotal').on('keyup', function (e) {
    calculateTotalShareholders();
})




$('#addOtherAssetsBtn').on('click', function (e) {
    e.preventDefault();
    const type = $(this).data('type');
    const typeClass = $(this).data('type-class');
    const otherAssetsInputslength = $('.other-assets-inpt').length;

    createOtherValueRow(type, typeClass, otherAssetsInputslength)
})

$('#addOtherLiabilitiesBtn').on('click', function (e) {
    e.preventDefault();
    const type = $(this).data('type');
    const typeClass = $(this).data('type-class');
    const otherLiabilitiesInputslength = $('.other-liabilities-inpt').length;

    createOtherValueRow(type, typeClass, otherLiabilitiesInputslength)

})

function calculateTotalShareholders() {
    const assetsTotalStr = $('#completeDetailsAssetsTotal').val();
    const liabilitiesTotalStr = $('#completeDetailsLiabilitiesTotal').val();
    const assetsTotal = parseFloat(assetsTotalStr.replace(/,/g, '')) || 0;
    const liabilitiesTotal = parseFloat(liabilitiesTotalStr.replace(/,/g, '')) || 0;
    const totalShareholdersEquity = (assetsTotal - liabilitiesTotal);
    $('#completeDetailsShareholderEquity').val(showDecimalValue(totalShareholdersEquity))
}