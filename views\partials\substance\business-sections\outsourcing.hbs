<div  id="outsourcingContent">

    <!--4G.a-->
    <div class="row">
        <div class="col-md-8">
            <div class="form-group mb-3">
                <label class="mb-2" for="CoreIncomeGeneratingOutsourced">Has
                    any core income generating activity (CIGA) been
                    outsourced to another entity?</label>
            </div>
        </div>
        <div class="col-md-4" align="right">
            <div class="radio form-check-inline">
                <input type="radio" id="CoreIncomeGeneratingOutsourcedYes" name="CoreIncomeGeneratingOutsourced" value="Yes"

                    {{#if data.core_income_generating_outsourced}}checked{{/if}}>
                <label for="CoreIncomeGeneratingOutsourcedYes">Yes</label>
            </div>
            <div class="radio form-check-inline">
                <input type="radio" id="CoreIncomeGeneratingOutsourcedNo" name="CoreIncomeGeneratingOutsourced" value="No" 

                    {{#unless data.core_income_generating_outsourced}}checked{{/unless}}>


                <label for="CoreIncomeGeneratingOutsourcedNo">No</label>
            </div>
        </div>
    </div>

    <div id="Outsourcing" 
        {{#ifCond data.core_income_generating_outsourced "!==" true}} class="hide-element"{{/ifCond}}>
        <!-- 4G.a -->
        <div class="row">
            <div class="col-md-8">
                <div class="form-group mb-3">
                    <label class="mb-2" for="AdressesOfPremisesInBVI">
                        Provide details of all outsourcing providers:
                    </label>
                </div>
            </div>
            <div class="col-md-4">
                <div class="form-group mb-3">
                    <button type="button" class="btn solid royal-blue w-100" data-toggle="modal" data-target="#outsourcingProviderModal"
                        data-activity-type={{activityType}} data-id="{{entry._id}}">
                        Add Outsourcing Provider
                    </button>
                </div>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped mb-0">
                <thead>
                    <tr>
                        <th class="header-15-percent">Entity Name</th>
                        <th class="header-25-percent">Details of Resources</th>
                        <th class="header-15-percent">Number of staff</th>
                        <th class="header-15-percent">Hours per month</th>
                        <th class="header-20-percent">Monitoring and Control</th>
                        <th class="header-10-percent">
                    </tr>
                </thead>
                <tbody id="outsourcingProvidersTableBody">
                    {{#each data.outsourcing_providers}}
                        <tr id="outsourcing-providers-table-row-{{_id}}" class="outsourcing-row">
                            <td> {{entity_name}} </td>
                            <td class="text-truncate header-max-width-150" > {{resource_details}} </td>
                            <td> {{staff_count}} </td>
                            <td> {{hours_per_month}} </td>
                            <td>{{#if monitoring_control}} Yes {{else}} No {{/if}}</td>
                            <td class="justify-content-center d-flex">
                                <button type="button" class="btn btn-sm royal-blue solid mr-1" data-activity-type="{{../activityType}}"
                                    data-id="{{../entryId}}" data-provider-id="{{_id}}" data-toggle="modal" data-target="#outsourcingProviderModal">
                                    <i class="fa fa-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-danger deleteOutsourcingProvider"
                                    data-entry-id="{{../entryId}}" data-activity-type="{{../activityType}}" data-id="{{_id}}">
                                    <i class="fa fa-times"></i>
                                </button>
                            </td>
                        </tr>
                    {{else}}
                        <tr>
                            <td colspan="6">
                                No outsourcing providers found
                            </td>
                        </tr>
                    {{/each}}
                </tbody>
            </table>
        </div>
        <br />
    </div>

    <!-- 4G.b -->

    <div class="row mb-2">
        <div class="col-md-8">
            <label for="outsourcingTotalExpenditure">
                Total expenditure incurred on outsourcing in the Virgin Islands during the financial period?
            </label>
        </div>
    
        <div class="col-md-4">
            <input type="text" name="outsourcingTotalExpenditureCurrency" id="outsourcingTotalExpenditureCurrency" class="form-control"
                {{#if selectedCurrency}} value="{{selectedCurrency.cc}} - {{selectedCurrency.name}}" {{else}}
                value="{{defaultCurrency.cc}} - {{defaultCurrency.name}}" {{/if}} disabled />
            
            <input type="text" id="outsourcingTotalExpenditure" class="form-control mt-2  autonumber" data-a-sep="," data-m-dec="2"
                data-min="0" data-max="1000000000" placeholder="0.0" name="outsourcingTotalExpenditure"
                value="{{data.outsourcing_total_expenditure}}" /> 
        </div>
    </div>
</div>