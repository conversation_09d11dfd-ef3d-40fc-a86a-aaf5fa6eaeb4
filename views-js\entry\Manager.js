$(document).ready(function() {
    const country = $('#StateOrCountryName').attr('data-value')
    const position_held = $('#PositionHeld').attr('data-value');
    $("#StateOrCountryName").val(country);
    $("#select2-StateOrCountryName-container").text(country);
    $("#select2-StateOrCountryName-container").attr('title',country)
    $("#PositionHeld").val(position_held);
    $("#select2-PositionHeld-container").text(position_held);
    $("#select2-PositionHeld-container").attr('title',position_held);

    const hasError = $('#hasErrorVal').val();
    if (hasError === 'false') {
        window.parent.closeDirectorIFrame();
    }
});