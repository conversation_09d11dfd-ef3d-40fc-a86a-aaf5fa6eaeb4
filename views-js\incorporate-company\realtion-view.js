$('.toggle-section-check').change(function () {
    const name = "content-" + $(this).attr('id');
    $('#' + name).toggle(200);
});

$("#additional-percentage").on('keyup', function () {
    const value = $(this).val();
    if (value && parseInt(value) < 0 || parseInt(value) > 100) {
        $('#additional-percentage').toggleClass("is-invalid", true);
    } else {
        $('#additional-percentage').toggleClass("is-invalid", false);
    }
});

$('#shareholderType').change(function () {
    if ($('#shareholderType').is(':checked')) {
        $('#shareholderAdditionalForm').show(200);
    } else {
        $('#shareholderAdditionalForm').hide(200);
    }
});


$('.addCertificatePartnerRow').on('click', function () {
    const tableId = $(this).data("table-id");
    const reviewId = $(this).data("review-id");
    const relationId = $(this).data("relation-id");
    const group = $(this).data("group");
    const fileRow = $(this).data("row");
    const rowCount = $('#' + tableId + ' tr').length - 1;
    const mcc = window.location.pathname.split('/')[2];
    const incorporationId = window.location.pathname.split('/')[4]
    $.ajax({
        url: '/masterclients/'+mcc+'/incorporate-company/relations/get-template-files',
        type: 'GET',
        timeout: 5000,
        data: {
            fieldToSearch: 'relationFiles',
            group: group,
            fileType: 'detailsPartner',
            row: fileRow,
            newFile: true
        },
        success: function (response) {
            if (response.success) {
                let template = Handlebars.templates.addpartnerfilerow;
                let d = {
                    file: response.data,
                    row: rowCount,
                    group: group,
                    reviewId: reviewId,
                    relationId: relationId,
                    mcc: mcc,
                    incorporationId: incorporationId
                };
                let html = template(d);
                $("#" + tableId + " > table > tbody").append(html);
            } else {
                Swal.fire('Error', 'There was an error adding a new row file', 'error');
            }
        },
        error: function () {
            Swal.fire('Error', 'There was an error adding a new row file', 'error');
        },
    });
});
$('[data-toggle="tooltip"]').tooltip({trigger: 'hover'});


$('input[type="text"][required], textarea[required]').on('keyup', function () {
    const empty = $(this).val() === "";
    $(this).toggleClass("is-invalid", empty);
});

$('input[type="email"]').on('keyup', function () {
    const valid_email = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    const val = $(this).val();
    const bad = !val.match(valid_email);
    $(this).toggleClass('is-invalid', bad);
});

$('input[type="date"][required]').on('change', function () {
    const empty = $(this).val() === "";
    $(this).toggleClass("is-invalid", empty);
});

$('select[required]:visible').on('change', function () {
    const empty = $(this).val() === "";
    // Used for select2 selectors (country select)
    $(`span[aria-labelledby='select2-${$(this).attr('id').replace('[', '').replace(']', '')}-container']`).toggleClass("is-invalid", empty);
    $(this).toggleClass("is-invalid", empty);
});

$("input[type='radio'][required]:visible").on('change', function () {
    const empty = $('input[name="' + this.name + '"]:checked').val() === "";
    $('input[name="' + this.name + '"]').toggleClass("is-invalid", empty);
});

$("input[type='checkbox'][required]:visible").on('change', function () {
    const empty = !($('input[name="' + this.name + '"]:checked').val());
    $('input[name="' + this.name + '"]').toggleClass("is-invalid", empty);
});

$('#reviewRelationForm').submit(async function (event) {
    const submitBtn = $("#submitRelationBtn");
    let invalidRadios = false;

    submitBtn.prop('disabled', true);
    event.preventDefault();

    let incorporationId = $('button[type=submit]').data('id');
    let masterClientCode = $('button[type=submit]').data('mcc');
    let editIndex = $('button[type=submit]').data('index');

    $('input[required]:visible').trigger('keyup');
    $('textarea[required]:visible').trigger('keyup');
    $('select[required]:visible').trigger('change');
    $("input[type='checkbox'][required]:visible").each(function () {
        const val = $('input[name="' + this.name + '"]:checked').val();
        if (val === undefined) {
            $('input[name="' + this.name + '"]').toggleClass("is-invalid", true);
            invalidRadios = true;
        } else {
            $('input[name="' + this.name + '"]').toggleClass("is-invalid", false);
        }
    });

    $("input[type='radio'][required]:visible").each(function () {
        const val = $('input[name="' + this.name + '"]:checked').val();
        if (val === undefined) {
            $('input[name="' + this.name + '"]').toggleClass("is-invalid", true);
            invalidRadios = true;
        } else {
            $('input[name="' + this.name + '"]').toggleClass("is-invalid", false);
        }
    });


    if ($(".is-invalid:visible").length === 0 && !invalidRadios) {
        $.ajax({
            url: '/masterclients/' + masterClientCode + '/incorporate-company/' + incorporationId + '/relations/' + editIndex + '/edit',
            type: 'POST',
            timeout: 5000,
            contentType: 'application/json',
            data: JSON.stringify($(this).serializeJSON()),

            success: function (data) {
                submitBtn.prop('disabled', false);
                if(data.status === 200){

                    Swal.fire('Success', 'Information update successfully', 'success').then(() => {
                        location.href = '/masterclients/' + data.masterClientCode + '/incorporate-company/' + data.incorporationId + '?relations=true';
                    });
                }
                else{
                    if (data.errors && data.errors.length > 0){
                        for (let i = 0; i < data.errors.length; i++) {
                            toastr["warning"](data.errors[i], 'Error!');
                        }
                    }
                    else{
                        toastr["warning"]('There was an error updating the relation', 'Error!');
                    }
                }
            },
            error: function () {
                submitBtn.prop('disabled', false);
                Swal.fire('Error', 'There was an error updating the relation', 'error');
            },
        });
    } else {
        submitBtn.prop('disabled', false);
    }
});