exports.validate = function(premises, version, previousPremises, editPremiseId) {

    let errors = [];

    if (premises.address_line1 == undefined || premises.address_line1.length == 0) {
        errors.push({msg: 'Provide the address', field: "PremisesAddressLine1"})     
    }

    if (premises.country == undefined || premises.country.length == 0) {
        errors.push({msg: 'Provide the country', field: "PremisesCountry"})     
    }            


    if(version < 5){
        if (premises.city == undefined || premises.city.length == 0) {
            errors.push({ msg: 'Provide the island', field: "PremisesIsland" })
        }

        if (premises.postalcode == undefined || premises.postalcode.length == 0) {
            errors.push({ msg: 'Provide the postalcode', field: "PremisesZIPCodePerson" })
        } else if (premises.postalcode.toUpperCase().indexOf("VG") != 0) {
            errors.push({ msg: 'Provide correct postalcode', field: "PremisesZIPCodePerson" })
        }
    }else{
        
        if(previousPremises?.length > 0){
            const physicalOfficesNone = premises.are_physical_offices === false;
            let currentPremise;

            if (editPremiseId){
                currentPremise = previousPremises.find((p) => p._id?.toString() === editPremiseId);
            }
            const existsPreviousPremiseNone = previousPremises.find((p) => p.address_line1?.toLowerCase() === 'none' );
            const existsOtherPremises = previousPremises.filter((p) => p.address_line1?.toLowerCase() !== 'none' && p._id !== currentPremise?._id);


            // if exists none
            if (existsPreviousPremiseNone ){
                if (existsPreviousPremiseNone._id !== currentPremise?._id){
                    errors.push({ msg: 'You cannot add a premises on the BVI, if you have indicated not to have one (None)' })
                }
               
            }else{
                if (existsOtherPremises.length > 0 && physicalOfficesNone){
                    errors.push({ msg: 'You cannot add a premises (None) on the BVI, if you have indicated other premises' })
                }
            }


        }
    }

    //
    return errors;
}