<script src="https://www.recaptcha.net/recaptcha/api.js?render={{RECAPTCHA_SITE_KEY}}"></script>
<main class="">
    <div class="container">
        <div class="row">
            <div class="col-lg-2"></div>
            <div class="col-lg-8 col-sm-12">
                <div class='contour'>
                    <div class="header-title-container">
                        <h1>{{title}}</h1>
                    </div>
                     {{#if message}}
                        <p class="alert alert-danger"><a class="alert-danger" href="#email">{{message}}</a></p>
                    {{/if}}
                    <form id="loginForm" class='enquiry' method="POST" action="/users/login" autocomplete="off">
                        <input type="text" hidden name="csrf-token" value="{{csrfToken}}">
                        <div class="container-fluid">						
                            <div class="row">
                                
                                <div class="col-lg-12">
                                    <div class="card">
                                        <div class="card-body">    
                                            {{#unless blocked}}
                                                <input type="hidden" id="g-recaptcha-response" name="g-recaptcha-response">
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-group mb-3">
                                                            <label class="mb-2" for="email">Email address</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-8">
                                                        <div class="form-group mb-3">                                                            
                                                            <input id="email" maxlength="255" name="email" size="20" type="text" required="" aria-required="true" class="form-control" data-toggle="tooltip" placement="top" title="This is the email address you registered with">			
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-group mb-3">
                                                            <label class="mb-2" for="password">Password</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-8">
                                                        <div class="form-group mb-3">
                                                        <input  id="password" type="password" maxlength="40" name="password" size="20" type="text" required="" aria-required="true" class="form-control"  >	
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-8">If you do not have an account, please <a href="/users/register">register</a>.
                                                        <br/><br/>  
                                                        <a href="/users/forgot">Reset password</a>
                                                    
                                                    </div>

                                                    <div class="col-md-4">
                                                        <div class="form-group mb-3" align="right">
                                                    
                                                            <button id="loginBtn" type="submit" class="btn btn-primary waves-effect waves-light "
                                                                data-sitekey="{{RECAPTCHA_SITE_KEY}}">
                                                                Login
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                             {{/unless}}
                                        </div>
                                    </div>  
                                </div>   
                            </div>
                        </div>
                    </form>                                                                 
                </div>
            </div>
            <div class="col-lg-2"></div>
        </div>
    </div>
</main>

<script type='text/javascript' src="/views-js/helpers/set-captcha-token.js"></script>
<script type='text/javascript' src="/views-js/user/login.js"></script>