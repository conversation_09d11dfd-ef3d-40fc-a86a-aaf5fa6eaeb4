{{#if files}}
    <div class="table-responsive">
        <table class="table table-striped mb-0">
            <thead>
            <tr>
                <th>File name: {{title}}</th>
                <th class="header-20-percent">Download</th>
                <th class="header-150">Delete File</th>
            </tr>
            </thead>
            <tbody>
            {{#each files}}
                <tr>
                    <td>
                        {{#if originalName}}
                            {{originalName}}
                        {{else}}
                            {{originalname}}
                        {{/if}}
                    </td>
                    <td>
                        <a type="button" class="btn btn btn-xs solid royal-blue downloadIncorporationFile"
                           data-id="{{../id}}" data-name="{{../group}}" data-field-id="{{fileId}}">
                            Download</a>
                    </td>
                    <td>
                        <button class="demo-delete-row btn btn-danger btn-xs btn-icon deleteIncorporationFile"
                               data-id="{{../id}}" data-name="{{../group}}" data-field-id="{{fileId}}"><i class="fa fa-times"></i>
                        </button>
                    </td>
                </tr>
            {{/each}}
            </tbody>
        </table>
    </div>
{{/if}}

