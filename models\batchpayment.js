const mongoose = require('mongoose');

const paymentObjectSchema = new mongoose.Schema({
    type_object: {type: String, required:true, max:100  }, //should be SUBSTANCE_ENTRY
    objectid: {type: String, required:true  }, //refers the object id
    amount: {type: Number, required:true  }    
  });

const PaymentBatchSchema = new mongoose.Schema(
  {
    code: {type: String, required: true, max: 100},
    masterclientcode:  {type: String, required: true, max: 100},
    total_amount: {type: Number, required:true  },    
    created_by:  {type: String, required: true, max: 100},
    status: {type: String, required: true, max: 25},
    transactionId: {type: String, required: true, max: 25},
    payment_objects: [paymentObjectSchema]
  },
  { 
    timestamps: { createdAt : 'createdAt', updatedAt : 'updatedAt'}
  }
);


//Export model
module.exports = mongoose.model('batchpayment', PaymentBatchSchema);
