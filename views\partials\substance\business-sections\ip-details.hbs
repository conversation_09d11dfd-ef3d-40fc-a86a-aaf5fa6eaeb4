<div id="intellectualPropertyContent">
    <!--4F.a-->
    <div class="row">
        <div class="col-md-8">
            <div class="form-group mb-3">
                <label class="mb-2" for="entityHighRisk">
                    Is the entity a high-risk intellectual property entity?
                    <span class="fa-stack tooltip-wrapper tooltip-top-margin" data-toggle="tooltip"
                        data-placement="top" data-html="true" title="<small>A “high risk IP legal entity”
                        is defined in ESA section 2 as a legal entity which carries
                        on an intellectual property business and
                        which:<br />(a)acquired the intellectual property asset
                        <br>(i) from an affiliate; or <br>(ii) inconsideration for
                        funding research and development by another person situated
                        in a country or territory other than the Virgin Islands;
                        <br>and (b) licences the intellectual property asset to one
                        or more affiliates or otherwise generates income from the
                        asset in consequence of activities (such as facilitating
                        sale agreements) performed by foreign affiliates</small>">
                        <i class="fa fa-info-circle fa-stack fa-lg"></i>
                    </span>
                </label>
            </div>
        </div>
        <div class="col-md-4" align="right">
            <div class="radio form-check-inline">
                <input type="radio" id="isHighRiskIntellectualPropertyYes" name="isHighRiskIntellectualProperty"
                    {{#ifCond data.high_risk_ip '===' true}}checked{{/ifCond}} value="Yes" />
                <label for="isHighRiskIntellectualPropertyYes">Yes</label>
            </div>
            <div class="radio form-check-inline">
                <input type="radio" id="isHighRiskIntellectualPropertyNo" name="isHighRiskIntellectualProperty"
                    value="No" {{#ifCond data.high_risk_ip '===' false}}checked{{/ifCond}}/>
                <label for="isHighRiskIntellectualPropertyNo">No</label>
            </div>
        </div>
    </div>

    <!--4F.b-->
    <div id="isHighRiskIntellectualPropertyRows" class="is-high-risk-ip-yes {{#ifCond data.high_risk_ip "!==" true}} hide-element
        {{/ifCond}}">
        <div class="row">
            <div class="col-md-8">
                <div class="form-group mb-3">
                    <label class="mb-2" for="providedPresumptionEvidences">
                        Does the entity wish to provide evidence to rebut the presumption as set out in ESA section
                        9(4)?
                    </label>
                </div>
            </div>
            <div class="col-md-4" align="right">
                <div class="radio form-check-inline">
                    <input type="radio" id="providedPresumptionEvidencesYes" name="providedPresumptionEvidences" {{#ifCond
                        data.evidence_high_risk_ip '===' true}}checked{{/ifCond}} value="Yes"/>
                    <label for="providedPresumptionEvidencesYes">Yes</label>
                </div>
                <div class="radio form-check-inline">
                    <input type="radio" id="providedPresumptionEvidencesNo" name="providedPresumptionEvidences"
                        value="No" {{#ifCond data.evidence_high_risk_ip '===' false}}checked{{/ifCond}}/>
                    <label for="providedPresumptionEvidencesNo">No</label>
                </div>
            </div>
        </div>


        <div id="providedPresumptionEvidencesRows {{#ifCond data.evidence_high_risk_ip "!==" true}}
            hide-element {{/ifCond}}">
            <!-- 4F.c -->
            <div class="row">
                <div class=" col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2">Upload evidence</label>
                    </div>
                </div>
                <div class=" col-md-4">
                    <div class="form-group mb-3">
                        <input type="button" class="btn solid royal-blue width-xs" data-toggle="modal"
                            data-target="#upload-modal" data-field="EvidenceHighRiskIp-" data-entry="{{entryId}}"
                            value="Add Evidence">

                    </div>
                </div>
            </div>
            <div class="table-responsive" id="high_risk_ip_uploaded_files"></div>
            <br>
        </div>

        <!-- 4F.d -->
        <div class="row mb-2">
            <div class=" col-md-8">
                <label for="activityAmountAssets">
                    Gross income through Royalties, if applicable:
                </label>
            </div>

            <div class=" col-md-4">
                <input type="text" name="highRiskGrossIncomeCurrency" id="highRiskGrossIncomeCurrency" class="form-control"
                    {{#if selectedCurrency}} 
                        value="{{selectedCurrency.cc}} - {{selectedCurrency.name}}" 
                    {{else}}
                        value="{{defaultCurrency.cc}} - {{defaultCurrency.name}}" 
                    {{/if}} 
                    disabled 
                />

                <input type="text" id="highRiskGrossIncome" class="form-control mt-2 autonumber" data-a-sep=","
                    data-min="0" data-max="1000000000" data-m-dec="2" name="highRiskGrossIncome" placeholder="0.0"
                    aria-describedby="basic-addon1" value="{{data.high_risk_gross_income_total}}" />

            </div>
        </div>


        <!-- 4F.e -->
        <div class="row mb-2">
            <div class="col-md-8">
                <label for="highRiskGrossIncomeAssets">
                    Gross income through Gains from sale of IP Assets, if applicable:
                </label>
            </div>

            <div class="col-md-4">
                <input type="text" name="highRiskGrossIncomeAssetsCurrency" id="highRiskGrossIncomeAssetsCurrency" class="form-control" 
                {{#if selectedCurrency}}
                    value="{{selectedCurrency.cc}} - {{selectedCurrency.name}}" 
                {{else}}
                    value="{{defaultCurrency.cc}} - {{defaultCurrency.name}}" 
                {{/if}} 
                disabled />

                <input type="text" id="highRiskGrossIncomeAssets" class="form-control mt-2 autonumber" data-a-sep=","
                    data-min="0" data-max="1000000000" data-m-dec="2" name="highRiskGrossIncomeAssets" placeholder="0.0"
                    aria-describedby="basic-addon1" value="{{data.high_risk_gross_income_assets}}" />

            </div>
        </div>

        <!-- 4F.f -->
        <div class="row mb-2">
            <div class="col-md-8">
                <label for="highRiskGrossIncomeOthers">
                    Gross income through others:
                </label>
            </div>

            <div class="col-md-4">
                <input type="text" name="highRiskGrossIncomeOthersCurrency" id="highRiskGrossIncomeOthersCurrency" class="form-control"
                    {{#if selectedCurrency}} value="{{selectedCurrency.cc}} - {{selectedCurrency.name}}" {{else}}
                    value="{{defaultCurrency.cc}} - {{defaultCurrency.name}}" {{/if}} disabled />

                <input type="text" id="highRiskGrossIncomeOthers" class="form-control mt-2 autonumber" data-a-sep=","
                    data-min="0" data-max="1000000000" data-m-dec="2" name="highRiskGrossIncomeOthers" placeholder="0.0"
                    aria-describedby="basic-addon1" value="{{data.high_risk_gross_income_others}}" />
            </div>
        </div>

        <!-- 4G.a -->
        <div class="row">
            <div class="col-md-12">
                <label for="tangibleAssetsName">
                    The relevant tangible asset which the corporate and legal entity holds:
                </label>
            </div>

            <div class="col-md-12">
                <textarea type="text" id="tangibleAssetsName" class="form-control" name="tangibleAssetsName" rows="2"
                    maxlength="255">{{data.tangible_assets_name}}</textarea>

            </div>
        </div>
        <br>
        <!-- 4G.B -->
        <div class="row">
            <div class="col-md-12">
                <label for="tangibleAssetsExplanation">
                    Explanation of how that tangible asset is being used to generate income:
                </label>
            </div>

            <div class="col-md-12">
                <textarea type="text" id="tangibleAssetsExplanation" class="form-control"
                    name="tangibleAssetsExplanation" rows="3"
                    maxlength="1500">{{data.tangible_assets_explanation}}</textarea>

            </div>
        </div>

        <!-- 4G.B Attachments -->
        <div class="row">
            <div class="col-md-8">
                <div class="form-group">
                    <label for="EvidenceTangibleAssetsExplanationUploadBtn">
                        Upload attachments
                    </label>
                </div>
            </div>
            <div class=" col-md-4">
                <div class="text-right">
                    <button type="button" class="btn px-4 solid royal-blue" data-toggle="modal"
                        data-target="#upload-modal" id="EvidenceTangibleAssetsExplanationUploadBtn"
                        data-field="EvidenceTangibleAssetsExplanation-" data-entry="{{entryId}}"> Upload
                        document(s)</button>
                </div>
            </div>
        </div>
        <div id="tangible_assets_explanation_uploaded_files" class="my-2">
        </div>
        <br>

        <!-- 4H.a -->
        <div class="row">
            <div class="col-md-12">
                <label for="intangibleAssetsDecisions">
                    Identify the decisions for which each employee is responsible for in respect of the generation of
                    income from the
                    intangible asset:
                </label>
            </div>

            <div class="col-md-12">
                <textarea type="text" id="intangibleAssetsDecisions" class="form-control"
                    name="intangibleAssetsDecisions" rows="3"
                    maxlength="1500">{{data.intangible_assets_decisions}}</textarea>

            </div>
        </div>

        <!-- 4H.a Attachments -->
        <div class="row">
            <div class=" col-md-8">
                <div class="form-group">
                    <label for="EvidenceIntangibleAssetsDecisionsUploadBtn">
                        Upload attachments
                    </label>
                </div>
            </div>
            <div class=" col-md-4">
                <div class="text-right">
                    <button type="button" class="btn px-4 solid royal-blue" data-toggle="modal"
                        data-target="#upload-modal" id="EvidenceIntangibleAssetsDecisionsUploadBtn"
                        data-field="EvidenceIntangibleAssetsDecisions-" data-entry="{{entryId}}"> Upload
                        document(s)</button>
                </div>
            </div>
        </div>
        <div id="intangible_assets_decisions_uploaded_files" class="my-2">
        </div>
        <br>

        <!-- 4H.b -->
        <div class="row">
            <div class="col-md-12">
                <label for="intangibleAssetsNature">
                    The nature and history of strategic decisions (if any) taken by the entity in the Virgin Islands:
                </label>
            </div>

            <div class="col-md-12">
                <textarea type="text" id="intangibleAssetsNature" class="form-control" name="intangibleAssetsNature"
                    rows="3" maxlength="1500">{{data.intangible_assets_nature}}</textarea>

            </div>
        </div>

        <!-- 4H.b Attachments -->
        <div class="row">
            <div class="col-md-8">
                <div class="form-group">
                    <label for="EvidenceIntangibleAssetsNatureUploadBtn">
                        Upload attachments
                    </label>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-right">
                    <button type="button" class="btn px-4 solid royal-blue" data-toggle="modal"
                        data-target="#upload-modal" id="EvidenceIntangibleAssetsNatureUploadBtn"
                        data-field="EvidenceIntangibleAssetsNature-" data-entry="{{entryId}}"> Upload
                        document(s)</button>
                </div>
            </div>
        </div>
        <div id="intangible_assets_nature_uploaded_files" class="my-2">
        </div>
        <br>

        <!-- 4H.c -->
        <div class="row">
            <div class="col-md-12">
                <label for="intangibleAssetsTradingNature">
                    The nature and history of the trading activities (if any carried out in the Virgin Islands by which)
                    the intangible
                    assets is exploited for the purpose of generating income from third parties:
                </label>
            </div>

            <div class="col-md-12">
                <textarea type="text" id="intangibleAssetsTradingNature" class="form-control"
                    name="intangibleAssetsTradingNature" rows="3"
                    maxlength="1500">{{data.intangible_assets_trading_nature}}</textarea>

            </div>
        </div>

        <!-- 4H.c Attachments -->
        <div class="row">
            <div class="col-md-8">
                <div class="form-group">
                    <label for="EvidenceIntangibleAssetsTradingNatureUploadBtn">
                        Upload attachments
                    </label>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-right">
                    <button type="button" class="btn px-4 solid royal-blue" data-toggle="modal"
                        data-target="#upload-modal" id="EvidenceIntangibleAssetsTradingNatureUploadBtn"
                        data-field="EvidenceIntangibleAssetsTradingNature-" data-entry="{{entryId}}"> Upload
                        document(s)</button>
                </div>
            </div>
        </div>
        <div id="intangible_assets_trading_nature_uploaded_files" class="my-2">
        </div>
    </div>
</div>