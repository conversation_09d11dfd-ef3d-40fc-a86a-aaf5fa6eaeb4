{"_from": "password-strength-meter", "_id": "password-strength-meter@1.2.2", "_inBundle": false, "_integrity": "sha512-us4S3GPJZQGepydf/La6MUPDJYBuWdDGb/p9RUibsqHrp+0o26qhoaGRnz9Gp5m9sui4KsOep9mmBI/0RrNt+w==", "_location": "/password-strength-meter", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "password-strength-meter", "name": "password-strength-meter", "escapedName": "password-strength-meter", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/password-strength-meter/-/password-strength-meter-1.2.2.tgz", "_shasum": "679ecdccd59f80c6558042d30feaa647d0e1bd5c", "_spec": "password-strength-meter", "_where": "D:\\Netpro\\Trident\\Trident - Substance", "author": {"name": "Òscar Casajuana", "email": "<EMAIL>", "url": "https://github.com/elboletaire"}, "browserify-shim": {}, "bugs": {"url": "https://github.com/elboletaire/password-strength-meter/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A password strength meter plugin for jQuery", "devDependencies": {"brfs": "^1.4.3", "browserify": "^14.3.0", "browserify-istanbul": "^2.0.0", "browserify-shim": "^3.8.14", "es6-arrow-function": "^0.6.6", "eslint": "^4.5.0", "gulp": "^3.9.1", "gulp-chmod": "^2.0.0", "gulp-clean": "^0.3.2", "gulp-clean-css": "^3.0.3", "gulp-rename": "^1.2.2", "gulp-replace": "^0.5.4", "gulp-uglify": "^2.1.0", "istanbul": "^0.4.5", "jasmine": "^2.6.0", "jquery": "^3.1.1", "karma": "^1.7.0", "karma-browserify": "^5.1.1", "karma-chrome-launcher": "^2.1.1", "karma-coverage": "^1.1.1", "karma-firefox-launcher": "^1.0.1", "karma-jasmine": "^1.1.0", "karma-jasmine-jquery": "^0.1.1", "karma-phantomjs-launcher": "^1.0.4", "karma-sauce-launcher": "^1.1.0", "karma-virtualboxany-launcher": "^0.1.3", "watchify": "^3.9.0"}, "files": ["dist", "LICENSE.md", "src/example.png"], "homepage": "https://github.com/elboletaire/password-strength-meter#readme", "keywords": ["jquery-plugin", "ecosystem:jquery", "j<PERSON>y", "plugin", "password", "strength", "meter", "form", "forms"], "license": "GPL-3.0", "main": "dist/password.min.js", "name": "password-strength-meter", "peerDependencies": {"jquery": ">=1.14.0"}, "repository": {"type": "git", "url": "git+ssh://**************/elboletaire/password-strength-meter.git"}, "scripts": {"chrome": "npm test -- --browsers Chrome --single-run", "firefox": "npm test -- --browsers Firefox --single-run", "phantom": "npm test -- --browsers PhantomJS --single-run", "postversion": "git push && git push --tags && npm publish", "test": "eslint src && karma start test/support/karma.conf.js", "test-virtual": "VIRTUAL=true npm test"}, "version": "1.2.2"}