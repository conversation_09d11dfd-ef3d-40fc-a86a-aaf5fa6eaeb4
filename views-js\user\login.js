$(document).ready(function () {
    let bookmark = $(location).attr('hash');
    if (bookmark && bookmark.startsWith('#msdynttrid=')) {
       window.location.href = "https://clientportal-nev.tridenttrust.com";   
    }
 });
 
$("#loginBtn").on('click', async function (e) {
    e.preventDefault();
    $("#loginBtn").prop('disabled', true);
    
    try {       
        const recaptcha_site = $("#loginBtn").data('sitekey');
        const isTokenSet = await setCaptchaToken(recaptcha_site);

        if (isTokenSet) {
            $("#loginForm").submit();
        } else {
            $("#loginBtn").prop('disabled', false);
        }
    }
    catch (e) {
        $("#loginBtn").prop('disabled', false);
    }
});