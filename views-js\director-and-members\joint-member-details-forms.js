$(document).ready(function () {
    // Initially hide both buttons until user makes a selection for all questions
    $('#confirmBtn').hide();
    $('#requestUpdateBtn').hide();
    $('#is-correct-row').hide();

    // Handle inputs with centralized function
    $('input[type="radio"]').on('change', handleInputChanges);

    // Handle nominee arrangement radio buttons
    $('input[name="nominee-arrangement"]').on('change', function() {
        const value = $(this).val();
        if (value === 'yes') {
            showNomineeArrangementModal();
        }
    });

    let hasNomineeArrangement = $('input[name="nominee-arrangement"]:checked').val();
    // Show row if values are prefilled
    if (hasNomineeArrangement === "yes") {
        // If has nominators, disable show is correct row
        if ($("#requestUpdateBtn").attr('data-has-nominators') === "true") {
            $('#is-correct-row').show();
            $('#requestUpdateBtn').hide();
          } else {
            $('#requestUpdateBtn').show();
            $('#is-correct-row').hide();
        }

    } else if (hasNomineeArrangement === "no") {
        if ($("#requestUpdateBtn").attr('data-missing-values') === "true") {
            $('#requestUpdateBtn').show();
        } else {
            $('#is-correct-row').show();
        }
    }

    // Show request update if is confirmed
    if ($("#requestUpdateBtn").attr('data-is-confirmed') === "true") {
        $('#requestUpdateBtn').show();
    }
});

function handleInputChanges() {
    const hasNomineeArrangement = $('input[name="nominee-arrangement"]:checked').val();
    const infoCorrect = $('input[name="information-correct"]:checked').val();
    const $confirm = $('#confirmBtn');
    const $request = $('#requestUpdateBtn');
    const missingValues = $request.attr('data-missing-values') === 'true';
    const hasNominators = $request.attr('data-has-nominators') === 'true';

    // If not nominee, do nothing
    if (!hasNomineeArrangement && !hasNominators) return;

    // If no nominee arrangement, only allow request update
    if (hasNomineeArrangement === 'yes' && !hasNominators) {
      $confirm.hide();
      $request.show();
      $('#is-correct-row').hide();
      return;
    }
    $confirm.hide();
    $request.hide();

    // If no nominee arrangement, show is this information correct row
    $('#is-correct-row').show();

    // If missing values, only allow request update
    if (missingValues) {
      $confirm.hide();
      $request.show();
      return;
    }

    if (infoCorrect === 'yes') {
      $confirm.show();
      $request.hide();
    } else if (infoCorrect === 'no') {
      $confirm.hide();
      $request.show();
    }
}
async function requestUpdate(certNr, missingValues) {
    let template = Handlebars.templates.requestupdatepopup;
    let d = {
        type: "member",
        hasMissingData: (missingValues === "true")
    };
    let html = template(d);

    // Get the URL parts to construct the correct endpoint URL
    const urlParts = window.location.pathname.split('/');
    const masterClientCode = urlParts[2];
    const companyCode = urlParts[4];
    const confirmUrl = `/masterclients/${masterClientCode}/director-and-members/${companyCode}/members/joint/${certNr}/request-update`;
    const redirectUrl = `/masterclients/${masterClientCode}/director-and-members/${companyCode}/members`;

    // Determine if nominee arrangement is yes
    const hasNomineeArrangement = $('input[name="nominee-arrangement"]:checked').val() === 'yes';

    Swal.fire(
        {
            title: "Are you sure you want to request an update?",
            icon: "warning",
            html: html,
            showCancelButton: !0,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Submit",
            focusConfirm: false,
            showLoaderOnConfirm: true,
            reverseButtons: true,
            preConfirm: async () => {
                const changeType = $('#changeType').val();
                const changeReason = $('#changeReason').val();
                if (!changeType || changeType === "") {
                    Swal.showValidationMessage('Please select an option')
                }
                return axios.post(confirmUrl,
                    JSON.stringify({
                        changeType: changeType,
                        changeReason: changeReason,
                        hasNomineeArrangement: hasNomineeArrangement
                    }),
                    {
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json',
                        },
                    }
                ).then(response => {
                    try {
                        return response.data
                    } catch (e) {
                        throw new Error(response.statusText)
                    }
                }).catch(error => {
                    if (error?.response?.data) {
                        return error.response.data
                    }
                    return { status: error.status || 500, error: error }

                });

            },
            allowOutsideClick: () => !Swal.isLoading()
        }).then(function (result) {
            if (result.isConfirmed) {
                swal.showLoading();
                if (result.value.status === 200) {
                    Swal.fire({
                        title: 'Success',
                        html: 'We have received your request to update the member information. A Trident Trust Representative will be in touch shortly.',
                        icon: 'success',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#3085d6'
                    }).then(() => {
                        // Redirect to the member list screen
                        window.location.href = redirectUrl;
                    });
                } else if (result.value.status === 400 || result.value.status === 404) {
                    Swal.fire('Error', result.value.error, 'error');
                } else {
                    Swal.fire('Error', 'There was an error generating the request', 'error');
                }
            }
        })
}

async function confirmInformation(certNr) {
    // Get the URL parts to construct the correct endpoint URL
    const urlParts = window.location.pathname.split('/');
    const masterClientCode = urlParts[2];
    const companyCode = urlParts[4];
    const confirmUrl = `/masterclients/${masterClientCode}/director-and-members/${companyCode}/members/joint/${certNr}/confirm`;
    const redirectUrl = `/masterclients/${masterClientCode}/director-and-members/${companyCode}/members`;

    // Determine if nominee arrangement is yes
    const hasNomineeArrangement = $('input[name="nominee-arrangement"]:checked').val() === 'yes';

    Swal.fire({
        title: '<h4><strong>Confirm</strong></h4>',
        html: 'Are you sure you want to confirm the information? <br><br> By confirming the information, please note that you have consented to us processing your data for the BVI Registry pursuant to the law requirements.',
        icon: "info",
        iconColor: '#e6b800',
        customClass: {
            icon: 'swal-icon-small',
            padding: '20px',
        },
        showCancelButton: true,
        confirmButtonText: 'Submit',
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        focusConfirm: false,
        showLoaderOnConfirm: true,
        reverseButtons: true,
        preConfirm: async () => {
            return axios.post(confirmUrl,
            {
                hasNomineeArrangement: hasNomineeArrangement
            },
            {
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            }).then(response => {
                try {
                    return response.data
                } catch (e) {
                    throw new Error(response.statusText)
                }
            }).catch(error => {
                if (error?.response?.data) {
                    return error.response.data
                }
                return { status: error.status || 500, error: error }

            });
        },
        allowOutsideClick: () => !Swal.isLoading()
    }).then(function (result) {
        if (result.isConfirmed) {
            swal.showLoading();
            if (result.value.status === 200) {
                Swal.fire('Success', result.value.message, 'success').then(() => {
                    window.location.href = redirectUrl;
                });
            } else if (result.value.status === 400 || result.value.status === 404) {
                Swal.fire('Error', result.value.error, 'error');
            } else {
                Swal.fire('Error', 'There was an error with the confirmation request', 'error');
            }
        }
    })
}

$('#confirmBtn').on('click', async function () {
    await confirmInformation($(this).attr('data-jointcode'))
})

$('#requestUpdateBtn').on('click', async function () {
    await requestUpdate($(this).attr('data-jointcode'), $(this).attr('data-missing-values'))
})

function showNomineeArrangementModal() {
    Swal.fire({
        title: 'Action required',
        icon: "warning",
        html: '<div class="px-2">In the next step, please click "Request Update"' +
              '<p> and select the option of "Confirmation for Nominee Arrangement" from the dropdown list.</p>' +
              '<p class="mt-3">Further information and/or documentation will be requested, and we will be in touch with you in due course.</p></div>',
        confirmButtonText: 'OK',
        confirmButtonColor: "#3085d6"
    });
}
