const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

const fileSchema = new mongoose.Schema({
    fileId: { type: String, required: true, default: uuidv4},
    fileTypeId: { type: String, required: true},
    fieldName: { type: String, required: true },
    originalName: { type: String, required: true },
    encoding: { type: String, required: false },
    mimeType: { type: String, required: true },
    blobName: { type: String, required: true },
    container: { type: String, required: true },
    blob: { type: String, required: true },
    blobType: { type: String, required: true },
    size: { type: String, required: false },
    etag: { type: String, required: false },
    url: { type: String, required: true },
});
/*****************************Needs to be addressed later , not used vars is disabled in esling ******
*******************************Implemented for <PERSON><PERSON>lint checking in CI/CD*******************************
*****************************/
/* eslint no-unused-vars:0 */
// File Type Schema
const fileTypeSchema = new mongoose.Schema({
    id: { type: String, required: true,  default: uuidv4},
    internal: { type: String, required: true },
    external: { type: String, required: true },
    fileGroup:  { type: String, required: true },
    present:  { type: Boolean, required: true, default: false },
    explanation: { type: String, required: true, max: 100 },
    comments: { type: String, required: false },
    validated: { type: Boolean, required: true, default: false },
    uploadFiles: [ {type: fileSchema, required: true, default: []}],
    provided: { type:Boolean, required: false },
});
