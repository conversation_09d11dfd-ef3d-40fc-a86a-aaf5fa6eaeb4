
$('input[name="corporate[isSamePrincipalAddress]"]').on('change', function () {
    const value = $(this).val() === 'YES';
    if (value){
        $('#organizationForm #mailingAddressDetails').hide(200);
    }
    else{
        $('#organizationForm #mailingAddressDetails').show(200);
    }
});

$('input[name="corporate[isTridentClient]"]').on('change', function () {
    const value = $(this).val() === 'YES';
    if (value){
        $('#organizationForm .isTridentClient').hide(200);
        $('#shareholderAdditionalForm').hide();
    }
    else{
        $('#organizationForm .isTridentClient').show(200);

        if ($("#shareholderType").is(':checked')){
            $('#shareholderAdditionalForm').show(200);
        }
    }
});