const moment = require('moment');

module.exports = (sequelize, Sequelize) => {
    const MemberProfiles = sequelize.define('mem_MemberProfiles', {
        MFCode: { type: Sequelize.STRING(10), allowNull: false },
        MFUniqueNr: { type: Sequelize.INTEGER, allowNull: false, primaryKey: true },
        MFName: Sequelize.STRING(356),
        MFTypeCode: Sequelize.STRING(1),
        MFLegacyID: Sequelize.STRING(30),
        MFType: Sequelize.STRING(50),
        MFIncropNr: Sequelize.STRING(30),
        MFIncorpDate: {
            type: Sequelize.DATE,
            allowNull: true,
            get() {
                const date = this.getDataValue('MFIncorpDate');
                return date ? moment.utc(date).format('YYYY-MM-DD') : null;
            }
        },
        MFDateOfBirth: {
            type: Sequelize.DATE,
            allowNull: true,
            get() {
                const date = this.getDataValue('MFDateOfBirth');
                return date ? moment.utc(date).format('YYYY-MM-DD') : null;
            }
        },
        MFIncorpCountry: Sequelize.STRING(100),
        MFFormerName: Sequelize.STRING(356),
        MFBirthCountry: Sequelize.STRING(50),
        MFNationality: Sequelize.STRING(35),
        MFRAAddress: Sequelize.TEXT,
        MFROAddress: Sequelize.TEXT,
        MFRSAddress: Sequelize.TEXT,
        MFStatusCode: Sequelize.STRING(1),
        MFStatus: Sequelize.STRING(50),
        MFProductionOffice: Sequelize.STRING(10)
    }, {
        sequelize,
        tableName: 'mem_MemberProfiles',
        schema: 'dbo',
        timestamps: false
    });

    // Define associate as a static method on the model
    MemberProfiles.associate = function(models) {
        MemberProfiles.hasMany(models.mem_Directors, {
            foreignKey: 'DirUniqueNr',
            sourceKey: 'MFUniqueNr'
        });
    };

    return MemberProfiles;
};
