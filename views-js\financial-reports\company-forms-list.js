$(function () {

    $('[data-toggle="tooltip"]').tooltip({
        container: "body"
    });
})

$(document).ready(function () {


    $("#searchText").on("keyup", function () {
        var value = $(this).val().toLowerCase();
        $(".container .col-lg-4").filter(function () {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
        });
    });

     $("#company-reports-table").DataTable({
         info: false,
         ordering: false,
         paging: false

    });
});

$('#deleteFinancialReport').click(function () {
    deleteFinancialReport($(this).attr('data-mcc'), $(this).attr('data-company'), $(this).attr('data-id'))
})

function deleteFinancialReport(mcc, company, id) {
    Swal.fire({
        title: "Delete?",
        text: "Are you sure you want to delete this financial report?",
        icon: "question",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        cancelButtonText: "Cancel",
        confirmButtonText: "Ok"
    }).then(async function (t) {

        if (t.value) {
           
            $.ajax({
                type: "DELETE",
                url: "/masterclients/" + mcc + "/financial-reports/companies/" + company + "/" + id,
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    if (data.status === 200) {
                        location.reload();
                    } else {
                        Swal.fire(
                            {
                                title: "Error!",
                                text: data.message,
                                type: "warning",
                                showCancelButton: false,
                                confirmButtonColor: "#3085d6",
                                confirmButtonText: "Ok"
                            });
                    }
                },
                error: function (err) {
                    toastr["warning"]('Financial report could not be deleted, please try again later.', 'Error!');
                }
            });
        }
    });
}
$(document).on('click', '.showReopenContinueModal', function (event) {
    event.stopPropagation();
    showReopenFinancialReportModal($(this).attr('data-mcc'), $(this).attr('data-code'), $(this).attr('data-id'), $(this).attr('data-reopened-id'))
})

function showReopenFinancialReportModal(mcc, company, reportId, reopenedId) {
    $(".showReopenContinueModal").prop('disabled', true);
    $.ajax({
        type: 'GET',
        url: `./${company}/${reportId}/reopened-info/${reopenedId}`,
        timeout: 5000,
        success: function (data) {
            if (data.status === 200) {
                Swal.fire({
                    title: "Re-opened financial report",
                    icon: "warning",
                    backdrop: true,
                    showCancelButton: true,
                    confirmButtonColor: "#005C81",
                    confirmButtonText: 'Continue',
                    reverseButtons: true,
                    allowOutsideClick: false,
                    html: "<div class='text-justify'> Please note the following message from your Trident Trust Officer: " + data.reopenedData.reason + "</div> " +
                        (data.reopenedData.changeFinancialPeriodDates === true ?
                            "<hr> " +
                            "<div class='text-justify'> <h5>  Note that your Financial Period has been changed by a " +
                            "Trident Officer according to the ITA guidelines. Please make sure the information on " +
                            "the remaining pages of this submission is correct and in accordance with the amended dates.  </h5> </div>" :
                            ""),
                }).then((result) => {

                    if (result.isConfirmed) {

                        window.location.href = `/masterclients/${mcc}/financial-reports/companies/${company}/${reportId}`
                    } else {
                        $(".showReopenContinueModal").prop('disabled', false);
                    }
                });
            } else {
                console.log("ERROR", data)
                Swal.fire('Error', 'There was an error opening the re-open submission... Please try again later.', 'error').then(() => {
                    $(".showReopenContinueModal").prop('disabled', false);
                });
            }

        },
        error: function (err) {
            console.log(err);
            Swal.fire('Error', 'There was an error opening the re-open submission... Please try again later.', 'error').then(() => {
                $(".showReopenContinueModal").prop('disabled', false);
            });
        },
    });


}
