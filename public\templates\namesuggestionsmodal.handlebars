<div>
    <div class="row">
        <div class="col-md-12">
            <p>
                The name <b>{{incorporation.name}}</b> has been rejected by the review officer.
            </p>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <label for="suggestName">Please select one of the suggested names or other to enter a new one to
                submit*</label>

            {{#each incorporation.nameReservationInfo.suggestions}}

                <div class="custom-control custom-radio ml-2">
                    <input class="custom-control-input form-control" type="radio"
                           id="needsProvideDocuments{{@index}}" name="suggestedName" value="{{this}}">
                    <label class="custom-control-label" for="needsProvideDocuments{{@index}}">
                        {{this}}
                    </label>
                </div>
            {{/each}}
            <div class="custom-control custom-radio ml-2">
                <input class="custom-control-input form-control" type="radio"
                       id="needsProvideDocumentsOther" name="suggestedName" value="other"
                >
                <label class="custom-control-label" for="needsProvideDocumentsOther">
                    Other
                </label>
            </div>


        </div>
    </div>
</div>
<br>
<div class="row hide-element" id="otherNameBox">
    <div class="col-md-12">
        <label for="otherName">Other name*</label>
        <input type="text" class="form-control" id="otherName"
               name="otherName" placeholder="New name..."
               value=""/>
    </div>
</div>

</div>
<script type="text/javascript">

    $('input[name="suggestedName"]').on('change', function () {
        if ($(this).val() !== ''){
            $("input[name='suggestedName']").toggleClass("is-invalid", false);
        }

        if ($(this).val() === "other") {
            $("#otherName").prop('required', true);
            $('#otherNameBox').show(200);
        } else {
            $("#otherName").prop('required', false);
            $('#otherNameBox').hide(200);
        }
    });


</script>
