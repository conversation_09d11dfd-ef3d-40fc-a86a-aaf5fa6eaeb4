$(document).ready(function () {
    const missingDirBoData = $("#missingDirBoData").val();

    if (missingDirBoData === "true") {
        $('#missingDataModal').modal('show')
    }
});


async function requestUpdate(id, missingValues) {

    let template = Handlebars.templates.requestupdatepopup;
    let d = {
        isDirector: true,
        hasMissingData: (missingValues === "true")
    };
    let html = template(d);


    Swal.fire(
        {
            title: "Are you sure you want to request an update?",
            icon: "warning",
            html: html,
            showCancelButton: !0,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Submit",
            focusConfirm: false,
            showLoaderOnConfirm: true,
            reverseButtons: true,
            preConfirm: async  () => {
                const changeType = $('#changeType').val();
                const changeReason = $('#changeReason').val();
                if (!changeType || changeType === "") {
                    Swal.showValidationMessage('Please select an option')
                }
                return axios.post(`${window.location.href}/${id}/request-update`,
                    JSON.stringify({ changeType: changeType, changeReason: changeReason }),
                    {
                        headers: {
                            'Accept': 'application/json',
                            'Content-Type': 'application/json',
                        }, 
                    }
                ).then(response => {
                    try {
                        return response.data
                    } catch (e) {
                        throw new Error(response.statusText)
                    }
                }).catch(error => {
                    if (error?.response?.data) {
                        return error.response.data
                    }
                    return { status: error.status || 500, error: error }

                });

            },
            allowOutsideClick: () => !Swal.isLoading()
        }).then(function (result) {
            if (result.isConfirmed) {
                swal.showLoading();
                if (result.value.status === 200) {
                    Swal.fire('Success', result.value.message, 'success').then(() => {
                        location.reload();
                    });
                } else if (result.value.status === 400 || result.value.status === 404) {
                    Swal.fire('Error', result.value.error, 'error');
                } else {
                    Swal.fire('Error', 'There was an error generating the request', 'error');
                }
            }
        })
}

function showLastChange(type, reason) {
    let template = Handlebars.templates.requestupdatelog;
    let d = {
        changeType: type,
        changeReason: reason
    };
    let html = template(d);

    Swal.fire({
        title: "Last request for update",
        icon: "info",
        html: html
    })
}

async function confirmInformation(id) {
    Swal.fire({
        title: 'Do you want to confirm the information?',
        showCancelButton: true,
        confirmButtonText: 'Submit',
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        focusConfirm: false,
        showLoaderOnConfirm: true,
        reverseButtons: true,
        preConfirm: async () => {
            return axios.post(`${window.location.href}/${id}/confirm`, {},
            {
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            }).then(response => {
                try {
                    return response.data
                } catch (e) {
                    throw new Error(response.statusText)
                }
            }).catch(error => {
                if (error?.response?.data) {
                    return error.response.data
                }
                return { status: error.status || 500, error: error }

            });
        },
        allowOutsideClick: () => !Swal.isLoading()
    }).then(function (result) {
        if (result.isConfirmed) {
            swal.showLoading();
            if (result.value.status === 200) {
                Swal.fire('Success', result.value.message, 'success').then(() => {
                    location.reload();
                });
            } else if (result.value.status === 400 || result.value.status === 404) {
                Swal.fire('Error', result.value.error, 'error');
            } else {
                Swal.fire('Error', 'There was an error with the confirmation request', 'error');
            }
        }
    })
}

$('.confirmInformation').on('click', async function () {
    await confirmInformation($(this).attr('data-id'))
})

$('.requestUpdate').on('click', async function () {
    await requestUpdate($(this).attr('data-id'), $(this).attr('data-missing-values'))
})

$('.showLastChange').on('click', function () {
    showLastChange($(this).attr('data-type'), $(this).attr('data-reason'))
})

$(".isCorrectRadioBtn").on('change', (e) => {
    const target = e.target;
    const value = target.value;
    const vpUniqueRelationId = target.dataset.vpuniquerelationid;
    $(`#showInfo-${vpUniqueRelationId}`).hide(100);
    if (value === "NO") {
        $(`#confirmDataBtn-${vpUniqueRelationId}`).hide();
        $(`#updateBtn-${vpUniqueRelationId}`).show(100);
    } else {
        $(`#updateBtn-${vpUniqueRelationId}`).hide();
        $(`#confirmDataBtn-${vpUniqueRelationId}`).show(100);
    }

})

$('#requestAssistanceBtn').on('click', (event) => {
    $(this).prop('disabled', true);
    event.preventDefault();

    Swal.fire({
        title: "Do you want to request assistance?",
        icon: "question",
        showCancelButton: !0,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Confirm",
        focusConfirm: false,
        showLoaderOnConfirm: true,
        reverseButtons: true,
        backdrop: true,
        preConfirm: async  () => {
            return axios.post(`${window.location.href}/request-assistance`, {},
            {
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            }).then(response => {
                try {
                    return response.data
                } catch (e) {
                    throw new Error(response.statusText)
                }
            }).catch(error => {
                if (error?.response?.data) {
                    return error.response.data
                }
                return { status: error.status || 500, error: error }

            });

        },
        allowOutsideClick: () => !Swal.isLoading()
    }).then(function (result) {
        if (result.isConfirmed) {
            swal.showLoading();
            if (result.value.status === 200) {
                Swal.fire('Success', result.value.message, 'success').then(() => {
                    location.reload();
                });
            } else if (result.value.status === 400 || result.value.status === 404) {
                Swal.fire('Error', result.value.error, 'error');
                $("#requestAssistanceBtn").prop('disabled', false);
            } else {
                $("#requestAssistanceBtn").prop('disabled', false);
                Swal.fire('Error', 'There was an error generating the request', 'error');

            }
        } else {
            $("#requestAssistanceBtn").prop('disabled', false);
        }
    })

})