<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h3>
                            {{title}}
                        </h3>
                    </div>
                    <div class="card-body">
                        {{#if companyRequest.reviewerComment}}
                            <div class="row">
                                <div class="col-md-12 text-justify">
                                    <span>Reason for Request: {{companyRequest.reviewerComment}}</span>
                                </div>
                            </div>
                        {{/if}}


                        <form id="requestedFilesForm" method="POST">
                            <input type="text" hidden name="csrf-token" value="{{csrfToken}}">
                            <!-- STANDARD FILES TABLE -->
                            <div id="standard-files-table" class="table-responsive pt-2">
                                <table class="table table-striped">
                                    <thead>
                                    <tr>
                                        <th class="header-60-percent">Files Required</th>
                                        <th class="header-20-percent" class="text-center">
                                            Download Template
                                        </th>
                                        <th class="header-20-percent" class="text-center">Upload</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {{#each companyRequest.files }}
                                        <tr>
                                            <td>{{ name }}</td>
                                            <td class="text-center">
                                                {{#ifEquals name 'Entity Information Form (duly executed)'}}
                                                    <a href="/Entity information form.pdf" target="_blank" download
                                                       class="btn  solid royal-blue waves-effect waves-light width-xl">Download</a>
                                                {{/ifEquals}}
                                                {{#ifEquals name 'Relevant Person Information Form (duly executed)'}}
                                                    <a href="/Relevant person information form.pdf" target="_blank"
                                                       download
                                                       class="btn solid royal-blue waves-effect waves-light width-xl">Download</a>
                                                {{/ifEquals}}
                                            </td>
                                            <td class="text-center">
                                                <button
                                                        type="button"
                                                        form="requestedFilesForm"
                                                        class="btn solid royal-blue upload-button {{#if uploadFiles}}uploaded-files-btn{{/if}}"
                                                        id="btn-{{id}}"
                                                        data-toggle="modal"
                                                        data-target="#upload-request-file-modal"
                                                        data-file-type-id="{{id}}"
                                                        data-field="{{name}}"
                                                        data-request-id="{{../companyRequest._id}}"
                                                        data-mcc="{{../companyRequest.masterClientCode}}"
                                                        data-company="{{../companyRequest.companyCode}}"
                                                > {{#if uploadFiles}}
                                                        Modify
                                                    {{else}}
                                                        Upload
                                                    {{/if}}
                                                </button>
                                            </td>
                                        </tr>
                                    {{/each}}
                                    </tbody>
                                </table>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <label for="comment">Comment:</label>
                                    <textarea class="form-control" name="comment" id="comment" rows="5"
                                              placeholder="Add a comment..." maxlength="1000">{{companyRequest.comments}}</textarea>
                                </div>
                            </div>

                        </form>
                    </div>
                    <!-- CARD BODY END -->
                    <!-- CARD FOOTER NAV -->
                    <div class="row mt-2 justify-content-between">
                        <div class="col-md-2">
                            <a href="/masterclients/{{masterClientCode}}/company-files"
                               class="btn btn-secondary width-lg waves-effect waves-light">
                                Back
                            </a>
                        </div>
                        <div class="col-10 d-flex justify-content-end">
                            <button
                                    type="button"
                                    id="save-requested-files-button"
                                    class="btn solid royal-blue mx-2 px-4"
                                    data-toggle="modal"
                                    data-id="{{ id }}"
                                    data-mcc="{{masterClientCode}}"
                                    data-page="1"
                            >
                                Save
                            </button>

                            <button
                                    type="button"
                                    id="submit-requested-files-button"
                                    class="btn solid royal-blue mx-1 px-4"
                                    data-toggle="modal"
                                    data-id="{{ id }}"
                                    data-mcc="{{masterClientCode}}"
                                    data-page="1"
                            >
                                SUBMIT
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
{{>requested-files/modals/upload-file-modal}}
<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script type="text/javascript" src="/views-js/company-requested-files/open-requested-file-view.js"></script>
