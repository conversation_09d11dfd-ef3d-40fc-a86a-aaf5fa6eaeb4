<div id="trustForm">

    <!-- TRUST DETAILS -->
    <div id="trustDetails">
        <h4>Details</h4>
        <div class="row mt-2">
            <div class="col-6 d-flex justify-content-between">
                <label>Is this company already a TridentTrust client?</label>
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" class="custom-control-input" id="trust-is-already-client-yes"
                           name="trust[isTridentClient]" {{#if relation.details.isTridentClient }}
                           checked {{/if}} value="YES"/>
                    <label class="custom-control-label" for="trust-is-already-client-yes">Yes</label>
                </div>
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" class="custom-control-input" id="trust-is-already-client-no"
                           name="trust[isTridentClient]" {{#unless relation.details.isTridentClient }}
                           checked {{/unless}} value="NO"/>
                    <label class="custom-control-label" for="trust-is-already-client-no">No</label>
                </div>
            </div>
            <div class="col-6">
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-2">
                <label for="trust-details-organization-name">Trust Name*</label>
            </div>
            <div class="col-4">
                <input type="text" class="form-control"  id="trust-details-organization-name"  name="details[organizationName]"
                       value="{{relation.details.organizationName}}" required/>
            </div>
            <div class="col-2">
                <label for="trust-details-incorporation-number">Trustee Name</label>
            </div>
            <div class="col-4">
                <input id="trust-details-incorporation-number" class="form-control" type="text" name="details[incorporationNumber]"
                       value="{{relation.details.incorporationNumber}}" required/>
            </div>
        </div>
        <div class="row mt-2 isTridentClient {{#if relation.details.isTridentClient}}
             hide-element {{/if}}">
            <div class="col-2">
                <label for="trust-details-tax-residence">Tax Residence</label>
            </div>
            <div class="col-4">
                {{>file-reviewer/shared/select-country selectId="details[taxResidence]" group="trust" required="true"
                        value=relation.details.taxResidence}}
            </div>
            <div class="col-2">
                <label for="trust-details-registration-number"
                >Business Registration Number (if applicable)</label
                >
            </div>
            <div class="col-4">
                <input class="form-control" type="text"  id="trust-details-registration-number" name="details[businessNumber]"
                       value="{{relation.details.businessNumber}}" required/>
            </div>
        </div>
        <div class="row mt-2 isTridentClient {{#if relation.details.isTridentClient}}
             hide-element {{/if}}">
            <div class="col-2">
                <label for="trust-details-incorporation-date">Date of Trust Establishment</label>
            </div>
            <div class="col-4">
                <input class="form-control datepicker" type="date" id="trust-details-incorporation-date"
                       name="details[incorporationDate]" required placeholder="mm/dd/yyyy"
                       value="{{#formatDate relation.details.incorporationDate "YYYY-MM-DD"}} {{/formatDate }}"/>
            </div>
            <div class="col-2">
                <label for="details[incorporationCountry]">Country of Incorporation</label>
            </div>
            <div class="col-4">
                {{>file-reviewer/shared/select-country selectId="details[incorporationCountry]" group="trust" required="true"
                        value=relation.details.incorporationCountry}}
            </div>
        </div>

        <div class="isTridentClient {{#if relation.details.isTridentClient}}
             hide-element {{/if}}">
            <!-- DETAILS TABLE -->
            {{>file-reviewer/shared/relation-file-table tableId="detailsTable"  name="details" group="trust"
                    files=(ternary newRelation relation.trustFiles.details relation.details.files)}}

            <!-- DETAILS PARTNER TABLE -->
            {{>file-reviewer/shared/certificate-partner-table group="trust"
                    partnerFiles=(ternary newRelation relation.trustFiles.detailsPartner relation.detailsPartner.files)
                    relationId=relation._id}}
        </div>


    </div>


    <div class="isTridentClient {{#if relation.details.isTridentClient}}
         hide-element {{/if}}">
        <hr class="mt-2"/>
        <!-- PRINCIPAL ADDRESS DETAILS -->
        <div id="principalAddressDetails">
            <h4>Principal Address</h4>
            {{>file-reviewer/relations/sections/address-details-form group="trust"
                    principalAddress=relation.principalAddress formType="principalAddress"}}
        </div>
        <hr class="mt-2" />

        <div class="row mt-2">
            <div class="col-6">
                <h4>Is mailing address the same as Principal address?</h4>
            </div>
            <div class="col-6 d-flex justify-content-end">
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" class="custom-control-input" id="trust-is-same-address-yes"
                           name="trust[isSamePrincipalAddress]" {{#if relation.isSamePrincipalAddress }} checked {{/if}} value="YES"/>
                    <label class="custom-control-label" for="trust-is-same-address-yes">Yes</label>
                </div>
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" class="custom-control-input" id="trust-is-same-address-no"
                           name="trust[isSamePrincipalAddress]" {{#unless relation.isSamePrincipalAddress }}
                           checked {{/unless}} value="NO"/>
                    <label class="custom-control-label" for="trust-is-same-address-no">No</label>
                </div>
            </div>
        </div>
        <br>

        <!-- MAILING ADDRESS DETAILS -->
        <div id="mailingAddressDetails" {{#if relation.isSamePrincipalAddress}} class="hide-element" {{/if}}>
            <h4>Mailing Address</h4>
            {{>file-reviewer/relations/sections/address-details-form group="trust"
                    principalAddress=relation.mailingAddress formType="mailingAddress"}}
        </div>
        <hr class="mt-2" />

        <!-- MUTUAL FUND DETAILS -->
        <div class="mutualFundDetails">
            {{>file-reviewer/relations/sections/mutual-fund-details-form group="trust"
                    mutualFund=(ternary newRelation relation.trustFiles.mutualFund relation.mutualFundDetails) relationId=relation._id}}
        </div>
        <hr class="mt-2" />
    </div>


</div>

<script type="text/javascript" src="/views-js/partials/file-reviewer/relations/trust-form-component.js"></script>
