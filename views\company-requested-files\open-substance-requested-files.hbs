<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h3>
                            {{title}}
                        </h3>
                    </div>
                    <div class="card-body">
                        {{#if request.comment}}
                            <div class="row">
                                <div class="col-md-12 text-justify">
                                    <label>Reason for Request: {{request.comment}}</label>
                                </div>
                            </div>
                        {{/if}}
                        <br>
                        {{#if request.files}}
                            <label>
                                Attachments:
                            </label>
                            <br>

                            <ul>
                                {{#each request.files}}
                                    <li>
                                        <a target="_blank" href="/masterclients/{{../masterClientCode}}/company-files/substance/{{../entry.company_data.code}}/{{../entry._id}}/download-rfi-file/{{../request.id}}/{{fileId}}"> {{originalname}} </a>
                                    </li>
                                {{/each}}
                            </ul>

                            <br>
                        {{/if}}

                        <div class="row">
                            <input type="text" hidden name="csrf-token" value="{{csrfToken}}">
                            <div class="col-md-12">
                                <label>Include any original document to support your response below - must be an original English or an English Translation:</label>
                                <div id="frmUploadModal" class="dropzone">
                                    <div class="fallback">
                                        <input name="SubmitEvidence" type="file" multiple />
                                    </div>
                                    <div class="dz-message needsclick">
                                        <i class="h1 text-muted dripicons-cloud-upload"></i>
                                        <h3>Drop files here or click to upload.</h3>
                                        <span class="text-muted font-13">Maximum of 10 Files, PDF only (maximum 5 MB per file). Files must not be password
                                protected.</span>
                                    </div>
                                </div>
                            </div> <!-- end col -->
                            <div id="uploadedRequestedFiles" class="mt-2 text-left text-muted col-md-8 my-3">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <label for="comment">Please provide your response to the ITA in the below comments -  English ONLY:</label>
                                <textarea class="form-control" name="comment" id="comment" rows="5" required
                                    maxlength="1000"></textarea>
                            </div>
                        </div>


                    </div>
                    <!-- CARD BODY END -->
                    <!-- CARD FOOTER NAV -->
                    <div class="row mt-2 justify-content-between">
                        <div class="col-md-2">
                            <a href="/masterclients/{{masterClientCode}}/company-files"
                                class="btn btn-secondary width-lg waves-effect waves-light">
                                Back
                            </a>
                        </div>
                        <div class="col-10 d-flex justify-content-end">
                            <button type="button" id="submit-requested-files-button"
                                class="btn solid royal-blue mx-1 px-4" data-toggle="modal" data-id="{{ id }}"
                                data-mcc="{{masterClientCode}}" data-page="1">
                                SUBMIT
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script src="/templates/uploadedfiles.precompiled.js"></script>
<script type="text/javascript" src="/views-js/company-requested-files/open-substance-requested-files.js"></script>
