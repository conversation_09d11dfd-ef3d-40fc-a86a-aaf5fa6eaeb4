<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <h4 class="mt-3">{{title}}</h4>
                    <div class="table-responsive">
                        <table id="horizontal-datatable" class="table table-striped ">
                            <thead>
                                <tr>
                                    <th class="header-5-percent">New</th>
                                    <th class="header-55-percent">Subject</th>
                                    <th class="header-15-percent">Master Clients</th>
                                    <th class="header-15-percent">Received at</th>
                                    <th class="header-10-percent">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each results}}
                                <tr>
                                    <td class="text-center">{{#unless opened}}<i
                                            class="fa fa-circle text-danger"></i>{{/unless}}</td>
                                    <td>{{subject}} </td>
                                    <td>
                                        <span title="{{masterClientsText}}" data-toggle="tooltip"
                                            data-placement="top">{{firstMasterClients}} </span>
                                        {{#ifCond masterClientAmount '>' 5}}
                                        ...
                                        {{/ifCond}}
                                    </td>
                                    <td> {{formatDate sentAt "YYYY/MM/DD"}}</td>
                                    <td>
                                        <button data-message-id="{{messageId}}" data-ids="{{ids}}"
                                            class="btn solid btn-primary btn-block openMessage">Read
                                        </button>
                                    </td>
                                </tr>
                                {{else}}
                                <tr>
                                    <td colspan="4" class="text-center font-italic">
                                        There are no messages
                                    </td>
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                    </div>
                    <br>
                    <a href="/masterclients"
                        class="btn btn-secondary waves-effect waves-light width-xl mt-3 mr-2">Back</a>
                </div>
            </div>
        </div>
    </div>
</main>
{{>messages/message-modal }}
<script type="text/javascript" src="/views-js/messages/list.js"></script>
