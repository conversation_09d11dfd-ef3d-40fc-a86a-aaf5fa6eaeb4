const ObjectId = require('mongoose').Types.ObjectId;
const fileSchema = require('./file');
const mongoose = require('mongoose');

const freeQuestionSchema = new mongoose.Schema({
    internal: {type: String, required: true},
    external: {type: String, required: true},
});

const relationSchema = new mongoose.Schema({
    referenceId: {type: ObjectId, required: false},
    group: {type: String, required: false},
    percentage: {type: String, required: false}
});

const ClientSchema = new mongoose.Schema(
    {
        type: {type: String, required: true, default: 'non managed'},
        companyCode: {type: String, required: true},
        companyName: {type: String, required: false},
        recordDetails: {
            recordHolder: {type: String, max: 100},
            primaryAddress: {type: String, max: 100},
            secondaryAddress: {type: String, max: 100},
            state: {type: String, max: 100},
            city: {type: String, max: 100},
            postalCode: {type: String, max: 10},
            email: {type: String, max: 100},
            operationCountry: {type: String, max: 100},
        },
        companyActivity: {
            activity: {type: String, required: false},
        },
        files: [fileSchema],
        freeQuestions: [freeQuestionSchema],
        naturalRelations: [relationSchema],
        organizationRelations: [relationSchema],
        createdAt: {type: Date, required: true},
        updatedAt: {type: Date, required: true},
        organizationId: {type: ObjectId, required: false},
        partitionkey: {type: String, required: true}
    }
);


//Export model
module.exports = mongoose.model('clients', ClientSchema);
