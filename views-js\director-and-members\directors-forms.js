$(document).ready(function () {
    const missingDirMemberData = $("#missingDirMemberData").val();

    if (missingDirMemberData === "true") {
        $('#missingDataModal').modal('show')
    }
});

// Extract masterClientCode from URL path
const urlPath = window.location.pathname;
const pathParts = urlPath.split('/');
const masterClientCodeIndex = pathParts.indexOf('masterclients') + 1;
const masterClientCode = pathParts[masterClientCodeIndex];

function showLastChange(type, reason) {
    let template = Handlebars.templates.requestupdatelog;
    let d = {
        changeType: type,
        changeReason: reason
    };
    let html = template(d);

    Swal.fire({
        title: "Last request for update",
        icon: "info",
        html: html
    })
}

$('.showLastChange').on('click', function () {
    showLastChange($(this).attr('data-type'), $(this).attr('data-reason'))
})

$('#requestAssistanceBtn').on('click', (event) => {
    $(this).prop('disabled', true);
    event.preventDefault();

    Swal.fire({
        title: "Are you sure you want to request assistance?",
        icon: "info",
        iconColor: "#F59E0C",
        showCancelButton: !0,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Confirm",
        focusConfirm: false,
        showLoaderOnConfirm: true,
        reverseButtons: true,
        backdrop: true,
        preConfirm: async  () => {
            return axios.post(`${window.location.href}/request-assistance`, {},
            {
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            }).then(response => {
                try {
                    return response.data
                } catch (e) {
                    throw new Error(response.statusText)
                }
            }).catch(error => {
                if (error?.response?.data) {
                    return error.response.data
                }
                return { status: error.status || 500, error: error }

            });

        },
        allowOutsideClick: () => !Swal.isLoading()
    }).then(function (result) {
        if (result.isConfirmed) {
            swal.showLoading();
            if (result.value.status === 200) {
                Swal.fire('Success', result.value.message, 'success').then(() => {
                    location.reload();
                });
            } else if (result.value.status === 400 || result.value.status === 404) {
                Swal.fire('Error', result.value.error, 'error');
                $("#requestAssistanceBtn").prop('disabled', false);
            } else {
                $("#requestAssistanceBtn").prop('disabled', false);
                Swal.fire('Error', 'There was an error generating the request', 'error');

            }
        } else {
            $("#requestAssistanceBtn").prop('disabled', false);
        }
    })
})

$('.show-more-btn').on('click', function(e) {
    e.preventDefault();
    const directorId = $(this).data('id');
    window.location.href = `${window.location.href}/${directorId}/details`;
}) 