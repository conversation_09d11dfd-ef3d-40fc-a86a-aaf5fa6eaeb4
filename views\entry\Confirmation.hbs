<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class='contour'>
                    {{#ifCond entry.version '<' 5}}
                        <h2>6. Confirmation</h2>
                    {{else}}
                        <h2>7. Confirmation</h2>
                    {{/ifCond}}
                    {{# if validationErrors }}
                    {{# each validationErrors }}
                    {{renderValidationMessage this.msg this.field}}
                    {{/each}}
                    {{/if}}
                    {{#if submitted}}
                        {{#if scheduled}}
                            <p class="alert alert-success">Your submission is successfully scheduled and will be sent to Trident on {{formatDate entry.scheduled_at 'YYYY-MM-DD'}}.
                                Change to this application can be made prior to this date. <br>
                                The submission will be finalized on settlement of the annual invoice.
                            </p>
                        {{else}}
                            {{#if prePaid}}
                                <p class="alert alert-success">
                                    Thank you for submitting the declaration. Click Download for the summary of the submission. You have no outstanding payment for this submission.
                                </p>

                            {{else}}
                                {{#if pre2022Submission}}
                                    <p class="alert alert-success">Thank you for submitting the declaration.
                                        Click Download for the summary of the submission and contact the ES support team for settlement of the outstanding payment.</p>
                                    <p class="alert alert-warning">Please note that the declaration will not be filed with the ITA until payment is received.</p>
                                {{else}}
                                    <p class="alert alert-success">Thank you for submitting the declaration. Click Download for the summary of the submission.</p>
                                    <p class="alert alert-warning">
                                        Please note that payment is included within your annual licence fee invoice therefore,
                                        the declaration will be finalized once payment is received.
                                        <br>
                                        For further assistance, please contact your Trident officer.
                                    </p>
                                {{/if}}
                            {{/if}}
                        {{/if}}
                        <div class="col-12">
                            <div class="row justify-content-between">
                                <div>
                                    <a href="/masterclients" class="btn solid royal-blue width-xl">Home</a>
                                    <a href="/masterclients/{{entry.company_data.masterclientcode}}/substance/companies" class="btn solid royal-blue width-xl">Submission overview</a>
                                </div>
                                <div>
                                    <a href="/masterclients/{{entry.company_data.masterclientcode}}/substance/companies/{{entry.company_data.code}}/forms/{{entry._id}}/submission.pdf"
                                        target="_blank" class="btn solid royal-blue width-xl">Download</a>&nbsp;&nbsp;
                                </div>
                            </div>
                        </div>
                    {{else}}
                        <form method="POST" class='enquiry' autocomplete="off" id="submitForm">
                            <input type="text" hidden name="csrf-token" value="{{csrfToken}}">
                            <div class="container-fluid">
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="card">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-10">
                                                        <div class="form-group mb-3">
                                                            <label class="mb-2" for="confirmed">I confirm that the
                                                                information provided above is true and accurate to the
                                                                best
                                                                of my knowledge and belief and that by submission of
                                                                this
                                                                information to the Registered Agent, I have provided all
                                                                the
                                                                information required to complete the economic substance
                                                                self-assessment.</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <div class="checkbox checkbox-primary mb-2">
                                                            <input type="checkbox" class="custom-control-input"
                                                                id="confirmed" name="confirmed" {{#if
                                                                entry.confirmation.confirmed}}checked{{/if}}>
                                                            <label class="custom-control-label"
                                                                for="confirmed"><b>Confirm</b></label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-10">
                                                        <div class="form-group mb-3">
                                                            <label class="mb-2" for="confirmed_authority">Please confirm
                                                                you
                                                                have the authority to act on behalf of the
                                                                company.</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2" align="right">
                                                        <div class="checkbox checkbox-primary mb-2">
                                                            <input type="checkbox" class="custom-control-input"
                                                                id="confirmed_authority" name="confirmed_authority" {{#if
                                                                entry.confirmation.confirmed_authority}}checked{{/if}}>
                                                            <label class="custom-control-label"
                                                                for="confirmed_authority"><b>Confirm</b></label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-10">
                                                        <div class="form-group mb-3">
                                                            <label class="mb-2" for="confirmed_conditions">I confirm and
                                                                acknowledge that the Registered Agent has a legitimate
                                                                interest for processing any personal data provided
                                                                above;
                                                                specifically, in order to ensure the Registered Agent’s
                                                                and
                                                                the Entity’s compliance with relevant BVI law. I further
                                                                acknowledge that the processing of such personal data
                                                                may
                                                                include its transfer to BVI competent authorities and
                                                                that
                                                                the Registered Agent’s processing of any personal data
                                                                will
                                                                be done in accordance with the <a target="_blank" class="text-decoration-underline"
                                                                    href="https://tridenttrust.com/legal-pages/data-protection/">Trident Trust
                                                                    Data
                                                                    Privacy Policy</a>, which I have read and
                                                                understood</label>


                                                        </div>
                                                    </div>
                                                    <div class="col-md-2" align="right">
                                                        <div class="checkbox checkbox-primary mb-2">
                                                            <input type="checkbox" class="custom-control-input"
                                                                id="confirmed_conditions" name="confirmed_conditions" {{#if
                                                                entry.confirmation.confirmed_conditions}}checked{{/if}}>
                                                            <label class="custom-control-label"
                                                                for="confirmed_conditions"><b>Confirm</b></label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-10">
                                                        <div class="form-group mb-3">
                                                            <label class="mb-2" for="confirmed_payment">
                                                                {{#if pre2022Submission}}
                                                                    I confirm and
                                                                    acknowledge that the submission fee in the amount of
                                                                    US$200 is due and payable in order to complete the submission process
                                                                {{else}}
                                                                    I confirm and acknowledge that the US$200 submission fee is
                                                                    included within the annual licence fee invoice and
                                                                    that settlement of this invoice is due and payable in
                                                                    order to complete the submission process.
                                                                {{/if}}

                                                            </label>

                                                        </div>
                                                    </div>
                                                    <div class="col-md-2" align="right">
                                                        <div class="checkbox checkbox-primary mb-2">
                                                            <input type="checkbox" class="custom-control-input"
                                                                id="confirmed_payment" name="confirmed_payment" {{#if
                                                                entry.confirmation.confirmed_payment}}checked{{/if}}>
                                                            <label class="custom-control-label"
                                                                for="confirmed_payment"><b>Confirm</b></label>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <div class="form-group mb-3">
                                                            <label class="mb-2" for="relation_to_entity">Relation to
                                                                entity</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group mb-3">
                                                            <select class="form-control w-100 pb-1" id="relation_to_entity"
                                                                name="relation_to_entity" data-toggle="select2" value="{{entry.confirmation.relation_to_entity}}">
                                                                <option value="Director">Director</option>
                                                                <option value="Sole Director">Sole Director</option>
                                                                <option value="Alternate Director">Alternate Director
                                                                </option>
                                                                <option value="Secretary">Secretary</option>
                                                                <option value="Tax Advisor">Tax Advisor</option>
                                                                <option value="Legal Advisor">Legal Advisor</option>
                                                                <option value="Banker">Banker</option>
                                                                <option value="Authorized Agent">Authorized Agent
                                                                </option>
                                                                <option value="Authorized Representative">Authorized
                                                                    Representative
                                                                </option>
                                                                <option value="Accountant">Accountant</option>
                                                                <option value="Other (please specify)">Other (please
                                                                    specify)
                                                                </option>
                                                            </select>
                                                            <input type="text" id="relation_to_entity_other"
                                                                class="form-control hide-element mt-2"
                                                                name="relation_to_entity_other"
                                                                placeholder="Specify relation to entity"
                                                                value="{{entry.confirmation.relation_to_entity_other}}">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <div class="form-group mb-3">
                                                            <label class="mb-2" for="user_fullname">Name of the person
                                                                stating the declaration</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group mb-3">
                                                            <input type="text" id="user_fullname" class="form-control"
                                                                name="user_fullname"
                                                                value="{{entry.confirmation.user_fullname}}">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <div class="form-group mb-3">
                                                            <label class="mb-2" for="user_phonenumber">Phone
                                                                number</label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group mb-3">
                                                            <input type="text" id="user_phonenumber" class="form-control autonumber"
                                                                placeholder="****** 555 1234" data-toggle="tooltip" data-placement="top"
                                                                name="user_phonenumber" title="Please enter entire phone number including country & area code"
                                                                value="{{entry.confirmation.user_phonenumber}}">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <div class="progress">
                                                            {{#ifCond entry.version '<' 5}} 
                                                            <div class="progress-bar w-100" role="progressbar"
                                                                aria-valuenow="6" aria-valuemin="0"
                                                                aria-valuemax="6">6 of 6
                                                            </div>
                                                            {{else}}
                                                            <div class="progress-bar w-100" role="progressbar" aria-valuenow="7" aria-valuemin="0"
                                                                aria-valuemax="7">7 of 7</div>
                                                            {{/ifCond}}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div> <!-- end card-body -->
                                        </div> <!-- end card -->
                                        <div class="row">
                                            <div class="col-md-7">
                                                <div class="form-group mb-2">
                                                    <input type="submit" name="submitAction" value="Previous page"
                                                        class="btn btn-secondary waves-effect waves-light width-xl" />
                                                </div>
                                            </div>

                                            <div class="d-flex col-md-5 justify-content-end">
                                                <div class="form-group mb-2 mr-2" align="right">
                                                    <a href="/masterclients/{{entry.company_data.masterclientcode}}/substance/companies/{{entry.company_data.code}}/forms/{{entry._id}}/submission.pdf"
                                                        target="_blank"
                                                        class="btn btn-primary waves-effect waves-light width-xl">Preview</a>
                                                </div>

                                                {{#if alreadyPaid }}
                                                <div class="form-group mb-2 {{#if scheduledSubmission}}tooltip-wrapper{{/if}}"
                                                    {{#if scheduledSubmission}}data-toggle="tooltip" placement="top"
                                                    title="You have a submission scheduled on a future date. Once this submission is sent, you will be able to schedule a new one."
                                                    {{/if}} align="right">
                                                    <input type="submit" name="submitAction" 
                                                            {{#ifEquals entry.status 'RE-OPEN'}}
                                                               value="Resubmit" id="btnSubmitForm"
                                                            {{else}}
                                                               value="Submit" id="btnSubmitForm"
                                                            {{/ifEquals}}
                                                        form="submitForm"
                                                        class="btn btn-primary waves-effect waves-light width-xl" {{#if
                                                        scheduledSubmission}}disabled{{/if}} />
                                                </div>
                                                {{else}}

                                                    <div class="form-group mb-2" align="right">
                                                        <input type="submit" name="submitAction"
                                                            {{#ifEquals entry.status 'RE-OPEN'}}
                                                               value="Resubmit" id="btnSubmitForm"
                                                            {{else}}
                                                               value="Submit" id="btnSubmitForm"
                                                            {{/ifEquals}}
                                                               form="submitForm"
                                                                class="btn btn-primary waves-effect waves-light width-xl" />
                                                    </div>
                                                {{/if}}
                                            </div>
                                        </div>
                                    </div>
                                </div> <!-- end row -->
                            </div> <!-- end container-fluid -->
                        </form>
                    {{/if}}
                </div>
                <input hidden id="financialPeriodEnds" name="financialPeriodEnds" value="{{formatDate entry.entity_details.financial_period_ends 'YYYY-MM-DD'}}">
                <input hidden id="isReopened" name="isReopened" value="{{isReopened}}">
            </div>
        </div>
    </div>
</main>
<script src="/javascripts/libs/jquery-mask-plugin/jquery.mask.min.js"></script>
<script src="/views-js/entry/Confirmation.js"></script>
