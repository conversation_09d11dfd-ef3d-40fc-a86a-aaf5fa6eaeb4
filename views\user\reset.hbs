<link href="/javascripts/libs/password-strength-meter/dist/password.min.css" rel="stylesheet" type="text/css" />
<script src="/javascripts/libs/password-strength-meter/dist/password.min.js"></script>
<script src="https://www.recaptcha.net/recaptcha/api.js?render={{RECAPTCHA_SITE_KEY}}"></script>

<main class="">
    <div class="container">
        <div class="row">
            <div class="col-lg-2"></div>
            <div class="col-sm-12 col-lg-8">
                <div class='contour'>
                    <div class="header-title-container">
                        <h1>{{title}}</h1>
                    </div>
                    {{#if activated}}
                        <p class="alert alert-success">{{ message }}</p>
                        <p>Click <a href="/">here</a> to login to the portal</p>
                    {{else}}              
                        {{#if message }}                       
                                <p class="alert alert-danger">{{ message }}</p>                        
                        {{/if}}
                            <form id="resetPasswordForm" class='enquiry' method="POST" autocomplete="off">

                                <input type="text" hidden name="csrf-token" value="{{csrfToken}}">
                                <input type="hidden" id="g-recaptcha-response" name="g-recaptcha-response">

                                Create a new password in the form below and click <b>"Reset my account" </b> <br /><br />
                                
                                Please make sure to choose a new and unique password that you haven't used before. This helps us ensure the safety of
                                your account and sensitive information. <br> <br>

                                <div class="form-group mb-3">
                                    <label for="password">Password</label>
                                    <input id="password" maxlength="40" name="password" size="20" type="password" required="" aria-required="true" class="form-control" data-toggle="tooltip" placement="top" 
                                        title="The password should be at least 12 characters long and include the following: lowercase letters, uppercase letters, numbers, and special characters">
                                </div>
                                <div class="form-group mb-3">
                                    <label for="confirm">Confirm Password</label>
                                    <input id="confirm" maxlength="40" name="confirm" size="20" type="password" required="" aria-required="true" class="form-control">
                                </div>

                                <button id="resetPasswordBtn" type="submit" class="btn btn-primary waves-effect waves-light "
                                    data-sitekey="{{RECAPTCHA_SITE_KEY}}">
                                    Reset my account
                                </button>
                            </form>
                        {{/if}}
                </div>
            </div>
            <div class="col-lg-2"></div>
        </div>
    </div>
</main>
<script type='text/javascript' src="/views-js/helpers/set-captcha-token.js"></script>
<script type='text/javascript' src="/views-js/user/reset.js"></script>
