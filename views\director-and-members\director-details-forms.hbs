<main class="">    
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                {{#if isIndividual}}
                <!-- Individual Director Details -->
                <h4 class="mb-3"><strong>Individual Director Details</strong></h4>
                
                <table class="table">
                    <tbody>
                        <tr>
                            <th scope="row">
                                <strong>Director Type</strong>
                                {{> director-and-members/required-info-icon field="RelationType" missingValues=missingValues}}
                            </th>
                            <td>{{director.OfficerType}}</td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <strong>Name</strong>
                                {{> director-and-members/required-info-icon field="DirName" missingValues=missingValues}}
                            </th>
                            <td>{{director.Name}}</td>
                        </tr>
                        <tr>
                            <th scope="row"><strong>Former Name</strong></th>
                            <td>{{director.FormerN<PERSON>}}</td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <strong>Appointment Date</strong>
                                {{> director-and-members/required-info-icon field="DirFromDate" missingValues=missingValues}}
                            </th>
                            <td>{{formatDate director.FromDate "YYYY-MM-DD"}}</td>
                        </tr>
                        <tr>
                            <th scope="row"><strong>Cessation Date</strong></th>
                            <td>{{formatDate director.ToDate "YYYY-MM-DD"}}</td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <strong>Service Address</strong>
                                {{> director-and-members/required-info-icon field="ServiceAddress" missingValues=missingValues}}
                            </th>
                            <td>{{director.ServiceAddress}}</td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <strong>Residential Address</strong>
                                {{> director-and-members/required-info-icon field="ResidentialOrRegisteredAddress" missingValues=missingValues}}
                            </th>
                            <td>{{director.ResidentialOrRegisteredAddress}}</td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <strong>Date of Birth</strong>
                                {{> director-and-members/required-info-icon field="DateOfBirthOrIncorp" missingValues=missingValues}}
                            </th>
                            <td>{{formatDate director.DateOfBirthOrIncorp "YYYY-MM-DD"}}</td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <strong>Place of Birth</strong>
                                {{> director-and-members/required-info-icon field="PlaceOfBirthOrIncorp" missingValues=missingValues}}
                            </th>
                            <td>{{director.PlaceOfBirthOrIncorp}}</td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <strong>Nationality</strong>
                                {{> director-and-members/required-info-icon field="Nationality" missingValues=missingValues}}
                            </th>
                            <td>{{director.Nationality}}</td>
                        </tr>
                        {{#if hasDirectorCapacity}}
                        <tr {{#if isConfirmedInHistory}}class="highlight-blue"{{/if}}>
                            <th scope="row"><strong>Director Capacity</strong></th>
                            <td>{{director.DirectorCapacity}}</td>
                        </tr>
                        {{#if hasLicenseeEntity}}
                        <tr {{#if isConfirmedInHistory}}class="highlight-blue"{{/if}}>
                            <th scope="row"><strong>Licensee Entity</strong></th>
                            <td>{{director.LicenseeEntity}}</td>
                        </tr>
                        {{/if}}
                        {{#unless missingValues}}
                            <tr id="is-correct-row">
                            {{#unless isConfirmedNew}}
                                <th scope="row"><strong class="font-weight-bold text-dark">Is the information displayed above accurate and complete?</strong></th>
                                <td>
                                    <div class="custom-control custom-radio custom-control-inline">
                                        <input type="radio" id="information-correct-yes" name="information-correct" class="custom-control-input" value="yes">
                                        <label class="custom-control-label" for="information-correct-yes">Yes</label>
                                    </div>
                                    <div class="custom-control custom-radio custom-control-inline">
                                        <input type="radio" id="information-correct-no" name="information-correct" class="custom-control-input" value="no">
                                        <label class="custom-control-label" for="information-correct-no">No</label>
                                    </div>
                                </td>
                            </tr>
                            {{/unless}}
                        {{/unless}}
                        {{else}}
                        {{#unless director.hasVPDataReceived}}
                        <tr id="licensing-row" {{#if isConfirmedInHistory}}class="highlight-blue"{{/if}}>
                            <th class="font-weight-normal" scope="row">Is the director providing director services for this entity and acting on behalf of a person licensed by the BVI Financial Services Commission?</th>
                            <td>
                                <div class="custom-control custom-radio custom-control-inline">
                                    <input type="radio" id="director-licensed-yes" name="director-licensed" class="custom-control-input" value="yes"
                                    {{#ifEquals director.isLicensed true}}checked{{/ifEquals}}
                                    {{#if isConfirmedNew}}disabled{{/if}}
                                    >
                                    <label class="custom-control-label" for="director-licensed-yes">Yes</label>
                                </div>
                                <div class="custom-control custom-radio custom-control-inline">
                                    <input type="radio" id="director-licensed-no" name="director-licensed" class="custom-control-input" value="no"
                                    {{#ifEquals director.isLicensed false}}checked{{/ifEquals}}
                                    {{#if isConfirmedNew}}disabled{{/if}}
                                    >
                                    <label class="custom-control-label" for="director-licensed-no">No</label>
                                </div>
                            </td>
                        </tr>
                        {{/unless}}
                        {{#unless missingValues}}
                            {{#unless isConfirmedNew}}
                                <tr id="is-correct-row">
                                    <th scope="row"><strong class="font-weight-bold text-dark">Is the information displayed above accurate and complete?</strong></th>
                                    <td>
                                        <div class="custom-control custom-radio custom-control-inline">
                                            <input type="radio" id="information-correct-yes" name="information-correct" class="custom-control-input" value="yes">
                                            <label class="custom-control-label" for="information-correct-yes">Yes</label>
                                        </div>
                                        <div class="custom-control custom-radio custom-control-inline">
                                            <input type="radio" id="information-correct-no" name="information-correct" class="custom-control-input" value="no">
                                            <label class="custom-control-label" for="information-correct-no">No</label>
                                        </div>
                                    </td>
                                </tr>
                            {{/unless}}
                        {{/unless}}
                        {{/if}}
                    </tbody>
                </table>
                {{/if}}
                
                {{#if isCorporate}}
                <!-- Corporate Director Details -->
                <h4 class="mb-3"><strong>Corporate Director Details</strong></h4>
                
                <table class="table">
                    <tbody>
                        <tr>
                            <th scope="row">
                                <strong>Director Type</strong>
                                {{> director-and-members/required-info-icon field="RelationType" missingValues=missingValues}}
                            </th>
                            <td>{{director.OfficerType}}</td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <strong>Name</strong>
                                {{> director-and-members/required-info-icon field="DirName" missingValues=missingValues}}
                            </th>
                            <td>{{director.Name}}</td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <strong>Corporate Number</strong>
                                {{> director-and-members/required-info-icon field="MFIncropNr" missingValues=missingValues}}
                            </th>
                            <td>{{director.BoDirIncorporationNumber}}</td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <strong>Incorporation Place</strong>
                                {{> director-and-members/required-info-icon field="MFIncorpCountry" missingValues=missingValues}}
                            </th>
                            <td>{{director.PlaceOfBirthOrIncorp}}</td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <strong>Incorporation Date</strong>
                                {{> director-and-members/required-info-icon field="MFIncorpDate" missingValues=missingValues}}
                            </th>
                            <td>{{formatDate director.DateOfBirthOrIncorp "YYYY-MM-DD"}}</td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <strong>Address</strong>
                                {{> director-and-members/required-info-icon field="MFROAddress" missingValues=missingValues}}
                            </th>
                            <td>{{director.ResidentialOrRegisteredAddress}}</td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <strong>Appointment Date</strong>
                                {{> director-and-members/required-info-icon field="DirFromDate" missingValues=missingValues}}
                            </th>
                            <td>{{formatDate director.FromDate "YYYY-MM-DD"}}</td>
                        </tr>
                        <tr>
                            <th scope="row"><strong>Cessation Date</strong></th>
                            <td>{{formatDate director.ToDate "YYYY-MM-DD"}}</td>
                        </tr>
                        {{#if hasDirectorCapacity}}
                            <tr {{#if isConfirmedInHistory}}class="highlight-blue"{{/if}}>
                                <th scope="row"><strong>Director Capacity</strong></th>
                                <td>{{director.DirectorCapacity}}</td>
                            </tr>
                            {{#unless missingValues}}
                                {{#unless isConfirmedNew}}
                                <tr id="is-correct-row">
                                    <th scope="row"><strong class="font-weight-bold text-dark">Is the information displayed above accurate and complete?</strong></th>
                                    <td>
                                        <div class="custom-control custom-radio custom-control-inline">
                                            <input type="radio" id="information-correct-yes" name="information-correct" class="custom-control-input" value="yes">
                                            <label class="custom-control-label" for="information-correct-yes">Yes</label>
                                        </div>
                                        <div class="custom-control custom-radio custom-control-inline">
                                            <input type="radio" id="information-correct-no" name="information-correct" class="custom-control-input" value="no">
                                            <label class="custom-control-label" for="information-correct-no">No</label>
                                        </div>
                                    </td>
                                </tr>
                                {{/unless}}
                            {{/unless}}
                        {{else}}
                        {{#unless director.hasVPDataReceived}}
                        <tr id="licensing-row" {{#if isConfirmedInHistory}}class="highlight-blue"{{/if}}>
                            <th class="font-weight-normal" scope="row">Is the director licensed by BVI Financial Services Commission and providing director services for this entity?</th>
                            <td>
                                <div class="custom-control custom-radio custom-control-inline">
                                    <input type="radio" id="director-licensed-yes" name="director-licensed" class="custom-control-input" value="yes"
                                    {{#ifEquals director.isLicensed true}}checked{{/ifEquals}}
                                    {{#if isConfirmedNew}}disabled{{/if}}
                                    >
                                    <label class="custom-control-label" for="director-licensed-yes">Yes</label>
                                </div>
                                <div class="custom-control custom-radio custom-control-inline">
                                    <input type="radio" id="director-licensed-no" name="director-licensed" class="custom-control-input" value="no"
                                    {{#ifEquals director.isLicensed false}}checked{{/ifEquals}}
                                    {{#if isConfirmedNew}}disabled{{/if}}
                                    >
                                    <label class="custom-control-label" for="director-licensed-no">No</label>
                                </div>
                            </td>
                        </tr>
                        {{/unless}}
                        {{#unless missingValues}}
                            {{#unless isConfirmedNew}}
                            <tr id="is-correct-row">
                                <th scope="row"><strong class="font-weight-bold text-dark">Is the information displayed above accurate and complete?</strong></th>
                                <td>
                                    <div class="custom-control custom-radio custom-control-inline">
                                        <input type="radio" id="information-correct-yes" name="information-correct" class="custom-control-input" value="yes">
                                        <label class="custom-control-label" for="information-correct-yes">Yes</label>
                                    </div>
                                    <div class="custom-control custom-radio custom-control-inline">
                                        <input type="radio" id="information-correct-no" name="information-correct" class="custom-control-input" value="no">
                                        <label class="custom-control-label" for="information-correct-no">No</label>
                                    </div>
                                </td>
                            </tr>
                            {{/unless}}
                        {{/unless}}
                        {{/if}}
                    </tbody>
                </table>
                {{/if}}
                
                <div class="mt-4">
                    <a href="/masterclients/{{masterClientCode}}/director-and-members/{{company.code}}/directors" class="btn btn-secondary waves-effect waves-light width-xl">Back</a>
                    {{#if showConfirmButton}}
                    <button type="button" id="confirmBtn" data-id="{{director.UniqueRelationID}}" class="btn solid royal-blue width-xl ml-2 confirm-btn confirmInformation hide-element">Confirm</button>
                    {{/if}}
                    <button
                    type="button"
                    id="requestUpdateBtn"
                    data-id="{{director.UniqueRelationID}}"
                    data-type="DIRECTOR"
                    class="btn solid royal-blue width-xl ml-2 request-update-btn requestUpdate hide-element"
                    data-missing-values="{{#if missingValues}}true{{else}}false{{/if}}"
                    data-is-confirmed-new="{{#if isConfirmedNew}}true{{else}}false{{/if}}"
                    data-has-capacity="{{#if hasDirectorCapacity}}true{{else}}false{{/if}}"
                    >Request Update</button>
                </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script type="text/javascript" src="/templates/director-and-members/requestupdatepopup.precompiled.js"></script>
<script type="text/javascript" src="/templates/director-and-members/requestupdatelog.precompiled.js"></script>
<script type="text/javascript" src="/views-js/director-and-members/director-details-forms.js"></script>