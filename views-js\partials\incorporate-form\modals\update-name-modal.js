let incorpId2= "";
let mcc3 = "";

$('#updateNameReservationModal').on('show.bs.modal', function (event) {
    let button = $(event.relatedTarget); // Button that triggered the modal
    incorpId2 = button.data('incorporation-id');
    mcc3 = button.data('mcc');
    $.ajax({
        type: "GET",
        url: "/masterclients/"+mcc3+"/incorporate-company/"+ incorpId2 + "/name-reservation-info",
        success: (response) => {
            if (response.status === 200) {
                console.log(response.data);

                let template = Handlebars.templates.namesuggestionsmodal;
                let d = {
                    incorporation: response.data
                };
                let html = template(d);
                $("#suggestBody").html(html);
            } else {
                Swal.fire('Error', 'There was an error getting the information.', 'error').then(() => {
                    $('#updateNameReservationModal').modal('hide');
                });
            }
        },
        error: (err) => {
            Swal.fire('Error', 'There was an error getting the information.', 'error').then(() => {
                $('#updateNameReservationModal').modal('hide');
            });
        },
    });
});

$('#updateNameReservationModal').on('hide.bs.modal', function (event) {
    $("#sendReservationNameBtn").prop('disabled', false);
    $("#saveSuggestionNameForm").trigger("reset");
    $("#suggestBody").html('');
});


$("#saveSuggestionNameForm").on('submit', async function (event) {
    event.preventDefault();
    
    $(this).prop('disabled', true);

    $('#saveSuggestionNameForm input[required]:visible ').trigger('keyup');

    const name= $("input[name='suggestedName']:checked").val();

    if (!name){
      $("input[name='suggestedName']").toggleClass("is-invalid", true);
    }

    if ($(".is-invalid:visible").length === 0) {
        
        $.ajax({
            type: "POST",
            url: "/masterclients/"+mcc3+"/incorporate-company/"+ incorpId2 + "/name-reservation-info",
            data: $(this).serialize(),
            success: () => {
                Swal.fire('Success', 'Updated name successfully.', 'success').then(() => {
                    location.reload();
                });

            },
            error: (err) => {
                Swal.fire('Error', 'There was an error updating the name.', 'error').then(() => {
                    $('#updateNameReservationModal').modal('hide');
                });
            },
        });
    } else {
        setTimeout(function () {
            $("#sendReservationNameBtn").prop('disabled', false);
        }, 1);
    }
});