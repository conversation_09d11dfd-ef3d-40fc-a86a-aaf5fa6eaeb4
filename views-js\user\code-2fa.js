
$(document).ready(function () {
    startEmailCodeCountdown(false);
})



$("#sendEmailCodeBtn").on('click', function (e) {
    e.preventDefault();
    startEmailCodeCountdown(true);
})


function startEmailCodeCountdown(isOnButtonEvent) {
    $('#sendEmailCodeBtn').prop('disabled', true);
    let reSendCountdown = 60;
    let timer = setInterval(function () {
        reSendCountdown--;
        if (reSendCountdown < 0) {
            clearInterval(timer);
            $('#sendEmailCodeBtn').html('Resend code');
            $('#sendEmailCodeBtn').prop('disabled', false);
        } else {
            $('#sendEmailCodeBtn').html('Resend code in ' + reSendCountdown + ' seconds');
        }
    }, 1000);


    if (isOnButtonEvent) {
        const csrfToken = $("input[name='csrf-token']").val()
        $.ajax({
            type: "POST",
            url: "/users/send-email-code",
            contentType: "application/json; charset=utf-8",
            headers: {
                "x-csrf-token": csrfToken
            },
            success: function (data) {
                if (data.success) {
                    toastr["success"]('Verification email has been sent.', 'Success!');
                } else {
                    toastr["warning"](data.message || 'Verification email has been sent.', 'Error!');
                }

            },
            error: function (error) {
                toastr["warning"](error.message || 'Verification email could not be sent.', 'Error!');
            }
        });
    }

}