let reviewIdBtn;
let fileRowBtn;
let fileGroupDownload;
$('#downloadFileModal').on('show.bs.modal', function (event) {
    let button = $(event.relatedTarget); // Button that triggered the modal
    reviewIdBtn = button.data('review-id');
    const relationId = button.data('relation-id');
    fileRowBtn =  button.data('file-id');
    fileGroupDownload =  button.data("file-group");
    const id = fileGroupDownload === "standard" ? reviewIdBtn : relationId;
    $.ajax({
        type: 'GET',
        url: '/file-reviewer/company-file-review/uploaded-files',
        data: {reviewId: id,  fileId: fileRowBtn, type: fileGroupDownload},
        timeout: 5000,
        success: (data) => {
            if (data.success === false){
                toastr["warning"](data.message);
            }
            else{
                let strFileLinks = "";
                if (data.files.length > 0) {
                    for (let idx = 0; idx < data.files.length; idx++) {
                        if (fileGroupDownload === "standard"){
                            strFileLinks += "<a style='font-size: x-large' href='/file-reviewer/reviews/" + reviewIdBtn +
                                    "/download/" + fileGroupDownload + "/" + fileRowBtn+ "/" + data.files[idx].fileId + "' target='_blank'>" +  '<i class="fa fa-arrow-circle-down" aria-hidden="true"></i>  '+
                                    data.files[idx].originalName + "</a><br> <hr>"
                        }
                        else{
                            const url = "/file-reviewer/reviews/" + reviewIdBtn + "/relations/" + relationId + "/download/"+ fileGroupDownload  +"/" + fileRowBtn + "/" + data.files[idx].fileId;
                            strFileLinks += "<a style='font-size: x-large' href='"+ url + "' target='_blank'>" +  '<i class="fa fa-arrow-circle-down" aria-hidden="true"></i>  '+
                                    data.files[idx].originalName + "</a><br> <hr>"
                        }

                    }
                    if (strFileLinks){
                        $("#downloadFiles").html(strFileLinks);
                    }
                }
                else{
                    $("#downloadFiles").html("<p style='text-align: center'>Files not found</p>");
                }

            }
        },
        error: (err) => {
            console.log(err);
            Swal.fire('Error', 'There was an error downloading the file', 'error').then(()=>{
                $('#downloadFileModal').modal('hide');
            });

        },
    });
});

$('#downloadFileModal').on('hide.bs.modal', function (event) {
    $("#downloadFiles").html("<p style='text-align: center'>Searching files...</p>");
    $('.modal').css('overflow-y', 'auto');
});