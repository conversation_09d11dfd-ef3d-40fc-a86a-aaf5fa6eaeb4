/**
  * bootstrap-table - An extended table to integration with some of the most widely used CSS frameworks. (Supports Bootstrap, Semantic UI, Bulma, Material Design, Foundation)
  *
  * @version v1.22.3
  * @homepage https://bootstrap-table.com
  * <AUTHOR> <<EMAIL>> (http://wenzhixin.net.cn/)
  * @license MIT
  */

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("jquery")):"function"==typeof define&&define.amd?define(["jquery"],e):(t="undefined"!=typeof globalThis?globalThis:t||self).BootstrapTable=e(t.jQuery)}(this,(function(t){"use strict";function e(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e||"default");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:String(e)}function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function n(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t,i){for(var n=0;n<i.length;n++){var o=i[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,e(o.key),o)}}function r(t,e,i){return e&&o(t.prototype,e),i&&o(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t}function a(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var i=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=i){var n,o,r,a,s=[],l=!0,c=!1;try{if(r=(i=i.call(t)).next,0===e){if(Object(i)!==i)return;l=!1}else for(;!(l=(n=r.call(i)).done)&&(s.push(n.value),s.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=i.return&&(a=i.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}(t,e)||l(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(t){return function(t){if(Array.isArray(t))return c(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||l(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(t,e){if(t){if("string"==typeof t)return c(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?c(t,e):void 0}}function c(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function h(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=l(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,a=!0,s=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return a=t.done,t},e:function(t){s=!0,r=t},f:function(){try{a||null==i.return||i.return()}finally{if(s)throw r}}}}var u="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},d=function(t){return t&&t.Math===Math&&t},p=d("object"==typeof globalThis&&globalThis)||d("object"==typeof window&&window)||d("object"==typeof self&&self)||d("object"==typeof u&&u)||d("object"==typeof u&&u)||function(){return this}()||Function("return this")(),f={},g=function(t){try{return!!t()}catch(t){return!0}},v=!g((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})),b=!g((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),m=b,y=Function.prototype.call,w=m?y.bind(y):function(){return y.apply(y,arguments)},S={},x={}.propertyIsEnumerable,O=Object.getOwnPropertyDescriptor,k=O&&!x.call({1:2},1);S.f=k?function(t){var e=O(this,t);return!!e&&e.enumerable}:x;var C,T,P=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},I=b,A=Function.prototype,$=A.call,R=I&&A.bind.bind($,$),E=I?R:function(t){return function(){return $.apply(t,arguments)}},j=E,_=j({}.toString),F=j("".slice),N=function(t){return F(_(t),8,-1)},D=g,V=N,B=Object,L=E("".split),H=D((function(){return!B("z").propertyIsEnumerable(0)}))?function(t){return"String"===V(t)?L(t,""):B(t)}:B,M=function(t){return null==t},U=M,z=TypeError,q=function(t){if(U(t))throw new z("Can't call method on "+t);return t},W=H,G=q,K=function(t){return W(G(t))},Y="object"==typeof document&&document.all,J=void 0===Y&&void 0!==Y?function(t){return"function"==typeof t||t===Y}:function(t){return"function"==typeof t},X=J,Q=function(t){return"object"==typeof t?null!==t:X(t)},Z=p,tt=J,et=function(t){return tt(t)?t:void 0},it=function(t,e){return arguments.length<2?et(Z[t]):Z[t]&&Z[t][e]},nt=E({}.isPrototypeOf),ot="undefined"!=typeof navigator&&String(navigator.userAgent)||"",rt=p,at=ot,st=rt.process,lt=rt.Deno,ct=st&&st.versions||lt&&lt.version,ht=ct&&ct.v8;ht&&(T=(C=ht.split("."))[0]>0&&C[0]<4?1:+(C[0]+C[1])),!T&&at&&(!(C=at.match(/Edge\/(\d+)/))||C[1]>=74)&&(C=at.match(/Chrome\/(\d+)/))&&(T=+C[1]);var ut=T,dt=ut,pt=g,ft=p.String,gt=!!Object.getOwnPropertySymbols&&!pt((function(){var t=Symbol("symbol detection");return!ft(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&dt&&dt<41})),vt=gt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,bt=it,mt=J,yt=nt,wt=Object,St=vt?function(t){return"symbol"==typeof t}:function(t){var e=bt("Symbol");return mt(e)&&yt(e.prototype,wt(t))},xt=String,Ot=function(t){try{return xt(t)}catch(t){return"Object"}},kt=J,Ct=Ot,Tt=TypeError,Pt=function(t){if(kt(t))return t;throw new Tt(Ct(t)+" is not a function")},It=Pt,At=M,$t=function(t,e){var i=t[e];return At(i)?void 0:It(i)},Rt=w,Et=J,jt=Q,_t=TypeError,Ft={exports:{}},Nt=p,Dt=Object.defineProperty,Vt=function(t,e){try{Dt(Nt,t,{value:e,configurable:!0,writable:!0})}catch(i){Nt[t]=e}return e},Bt=p,Lt=Vt,Ht="__core-js_shared__",Mt=Ft.exports=Bt[Ht]||Lt(Ht,{});(Mt.versions||(Mt.versions=[])).push({version:"3.36.0",mode:"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.36.0/LICENSE",source:"https://github.com/zloirock/core-js"});var Ut=Ft.exports,zt=Ut,qt=function(t,e){return zt[t]||(zt[t]=e||{})},Wt=q,Gt=Object,Kt=function(t){return Gt(Wt(t))},Yt=Kt,Jt=E({}.hasOwnProperty),Xt=Object.hasOwn||function(t,e){return Jt(Yt(t),e)},Qt=E,Zt=0,te=Math.random(),ee=Qt(1..toString),ie=function(t){return"Symbol("+(void 0===t?"":t)+")_"+ee(++Zt+te,36)},ne=qt,oe=Xt,re=ie,ae=gt,se=vt,le=p.Symbol,ce=ne("wks"),he=se?le.for||le:le&&le.withoutSetter||re,ue=function(t){return oe(ce,t)||(ce[t]=ae&&oe(le,t)?le[t]:he("Symbol."+t)),ce[t]},de=w,pe=Q,fe=St,ge=$t,ve=function(t,e){var i,n;if("string"===e&&Et(i=t.toString)&&!jt(n=Rt(i,t)))return n;if(Et(i=t.valueOf)&&!jt(n=Rt(i,t)))return n;if("string"!==e&&Et(i=t.toString)&&!jt(n=Rt(i,t)))return n;throw new _t("Can't convert object to primitive value")},be=TypeError,me=ue("toPrimitive"),ye=function(t,e){if(!pe(t)||fe(t))return t;var i,n=ge(t,me);if(n){if(void 0===e&&(e="default"),i=de(n,t,e),!pe(i)||fe(i))return i;throw new be("Can't convert object to primitive value")}return void 0===e&&(e="number"),ve(t,e)},we=ye,Se=St,xe=function(t){var e=we(t,"string");return Se(e)?e:e+""},Oe=Q,ke=p.document,Ce=Oe(ke)&&Oe(ke.createElement),Te=function(t){return Ce?ke.createElement(t):{}},Pe=Te,Ie=!v&&!g((function(){return 7!==Object.defineProperty(Pe("div"),"a",{get:function(){return 7}}).a})),Ae=v,$e=w,Re=S,Ee=P,je=K,_e=xe,Fe=Xt,Ne=Ie,De=Object.getOwnPropertyDescriptor;f.f=Ae?De:function(t,e){if(t=je(t),e=_e(e),Ne)try{return De(t,e)}catch(t){}if(Fe(t,e))return Ee(!$e(Re.f,t,e),t[e])};var Ve={},Be=v&&g((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Le=Q,He=String,Me=TypeError,Ue=function(t){if(Le(t))return t;throw new Me(He(t)+" is not an object")},ze=v,qe=Ie,We=Be,Ge=Ue,Ke=xe,Ye=TypeError,Je=Object.defineProperty,Xe=Object.getOwnPropertyDescriptor,Qe="enumerable",Ze="configurable",ti="writable";Ve.f=ze?We?function(t,e,i){if(Ge(t),e=Ke(e),Ge(i),"function"==typeof t&&"prototype"===e&&"value"in i&&ti in i&&!i.writable){var n=Xe(t,e);n&&n.writable&&(t[e]=i.value,i={configurable:Ze in i?i.configurable:n.configurable,enumerable:Qe in i?i.enumerable:n.enumerable,writable:!1})}return Je(t,e,i)}:Je:function(t,e,i){if(Ge(t),e=Ke(e),Ge(i),qe)try{return Je(t,e,i)}catch(t){}if("get"in i||"set"in i)throw new Ye("Accessors not supported");return"value"in i&&(t[e]=i.value),t};var ei=Ve,ii=P,ni=v?function(t,e,i){return ei.f(t,e,ii(1,i))}:function(t,e,i){return t[e]=i,t},oi={exports:{}},ri=v,ai=Xt,si=Function.prototype,li=ri&&Object.getOwnPropertyDescriptor,ci=ai(si,"name"),hi={EXISTS:ci,PROPER:ci&&"something"===function(){}.name,CONFIGURABLE:ci&&(!ri||ri&&li(si,"name").configurable)},ui=J,di=Ut,pi=E(Function.toString);ui(di.inspectSource)||(di.inspectSource=function(t){return pi(t)});var fi,gi,vi,bi=di.inspectSource,mi=J,yi=p.WeakMap,wi=mi(yi)&&/native code/.test(String(yi)),Si=ie,xi=qt("keys"),Oi=function(t){return xi[t]||(xi[t]=Si(t))},ki={},Ci=wi,Ti=p,Pi=Q,Ii=ni,Ai=Xt,$i=Ut,Ri=Oi,Ei=ki,ji="Object already initialized",_i=Ti.TypeError,Fi=Ti.WeakMap;if(Ci||$i.state){var Ni=$i.state||($i.state=new Fi);Ni.get=Ni.get,Ni.has=Ni.has,Ni.set=Ni.set,fi=function(t,e){if(Ni.has(t))throw new _i(ji);return e.facade=t,Ni.set(t,e),e},gi=function(t){return Ni.get(t)||{}},vi=function(t){return Ni.has(t)}}else{var Di=Ri("state");Ei[Di]=!0,fi=function(t,e){if(Ai(t,Di))throw new _i(ji);return e.facade=t,Ii(t,Di,e),e},gi=function(t){return Ai(t,Di)?t[Di]:{}},vi=function(t){return Ai(t,Di)}}var Vi={set:fi,get:gi,has:vi,enforce:function(t){return vi(t)?gi(t):fi(t,{})},getterFor:function(t){return function(e){var i;if(!Pi(e)||(i=gi(e)).type!==t)throw new _i("Incompatible receiver, "+t+" required");return i}}},Bi=E,Li=g,Hi=J,Mi=Xt,Ui=v,zi=hi.CONFIGURABLE,qi=bi,Wi=Vi.enforce,Gi=Vi.get,Ki=String,Yi=Object.defineProperty,Ji=Bi("".slice),Xi=Bi("".replace),Qi=Bi([].join),Zi=Ui&&!Li((function(){return 8!==Yi((function(){}),"length",{value:8}).length})),tn=String(String).split("String"),en=oi.exports=function(t,e,i){"Symbol("===Ji(Ki(e),0,7)&&(e="["+Xi(Ki(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),i&&i.getter&&(e="get "+e),i&&i.setter&&(e="set "+e),(!Mi(t,"name")||zi&&t.name!==e)&&(Ui?Yi(t,"name",{value:e,configurable:!0}):t.name=e),Zi&&i&&Mi(i,"arity")&&t.length!==i.arity&&Yi(t,"length",{value:i.arity});try{i&&Mi(i,"constructor")&&i.constructor?Ui&&Yi(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var n=Wi(t);return Mi(n,"source")||(n.source=Qi(tn,"string"==typeof e?e:"")),t};Function.prototype.toString=en((function(){return Hi(this)&&Gi(this).source||qi(this)}),"toString");var nn=oi.exports,on=J,rn=Ve,an=nn,sn=Vt,ln=function(t,e,i,n){n||(n={});var o=n.enumerable,r=void 0!==n.name?n.name:e;if(on(i)&&an(i,r,n),n.global)o?t[e]=i:sn(e,i);else{try{n.unsafe?t[e]&&(o=!0):delete t[e]}catch(t){}o?t[e]=i:rn.f(t,e,{value:i,enumerable:!1,configurable:!n.nonConfigurable,writable:!n.nonWritable})}return t},cn={},hn=Math.ceil,un=Math.floor,dn=Math.trunc||function(t){var e=+t;return(e>0?un:hn)(e)},pn=function(t){var e=+t;return e!=e||0===e?0:dn(e)},fn=pn,gn=Math.max,vn=Math.min,bn=function(t,e){var i=fn(t);return i<0?gn(i+e,0):vn(i,e)},mn=pn,yn=Math.min,wn=function(t){var e=mn(t);return e>0?yn(e,9007199254740991):0},Sn=wn,xn=function(t){return Sn(t.length)},On=K,kn=bn,Cn=xn,Tn=function(t){return function(e,i,n){var o=On(e),r=Cn(o);if(0===r)return!t&&-1;var a,s=kn(n,r);if(t&&i!=i){for(;r>s;)if((a=o[s++])!=a)return!0}else for(;r>s;s++)if((t||s in o)&&o[s]===i)return t||s||0;return!t&&-1}},Pn={includes:Tn(!0),indexOf:Tn(!1)},In=Xt,An=K,$n=Pn.indexOf,Rn=ki,En=E([].push),jn=function(t,e){var i,n=An(t),o=0,r=[];for(i in n)!In(Rn,i)&&In(n,i)&&En(r,i);for(;e.length>o;)In(n,i=e[o++])&&(~$n(r,i)||En(r,i));return r},_n=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Fn=jn,Nn=_n.concat("length","prototype");cn.f=Object.getOwnPropertyNames||function(t){return Fn(t,Nn)};var Dn={};Dn.f=Object.getOwnPropertySymbols;var Vn=it,Bn=cn,Ln=Dn,Hn=Ue,Mn=E([].concat),Un=Vn("Reflect","ownKeys")||function(t){var e=Bn.f(Hn(t)),i=Ln.f;return i?Mn(e,i(t)):e},zn=Xt,qn=Un,Wn=f,Gn=Ve,Kn=g,Yn=J,Jn=/#|\.prototype\./,Xn=function(t,e){var i=Zn[Qn(t)];return i===eo||i!==to&&(Yn(e)?Kn(e):!!e)},Qn=Xn.normalize=function(t){return String(t).replace(Jn,".").toLowerCase()},Zn=Xn.data={},to=Xn.NATIVE="N",eo=Xn.POLYFILL="P",io=Xn,no=p,oo=f.f,ro=ni,ao=ln,so=Vt,lo=function(t,e,i){for(var n=qn(e),o=Gn.f,r=Wn.f,a=0;a<n.length;a++){var s=n[a];zn(t,s)||i&&zn(i,s)||o(t,s,r(e,s))}},co=io,ho=function(t,e){var i,n,o,r,a,s=t.target,l=t.global,c=t.stat;if(i=l?no:c?no[s]||so(s,{}):no[s]&&no[s].prototype)for(n in e){if(r=e[n],o=t.dontCallGetSet?(a=oo(i,n))&&a.value:i[n],!co(l?n:s+(c?".":"#")+n,t.forced)&&void 0!==o){if(typeof r==typeof o)continue;lo(r,o)}(t.sham||o&&o.sham)&&ro(r,"sham",!0),ao(i,n,r,t)}},uo=N,po=Array.isArray||function(t){return"Array"===uo(t)},fo=TypeError,go=function(t){if(t>9007199254740991)throw fo("Maximum allowed index exceeded");return t},vo=v,bo=Ve,mo=P,yo=function(t,e,i){vo?bo.f(t,e,mo(0,i)):t[e]=i},wo={};wo[ue("toStringTag")]="z";var So="[object z]"===String(wo),xo=So,Oo=J,ko=N,Co=ue("toStringTag"),To=Object,Po="Arguments"===ko(function(){return arguments}()),Io=xo?ko:function(t){var e,i,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(i=function(t,e){try{return t[e]}catch(t){}}(e=To(t),Co))?i:Po?ko(e):"Object"===(n=ko(e))&&Oo(e.callee)?"Arguments":n},Ao=E,$o=g,Ro=J,Eo=Io,jo=bi,_o=function(){},Fo=it("Reflect","construct"),No=/^\s*(?:class|function)\b/,Do=Ao(No.exec),Vo=!No.test(_o),Bo=function(t){if(!Ro(t))return!1;try{return Fo(_o,[],t),!0}catch(t){return!1}},Lo=function(t){if(!Ro(t))return!1;switch(Eo(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Vo||!!Do(No,jo(t))}catch(t){return!0}};Lo.sham=!0;var Ho=!Fo||$o((function(){var t;return Bo(Bo.call)||!Bo(Object)||!Bo((function(){t=!0}))||t}))?Lo:Bo,Mo=po,Uo=Ho,zo=Q,qo=ue("species"),Wo=Array,Go=function(t){var e;return Mo(t)&&(e=t.constructor,(Uo(e)&&(e===Wo||Mo(e.prototype))||zo(e)&&null===(e=e[qo]))&&(e=void 0)),void 0===e?Wo:e},Ko=function(t,e){return new(Go(t))(0===e?0:e)},Yo=g,Jo=ut,Xo=ue("species"),Qo=function(t){return Jo>=51||!Yo((function(){var e=[];return(e.constructor={})[Xo]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},Zo=ho,tr=g,er=po,ir=Q,nr=Kt,or=xn,rr=go,ar=yo,sr=Ko,lr=Qo,cr=ut,hr=ue("isConcatSpreadable"),ur=cr>=51||!tr((function(){var t=[];return t[hr]=!1,t.concat()[0]!==t})),dr=function(t){if(!ir(t))return!1;var e=t[hr];return void 0!==e?!!e:er(t)};Zo({target:"Array",proto:!0,arity:1,forced:!ur||!lr("concat")},{concat:function(t){var e,i,n,o,r,a=nr(this),s=sr(a,0),l=0;for(e=-1,n=arguments.length;e<n;e++)if(dr(r=-1===e?a:arguments[e]))for(o=or(r),rr(l+o),i=0;i<o;i++,l++)i in r&&ar(s,l,r[i]);else rr(l+1),ar(s,l++,r);return s.length=l,s}});var pr=N,fr=E,gr=function(t){if("Function"===pr(t))return fr(t)},vr=Pt,br=b,mr=gr(gr.bind),yr=function(t,e){return vr(t),void 0===e?t:br?mr(t,e):function(){return t.apply(e,arguments)}},wr=H,Sr=Kt,xr=xn,Or=Ko,kr=E([].push),Cr=function(t){var e=1===t,i=2===t,n=3===t,o=4===t,r=6===t,a=7===t,s=5===t||r;return function(l,c,h,u){for(var d,p,f=Sr(l),g=wr(f),v=xr(g),b=yr(c,h),m=0,y=u||Or,w=e?y(l,v):i||a?y(l,0):void 0;v>m;m++)if((s||m in g)&&(p=b(d=g[m],m,f),t))if(e)w[m]=p;else if(p)switch(t){case 3:return!0;case 5:return d;case 6:return m;case 2:kr(w,d)}else switch(t){case 4:return!1;case 7:kr(w,d)}return r?-1:n||o?o:w}},Tr={forEach:Cr(0),map:Cr(1),filter:Cr(2),some:Cr(3),every:Cr(4),find:Cr(5),findIndex:Cr(6),filterReject:Cr(7)},Pr=Tr.filter;ho({target:"Array",proto:!0,forced:!Qo("filter")},{filter:function(t){return Pr(this,t,arguments.length>1?arguments[1]:void 0)}});var Ir={},Ar=jn,$r=_n,Rr=Object.keys||function(t){return Ar(t,$r)},Er=v,jr=Be,_r=Ve,Fr=Ue,Nr=K,Dr=Rr;Ir.f=Er&&!jr?Object.defineProperties:function(t,e){Fr(t);for(var i,n=Nr(e),o=Dr(e),r=o.length,a=0;r>a;)_r.f(t,i=o[a++],n[i]);return t};var Vr,Br=it("document","documentElement"),Lr=Ue,Hr=Ir,Mr=_n,Ur=ki,zr=Br,qr=Te,Wr=Oi("IE_PROTO"),Gr=function(){},Kr=function(t){return"<script>"+t+"</"+"script>"},Yr=function(t){t.write(Kr("")),t.close();var e=t.parentWindow.Object;return t=null,e},Jr=function(){try{Vr=new ActiveXObject("htmlfile")}catch(t){}var t,e;Jr="undefined"!=typeof document?document.domain&&Vr?Yr(Vr):((e=qr("iframe")).style.display="none",zr.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(Kr("document.F=Object")),t.close(),t.F):Yr(Vr);for(var i=Mr.length;i--;)delete Jr.prototype[Mr[i]];return Jr()};Ur[Wr]=!0;var Xr=Object.create||function(t,e){var i;return null!==t?(Gr.prototype=Lr(t),i=new Gr,Gr.prototype=null,i[Wr]=t):i=Jr(),void 0===e?i:Hr.f(i,e)},Qr=ue,Zr=Xr,ta=Ve.f,ea=Qr("unscopables"),ia=Array.prototype;void 0===ia[ea]&&ta(ia,ea,{configurable:!0,value:Zr(null)});var na=function(t){ia[ea][t]=!0},oa=ho,ra=Tr.find,aa=na,sa="find",la=!0;sa in[]&&Array(1).find((function(){la=!1})),oa({target:"Array",proto:!0,forced:la},{find:function(t){return ra(this,t,arguments.length>1?arguments[1]:void 0)}}),aa(sa);var ca=ho,ha=Tr.findIndex,ua=na,da="findIndex",pa=!0;da in[]&&Array(1).findIndex((function(){pa=!1})),ca({target:"Array",proto:!0,forced:pa},{findIndex:function(t){return ha(this,t,arguments.length>1?arguments[1]:void 0)}}),ua(da);var fa=Pn.includes,ga=na;ho({target:"Array",proto:!0,forced:g((function(){return!Array(1).includes()}))},{includes:function(t){return fa(this,t,arguments.length>1?arguments[1]:void 0)}}),ga("includes");var va=g,ba=function(t,e){var i=[][t];return!!i&&va((function(){i.call(null,e||function(){return 1},1)}))},ma=ho,ya=Pn.indexOf,wa=ba,Sa=gr([].indexOf),xa=!!Sa&&1/Sa([1],1,-0)<0;ma({target:"Array",proto:!0,forced:xa||!wa("indexOf")},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return xa?Sa(this,t,e)||0:ya(this,t,e)}});var Oa,ka,Ca,Ta={},Pa=!g((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype})),Ia=Xt,Aa=J,$a=Kt,Ra=Pa,Ea=Oi("IE_PROTO"),ja=Object,_a=ja.prototype,Fa=Ra?ja.getPrototypeOf:function(t){var e=$a(t);if(Ia(e,Ea))return e[Ea];var i=e.constructor;return Aa(i)&&e instanceof i?i.prototype:e instanceof ja?_a:null},Na=g,Da=J,Va=Q,Ba=Fa,La=ln,Ha=ue("iterator"),Ma=!1;[].keys&&("next"in(Ca=[].keys())?(ka=Ba(Ba(Ca)))!==Object.prototype&&(Oa=ka):Ma=!0);var Ua=!Va(Oa)||Na((function(){var t={};return Oa[Ha].call(t)!==t}));Ua&&(Oa={}),Da(Oa[Ha])||La(Oa,Ha,(function(){return this}));var za={IteratorPrototype:Oa,BUGGY_SAFARI_ITERATORS:Ma},qa=Ve.f,Wa=Xt,Ga=ue("toStringTag"),Ka=function(t,e,i){t&&!i&&(t=t.prototype),t&&!Wa(t,Ga)&&qa(t,Ga,{configurable:!0,value:e})},Ya=za.IteratorPrototype,Ja=Xr,Xa=P,Qa=Ka,Za=Ta,ts=function(){return this},es=E,is=Pt,ns=Q,os=function(t){return ns(t)||null===t},rs=String,as=TypeError,ss=function(t,e,i){try{return es(is(Object.getOwnPropertyDescriptor(t,e)[i]))}catch(t){}},ls=Ue,cs=function(t){if(os(t))return t;throw new as("Can't set "+rs(t)+" as a prototype")},hs=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,i={};try{(t=ss(Object.prototype,"__proto__","set"))(i,[]),e=i instanceof Array}catch(t){}return function(i,n){return ls(i),cs(n),e?t(i,n):i.__proto__=n,i}}():void 0),us=ho,ds=w,ps=J,fs=function(t,e,i,n){var o=e+" Iterator";return t.prototype=Ja(Ya,{next:Xa(+!n,i)}),Qa(t,o,!1),Za[o]=ts,t},gs=Fa,vs=hs,bs=Ka,ms=ni,ys=ln,ws=Ta,Ss=hi.PROPER,xs=hi.CONFIGURABLE,Os=za.IteratorPrototype,ks=za.BUGGY_SAFARI_ITERATORS,Cs=ue("iterator"),Ts="keys",Ps="values",Is="entries",As=function(){return this},$s=K,Rs=na,Es=Ta,js=Vi,_s=Ve.f,Fs=function(t,e,i,n,o,r,a){fs(i,e,n);var s,l,c,h=function(t){if(t===o&&g)return g;if(!ks&&t&&t in p)return p[t];switch(t){case Ts:case Ps:case Is:return function(){return new i(this,t)}}return function(){return new i(this)}},u=e+" Iterator",d=!1,p=t.prototype,f=p[Cs]||p["@@iterator"]||o&&p[o],g=!ks&&f||h(o),v="Array"===e&&p.entries||f;if(v&&(s=gs(v.call(new t)))!==Object.prototype&&s.next&&(gs(s)!==Os&&(vs?vs(s,Os):ps(s[Cs])||ys(s,Cs,As)),bs(s,u,!0)),Ss&&o===Ps&&f&&f.name!==Ps&&(xs?ms(p,"name",Ps):(d=!0,g=function(){return ds(f,this)})),o)if(l={values:h(Ps),keys:r?g:h(Ts),entries:h(Is)},a)for(c in l)(ks||d||!(c in p))&&ys(p,c,l[c]);else us({target:e,proto:!0,forced:ks||d},l);return p[Cs]!==g&&ys(p,Cs,g,{name:o}),ws[e]=g,l},Ns=function(t,e){return{value:t,done:e}},Ds=v,Vs="Array Iterator",Bs=js.set,Ls=js.getterFor(Vs),Hs=Fs(Array,"Array",(function(t,e){Bs(this,{type:Vs,target:$s(t),index:0,kind:e})}),(function(){var t=Ls(this),e=t.target,i=t.index++;if(!e||i>=e.length)return t.target=void 0,Ns(void 0,!0);switch(t.kind){case"keys":return Ns(i,!1);case"values":return Ns(e[i],!1)}return Ns([i,e[i]],!1)}),"values"),Ms=Es.Arguments=Es.Array;if(Rs("keys"),Rs("values"),Rs("entries"),Ds&&"values"!==Ms.name)try{_s(Ms,"name",{value:"values"})}catch(t){}var Us=ho,zs=H,qs=K,Ws=ba,Gs=E([].join);Us({target:"Array",proto:!0,forced:zs!==Object||!Ws("join",",")},{join:function(t){return Gs(qs(this),void 0===t?",":t)}});var Ks=Tr.map;ho({target:"Array",proto:!0,forced:!Qo("map")},{map:function(t){return Ks(this,t,arguments.length>1?arguments[1]:void 0)}});var Ys=ho,Js=po,Xs=E([].reverse),Qs=[1,2];Ys({target:"Array",proto:!0,forced:String(Qs)===String(Qs.reverse())},{reverse:function(){return Js(this)&&(this.length=this.length),Xs(this)}});var Zs=E([].slice),tl=ho,el=po,il=Ho,nl=Q,ol=bn,rl=xn,al=K,sl=yo,ll=ue,cl=Zs,hl=Qo("slice"),ul=ll("species"),dl=Array,pl=Math.max;tl({target:"Array",proto:!0,forced:!hl},{slice:function(t,e){var i,n,o,r=al(this),a=rl(r),s=ol(t,a),l=ol(void 0===e?a:e,a);if(el(r)&&(i=r.constructor,(il(i)&&(i===dl||el(i.prototype))||nl(i)&&null===(i=i[ul]))&&(i=void 0),i===dl||void 0===i))return cl(r,s,l);for(n=new(void 0===i?dl:i)(pl(l-s,0)),o=0;s<l;s++,o++)s in r&&sl(n,o,r[s]);return n.length=o,n}});var fl=Ot,gl=TypeError,vl=function(t,e){if(!delete t[e])throw new gl("Cannot delete property "+fl(e)+" of "+fl(t))},bl=Io,ml=String,yl=function(t){if("Symbol"===bl(t))throw new TypeError("Cannot convert a Symbol value to a string");return ml(t)},wl=Zs,Sl=Math.floor,xl=function(t,e){var i=t.length;if(i<8)for(var n,o,r=1;r<i;){for(o=r,n=t[r];o&&e(t[o-1],n)>0;)t[o]=t[--o];o!==r++&&(t[o]=n)}else for(var a=Sl(i/2),s=xl(wl(t,0,a),e),l=xl(wl(t,a),e),c=s.length,h=l.length,u=0,d=0;u<c||d<h;)t[u+d]=u<c&&d<h?e(s[u],l[d])<=0?s[u++]:l[d++]:u<c?s[u++]:l[d++];return t},Ol=xl,kl=ot.match(/firefox\/(\d+)/i),Cl=!!kl&&+kl[1],Tl=/MSIE|Trident/.test(ot),Pl=ot.match(/AppleWebKit\/(\d+)\./),Il=!!Pl&&+Pl[1],Al=ho,$l=E,Rl=Pt,El=Kt,jl=xn,_l=vl,Fl=yl,Nl=g,Dl=Ol,Vl=ba,Bl=Cl,Ll=Tl,Hl=ut,Ml=Il,Ul=[],zl=$l(Ul.sort),ql=$l(Ul.push),Wl=Nl((function(){Ul.sort(void 0)})),Gl=Nl((function(){Ul.sort(null)})),Kl=Vl("sort"),Yl=!Nl((function(){if(Hl)return Hl<70;if(!(Bl&&Bl>3)){if(Ll)return!0;if(Ml)return Ml<603;var t,e,i,n,o="";for(t=65;t<76;t++){switch(e=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:i=3;break;case 68:case 71:i=4;break;default:i=2}for(n=0;n<47;n++)Ul.push({k:e+n,v:i})}for(Ul.sort((function(t,e){return e.v-t.v})),n=0;n<Ul.length;n++)e=Ul[n].k.charAt(0),o.charAt(o.length-1)!==e&&(o+=e);return"DGBEFHACIJK"!==o}}));Al({target:"Array",proto:!0,forced:Wl||!Gl||!Kl||!Yl},{sort:function(t){void 0!==t&&Rl(t);var e=El(this);if(Yl)return void 0===t?zl(e):zl(e,t);var i,n,o=[],r=jl(e);for(n=0;n<r;n++)n in e&&ql(o,e[n]);for(Dl(o,function(t){return function(e,i){return void 0===i?-1:void 0===e?1:void 0!==t?+t(e,i)||0:Fl(e)>Fl(i)?1:-1}}(t)),i=jl(o),n=0;n<i;)e[n]=o[n++];for(;n<r;)_l(e,n++);return e}});var Jl=v,Xl=po,Ql=TypeError,Zl=Object.getOwnPropertyDescriptor,tc=Jl&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}(),ec=ho,ic=Kt,nc=bn,oc=pn,rc=xn,ac=tc?function(t,e){if(Xl(t)&&!Zl(t,"length").writable)throw new Ql("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e},sc=go,lc=Ko,cc=yo,hc=vl,uc=Qo("splice"),dc=Math.max,pc=Math.min;ec({target:"Array",proto:!0,forced:!uc},{splice:function(t,e){var i,n,o,r,a,s,l=ic(this),c=rc(l),h=nc(t,c),u=arguments.length;for(0===u?i=n=0:1===u?(i=0,n=c-h):(i=u-2,n=pc(dc(oc(e),0),c-h)),sc(c+i-n),o=lc(l,n),r=0;r<n;r++)(a=h+r)in l&&cc(o,r,l[a]);if(o.length=n,i<n){for(r=h;r<c-n;r++)s=r+i,(a=r+n)in l?l[s]=l[a]:hc(l,s);for(r=c;r>c-n+i;r--)hc(l,r-1)}else if(i>n)for(r=c-n;r>h;r--)s=r+i-1,(a=r+n-1)in l?l[s]=l[a]:hc(l,s);for(r=0;r<i;r++)l[r+h]=arguments[r+2];return ac(l,c-n+i),o}});var fc=p,gc=J,vc=Q,bc=hs,mc=function(t,e,i){var n,o;return bc&&gc(n=e.constructor)&&n!==i&&vc(o=n.prototype)&&o!==i.prototype&&bc(t,o),t},yc=E(1..valueOf),wc="\t\n\v\f\r                　\u2028\u2029\ufeff",Sc=q,xc=yl,Oc=wc,kc=E("".replace),Cc=RegExp("^["+Oc+"]+"),Tc=RegExp("(^|[^"+Oc+"])["+Oc+"]+$"),Pc=function(t){return function(e){var i=xc(Sc(e));return 1&t&&(i=kc(i,Cc,"")),2&t&&(i=kc(i,Tc,"$1")),i}},Ic={start:Pc(1),end:Pc(2),trim:Pc(3)},Ac=ho,$c=v,Rc=p,Ec=fc,jc=E,_c=io,Fc=Xt,Nc=mc,Dc=nt,Vc=St,Bc=ye,Lc=g,Hc=cn.f,Mc=f.f,Uc=Ve.f,zc=yc,qc=Ic.trim,Wc="Number",Gc=Rc.Number;Ec.Number;var Kc=Gc.prototype,Yc=Rc.TypeError,Jc=jc("".slice),Xc=jc("".charCodeAt),Qc=function(t){var e=Bc(t,"number");return"bigint"==typeof e?e:Zc(e)},Zc=function(t){var e,i,n,o,r,a,s,l,c=Bc(t,"number");if(Vc(c))throw new Yc("Cannot convert a Symbol value to a number");if("string"==typeof c&&c.length>2)if(c=qc(c),43===(e=Xc(c,0))||45===e){if(88===(i=Xc(c,2))||120===i)return NaN}else if(48===e){switch(Xc(c,1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+c}for(a=(r=Jc(c,2)).length,s=0;s<a;s++)if((l=Xc(r,s))<48||l>o)return NaN;return parseInt(r,n)}return+c},th=_c(Wc,!Gc(" 0o1")||!Gc("0b1")||Gc("+0x1")),eh=function(t){return Dc(Kc,t)&&Lc((function(){zc(t)}))},ih=function(t){var e=arguments.length<1?0:Gc(Qc(t));return eh(this)?Nc(Object(e),this,ih):e};ih.prototype=Kc,th&&(Kc.constructor=ih),Ac({global:!0,constructor:!0,wrap:!0,forced:th},{Number:ih});th&&function(t,e){for(var i,n=$c?Hc(e):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),o=0;n.length>o;o++)Fc(e,i=n[o])&&!Fc(t,i)&&Uc(t,i,Mc(e,i))}(Ec.Number,Gc);var nh=v,oh=E,rh=w,ah=g,sh=Rr,lh=Dn,ch=S,hh=Kt,uh=H,dh=Object.assign,ph=Object.defineProperty,fh=oh([].concat),gh=!dh||ah((function(){if(nh&&1!==dh({b:1},dh(ph({},"a",{enumerable:!0,get:function(){ph(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},i=Symbol("assign detection"),n="abcdefghijklmnopqrst";return t[i]=7,n.split("").forEach((function(t){e[t]=t})),7!==dh({},t)[i]||sh(dh({},e)).join("")!==n}))?function(t,e){for(var i=hh(t),n=arguments.length,o=1,r=lh.f,a=ch.f;n>o;)for(var s,l=uh(arguments[o++]),c=r?fh(sh(l),r(l)):sh(l),h=c.length,u=0;h>u;)s=c[u++],nh&&!rh(a,l,s)||(i[s]=l[s]);return i}:dh,vh=gh;ho({target:"Object",stat:!0,arity:2,forced:Object.assign!==vh},{assign:vh});var bh=v,mh=g,yh=E,wh=Fa,Sh=Rr,xh=K,Oh=yh(S.f),kh=yh([].push),Ch=bh&&mh((function(){var t=Object.create(null);return t[2]=2,!Oh(t,2)})),Th=function(t){return function(e){for(var i,n=xh(e),o=Sh(n),r=Ch&&null===wh(n),a=o.length,s=0,l=[];a>s;)i=o[s++],bh&&!(r?i in n:Oh(n,i))||kh(l,t?[i,n[i]]:n[i]);return l}},Ph={entries:Th(!0),values:Th(!1)}.entries;ho({target:"Object",stat:!0},{entries:function(t){return Ph(t)}});var Ih=Kt,Ah=Rr;ho({target:"Object",stat:!0,forced:g((function(){Ah(1)}))},{keys:function(t){return Ah(Ih(t))}});var $h=Io,Rh=So?{}.toString:function(){return"[object "+$h(this)+"]"};So||ln(Object.prototype,"toString",Rh,{unsafe:!0});var Eh=p,jh=g,_h=yl,Fh=Ic.trim,Nh=E("".charAt),Dh=Eh.parseFloat,Vh=Eh.Symbol,Bh=Vh&&Vh.iterator,Lh=1/Dh("\t\n\v\f\r                　\u2028\u2029\ufeff-0")!=-1/0||Bh&&!jh((function(){Dh(Object(Bh))}))?function(t){var e=Fh(_h(t)),i=Dh(e);return 0===i&&"-"===Nh(e,0)?-0:i}:Dh;ho({global:!0,forced:parseFloat!==Lh},{parseFloat:Lh});var Hh=p,Mh=g,Uh=E,zh=yl,qh=Ic.trim,Wh=wc,Gh=Hh.parseInt,Kh=Hh.Symbol,Yh=Kh&&Kh.iterator,Jh=/^[+-]?0x/i,Xh=Uh(Jh.exec),Qh=8!==Gh(Wh+"08")||22!==Gh(Wh+"0x16")||Yh&&!Mh((function(){Gh(Object(Yh))}))?function(t,e){var i=qh(zh(t));return Gh(i,e>>>0||(Xh(Jh,i)?16:10))}:Gh;ho({global:!0,forced:parseInt!==Qh},{parseInt:Qh});var Zh=Q,tu=N,eu=ue("match"),iu=function(t){var e;return Zh(t)&&(void 0!==(e=t[eu])?!!e:"RegExp"===tu(t))},nu=Ue,ou=function(){var t=nu(this),e="";return t.hasIndices&&(e+="d"),t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.unicodeSets&&(e+="v"),t.sticky&&(e+="y"),e},ru=w,au=Xt,su=nt,lu=ou,cu=RegExp.prototype,hu=function(t){var e=t.flags;return void 0!==e||"flags"in cu||au(t,"flags")||!su(cu,t)?e:ru(lu,t)},uu=g,du=p.RegExp,pu=uu((function(){var t=du("a","y");return t.lastIndex=2,null!==t.exec("abcd")})),fu=pu||uu((function(){return!du("a","y").sticky})),gu={BROKEN_CARET:pu||uu((function(){var t=du("^r","gy");return t.lastIndex=2,null!==t.exec("str")})),MISSED_STICKY:fu,UNSUPPORTED_Y:pu},vu=Ve.f,bu=nn,mu=Ve,yu=it,wu=function(t,e,i){return i.get&&bu(i.get,e,{getter:!0}),i.set&&bu(i.set,e,{setter:!0}),mu.f(t,e,i)},Su=v,xu=ue("species"),Ou=g,ku=p.RegExp,Cu=Ou((function(){var t=ku(".","s");return!(t.dotAll&&t.test("\n")&&"s"===t.flags)})),Tu=g,Pu=p.RegExp,Iu=Tu((function(){var t=Pu("(?<a>b)","g");return"b"!==t.exec("b").groups.a||"bc"!=="b".replace(t,"$<a>c")})),Au=v,$u=p,Ru=E,Eu=io,ju=mc,_u=ni,Fu=Xr,Nu=cn.f,Du=nt,Vu=iu,Bu=yl,Lu=hu,Hu=gu,Mu=function(t,e,i){i in t||vu(t,i,{configurable:!0,get:function(){return e[i]},set:function(t){e[i]=t}})},Uu=ln,zu=g,qu=Xt,Wu=Vi.enforce,Gu=function(t){var e=yu(t);Su&&e&&!e[xu]&&wu(e,xu,{configurable:!0,get:function(){return this}})},Ku=Cu,Yu=Iu,Ju=ue("match"),Xu=$u.RegExp,Qu=Xu.prototype,Zu=$u.SyntaxError,td=Ru(Qu.exec),ed=Ru("".charAt),id=Ru("".replace),nd=Ru("".indexOf),od=Ru("".slice),rd=/^\?<[^\s\d!#%&*+<=>@^][^\s!#%&*+<=>@^]*>/,ad=/a/g,sd=/a/g,ld=new Xu(ad)!==ad,cd=Hu.MISSED_STICKY,hd=Hu.UNSUPPORTED_Y,ud=Au&&(!ld||cd||Ku||Yu||zu((function(){return sd[Ju]=!1,Xu(ad)!==ad||Xu(sd)===sd||"/a/i"!==String(Xu(ad,"i"))})));if(Eu("RegExp",ud)){for(var dd=function(t,e){var i,n,o,r,a,s,l=Du(Qu,this),c=Vu(t),h=void 0===e,u=[],d=t;if(!l&&c&&h&&t.constructor===dd)return t;if((c||Du(Qu,t))&&(t=t.source,h&&(e=Lu(d))),t=void 0===t?"":Bu(t),e=void 0===e?"":Bu(e),d=t,Ku&&"dotAll"in ad&&(n=!!e&&nd(e,"s")>-1)&&(e=id(e,/s/g,"")),i=e,cd&&"sticky"in ad&&(o=!!e&&nd(e,"y")>-1)&&hd&&(e=id(e,/y/g,"")),Yu&&(r=function(t){for(var e,i=t.length,n=0,o="",r=[],a=Fu(null),s=!1,l=!1,c=0,h="";n<=i;n++){if("\\"===(e=ed(t,n)))e+=ed(t,++n);else if("]"===e)s=!1;else if(!s)switch(!0){case"["===e:s=!0;break;case"("===e:td(rd,od(t,n+1))&&(n+=2,l=!0),o+=e,c++;continue;case">"===e&&l:if(""===h||qu(a,h))throw new Zu("Invalid capture group name");a[h]=!0,r[r.length]=[h,c],l=!1,h="";continue}l?h+=e:o+=e}return[o,r]}(t),t=r[0],u=r[1]),a=ju(Xu(t,e),l?this:Qu,dd),(n||o||u.length)&&(s=Wu(a),n&&(s.dotAll=!0,s.raw=dd(function(t){for(var e,i=t.length,n=0,o="",r=!1;n<=i;n++)"\\"!==(e=ed(t,n))?r||"."!==e?("["===e?r=!0:"]"===e&&(r=!1),o+=e):o+="[\\s\\S]":o+=e+ed(t,++n);return o}(t),i)),o&&(s.sticky=!0),u.length&&(s.groups=u)),t!==d)try{_u(a,"source",""===d?"(?:)":d)}catch(t){}return a},pd=Nu(Xu),fd=0;pd.length>fd;)Mu(dd,Xu,pd[fd++]);Qu.constructor=dd,dd.prototype=Qu,Uu($u,"RegExp",dd,{constructor:!0})}Gu("RegExp");var gd=w,vd=E,bd=yl,md=ou,yd=gu,wd=Xr,Sd=Vi.get,xd=Cu,Od=Iu,kd=qt("native-string-replace",String.prototype.replace),Cd=RegExp.prototype.exec,Td=Cd,Pd=vd("".charAt),Id=vd("".indexOf),Ad=vd("".replace),$d=vd("".slice),Rd=function(){var t=/a/,e=/b*/g;return gd(Cd,t,"a"),gd(Cd,e,"a"),0!==t.lastIndex||0!==e.lastIndex}(),Ed=yd.BROKEN_CARET,jd=void 0!==/()??/.exec("")[1];(Rd||jd||Ed||xd||Od)&&(Td=function(t){var e,i,n,o,r,a,s,l=this,c=Sd(l),h=bd(t),u=c.raw;if(u)return u.lastIndex=l.lastIndex,e=gd(Td,u,h),l.lastIndex=u.lastIndex,e;var d=c.groups,p=Ed&&l.sticky,f=gd(md,l),g=l.source,v=0,b=h;if(p&&(f=Ad(f,"y",""),-1===Id(f,"g")&&(f+="g"),b=$d(h,l.lastIndex),l.lastIndex>0&&(!l.multiline||l.multiline&&"\n"!==Pd(h,l.lastIndex-1))&&(g="(?: "+g+")",b=" "+b,v++),i=new RegExp("^(?:"+g+")",f)),jd&&(i=new RegExp("^"+g+"$(?!\\s)",f)),Rd&&(n=l.lastIndex),o=gd(Cd,p?i:l,b),p?o?(o.input=$d(o.input,v),o[0]=$d(o[0],v),o.index=l.lastIndex,l.lastIndex+=o[0].length):l.lastIndex=0:Rd&&o&&(l.lastIndex=l.global?o.index+o[0].length:n),jd&&o&&o.length>1&&gd(kd,o[0],i,(function(){for(r=1;r<arguments.length-2;r++)void 0===arguments[r]&&(o[r]=void 0)})),o&&d)for(o.groups=a=wd(null),r=0;r<d.length;r++)a[(s=d[r])[0]]=o[s[1]];return o});var _d=Td;ho({target:"RegExp",proto:!0,forced:/./.exec!==_d},{exec:_d});var Fd=hi.PROPER,Nd=ln,Dd=Ue,Vd=yl,Bd=g,Ld=hu,Hd="toString",Md=RegExp.prototype,Ud=Md.toString,zd=Bd((function(){return"/a/b"!==Ud.call({source:"a",flags:"b"})})),qd=Fd&&Ud.name!==Hd;(zd||qd)&&Nd(Md,Hd,(function(){var t=Dd(this);return"/"+Vd(t.source)+"/"+Vd(Ld(t))}),{unsafe:!0});var Wd=iu,Gd=TypeError,Kd=function(t){if(Wd(t))throw new Gd("The method doesn't accept regular expressions");return t},Yd=ue("match"),Jd=function(t){var e=/./;try{"/./"[t](e)}catch(i){try{return e[Yd]=!1,"/./"[t](e)}catch(t){}}return!1},Xd=ho,Qd=Kd,Zd=q,tp=yl,ep=Jd,ip=E("".indexOf);Xd({target:"String",proto:!0,forced:!ep("includes")},{includes:function(t){return!!~ip(tp(Zd(this)),tp(Qd(t)),arguments.length>1?arguments[1]:void 0)}});var np=b,op=Function.prototype,rp=op.apply,ap=op.call,sp="object"==typeof Reflect&&Reflect.apply||(np?ap.bind(rp):function(){return ap.apply(rp,arguments)}),lp=w,cp=ln,hp=_d,up=g,dp=ue,pp=ni,fp=dp("species"),gp=RegExp.prototype,vp=function(t,e,i,n){var o=dp(t),r=!up((function(){var e={};return e[o]=function(){return 7},7!==""[t](e)})),a=r&&!up((function(){var e=!1,i=/a/;return"split"===t&&((i={}).constructor={},i.constructor[fp]=function(){return i},i.flags="",i[o]=/./[o]),i.exec=function(){return e=!0,null},i[o](""),!e}));if(!r||!a||i){var s=/./[o],l=e(o,""[t],(function(t,e,i,n,o){var a=e.exec;return a===hp||a===gp.exec?r&&!o?{done:!0,value:lp(s,e,i,n)}:{done:!0,value:lp(t,i,e,n)}:{done:!1}}));cp(String.prototype,t,l[0]),cp(gp,o,l[1])}n&&pp(gp[o],"sham",!0)},bp=E,mp=pn,yp=yl,wp=q,Sp=bp("".charAt),xp=bp("".charCodeAt),Op=bp("".slice),kp=function(t){return function(e,i){var n,o,r=yp(wp(e)),a=mp(i),s=r.length;return a<0||a>=s?t?"":void 0:(n=xp(r,a))<55296||n>56319||a+1===s||(o=xp(r,a+1))<56320||o>57343?t?Sp(r,a):n:t?Op(r,a,a+2):o-56320+(n-55296<<10)+65536}},Cp={codeAt:kp(!1),charAt:kp(!0)}.charAt,Tp=function(t,e,i){return e+(i?Cp(t,e).length:1)},Pp=E,Ip=Kt,Ap=Math.floor,$p=Pp("".charAt),Rp=Pp("".replace),Ep=Pp("".slice),jp=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,_p=/\$([$&'`]|\d{1,2})/g,Fp=w,Np=Ue,Dp=J,Vp=N,Bp=_d,Lp=TypeError,Hp=function(t,e){var i=t.exec;if(Dp(i)){var n=Fp(i,t,e);return null!==n&&Np(n),n}if("RegExp"===Vp(t))return Fp(Bp,t,e);throw new Lp("RegExp#exec called on incompatible receiver")},Mp=sp,Up=w,zp=E,qp=vp,Wp=g,Gp=Ue,Kp=J,Yp=M,Jp=pn,Xp=wn,Qp=yl,Zp=q,tf=Tp,ef=$t,nf=function(t,e,i,n,o,r){var a=i+t.length,s=n.length,l=_p;return void 0!==o&&(o=Ip(o),l=jp),Rp(r,l,(function(r,l){var c;switch($p(l,0)){case"$":return"$";case"&":return t;case"`":return Ep(e,0,i);case"'":return Ep(e,a);case"<":c=o[Ep(l,1,-1)];break;default:var h=+l;if(0===h)return r;if(h>s){var u=Ap(h/10);return 0===u?r:u<=s?void 0===n[u-1]?$p(l,1):n[u-1]+$p(l,1):r}c=n[h-1]}return void 0===c?"":c}))},of=Hp,rf=ue("replace"),af=Math.max,sf=Math.min,lf=zp([].concat),cf=zp([].push),hf=zp("".indexOf),uf=zp("".slice),df="$0"==="a".replace(/./,"$0"),pf=!!/./[rf]&&""===/./[rf]("a","$0");qp("replace",(function(t,e,i){var n=pf?"$":"$0";return[function(t,i){var n=Zp(this),o=Yp(t)?void 0:ef(t,rf);return o?Up(o,t,n,i):Up(e,Qp(n),t,i)},function(t,o){var r=Gp(this),a=Qp(t);if("string"==typeof o&&-1===hf(o,n)&&-1===hf(o,"$<")){var s=i(e,r,a,o);if(s.done)return s.value}var l=Kp(o);l||(o=Qp(o));var c,h=r.global;h&&(c=r.unicode,r.lastIndex=0);for(var u,d=[];null!==(u=of(r,a))&&(cf(d,u),h);){""===Qp(u[0])&&(r.lastIndex=tf(a,Xp(r.lastIndex),c))}for(var p,f="",g=0,v=0;v<d.length;v++){for(var b,m=Qp((u=d[v])[0]),y=af(sf(Jp(u.index),a.length),0),w=[],S=1;S<u.length;S++)cf(w,void 0===(p=u[S])?p:String(p));var x=u.groups;if(l){var O=lf([m],w,y,a);void 0!==x&&cf(O,x),b=Qp(Mp(o,void 0,O))}else b=nf(m,a,y,w,x,o);y>=g&&(f+=uf(a,g,y)+b,g=y+m.length)}return f+uf(a,g)}]}),!!Wp((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")}))||!df||pf);var ff=Object.is||function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e},gf=w,vf=Ue,bf=M,mf=q,yf=ff,wf=yl,Sf=$t,xf=Hp;vp("search",(function(t,e,i){return[function(e){var i=mf(this),n=bf(e)?void 0:Sf(e,t);return n?gf(n,e,i):new RegExp(e)[t](wf(i))},function(t){var n=vf(this),o=wf(t),r=i(e,n,o);if(r.done)return r.value;var a=n.lastIndex;yf(a,0)||(n.lastIndex=0);var s=xf(n,o);return yf(n.lastIndex,a)||(n.lastIndex=a),null===s?-1:s.index}]}));var Of=Ho,kf=Ot,Cf=TypeError,Tf=Ue,Pf=function(t){if(Of(t))return t;throw new Cf(kf(t)+" is not a constructor")},If=M,Af=ue("species"),$f=w,Rf=E,Ef=vp,jf=Ue,_f=M,Ff=q,Nf=function(t,e){var i,n=Tf(t).constructor;return void 0===n||If(i=Tf(n)[Af])?e:Pf(i)},Df=Tp,Vf=wn,Bf=yl,Lf=$t,Hf=Hp,Mf=g,Uf=gu.UNSUPPORTED_Y,zf=Math.min,qf=Rf([].push),Wf=Rf("".slice),Gf=!Mf((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var i="ab".split(t);return 2!==i.length||"a"!==i[0]||"b"!==i[1]})),Kf="c"==="abbc".split(/(b)*/)[1]||4!=="test".split(/(?:)/,-1).length||2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length;Ef("split",(function(t,e,i){var n="0".split(void 0,0).length?function(t,i){return void 0===t&&0===i?[]:$f(e,this,t,i)}:e;return[function(e,i){var o=Ff(this),r=_f(e)?void 0:Lf(e,t);return r?$f(r,e,o,i):$f(n,Bf(o),e,i)},function(t,o){var r=jf(this),a=Bf(t);if(!Kf){var s=i(n,r,a,o,n!==e);if(s.done)return s.value}var l=Nf(r,RegExp),c=r.unicode,h=(r.ignoreCase?"i":"")+(r.multiline?"m":"")+(r.unicode?"u":"")+(Uf?"g":"y"),u=new l(Uf?"^(?:"+r.source+")":r,h),d=void 0===o?4294967295:o>>>0;if(0===d)return[];if(0===a.length)return null===Hf(u,a)?[a]:[];for(var p=0,f=0,g=[];f<a.length;){u.lastIndex=Uf?0:f;var v,b=Hf(u,Uf?Wf(a,f):a);if(null===b||(v=zf(Vf(u.lastIndex+(Uf?f:0)),a.length))===p)f=Df(a,f,c);else{if(qf(g,Wf(a,p,f)),g.length===d)return g;for(var m=1;m<=b.length-1;m++)if(qf(g,b[m]),g.length===d)return g;f=p=v}}return qf(g,Wf(a,p)),g}]}),Kf||!Gf,Uf);var Yf=hi.PROPER,Jf=g,Xf=wc,Qf=Ic.trim;ho({target:"String",proto:!0,forced:function(t){return Jf((function(){return!!Xf[t]()||"​᠎"!=="​᠎"[t]()||Yf&&Xf[t].name!==t}))}("trim")},{trim:function(){return Qf(this)}});var Zf={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},tg=Te("span").classList,eg=tg&&tg.constructor&&tg.constructor.prototype,ig=eg===Object.prototype?void 0:eg,ng=Tr.forEach,og=ba("forEach")?[].forEach:function(t){return ng(this,t,arguments.length>1?arguments[1]:void 0)},rg=p,ag=Zf,sg=ig,lg=og,cg=ni,hg=function(t){if(t&&t.forEach!==lg)try{cg(t,"forEach",lg)}catch(e){t.forEach=lg}};for(var ug in ag)ag[ug]&&hg(rg[ug]&&rg[ug].prototype);hg(sg);var dg=p,pg=Zf,fg=ig,gg=Hs,vg=ni,bg=Ka,mg=ue("iterator"),yg=gg.values,wg=function(t,e){if(t){if(t[mg]!==yg)try{vg(t,mg,yg)}catch(e){t[mg]=yg}if(bg(t,e,!0),pg[e])for(var i in gg)if(t[i]!==gg[i])try{vg(t,i,gg[i])}catch(e){t[i]=gg[i]}}};for(var Sg in pg)wg(dg[Sg]&&dg[Sg].prototype,Sg);wg(fg,"DOMTokenList");var xg=Kt,Og=Fa,kg=Pa;ho({target:"Object",stat:!0,forced:g((function(){Og(1)})),sham:!kg},{getPrototypeOf:function(t){return Og(xg(t))}});var Cg,Tg=ho,Pg=gr,Ig=f.f,Ag=wn,$g=yl,Rg=Kd,Eg=q,jg=Jd,_g=Pg("".slice),Fg=Math.min,Ng=jg("endsWith");Tg({target:"String",proto:!0,forced:!!(Ng||(Cg=Ig(String.prototype,"endsWith"),!Cg||Cg.writable))&&!Ng},{endsWith:function(t){var e=$g(Eg(this));Rg(t);var i=arguments.length>1?arguments[1]:void 0,n=e.length,o=void 0===i?n:Fg(Ag(i),n),r=$g(t);return _g(e,o-r.length,o)===r}});var Dg=w,Vg=Ue,Bg=M,Lg=wn,Hg=yl,Mg=q,Ug=$t,zg=Tp,qg=Hp;vp("match",(function(t,e,i){return[function(e){var i=Mg(this),n=Bg(e)?void 0:Ug(e,t);return n?Dg(n,e,i):new RegExp(e)[t](Hg(i))},function(t){var n=Vg(this),o=Hg(t),r=i(e,n,o);if(r.done)return r.value;if(!n.global)return qg(n,o);var a=n.unicode;n.lastIndex=0;for(var s,l=[],c=0;null!==(s=qg(n,o));){var h=Hg(s[0]);l[c]=h,""===h&&(n.lastIndex=zg(o,Lg(n.lastIndex),a)),c++}return 0===c?null:l}]}));var Wg=ho,Gg=gr,Kg=f.f,Yg=wn,Jg=yl,Xg=Kd,Qg=q,Zg=Jd,tv=Gg("".slice),ev=Math.min,iv=Zg("startsWith"),nv=!iv&&!!function(){var t=Kg(String.prototype,"startsWith");return t&&!t.writable}();Wg({target:"String",proto:!0,forced:!nv&&!iv},{startsWith:function(t){var e=Jg(Qg(this));Xg(t);var i=Yg(ev(arguments.length>1?arguments[1]:void 0,e.length)),n=Jg(t);return tv(e,i,i+n.length)===n}});var ov={getBootstrapVersion:function(){var e=5;try{var i=t.fn.dropdown.Constructor.VERSION;void 0!==i&&(e=parseInt(i,10))}catch(t){}try{var n=bootstrap.Tooltip.VERSION;void 0!==n&&(e=parseInt(n,10))}catch(t){}return e},getIconsPrefix:function(t){return{bootstrap3:"glyphicon",bootstrap4:"fa",bootstrap5:"bi","bootstrap-table":"icon",bulma:"fa",foundation:"fa",materialize:"material-icons",semantic:"fa"}[t]||"fa"},getIcons:function(t){return{glyphicon:{paginationSwitchDown:"glyphicon-collapse-down icon-chevron-down",paginationSwitchUp:"glyphicon-collapse-up icon-chevron-up",refresh:"glyphicon-refresh icon-refresh",toggleOff:"glyphicon-list-alt icon-list-alt",toggleOn:"glyphicon-list-alt icon-list-alt",columns:"glyphicon-th icon-th",detailOpen:"glyphicon-plus icon-plus",detailClose:"glyphicon-minus icon-minus",fullscreen:"glyphicon-fullscreen",search:"glyphicon-search",clearSearch:"glyphicon-trash"},fa:{paginationSwitchDown:"fa-caret-square-down",paginationSwitchUp:"fa-caret-square-up",refresh:"fa-sync",toggleOff:"fa-toggle-off",toggleOn:"fa-toggle-on",columns:"fa-th-list",detailOpen:"fa-plus",detailClose:"fa-minus",fullscreen:"fa-arrows-alt",search:"fa-search",clearSearch:"fa-trash"},bi:{paginationSwitchDown:"bi-caret-down-square",paginationSwitchUp:"bi-caret-up-square",refresh:"bi-arrow-clockwise",toggleOff:"bi-toggle-off",toggleOn:"bi-toggle-on",columns:"bi-list-ul",detailOpen:"bi-plus",detailClose:"bi-dash",fullscreen:"bi-arrows-move",search:"bi-search",clearSearch:"bi-trash"},icon:{paginationSwitchDown:"icon-arrow-up-circle",paginationSwitchUp:"icon-arrow-down-circle",refresh:"icon-refresh-cw",toggleOff:"icon-toggle-right",toggleOn:"icon-toggle-right",columns:"icon-list",detailOpen:"icon-plus",detailClose:"icon-minus",fullscreen:"icon-maximize",search:"icon-search",clearSearch:"icon-trash-2"},"material-icons":{paginationSwitchDown:"grid_on",paginationSwitchUp:"grid_off",refresh:"refresh",toggleOff:"tablet",toggleOn:"tablet_android",columns:"view_list",detailOpen:"add",detailClose:"remove",fullscreen:"fullscreen",sort:"sort",search:"search",clearSearch:"delete"}}[t]},getSearchInput:function(e){return"string"==typeof e.options.searchSelector?t(e.options.searchSelector):e.$toolbar.find(".search input")},extend:function(){for(var t=this,e=arguments.length,n=new Array(e),o=0;o<e;o++)n[o]=arguments[o];var r,a=n[0]||{},s=1,l=!1;for("boolean"==typeof a&&(l=a,a=n[s]||{},s++),"object"!==i(a)&&"function"!=typeof a&&(a={});s<n.length;s++){var c=n[s];if(null!=c)for(var h in c){var u=c[h];if("__proto__"!==h&&a!==u){var d=Array.isArray(u);if(l&&u&&(this.isObject(u)||d)){var p=a[h];if(d&&Array.isArray(p)&&p.every((function(e){return!t.isObject(e)&&!Array.isArray(e)}))){a[h]=u;continue}r=d&&!Array.isArray(p)?[]:d||this.isObject(p)?p:{},a[h]=this.extend(l,r,u)}else void 0!==u&&(a[h]=u)}}}return a},sprintf:function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),n=1;n<e;n++)i[n-1]=arguments[n];var o=!0,r=0,a=t.replace(/%s/g,(function(){var t=i[r++];return void 0===t?(o=!1,""):t}));return o?a:""},isObject:function(t){if("object"!==i(t)||null===t)return!1;for(var e=t;null!==Object.getPrototypeOf(e);)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e},isEmptyObject:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return 0===Object.entries(t).length&&t.constructor===Object},isNumeric:function(t){return!isNaN(parseFloat(t))&&isFinite(t)},getFieldTitle:function(t,e){var i,n=h(t);try{for(n.s();!(i=n.n()).done;){var o=i.value;if(o.field===e)return o.title}}catch(t){n.e(t)}finally{n.f()}return""},setFieldIndex:function(t){var e,i=0,n=[],o=h(t[0]);try{for(o.s();!(e=o.n()).done;){i+=e.value.colspan||1}}catch(t){o.e(t)}finally{o.f()}for(var r=0;r<t.length;r++){n[r]=[];for(var a=0;a<i;a++)n[r][a]=!1}for(var s=0;s<t.length;s++){var l,c=h(t[s]);try{for(c.s();!(l=c.n()).done;){var u=l.value,d=u.rowspan||1,p=u.colspan||1,f=n[s].indexOf(!1);u.colspanIndex=f,1===p?(u.fieldIndex=f,void 0===u.field&&(u.field=f)):u.colspanGroup=u.colspan;for(var g=0;g<d;g++)for(var v=0;v<p;v++)n[s+g][f+v]=!0}}catch(t){c.e(t)}finally{c.f()}}},normalizeAccent:function(t){return"string"!=typeof t?t:t.normalize("NFD").replace(/[\u0300-\u036f]/g,"")},updateFieldGroup:function(t,e){var i,n,o=(i=[]).concat.apply(i,s(t)),r=h(t);try{for(r.s();!(n=r.n()).done;){var a,l=h(n.value);try{for(l.s();!(a=l.n()).done;){var c=a.value;if(c.colspanGroup>1){for(var u=0,d=function(t){o.find((function(e){return e.fieldIndex===t})).visible&&u++},p=c.colspanIndex;p<c.colspanIndex+c.colspanGroup;p++)d(p);c.colspan=u,c.visible=u>0}}}catch(t){l.e(t)}finally{l.f()}}}catch(t){r.e(t)}finally{r.f()}if(!(t.length<2)){var f,g=h(e);try{var v=function(){var t=f.value,e=o.filter((function(e){return e.fieldIndex===t.fieldIndex}));if(e.length>1){var i,n=h(e);try{for(n.s();!(i=n.n()).done;){i.value.visible=t.visible}}catch(t){n.e(t)}finally{n.f()}}};for(g.s();!(f=g.n()).done;)v()}catch(t){g.e(t)}finally{g.f()}}},getScrollBarWidth:function(){if(void 0===this.cachedWidth){var e=t("<div/>").addClass("fixed-table-scroll-inner"),i=t("<div/>").addClass("fixed-table-scroll-outer");i.append(e),t("body").append(i);var n=e[0].offsetWidth;i.css("overflow","scroll");var o=e[0].offsetWidth;n===o&&(o=i[0].clientWidth),i.remove(),this.cachedWidth=n-o}return this.cachedWidth},calculateObjectValue:function(t,e,n,o){var r=e;if("string"==typeof e){var a=e.split(".");if(a.length>1){r=window;var l,c=h(a);try{for(c.s();!(l=c.n()).done;){r=r[l.value]}}catch(t){c.e(t)}finally{c.f()}}else r=window[e]}return null!==r&&"object"===i(r)?r:"function"==typeof r?r.apply(t,n||[]):!r&&"string"==typeof e&&n&&this.sprintf.apply(this,[e].concat(s(n)))?this.sprintf.apply(this,[e].concat(s(n))):o},compareObjects:function(t,e,i){var n=Object.keys(t),o=Object.keys(e);if(i&&n.length!==o.length)return!1;for(var r=0,a=n;r<a.length;r++){var s=a[r];if(o.includes(s)&&t[s]!==e[s])return!1}return!0},regexCompare:function(t,e){try{var i=e.match(/^\/(.*?)\/([gim]*)$/);if(-1!==t.toString().search(i?new RegExp(i[1],i[2]):new RegExp(e,"gim")))return!0}catch(t){return!1}return!1},escapeApostrophe:function(t){return t.toString().replace(/'/g,"&#39;")},escapeHTML:function(t){return t?t.toString().replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;"):t},unescapeHTML:function(t){return"string"==typeof t&&t?t.toString().replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#39;/g,"'"):t},removeHTML:function(t){return t?t.toString().replace(/(<([^>]+)>)/gi,"").replace(/&[#A-Za-z0-9]+;/gi,"").trim():t},getRealDataAttr:function(t){for(var e=0,i=Object.entries(t);e<i.length;e++){var n=a(i[e],2),o=n[0],r=n[1],s=o.split(/(?=[A-Z])/).join("-").toLowerCase();s!==o&&(t[s]=r,delete t[o])}return t},getItemField:function(t,e,i){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:void 0,o=t;if(void 0!==n&&(i=n),"string"!=typeof e||t.hasOwnProperty(e))return i?this.escapeHTML(t[e]):t[e];var r,a=e.split("."),s=h(a);try{for(s.s();!(r=s.n()).done;){var l=r.value;o=o&&o[l]}}catch(t){s.e(t)}finally{s.f()}return i?this.escapeHTML(o):o},isIEBrowser:function(){return navigator.userAgent.includes("MSIE ")||/Trident.*rv:11\./.test(navigator.userAgent)},findIndex:function(t,e){var i,n=h(t);try{for(n.s();!(i=n.n()).done;){var o=i.value;if(JSON.stringify(o)===JSON.stringify(e))return t.indexOf(o)}}catch(t){n.e(t)}finally{n.f()}return-1},trToData:function(e,i){var n=this,o=[],r=[];return i.each((function(i,a){var s=t(a),l={};l._id=s.attr("id"),l._class=s.attr("class"),l._data=n.getRealDataAttr(s.data()),l._style=s.attr("style"),s.find(">td,>th").each((function(o,a){for(var s=t(a),c=+s.attr("colspan")||1,h=+s.attr("rowspan")||1,u=o;r[i]&&r[i][u];u++);for(var d=u;d<u+c;d++)for(var p=i;p<i+h;p++)r[p]||(r[p]=[]),r[p][d]=!0;var f=e[u].field;l[f]=n.escapeApostrophe(s.html().trim()),l["_".concat(f,"_id")]=s.attr("id"),l["_".concat(f,"_class")]=s.attr("class"),l["_".concat(f,"_rowspan")]=s.attr("rowspan"),l["_".concat(f,"_colspan")]=s.attr("colspan"),l["_".concat(f,"_title")]=s.attr("title"),l["_".concat(f,"_data")]=n.getRealDataAttr(s.data()),l["_".concat(f,"_style")]=s.attr("style")})),o.push(l)})),o},sort:function(t,e,i,n,o,r){if(null==t&&(t=""),null==e&&(e=""),n.sortStable&&t===e&&(t=o,e=r),this.isNumeric(t)&&this.isNumeric(e))return(t=parseFloat(t))<(e=parseFloat(e))?-1*i:t>e?i:0;if(n.sortEmptyLast){if(""===t)return 1;if(""===e)return-1}return t===e?0:("string"!=typeof t&&(t=t.toString()),-1===t.localeCompare(e)?-1*i:i)},getEventName:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e=e||"".concat(+new Date).concat(~~(1e6*Math.random())),"".concat(t,"-").concat(e)},hasDetailViewIcon:function(t){return t.detailView&&t.detailViewIcon&&!t.cardView},getDetailViewIndexOffset:function(t){return this.hasDetailViewIcon(t)&&"right"!==t.detailViewAlign?1:0},checkAutoMergeCells:function(t){var e,i=h(t);try{for(i.s();!(e=i.n()).done;)for(var n=e.value,o=0,r=Object.keys(n);o<r.length;o++){var a=r[o];if(a.startsWith("_")&&(a.endsWith("_rowspan")||a.endsWith("_colspan")))return!0}}catch(t){i.e(t)}finally{i.f()}return!1},deepCopy:function(t){return void 0===t?t:this.extend(!0,Array.isArray(t)?[]:{},t)},debounce:function(t,e,i){var n;return function(){var o=this,r=arguments,a=function(){n=null,i||t.apply(o,r)},s=i&&!n;clearTimeout(n),n=setTimeout(a,e),s&&t.apply(o,r)}}},rv=ov.getBootstrapVersion(),av={3:{classes:{buttonsPrefix:"btn",buttons:"default",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"pull",inputGroup:"input-group",inputPrefix:"input-",input:"form-control",select:"form-control",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{toolbarDropdown:['<ul class="dropdown-menu" role="menu">',"</ul>"],toolbarDropdownItem:'<li class="dropdown-item-marker" role="menuitem"><label>%s</label></li>',toolbarDropdownSeparator:'<li class="divider"></li>',pageDropdown:['<ul class="dropdown-menu" role="menu">',"</ul>"],pageDropdownItem:'<li role="menuitem" class="%s"><a href="#">%s</a></li>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s<span class="input-group-btn">%s</span></div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}},4:{classes:{buttonsPrefix:"btn",buttons:"secondary",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"float",inputGroup:"btn-group",inputPrefix:"form-control-",input:"form-control",select:"form-control",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{toolbarDropdown:['<div class="dropdown-menu dropdown-menu-right">',"</div>"],toolbarDropdownItem:'<label class="dropdown-item dropdown-item-marker">%s</label>',pageDropdown:['<div class="dropdown-menu">',"</div>"],pageDropdownItem:'<a class="dropdown-item %s" href="#">%s</a>',toolbarDropdownSeparator:'<div class="dropdown-divider"></div>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s<div class="input-group-append">%s</div></div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}},5:{classes:{buttonsPrefix:"btn",buttons:"secondary",buttonsGroup:"btn-group",buttonsDropdown:"btn-group",pull:"float",inputGroup:"btn-group",inputPrefix:"form-control-",input:"form-control",select:"form-select",paginationDropdown:"btn-group dropdown",dropup:"dropup",dropdownActive:"active",paginationActive:"active",buttonActive:"active"},html:{dataToggle:"data-bs-toggle",toolbarDropdown:['<div class="dropdown-menu dropdown-menu-end">',"</div>"],toolbarDropdownItem:'<label class="dropdown-item dropdown-item-marker">%s</label>',pageDropdown:['<div class="dropdown-menu">',"</div>"],pageDropdownItem:'<a class="dropdown-item %s" href="#">%s</a>',toolbarDropdownSeparator:'<div class="dropdown-divider"></div>',dropdownCaret:'<span class="caret"></span>',pagination:['<ul class="pagination%s">',"</ul>"],paginationItem:'<li class="page-item%s"><a class="page-link" aria-label="%s" href="javascript:void(0)">%s</a></li>',icon:'<i class="%s %s"></i>',inputGroup:'<div class="input-group">%s%s</div>',searchInput:'<input class="%s%s" type="text" placeholder="%s">',searchButton:'<button class="%s" type="button" name="search" title="%s">%s %s</button>',searchClearButton:'<button class="%s" type="button" name="clearSearch" title="%s">%s %s</button>'}}}[rv],sv={height:void 0,classes:"table table-bordered table-hover",buttons:{},theadClasses:"",headerStyle:function(t){return{}},rowStyle:function(t,e){return{}},rowAttributes:function(t,e){return{}},undefinedText:"-",locale:void 0,virtualScroll:!1,virtualScrollItemHeight:void 0,sortable:!0,sortClass:void 0,silentSort:!0,sortEmptyLast:!1,sortName:void 0,sortOrder:void 0,sortReset:!1,sortStable:!1,sortResetPage:!1,rememberOrder:!1,serverSort:!0,customSort:void 0,columns:[[]],data:[],url:void 0,method:"get",cache:!0,contentType:"application/json",dataType:"json",ajax:void 0,ajaxOptions:{},queryParams:function(t){return t},queryParamsType:"limit",responseHandler:function(t){return t},totalField:"total",totalNotFilteredField:"totalNotFiltered",dataField:"rows",footerField:"footer",pagination:!1,paginationParts:["pageInfo","pageSize","pageList"],showExtendedPagination:!1,paginationLoop:!0,sidePagination:"client",totalRows:0,totalNotFiltered:0,pageNumber:1,pageSize:10,pageList:[10,25,50,100],paginationHAlign:"right",paginationVAlign:"bottom",paginationDetailHAlign:"left",paginationPreText:"&lsaquo;",paginationNextText:"&rsaquo;",paginationSuccessivelySize:5,paginationPagesBySide:1,paginationUseIntermediate:!1,search:!1,searchable:!1,searchHighlight:!1,searchOnEnterKey:!1,strictSearch:!1,regexSearch:!1,searchSelector:!1,visibleSearch:!1,showButtonIcons:!0,showButtonText:!1,showSearchButton:!1,showSearchClearButton:!1,trimOnSearch:!0,searchAlign:"right",searchTimeOut:500,searchText:"",customSearch:void 0,showHeader:!0,showFooter:!1,footerStyle:function(t){return{}},searchAccentNeutralise:!1,showColumns:!1,showColumnsToggleAll:!1,showColumnsSearch:!1,minimumCountColumns:1,showPaginationSwitch:!1,showRefresh:!1,showToggle:!1,showFullscreen:!1,smartDisplay:!0,escape:!1,escapeTitle:!0,filterOptions:{filterAlgorithm:"and"},idField:void 0,selectItemName:"btSelectItem",clickToSelect:!1,ignoreClickToSelectOn:function(t){var e=t.tagName;return["A","BUTTON"].includes(e)},singleSelect:!1,checkboxHeader:!0,maintainMetaData:!1,multipleSelectRow:!1,uniqueId:void 0,cardView:!1,detailView:!1,detailViewIcon:!0,detailViewByClick:!1,detailViewAlign:"left",detailFormatter:function(t,e){return""},detailFilter:function(t,e){return!0},toolbar:void 0,toolbarAlign:"left",buttonsToolbar:void 0,buttonsAlign:"right",buttonsOrder:["paginationSwitch","refresh","toggle","fullscreen","columns"],buttonsPrefix:av.classes.buttonsPrefix,buttonsClass:av.classes.buttons,iconsPrefix:void 0,icons:{},iconSize:void 0,fixedScroll:!1,loadingFontSize:"auto",loadingTemplate:function(t){return'<span class="loading-wrap">\n      <span class="loading-text">'.concat(t,'</span>\n      <span class="animation-wrap"><span class="animation-dot"></span></span>\n      </span>\n    ')},onAll:function(t,e){return!1},onClickCell:function(t,e,i,n){return!1},onDblClickCell:function(t,e,i,n){return!1},onClickRow:function(t,e){return!1},onDblClickRow:function(t,e){return!1},onSort:function(t,e){return!1},onCheck:function(t){return!1},onUncheck:function(t){return!1},onCheckAll:function(t){return!1},onUncheckAll:function(t){return!1},onCheckSome:function(t){return!1},onUncheckSome:function(t){return!1},onLoadSuccess:function(t){return!1},onLoadError:function(t){return!1},onColumnSwitch:function(t,e){return!1},onColumnSwitchAll:function(t){return!1},onPageChange:function(t,e){return!1},onSearch:function(t){return!1},onToggle:function(t){return!1},onPreBody:function(t){return!1},onPostBody:function(){return!1},onPostHeader:function(){return!1},onPostFooter:function(){return!1},onExpandRow:function(t,e,i){return!1},onCollapseRow:function(t,e){return!1},onRefreshOptions:function(t){return!1},onRefresh:function(t){return!1},onResetView:function(){return!1},onScrollBody:function(){return!1},onTogglePagination:function(t){return!1},onVirtualScroll:function(t,e){return!1}},lv={formatLoadingMessage:function(){return"Loading, please wait"},formatRecordsPerPage:function(t){return"".concat(t," rows per page")},formatShowingRows:function(t,e,i,n){return void 0!==n&&n>0&&n>i?"Showing ".concat(t," to ").concat(e," of ").concat(i," rows (filtered from ").concat(n," total rows)"):"Showing ".concat(t," to ").concat(e," of ").concat(i," rows")},formatSRPaginationPreText:function(){return"previous page"},formatSRPaginationPageText:function(t){return"to page ".concat(t)},formatSRPaginationNextText:function(){return"next page"},formatDetailPagination:function(t){return"Showing ".concat(t," rows")},formatSearch:function(){return"Search"},formatClearSearch:function(){return"Clear Search"},formatNoMatches:function(){return"No matching records found"},formatPaginationSwitch:function(){return"Hide/Show pagination"},formatPaginationSwitchDown:function(){return"Show pagination"},formatPaginationSwitchUp:function(){return"Hide pagination"},formatRefresh:function(){return"Refresh"},formatToggleOn:function(){return"Show card view"},formatToggleOff:function(){return"Hide card view"},formatColumns:function(){return"Columns"},formatColumnsToggleAll:function(){return"Toggle all"},formatFullscreen:function(){return"Fullscreen"},formatAllRows:function(){return"All"}},cv={field:void 0,title:void 0,titleTooltip:void 0,class:void 0,width:void 0,widthUnit:"px",rowspan:void 0,colspan:void 0,align:void 0,halign:void 0,falign:void 0,valign:void 0,cellStyle:void 0,radio:!1,checkbox:!1,checkboxEnabled:!0,clickToSelect:!0,showSelectTitle:!1,sortable:!1,sortName:void 0,order:"asc",sorter:void 0,visible:!0,switchable:!0,switchableLabel:void 0,cardVisible:!0,searchable:!0,formatter:void 0,footerFormatter:void 0,footerStyle:void 0,detailFormatter:void 0,searchFormatter:!0,searchHighlightFormatter:!1,escape:void 0,events:void 0};Object.assign(sv,lv);var hv={VERSION:"1.22.3",THEME:"bootstrap".concat(rv),CONSTANTS:av,DEFAULTS:sv,COLUMN_DEFAULTS:cv,METHODS:["getOptions","refreshOptions","getData","getSelections","load","append","prepend","remove","removeAll","insertRow","updateRow","getRowByUniqueId","updateByUniqueId","removeByUniqueId","updateCell","updateCellByUniqueId","showRow","hideRow","getHiddenRows","showColumn","hideColumn","getVisibleColumns","getHiddenColumns","showAllColumns","hideAllColumns","mergeCells","checkAll","uncheckAll","checkInvert","check","uncheck","checkBy","uncheckBy","refresh","destroy","resetView","showLoading","hideLoading","togglePagination","toggleFullscreen","toggleView","resetSearch","filterBy","sortBy","scrollTo","getScrollPosition","selectPage","prevPage","nextPage","toggleDetailView","expandRow","collapseRow","expandRowByUniqueId","collapseRowByUniqueId","expandAllRows","collapseAllRows","updateColumnTitle","updateFormatText"],EVENTS:{"all.bs.table":"onAll","click-row.bs.table":"onClickRow","dbl-click-row.bs.table":"onDblClickRow","click-cell.bs.table":"onClickCell","dbl-click-cell.bs.table":"onDblClickCell","sort.bs.table":"onSort","check.bs.table":"onCheck","uncheck.bs.table":"onUncheck","check-all.bs.table":"onCheckAll","uncheck-all.bs.table":"onUncheckAll","check-some.bs.table":"onCheckSome","uncheck-some.bs.table":"onUncheckSome","load-success.bs.table":"onLoadSuccess","load-error.bs.table":"onLoadError","column-switch.bs.table":"onColumnSwitch","column-switch-all.bs.table":"onColumnSwitchAll","page-change.bs.table":"onPageChange","search.bs.table":"onSearch","toggle.bs.table":"onToggle","pre-body.bs.table":"onPreBody","post-body.bs.table":"onPostBody","post-header.bs.table":"onPostHeader","post-footer.bs.table":"onPostFooter","expand-row.bs.table":"onExpandRow","collapse-row.bs.table":"onCollapseRow","refresh-options.bs.table":"onRefreshOptions","reset-view.bs.table":"onResetView","refresh.bs.table":"onRefresh","scroll-body.bs.table":"onScrollBody","toggle-pagination.bs.table":"onTogglePagination","virtual-scroll.bs.table":"onVirtualScroll"},LOCALES:{en:lv,"en-US":lv}},uv=function(){function t(e){var i=this;n(this,t),this.rows=e.rows,this.scrollEl=e.scrollEl,this.contentEl=e.contentEl,this.callback=e.callback,this.itemHeight=e.itemHeight,this.cache={},this.scrollTop=this.scrollEl.scrollTop,this.initDOM(this.rows,e.fixedScroll),this.scrollEl.scrollTop=this.scrollTop,this.lastCluster=0;var o=function(){i.lastCluster!==(i.lastCluster=i.getNum())&&(i.initDOM(i.rows),i.callback(i.startIndex,i.endIndex))};this.scrollEl.addEventListener("scroll",o,!1),this.destroy=function(){i.contentEl.innerHtml="",i.scrollEl.removeEventListener("scroll",o,!1)}}return r(t,[{key:"initDOM",value:function(t,e){void 0===this.clusterHeight&&(this.cache.scrollTop=this.scrollEl.scrollTop,this.cache.data=this.contentEl.innerHTML=t[0]+t[0]+t[0],this.getRowsHeight(t));var i=this.initData(t,this.getNum(e)),n=i.rows.join(""),o=this.checkChanges("data",n),r=this.checkChanges("top",i.topOffset),a=this.checkChanges("bottom",i.bottomOffset),s=[];o&&r?(i.topOffset&&s.push(this.getExtra("top",i.topOffset)),s.push(n),i.bottomOffset&&s.push(this.getExtra("bottom",i.bottomOffset)),this.startIndex=i.start,this.endIndex=i.end,this.contentEl.innerHTML=s.join(""),e&&(this.contentEl.scrollTop=this.cache.scrollTop)):a&&(this.contentEl.lastChild.style.height="".concat(i.bottomOffset,"px"))}},{key:"getRowsHeight",value:function(){if(void 0===this.itemHeight){var t=this.contentEl.children,e=t[Math.floor(t.length/2)];this.itemHeight=e.offsetHeight}this.blockHeight=50*this.itemHeight,this.clusterRows=200,this.clusterHeight=4*this.blockHeight}},{key:"getNum",value:function(t){return this.scrollTop=t?this.cache.scrollTop:this.scrollEl.scrollTop,Math.floor(this.scrollTop/(this.clusterHeight-this.blockHeight))||0}},{key:"initData",value:function(t,e){if(t.length<50)return{topOffset:0,bottomOffset:0,rowsAbove:0,rows:t};var i=Math.max((this.clusterRows-50)*e,0),n=i+this.clusterRows,o=Math.max(i*this.itemHeight,0),r=Math.max((t.length-n)*this.itemHeight,0),a=[],s=i;o<1&&s++;for(var l=i;l<n;l++)t[l]&&a.push(t[l]);return{start:i,end:n,topOffset:o,bottomOffset:r,rowsAbove:s,rows:a}}},{key:"checkChanges",value:function(t,e){var i=e!==this.cache[t];return this.cache[t]=e,i}},{key:"getExtra",value:function(t,e){var i=document.createElement("tr");return i.className="virtual-scroll-".concat(t),e&&(i.style.height="".concat(e,"px")),i.outerHTML}}]),t}(),dv=function(){function e(i,o){n(this,e),this.options=o,this.$el=t(i),this.$el_=this.$el.clone(),this.timeoutId_=0,this.timeoutFooter_=0}return r(e,[{key:"init",value:function(){this.initConstants(),this.initLocale(),this.initContainer(),this.initTable(),this.initHeader(),this.initData(),this.initHiddenRows(),this.initToolbar(),this.initPagination(),this.initBody(),this.initSearchText(),this.initServer()}},{key:"initConstants",value:function(){var e=this.options;this.constants=hv.CONSTANTS,this.constants.theme=t.fn.bootstrapTable.theme,this.constants.dataToggle=this.constants.html.dataToggle||"data-toggle";var n=ov.getIconsPrefix(t.fn.bootstrapTable.theme);"string"==typeof e.icons&&(e.icons=ov.calculateObjectValue(null,e.icons)),e.iconsPrefix=e.iconsPrefix||t.fn.bootstrapTable.defaults.iconsPrefix||n,e.icons=Object.assign(ov.getIcons(e.iconsPrefix),t.fn.bootstrapTable.defaults.icons,e.icons);var o=e.buttonsPrefix?"".concat(e.buttonsPrefix,"-"):"";this.constants.buttonsClass=[e.buttonsPrefix,o+e.buttonsClass,ov.sprintf("".concat(o,"%s"),e.iconSize)].join(" ").trim(),this.buttons=ov.calculateObjectValue(this,e.buttons,[],{}),"object"!==i(this.buttons)&&(this.buttons={})}},{key:"initLocale",value:function(){if(this.options.locale){var i=t.fn.bootstrapTable.locales,n=this.options.locale.split(/-|_/);n[0]=n[0].toLowerCase(),n[1]&&(n[1]=n[1].toUpperCase());var o={};i[this.options.locale]?o=i[this.options.locale]:i[n.join("-")]?o=i[n.join("-")]:i[n[0]]&&(o=i[n[0]]);for(var r=0,s=Object.entries(o);r<s.length;r++){var l=a(s[r],2),c=l[0],h=l[1];this.options[c]===e.DEFAULTS[c]&&(this.options[c]=h)}}}},{key:"initContainer",value:function(){var e=["top","both"].includes(this.options.paginationVAlign)?'<div class="fixed-table-pagination clearfix"></div>':"",i=["bottom","both"].includes(this.options.paginationVAlign)?'<div class="fixed-table-pagination"></div>':"",n=ov.calculateObjectValue(this.options,this.options.loadingTemplate,[this.options.formatLoadingMessage()]);this.$container=t('\n      <div class="bootstrap-table '.concat(this.constants.theme,'">\n      <div class="fixed-table-toolbar"></div>\n      ').concat(e,'\n      <div class="fixed-table-container">\n      <div class="fixed-table-header"><table></table></div>\n      <div class="fixed-table-body">\n      <div class="fixed-table-loading">\n      ').concat(n,'\n      </div>\n      </div>\n      <div class="fixed-table-footer"></div>\n      </div>\n      ').concat(i,"\n      </div>\n    ")),this.$container.insertAfter(this.$el),this.$tableContainer=this.$container.find(".fixed-table-container"),this.$tableHeader=this.$container.find(".fixed-table-header"),this.$tableBody=this.$container.find(".fixed-table-body"),this.$tableLoading=this.$container.find(".fixed-table-loading"),this.$tableFooter=this.$el.find("tfoot"),this.options.buttonsToolbar?this.$toolbar=t("body").find(this.options.buttonsToolbar):this.$toolbar=this.$container.find(".fixed-table-toolbar"),this.$pagination=this.$container.find(".fixed-table-pagination"),this.$tableBody.append(this.$el),this.$container.after('<div class="clearfix"></div>'),this.$el.addClass(this.options.classes),this.$tableLoading.addClass(this.options.classes),this.options.height&&(this.$tableContainer.addClass("fixed-height"),this.options.showFooter&&this.$tableContainer.addClass("has-footer"),this.options.classes.split(" ").includes("table-bordered")&&(this.$tableBody.append('<div class="fixed-table-border"></div>'),this.$tableBorder=this.$tableBody.find(".fixed-table-border"),this.$tableLoading.addClass("fixed-table-border")),this.$tableFooter=this.$container.find(".fixed-table-footer"))}},{key:"initTable",value:function(){var i=this,n=[];if(this.$header=this.$el.find(">thead"),this.$header.length?this.options.theadClasses&&this.$header.addClass(this.options.theadClasses):this.$header=t('<thead class="'.concat(this.options.theadClasses,'"></thead>')).appendTo(this.$el),this._headerTrClasses=[],this._headerTrStyles=[],this.$header.find("tr").each((function(e,o){var r=t(o),a=[];r.find("th").each((function(e,i){var n=t(i);void 0!==n.data("field")&&n.data("field","".concat(n.data("field")));var o=Object.assign({},n.data());for(var r in o)t.fn.bootstrapTable.columnDefaults.hasOwnProperty(r)&&delete o[r];a.push(ov.extend({},{_data:ov.getRealDataAttr(o),title:n.html(),class:n.attr("class"),titleTooltip:n.attr("title"),rowspan:n.attr("rowspan")?+n.attr("rowspan"):void 0,colspan:n.attr("colspan")?+n.attr("colspan"):void 0},n.data()))})),n.push(a),r.attr("class")&&i._headerTrClasses.push(r.attr("class")),r.attr("style")&&i._headerTrStyles.push(r.attr("style"))})),Array.isArray(this.options.columns[0])||(this.options.columns=[this.options.columns]),this.options.columns=ov.extend(!0,[],n,this.options.columns),this.columns=[],this.fieldsColumnsIndex=[],ov.setFieldIndex(this.options.columns),this.options.columns.forEach((function(t,n){t.forEach((function(t,o){var r=ov.extend({},e.COLUMN_DEFAULTS,t,{passed:t});void 0!==r.fieldIndex&&(i.columns[r.fieldIndex]=r,i.fieldsColumnsIndex[r.field]=r.fieldIndex),i.options.columns[n][o]=r}))})),!this.options.data.length){var o=ov.trToData(this.columns,this.$el.find(">tbody>tr"));o.length&&(this.options.data=o,this.fromHtml=!0)}this.options.pagination&&"server"!==this.options.sidePagination||(this.footerData=ov.trToData(this.columns,this.$el.find(">tfoot>tr"))),this.footerData&&this.$el.find("tfoot").html("<tr></tr>"),!this.options.showFooter||this.options.cardView?this.$tableFooter.hide():this.$tableFooter.show()}},{key:"initHeader",value:function(){var e=this,n={},o=[];this.header={fields:[],styles:[],classes:[],formatters:[],detailFormatters:[],events:[],sorters:[],sortNames:[],cellStyles:[],searchables:[]},ov.updateFieldGroup(this.options.columns,this.columns),this.options.columns.forEach((function(t,r){var s=[];s.push("<tr".concat(ov.sprintf(' class="%s"',e._headerTrClasses[r])," ").concat(ov.sprintf(' style="%s"',e._headerTrStyles[r]),">"));var l="";if(0===r&&ov.hasDetailViewIcon(e.options)){var c=e.options.columns.length>1?' rowspan="'.concat(e.options.columns.length,'"'):"";l='<th class="detail"'.concat(c,'>\n          <div class="fht-cell"></div>\n          </th>')}l&&"right"!==e.options.detailViewAlign&&s.push(l),t.forEach((function(t,o){var l=ov.sprintf(' class="%s"',t.class),c=t.widthUnit,h=parseFloat(t.width),u=t.halign?t.halign:t.align,d=ov.sprintf("text-align: %s; ",u),p=ov.sprintf("text-align: %s; ",t.align),f=ov.sprintf("vertical-align: %s; ",t.valign);if(f+=ov.sprintf("width: %s; ",!t.checkbox&&!t.radio||h?h?h+c:void 0:t.showSelectTitle?void 0:"36px"),void 0!==t.fieldIndex||t.visible){var g=ov.calculateObjectValue(null,e.options.headerStyle,[t]),v=[],b=[],m="";if(g&&g.css)for(var y=0,w=Object.entries(g.css);y<w.length;y++){var S=a(w[y],2),x=S[0],O=S[1];v.push("".concat(x,": ").concat(O))}if(g&&g.classes&&(m=ov.sprintf(' class="%s"',t.class?[t.class,g.classes].join(" "):g.classes)),void 0!==t.fieldIndex){if(e.header.fields[t.fieldIndex]=t.field,e.header.styles[t.fieldIndex]=p+f,e.header.classes[t.fieldIndex]=l,e.header.formatters[t.fieldIndex]=t.formatter,e.header.detailFormatters[t.fieldIndex]=t.detailFormatter,e.header.events[t.fieldIndex]=t.events,e.header.sorters[t.fieldIndex]=t.sorter,e.header.sortNames[t.fieldIndex]=t.sortName,e.header.cellStyles[t.fieldIndex]=t.cellStyle,e.header.searchables[t.fieldIndex]=t.searchable,!t.visible)return;if(e.options.cardView&&!t.cardVisible)return;n[t.field]=t}if(Object.keys(t._data||{}).length>0)for(var k=0,C=Object.entries(t._data);k<C.length;k++){var T=a(C[k],2),P=T[0],I=T[1];b.push("data-".concat(P,"='").concat("object"===i(I)?JSON.stringify(I):I,"'"))}s.push("<th".concat(ov.sprintf(' title="%s"',t.titleTooltip)),t.checkbox||t.radio?ov.sprintf(' class="bs-checkbox %s"',t.class||""):m||l,ov.sprintf(' style="%s"',d+f+v.join("; ")||void 0),ov.sprintf(' rowspan="%s"',t.rowspan),ov.sprintf(' colspan="%s"',t.colspan),ov.sprintf(' data-field="%s"',t.field),0===o&&r>0?" data-not-first-th":"",b.length>0?b.join(" "):"",">"),s.push(ov.sprintf('<div class="th-inner %s">',e.options.sortable&&t.sortable?"sortable".concat("center"===u?" sortable-center":""," both"):""));var A=e.options.escape&&e.options.escapeTitle?ov.escapeHTML(t.title):t.title,$=A;t.checkbox&&(A="",!e.options.singleSelect&&e.options.checkboxHeader&&(A='<label><input name="btSelectAll" type="checkbox" /><span></span></label>'),e.header.stateField=t.field),t.radio&&(A="",e.header.stateField=t.field),!A&&t.showSelectTitle&&(A+=$),s.push(A),s.push("</div>"),s.push('<div class="fht-cell"></div>'),s.push("</div>"),s.push("</th>")}})),l&&"right"===e.options.detailViewAlign&&s.push(l),s.push("</tr>"),s.length>3&&o.push(s.join(""))})),this.$header.html(o.join("")),this.$header.find("th[data-field]").each((function(e,i){t(i).data(n[t(i).data("field")])})),this.$container.off("click",".th-inner").on("click",".th-inner",(function(i){var n=t(i.currentTarget);if(e.options.detailView&&!n.parent().hasClass("bs-checkbox")&&n.closest(".bootstrap-table")[0]!==e.$container[0])return!1;e.options.sortable&&n.parent().data().sortable&&e.onSort(i)}));var r=ov.getEventName("resize.bootstrap-table",this.$el.attr("id"));t(window).off(r),!this.options.showHeader||this.options.cardView?(this.$header.hide(),this.$tableHeader.hide(),this.$tableLoading.css("top",0)):(this.$header.show(),this.$tableHeader.show(),this.$tableLoading.css("top",this.$header.outerHeight()+1),this.getCaret(),t(window).on(r,(function(){return e.resetView()}))),this.$selectAll=this.$header.find('[name="btSelectAll"]'),this.$selectAll.off("click").on("click",(function(i){i.stopPropagation();var n=t(i.currentTarget).prop("checked");e[n?"checkAll":"uncheckAll"](),e.updateSelected()}))}},{key:"initData",value:function(t,e){"append"===e?this.options.data=this.options.data.concat(t):"prepend"===e?this.options.data=[].concat(t).concat(this.options.data):(t=t||ov.deepCopy(this.options.data),this.options.data=Array.isArray(t)?t:t[this.options.dataField]),this.data=s(this.options.data),this.options.sortReset&&(this.unsortedData=s(this.data)),"server"!==this.options.sidePagination&&this.initSort()}},{key:"initSort",value:function(){var t=this,e=this.options.sortName,i="desc"===this.options.sortOrder?-1:1,n=this.header.fields.indexOf(this.options.sortName),o=0;-1!==n?(this.options.sortStable&&this.data.forEach((function(t,e){t.hasOwnProperty("_position")||(t._position=e)})),this.options.customSort?ov.calculateObjectValue(this.options,this.options.customSort,[this.options.sortName,this.options.sortOrder,this.data]):this.data.sort((function(o,r){t.header.sortNames[n]&&(e=t.header.sortNames[n]);var a=ov.getItemField(o,e,t.options.escape),s=ov.getItemField(r,e,t.options.escape),l=ov.calculateObjectValue(t.header,t.header.sorters[n],[a,s,o,r]);return void 0!==l?t.options.sortStable&&0===l?i*(o._position-r._position):i*l:ov.sort(a,s,i,t.options,o._position,r._position)})),void 0!==this.options.sortClass&&(clearTimeout(o),o=setTimeout((function(){t.$el.removeClass(t.options.sortClass);var e=t.$header.find('[data-field="'.concat(t.options.sortName,'"]')).index();t.$el.find("tr td:nth-child(".concat(e+1,")")).addClass(t.options.sortClass)}),250))):this.options.sortReset&&(this.data=s(this.unsortedData))}},{key:"sortBy",value:function(t){this.options.sortName=t.field,this.options.sortOrder=t.hasOwnProperty("sortOrder")?t.sortOrder:"asc",this._sort()}},{key:"onSort",value:function(e){var i=e.type,n=e.currentTarget,o="keypress"===i?t(n):t(n).parent(),r=this.$header.find("th").eq(o.index());if(this.$header.add(this.$header_).find("span.order").remove(),this.options.sortName===o.data("field")){var a=this.options.sortOrder,s=this.columns[this.fieldsColumnsIndex[o.data("field")]].sortOrder||this.columns[this.fieldsColumnsIndex[o.data("field")]].order;void 0===a?this.options.sortOrder="asc":"asc"===a?this.options.sortOrder=this.options.sortReset?"asc"===s?"desc":void 0:"desc":"desc"===this.options.sortOrder&&(this.options.sortOrder=this.options.sortReset?"desc"===s?"asc":void 0:"asc"),void 0===this.options.sortOrder&&(this.options.sortName=void 0)}else this.options.sortName=o.data("field"),this.options.rememberOrder?this.options.sortOrder="asc"===o.data("order")?"desc":"asc":this.options.sortOrder=this.columns[this.fieldsColumnsIndex[o.data("field")]].sortOrder||this.columns[this.fieldsColumnsIndex[o.data("field")]].order;o.add(r).data("order",this.options.sortOrder),this.getCaret(),this._sort()}},{key:"_sort",value:function(){if("server"===this.options.sidePagination&&this.options.serverSort)return this.options.pageNumber=1,void this.initServer(this.options.silentSort);this.options.pagination&&this.options.sortResetPage&&(this.options.pageNumber=1,this.initPagination()),this.trigger("sort",this.options.sortName,this.options.sortOrder),this.initSort(),this.initBody()}},{key:"initToolbar",value:function(){var e,n=this,o=this.options,r=[],s=0,l=0;this.$toolbar.find(".bs-bars").children().length&&t("body").append(t(o.toolbar)),this.$toolbar.html(""),"string"!=typeof o.toolbar&&"object"!==i(o.toolbar)||t(ov.sprintf('<div class="bs-bars %s-%s"></div>',this.constants.classes.pull,o.toolbarAlign)).appendTo(this.$toolbar).append(t(o.toolbar)),r=['<div class="'.concat(["columns","columns-".concat(o.buttonsAlign),this.constants.classes.buttonsGroup,"".concat(this.constants.classes.pull,"-").concat(o.buttonsAlign)].join(" "),'">')],"string"==typeof o.buttonsOrder&&(o.buttonsOrder=o.buttonsOrder.replace(/\[|\]| |'/g,"").split(",")),this.buttons=Object.assign(this.buttons,{paginationSwitch:{text:o.pagination?o.formatPaginationSwitchUp():o.formatPaginationSwitchDown(),icon:o.pagination?o.icons.paginationSwitchDown:o.icons.paginationSwitchUp,render:!1,event:this.togglePagination,attributes:{"aria-label":o.formatPaginationSwitch(),title:o.formatPaginationSwitch()}},refresh:{text:o.formatRefresh(),icon:o.icons.refresh,render:!1,event:this.refresh,attributes:{"aria-label":o.formatRefresh(),title:o.formatRefresh()}},toggle:{text:o.formatToggleOn(),icon:o.icons.toggleOff,render:!1,event:this.toggleView,attributes:{"aria-label":o.formatToggleOn(),title:o.formatToggleOn()}},fullscreen:{text:o.formatFullscreen(),icon:o.icons.fullscreen,render:!1,event:this.toggleFullscreen,attributes:{"aria-label":o.formatFullscreen(),title:o.formatFullscreen()}},columns:{render:!1,html:function(){var t=[];if(t.push('<div class="keep-open '.concat(n.constants.classes.buttonsDropdown,'">\n            <button class="').concat(n.constants.buttonsClass,' dropdown-toggle" type="button" ').concat(n.constants.dataToggle,'="dropdown"\n            aria-label="').concat(o.formatColumns(),'" title="').concat(o.formatColumns(),'">\n            ').concat(o.showButtonIcons?ov.sprintf(n.constants.html.icon,o.iconsPrefix,o.icons.columns):"","\n            ").concat(o.showButtonText?o.formatColumns():"","\n            ").concat(n.constants.html.dropdownCaret,"\n            </button>\n            ").concat(n.constants.html.toolbarDropdown[0])),o.showColumnsSearch&&(t.push(ov.sprintf(n.constants.html.toolbarDropdownItem,ov.sprintf('<input type="text" class="%s" name="columnsSearch" placeholder="%s" autocomplete="off">',n.constants.classes.input,o.formatSearch()))),t.push(n.constants.html.toolbarDropdownSeparator)),o.showColumnsToggleAll){var e=n.getVisibleColumns().length===n.columns.filter((function(t){return!n.isSelectionColumn(t)})).length;t.push(ov.sprintf(n.constants.html.toolbarDropdownItem,ov.sprintf('<input type="checkbox" class="toggle-all" %s> <span>%s</span>',e?'checked="checked"':"",o.formatColumnsToggleAll()))),t.push(n.constants.html.toolbarDropdownSeparator)}var i=0;return n.columns.forEach((function(t){t.visible&&i++})),n.columns.forEach((function(e,r){if(!n.isSelectionColumn(e)&&(!o.cardView||e.cardVisible)){var a=e.visible?' checked="checked"':"",s=i<=o.minimumCountColumns&&a?' disabled="disabled"':"";e.switchable&&(t.push(ov.sprintf(n.constants.html.toolbarDropdownItem,ov.sprintf('<input type="checkbox" data-field="%s" value="%s"%s%s> <span>%s</span>',e.field,r,a,s,e.switchableLabel?e.switchableLabel:e.title))),l++)}})),t.push(n.constants.html.toolbarDropdown[1],"</div>"),t.join("")}}});for(var c={},u=0,d=Object.entries(this.buttons);u<d.length;u++){var p=a(d[u],2),f=p[0],g=p[1],v=void 0;if(g.hasOwnProperty("html"))"function"==typeof g.html?v=g.html():"string"==typeof g.html&&(v=g.html);else{var b=this.constants.buttonsClass;if(g.hasOwnProperty("attributes")&&g.attributes.class&&(b+=" ".concat(g.attributes.class)),v='<button class="'.concat(b,'" type="button" name="').concat(f,'"'),g.hasOwnProperty("attributes"))for(var m=0,y=Object.entries(g.attributes);m<y.length;m++){var w=a(y[m],2),S=w[0],x=w[1];"class"!==S&&(v+=" ".concat(S,'="').concat(x,'"'))}v+=">",o.showButtonIcons&&g.hasOwnProperty("icon")&&(v+="".concat(ov.sprintf(this.constants.html.icon,o.iconsPrefix,g.icon)," ")),o.showButtonText&&g.hasOwnProperty("text")&&(v+=g.text),v+="</button>"}c[f]=v;var O="show".concat(f.charAt(0).toUpperCase()).concat(f.substring(1)),k=o[O];!(!g.hasOwnProperty("render")||g.hasOwnProperty("render")&&g.render)||void 0!==k&&!0!==k||(o[O]=!0),o.buttonsOrder.includes(f)||o.buttonsOrder.push(f)}var C,T=h(o.buttonsOrder);try{for(T.s();!(C=T.n()).done;){var P=C.value;o["show".concat(P.charAt(0).toUpperCase()).concat(P.substring(1))]&&r.push(c[P])}}catch(t){T.e(t)}finally{T.f()}r.push("</div>"),(this.showToolbar||r.length>2)&&this.$toolbar.append(r.join(""));for(var I=function(){var t=a($[A],2),e=t[0],i=t[1];if(i.hasOwnProperty("event")){if("function"==typeof i.event||"string"==typeof i.event){var o="string"==typeof i.event?window[i.event]:i.event;return n.$toolbar.find('button[name="'.concat(e,'"]')).off("click").on("click",(function(){return o.call(n)})),1}for(var r=function(){var t=a(l[s],2),i=t[0],o=t[1],r="string"==typeof o?window[o]:o;n.$toolbar.find('button[name="'.concat(e,'"]')).off(i).on(i,(function(){return r.call(n)}))},s=0,l=Object.entries(i.event);s<l.length;s++)r()}},A=0,$=Object.entries(this.buttons);A<$.length;A++)I();if(o.showColumns){var R=(e=this.$toolbar.find(".keep-open")).find('input[type="checkbox"]:not(".toggle-all")'),E=e.find('input[type="checkbox"].toggle-all');if(l<=o.minimumCountColumns&&e.find("input").prop("disabled",!0),e.find("li, label").off("click").on("click",(function(t){t.stopImmediatePropagation()})),R.off("click").on("click",(function(e){var i=e.currentTarget,o=t(i);n._toggleColumn(o.val(),o.prop("checked"),!1),n.trigger("column-switch",o.data("field"),o.prop("checked")),E.prop("checked",R.filter(":checked").length===n.columns.filter((function(t){return!n.isSelectionColumn(t)})).length)})),E.off("click").on("click",(function(e){var i=e.currentTarget;n._toggleAllColumns(t(i).prop("checked")),n.trigger("column-switch-all",t(i).prop("checked"))})),o.showColumnsSearch){var j=e.find('[name="columnsSearch"]'),_=e.find(".dropdown-item-marker");j.on("keyup paste change",(function(e){var i=e.currentTarget,n=t(i).val().toLowerCase();_.show(),R.each((function(e,i){var o=t(i).parents(".dropdown-item-marker");o.text().toLowerCase().includes(n)||o.hide()}))}))}}var F=function(t){var e=t.is("select")?"change":"keyup drop blur mouseup";t.off(e).on(e,(function(t){o.searchOnEnterKey&&13!==t.keyCode||[37,38,39,40].includes(t.keyCode)||(clearTimeout(s),s=setTimeout((function(){n.onSearch({currentTarget:t.currentTarget})}),o.searchTimeOut))}))};if((o.search||this.showSearchClearButton)&&"string"!=typeof o.searchSelector){r=[];var N=ov.sprintf(this.constants.html.searchButton,this.constants.buttonsClass,o.formatSearch(),o.showButtonIcons?ov.sprintf(this.constants.html.icon,o.iconsPrefix,o.icons.search):"",o.showButtonText?o.formatSearch():""),D=ov.sprintf(this.constants.html.searchClearButton,this.constants.buttonsClass,o.formatClearSearch(),o.showButtonIcons?ov.sprintf(this.constants.html.icon,o.iconsPrefix,o.icons.clearSearch):"",o.showButtonText?o.formatClearSearch():""),V='<input class="'.concat(this.constants.classes.input,"\n        ").concat(ov.sprintf(" %s%s",this.constants.classes.inputPrefix,o.iconSize),'\n        search-input" type="search" aria-label="').concat(o.formatSearch(),'" placeholder="').concat(o.formatSearch(),'" autocomplete="off">'),B=V;if(o.showSearchButton||o.showSearchClearButton){var L=(o.showSearchButton?N:"")+(o.showSearchClearButton?D:"");B=o.search?ov.sprintf(this.constants.html.inputGroup,V,L):L}r.push(ov.sprintf('\n        <div class="'.concat(this.constants.classes.pull,"-").concat(o.searchAlign," search ").concat(this.constants.classes.inputGroup,'">\n          %s\n        </div>\n      '),B)),this.$toolbar.append(r.join(""));var H=ov.getSearchInput(this);o.showSearchButton?(this.$toolbar.find(".search button[name=search]").off("click").on("click",(function(){clearTimeout(s),s=setTimeout((function(){n.onSearch({currentTarget:H})}),o.searchTimeOut)})),o.searchOnEnterKey&&F(H)):F(H),o.showSearchClearButton&&this.$toolbar.find(".search button[name=clearSearch]").click((function(){n.resetSearch()}))}else"string"==typeof o.searchSelector&&F(ov.getSearchInput(this))}},{key:"onSearch",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=e.currentTarget,n=e.firedByInitSearchText,o=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(void 0!==i&&t(i).length&&o){var r=t(i).val().trim();if(this.options.trimOnSearch&&t(i).val()!==r&&t(i).val(r),this.searchText===r)return;var a=ov.getSearchInput(this),s=i instanceof jQuery?i:t(i);(s.is(a)||s.hasClass("search-input"))&&(this.searchText=r,this.options.searchText=r)}n||(this.options.pageNumber=1),this.initSearch(),n?"client"===this.options.sidePagination&&this.updatePagination():this.updatePagination(),this.trigger("search",this.searchText)}},{key:"initSearch",value:function(){var t=this;if(this.filterOptions=this.filterOptions||this.options.filterOptions,"server"!==this.options.sidePagination){if(this.options.customSearch)return this.data=ov.calculateObjectValue(this.options,this.options.customSearch,[this.options.data,this.searchText,this.filterColumns]),this.options.sortReset&&(this.unsortedData=s(this.data)),void this.initSort();var e=this.searchText&&(this.fromHtml?ov.escapeHTML(this.searchText):this.searchText),i=e?e.toLowerCase():"",n=ov.isEmptyObject(this.filterColumns)?null:this.filterColumns;this.options.searchAccentNeutralise&&(i=ov.normalizeAccent(i)),"function"==typeof this.filterOptions.filterAlgorithm?this.data=this.options.data.filter((function(e){return t.filterOptions.filterAlgorithm.apply(null,[e,n])})):"string"==typeof this.filterOptions.filterAlgorithm&&(this.data=n?this.options.data.filter((function(e){var i=t.filterOptions.filterAlgorithm;if("and"===i){for(var o in n)if(Array.isArray(n[o])&&!n[o].includes(e[o])||!Array.isArray(n[o])&&e[o]!==n[o])return!1}else if("or"===i){var r=!1;for(var a in n)(Array.isArray(n[a])&&n[a].includes(e[a])||!Array.isArray(n[a])&&e[a]===n[a])&&(r=!0);return r}return!0})):s(this.options.data));var o=this.getVisibleFields();this.data=i?this.data.filter((function(n,r){for(var a=0;a<t.header.fields.length;a++)if(t.header.searchables[a]&&(!t.options.visibleSearch||-1!==o.indexOf(t.header.fields[a]))){var s=ov.isNumeric(t.header.fields[a])?parseInt(t.header.fields[a],10):t.header.fields[a],l=t.columns[t.fieldsColumnsIndex[s]],c=void 0;if("string"!=typeof s||n.hasOwnProperty(s))c=n[s];else{c=n;for(var h=s.split("."),u=0;u<h.length;u++){if(null===c[h[u]]){c=null;break}c=c[h[u]]}}if(t.options.searchAccentNeutralise&&(c=ov.normalizeAccent(c)),l&&l.searchFormatter&&(c=ov.calculateObjectValue(l,t.header.formatters[a],[c,n,r,l.field],c)),"string"==typeof c||"number"==typeof c){if(t.options.strictSearch&&"".concat(c).toLowerCase()===i||t.options.regexSearch&&ov.regexCompare(c,e))return!0;var d=/(?:(<=|=>|=<|>=|>|<)(?:\s+)?(-?\d+)?|(-?\d+)?(\s+)?(<=|=>|=<|>=|>|<))/gm.exec(t.searchText),p=!1;if(d){var f=d[1]||"".concat(d[5],"l"),g=d[2]||d[3],v=parseInt(c,10),b=parseInt(g,10);switch(f){case">":case"<l":p=v>b;break;case"<":case">l":p=v<b;break;case"<=":case"=<":case">=l":case"=>l":p=v<=b;break;case">=":case"=>":case"<=l":case"=<l":p=v>=b}}if(p||"".concat(c).toLowerCase().includes(i))return!0}}return!1})):this.data,this.options.sortReset&&(this.unsortedData=s(this.data)),this.initSort()}}},{key:"initPagination",value:function(){var t=this,e=this.options;if(e.pagination){this.$pagination.show();var i,n,o,r,a,s,l,c=[],h=!1,u=this.getData({includeHiddenRows:!1}),d=e.pageList;if("string"==typeof d&&(d=d.replace(/\[|\]| /g,"").toLowerCase().split(",")),d=d.map((function(t){return"string"==typeof t?t.toLowerCase()===e.formatAllRows().toLowerCase()||["all","unlimited"].includes(t.toLowerCase())?e.formatAllRows():+t:t})),this.paginationParts=e.paginationParts,"string"==typeof this.paginationParts&&(this.paginationParts=this.paginationParts.replace(/\[|\]| |'/g,"").split(",")),"server"!==e.sidePagination&&(e.totalRows=u.length),this.totalPages=0,e.totalRows&&(e.pageSize===e.formatAllRows()&&(e.pageSize=e.totalRows,h=!0),this.totalPages=1+~~((e.totalRows-1)/e.pageSize),e.totalPages=this.totalPages),this.totalPages>0&&e.pageNumber>this.totalPages&&(e.pageNumber=this.totalPages),this.pageFrom=(e.pageNumber-1)*e.pageSize+1,this.pageTo=e.pageNumber*e.pageSize,this.pageTo>e.totalRows&&(this.pageTo=e.totalRows),this.options.pagination&&"server"!==this.options.sidePagination&&(this.options.totalNotFiltered=this.options.data.length),this.options.showExtendedPagination||(this.options.totalNotFiltered=void 0),(this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")||this.paginationParts.includes("pageSize"))&&c.push('<div class="'.concat(this.constants.classes.pull,"-").concat(e.paginationDetailHAlign,' pagination-detail">')),this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")){var p=this.paginationParts.includes("pageInfoShort")?e.formatDetailPagination(e.totalRows):e.formatShowingRows(this.pageFrom,this.pageTo,e.totalRows,e.totalNotFiltered);c.push('<span class="pagination-info">\n      '.concat(p,"\n      </span>"))}if(this.paginationParts.includes("pageSize")){c.push('<div class="page-list">');var f=['<div class="'.concat(this.constants.classes.paginationDropdown,'">\n        <button class="').concat(this.constants.buttonsClass,' dropdown-toggle" type="button" ').concat(this.constants.dataToggle,'="dropdown">\n        <span class="page-size">\n        ').concat(h?e.formatAllRows():e.pageSize,"\n        </span>\n        ").concat(this.constants.html.dropdownCaret,"\n        </button>\n        ").concat(this.constants.html.pageDropdown[0])];d.forEach((function(i,n){var o;(!e.smartDisplay||0===n||d[n-1]<e.totalRows||i===e.formatAllRows())&&(o=h?i===e.formatAllRows()?t.constants.classes.dropdownActive:"":i===e.pageSize?t.constants.classes.dropdownActive:"",f.push(ov.sprintf(t.constants.html.pageDropdownItem,o,i)))})),f.push("".concat(this.constants.html.pageDropdown[1],"</div>")),c.push(e.formatRecordsPerPage(f.join("")))}if((this.paginationParts.includes("pageInfo")||this.paginationParts.includes("pageInfoShort")||this.paginationParts.includes("pageSize"))&&c.push("</div></div>"),this.paginationParts.includes("pageList")){c.push('<div class="'.concat(this.constants.classes.pull,"-").concat(e.paginationHAlign,' pagination">'),ov.sprintf(this.constants.html.pagination[0],ov.sprintf(" pagination-%s",e.iconSize)),ov.sprintf(this.constants.html.paginationItem," page-pre",e.formatSRPaginationPreText(),e.paginationPreText)),this.totalPages<e.paginationSuccessivelySize?(n=1,o=this.totalPages):o=(n=e.pageNumber-e.paginationPagesBySide)+2*e.paginationPagesBySide,e.pageNumber<e.paginationSuccessivelySize-1&&(o=e.paginationSuccessivelySize),e.paginationSuccessivelySize>this.totalPages-n&&(n=n-(e.paginationSuccessivelySize-(this.totalPages-n))+1),n<1&&(n=1),o>this.totalPages&&(o=this.totalPages);var g=Math.round(e.paginationPagesBySide/2),v=function(i){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return ov.sprintf(t.constants.html.paginationItem,n+(i===e.pageNumber?" ".concat(t.constants.classes.paginationActive):""),e.formatSRPaginationPageText(i),i)};if(n>1){var b=e.paginationPagesBySide;for(b>=n&&(b=n-1),i=1;i<=b;i++)c.push(v(i));n-1===b+1?(i=n-1,c.push(v(i))):n-1>b&&(n-2*e.paginationPagesBySide>e.paginationPagesBySide&&e.paginationUseIntermediate?(i=Math.round((n-g)/2+g),c.push(v(i," page-intermediate"))):c.push(ov.sprintf(this.constants.html.paginationItem," page-first-separator disabled","","...")))}for(i=n;i<=o;i++)c.push(v(i));if(this.totalPages>o){var m=this.totalPages-(e.paginationPagesBySide-1);for(o>=m&&(m=o+1),o+1===m-1?(i=o+1,c.push(v(i))):m>o+1&&(this.totalPages-o>2*e.paginationPagesBySide&&e.paginationUseIntermediate?(i=Math.round((this.totalPages-g-o)/2+o),c.push(v(i," page-intermediate"))):c.push(ov.sprintf(this.constants.html.paginationItem," page-last-separator disabled","","..."))),i=m;i<=this.totalPages;i++)c.push(v(i))}c.push(ov.sprintf(this.constants.html.paginationItem," page-next",e.formatSRPaginationNextText(),e.paginationNextText)),c.push(this.constants.html.pagination[1],"</div>")}this.$pagination.html(c.join(""));var y=["bottom","both"].includes(e.paginationVAlign)?" ".concat(this.constants.classes.dropup):"";this.$pagination.last().find(".page-list > div").addClass(y),e.onlyInfoPagination||(r=this.$pagination.find(".page-list a"),a=this.$pagination.find(".page-pre"),s=this.$pagination.find(".page-next"),l=this.$pagination.find(".page-item").not(".page-next, .page-pre, .page-last-separator, .page-first-separator"),this.totalPages<=1&&this.$pagination.find("div.pagination").hide(),e.smartDisplay&&(d.length<2||e.totalRows<=d[0])&&this.$pagination.find("div.page-list").hide(),this.$pagination[this.getData().length?"show":"hide"](),e.paginationLoop||(1===e.pageNumber&&a.addClass("disabled"),e.pageNumber===this.totalPages&&s.addClass("disabled")),h&&(e.pageSize=e.formatAllRows()),r.off("click").on("click",(function(e){return t.onPageListChange(e)})),a.off("click").on("click",(function(e){return t.onPagePre(e)})),s.off("click").on("click",(function(e){return t.onPageNext(e)})),l.off("click").on("click",(function(e){return t.onPageNumber(e)})))}else this.$pagination.hide()}},{key:"updatePagination",value:function(e){e&&t(e.currentTarget).hasClass("disabled")||(this.options.maintainMetaData||this.resetRows(),this.initPagination(),this.trigger("page-change",this.options.pageNumber,this.options.pageSize),"server"===this.options.sidePagination?this.initServer():this.initBody())}},{key:"onPageListChange",value:function(e){e.preventDefault();var i=t(e.currentTarget);return i.parent().addClass(this.constants.classes.dropdownActive).siblings().removeClass(this.constants.classes.dropdownActive),this.options.pageSize=i.text().toUpperCase()===this.options.formatAllRows().toUpperCase()?this.options.formatAllRows():+i.text(),this.$toolbar.find(".page-size").text(this.options.pageSize),this.updatePagination(e),!1}},{key:"onPagePre",value:function(e){if(!t(e.target).hasClass("disabled"))return e.preventDefault(),this.options.pageNumber-1==0?this.options.pageNumber=this.options.totalPages:this.options.pageNumber--,this.updatePagination(e),!1}},{key:"onPageNext",value:function(e){if(!t(e.target).hasClass("disabled"))return e.preventDefault(),this.options.pageNumber+1>this.options.totalPages?this.options.pageNumber=1:this.options.pageNumber++,this.updatePagination(e),!1}},{key:"onPageNumber",value:function(e){if(e.preventDefault(),this.options.pageNumber!==+t(e.currentTarget).text())return this.options.pageNumber=+t(e.currentTarget).text(),this.updatePagination(e),!1}},{key:"initRow",value:function(t,e,n,o){var r=this,s=[],l={},c=[],h="",u={},d=[];if(!(ov.findIndex(this.hiddenRows,t)>-1)){if((l=ov.calculateObjectValue(this.options,this.options.rowStyle,[t,e],l))&&l.css)for(var p=0,f=Object.entries(l.css);p<f.length;p++){var g=a(f[p],2),v=g[0],b=g[1];c.push("".concat(v,": ").concat(b))}if(u=ov.calculateObjectValue(this.options,this.options.rowAttributes,[t,e],u))for(var m=0,y=Object.entries(u);m<y.length;m++){var w=a(y[m],2),S=w[0],x=w[1];d.push("".concat(S,'="').concat(ov.escapeHTML(x),'"'))}if(t._data&&!ov.isEmptyObject(t._data))for(var O=0,k=Object.entries(t._data);O<k.length;O++){var C=a(k[O],2),T=C[0],P=C[1];if("index"===T)return;h+=" data-".concat(T,"='").concat("object"===i(P)?JSON.stringify(P):P,"'")}s.push("<tr",ov.sprintf(" %s",d.length?d.join(" "):void 0),ov.sprintf(' id="%s"',Array.isArray(t)?void 0:t._id),ov.sprintf(' class="%s"',l.classes||(Array.isArray(t)?void 0:t._class)),ov.sprintf(' style="%s"',Array.isArray(t)?void 0:t._style),' data-index="'.concat(e,'"'),ov.sprintf(' data-uniqueid="%s"',ov.getItemField(t,this.options.uniqueId,!1)),ov.sprintf(' data-has-detail-view="%s"',this.options.detailView&&ov.calculateObjectValue(null,this.options.detailFilter,[e,t])?"true":void 0),ov.sprintf("%s",h),">"),this.options.cardView&&s.push('<td colspan="'.concat(this.header.fields.length,'"><div class="card-views">'));var I="";return ov.hasDetailViewIcon(this.options)&&(I="<td>",ov.calculateObjectValue(null,this.options.detailFilter,[e,t])&&(I+='\n          <a class="detail-icon" href="#">\n          '.concat(ov.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailOpen),"\n          </a>\n        ")),I+="</td>"),I&&"right"!==this.options.detailViewAlign&&s.push(I),this.header.fields.forEach((function(i,n){var o=r.columns[n],l="",h=ov.getItemField(t,i,r.options.escape,o.escape),u="",d="",p={},f="",g=r.header.classes[n],v="",b="",m="",y="",w="",S="";if((!r.fromHtml&&!r.autoMergeCells||void 0!==h||o.checkbox||o.radio)&&o.visible&&(!r.options.cardView||o.cardVisible)){if(c.concat([r.header.styles[n]]).length&&(b+="".concat(c.concat([r.header.styles[n]]).join("; "))),t["_".concat(i,"_style")]&&(b+="".concat(t["_".concat(i,"_style")])),b&&(v=' style="'.concat(b,'"')),t["_".concat(i,"_id")]&&(f=ov.sprintf(' id="%s"',t["_".concat(i,"_id")])),t["_".concat(i,"_class")]&&(g=ov.sprintf(' class="%s"',t["_".concat(i,"_class")])),t["_".concat(i,"_rowspan")]&&(y=ov.sprintf(' rowspan="%s"',t["_".concat(i,"_rowspan")])),t["_".concat(i,"_colspan")]&&(w=ov.sprintf(' colspan="%s"',t["_".concat(i,"_colspan")])),t["_".concat(i,"_title")]&&(S=ov.sprintf(' title="%s"',t["_".concat(i,"_title")])),(p=ov.calculateObjectValue(r.header,r.header.cellStyles[n],[h,t,e,i],p)).classes&&(g=' class="'.concat(p.classes,'"')),p.css){for(var x=[],O=0,k=Object.entries(p.css);O<k.length;O++){var C=a(k[O],2),T=C[0],P=C[1];x.push("".concat(T,": ").concat(P))}v=' style="'.concat(x.concat(r.header.styles[n]).join("; "),'"')}if(u=ov.calculateObjectValue(o,r.header.formatters[n],[h,t,e,i],h),o.checkbox||o.radio||(u=null==u?r.options.undefinedText:u),o.searchable&&r.searchText&&r.options.searchHighlight&&!o.checkbox&&!o.radio){var I="",A=r.searchText.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");if(r.options.searchAccentNeutralise){var $=new RegExp("".concat(ov.normalizeAccent(A)),"gmi").exec(ov.normalizeAccent(u));$&&(A=u.substring($.index,$.index+A.length))}var R=new RegExp("(".concat(A,")"),"gim"),E="<mark>$1</mark>";if(u&&/<(?=.*? .*?\/ ?>|br|hr|input|!--|wbr)[a-z]+.*?>|<([a-z]+).*?<\/\1>/i.test(u)){var j=(new DOMParser).parseFromString(u.toString(),"text/html").documentElement.textContent,_=j.replace(R,E);j=j.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),I=u.replace(new RegExp("(>\\s*)(".concat(j,")(\\s*)"),"gm"),"$1".concat(_,"$3"))}else I=u.toString().replace(R,E);u=ov.calculateObjectValue(o,o.searchHighlightFormatter,[u,r.searchText],I)}if(t["_".concat(i,"_data")]&&!ov.isEmptyObject(t["_".concat(i,"_data")]))for(var F=0,N=Object.entries(t["_".concat(i,"_data")]);F<N.length;F++){var D=a(N[F],2),V=D[0],B=D[1];if("index"===V)return;m+=" data-".concat(V,'="').concat(B,'"')}if(o.checkbox||o.radio){d=o.checkbox?"checkbox":d,d=o.radio?"radio":d;var L=o.class||"",H=ov.isObject(u)&&u.hasOwnProperty("checked")?u.checked:(!0===u||h)&&!1!==u,M=!o.checkboxEnabled||u&&u.disabled;l=[r.options.cardView?'<div class="card-view '.concat(L,'">'):'<td class="bs-checkbox '.concat(L,'"').concat(g).concat(v,">"),'<label>\n            <input\n            data-index="'.concat(e,'"\n            name="').concat(r.options.selectItemName,'"\n            type="').concat(d,'"\n            ').concat(ov.sprintf('value="%s"',t[r.options.idField]),"\n            ").concat(ov.sprintf('checked="%s"',H?"checked":void 0),"\n            ").concat(ov.sprintf('disabled="%s"',M?"disabled":void 0)," />\n            <span></span>\n            </label>"),r.header.formatters[n]&&"string"==typeof u?u:"",r.options.cardView?"</div>":"</td>"].join(""),t[r.header.stateField]=!0===u||!!h||u&&u.checked}else if(r.options.cardView){var U=r.options.showHeader?'<span class="card-view-title '.concat(p.classes||"",'"').concat(v,">").concat(ov.getFieldTitle(r.columns,i),"</span>"):"";l='<div class="card-view">'.concat(U,'<span class="card-view-value ').concat(p.classes||"",'"').concat(v,">").concat(u,"</span></div>"),r.options.smartDisplay&&""===u&&(l='<div class="card-view"></div>')}else l="<td".concat(f).concat(g).concat(v).concat(m).concat(y).concat(w).concat(S,">").concat(u,"</td>");s.push(l)}})),I&&"right"===this.options.detailViewAlign&&s.push(I),this.options.cardView&&s.push("</div></td>"),s.push("</tr>"),s.join("")}}},{key:"initBody",value:function(e,i){var n=this,o=this.getData();this.trigger("pre-body",o),this.$body=this.$el.find(">tbody"),this.$body.length||(this.$body=t("<tbody></tbody>").appendTo(this.$el)),this.options.pagination&&"server"!==this.options.sidePagination||(this.pageFrom=1,this.pageTo=o.length);var r=[],a=t(document.createDocumentFragment()),s=!1,l=[];this.autoMergeCells=ov.checkAutoMergeCells(o.slice(this.pageFrom-1,this.pageTo));for(var c=this.pageFrom-1;c<this.pageTo;c++){var h=o[c],u=this.initRow(h,c,o,a);if(s=s||!!u,u&&"string"==typeof u){var d=this.options.uniqueId;if(d&&h.hasOwnProperty(d)){var p=h[d],f=this.$body.find(ov.sprintf('> tr[data-uniqueid="%s"][data-has-detail-view]',p)).next();f.is("tr.detail-view")&&(l.push(c),i&&p===i||(u+=f[0].outerHTML))}this.options.virtualScroll?r.push(u):a.append(u)}}s?this.options.virtualScroll?(this.virtualScroll&&this.virtualScroll.destroy(),this.virtualScroll=new uv({rows:r,fixedScroll:e,scrollEl:this.$tableBody[0],contentEl:this.$body[0],itemHeight:this.options.virtualScrollItemHeight,callback:function(t,e){n.fitHeader(),n.initBodyEvent(),n.trigger("virtual-scroll",t,e)}})):this.$body.html(a):this.$body.html('<tr class="no-records-found">'.concat(ov.sprintf('<td colspan="%s">%s</td>',this.getVisibleFields().length+ov.getDetailViewIndexOffset(this.options),this.options.formatNoMatches()),"</tr>")),l.forEach((function(t){n.expandRow(t)})),e||this.scrollTo(0),this.initBodyEvent(),this.initFooter(),this.resetView(),this.updateSelected(),"server"!==this.options.sidePagination&&(this.options.totalRows=o.length),this.trigger("post-body",o)}},{key:"initBodyEvent",value:function(){var e=this;this.$body.find("> tr[data-index] > td").off("click dblclick").on("click dblclick",(function(i){var n=t(i.currentTarget);if(!(n.find(".detail-icon").length||n.index()-ov.getDetailViewIndexOffset(e.options)<0)){var o=n.parent(),r=t(i.target).parents(".card-views").children(),a=t(i.target).parents(".card-view"),s=o.data("index"),l=e.data[s],c=e.options.cardView?r.index(a):n[0].cellIndex,h=e.getVisibleFields()[c-ov.getDetailViewIndexOffset(e.options)],u=e.columns[e.fieldsColumnsIndex[h]],d=ov.getItemField(l,h,e.options.escape,u.escape);if(e.trigger("click"===i.type?"click-cell":"dbl-click-cell",h,d,l,n),e.trigger("click"===i.type?"click-row":"dbl-click-row",l,o,h),"click"===i.type&&e.options.clickToSelect&&u.clickToSelect&&!ov.calculateObjectValue(e.options,e.options.ignoreClickToSelectOn,[i.target])){var p=o.find(ov.sprintf('[name="%s"]',e.options.selectItemName));p.length&&p[0].click()}"click"===i.type&&e.options.detailViewByClick&&e.toggleDetailView(s,e.header.detailFormatters[e.fieldsColumnsIndex[h]])}})).off("mousedown").on("mousedown",(function(t){e.multipleSelectRowCtrlKey=t.ctrlKey||t.metaKey,e.multipleSelectRowShiftKey=t.shiftKey})),this.$body.find("> tr[data-index] > td > .detail-icon").off("click").on("click",(function(i){return i.preventDefault(),e.toggleDetailView(t(i.currentTarget).parent().parent().data("index")),!1})),this.$selectItem=this.$body.find(ov.sprintf('[name="%s"]',this.options.selectItemName)),this.$selectItem.off("click").on("click",(function(i){i.stopImmediatePropagation();var n=t(i.currentTarget);e._toggleCheck(n.prop("checked"),n.data("index"))})),this.header.events.forEach((function(i,n){var o=i;if(o){if("string"==typeof o&&(o=ov.calculateObjectValue(null,o)),!o)throw new Error("Unknown event in the scope: ".concat(i));var r=e.header.fields[n],a=e.getVisibleFields().indexOf(r);if(-1!==a){a+=ov.getDetailViewIndexOffset(e.options);var s=function(i){if(!o.hasOwnProperty(i))return 1;var n=o[i];e.$body.find(">tr:not(.no-records-found)").each((function(o,s){var l=t(s),c=l.find(e.options.cardView?".card-views>.card-view":">td").eq(a),h=i.indexOf(" "),u=i.substring(0,h),d=i.substring(h+1);c.find(d).off(u).on(u,(function(t){var i=l.data("index"),o=e.data[i],a=o[r];n.apply(e,[t,a,o,i])}))}))};for(var l in o)s(l)}}}))}},{key:"initServer",value:function(e,i,n){var o=this,r={},a=this.header.fields.indexOf(this.options.sortName),s={searchText:this.searchText,sortName:this.options.sortName,sortOrder:this.options.sortOrder};if(this.header.sortNames[a]&&(s.sortName=this.header.sortNames[a]),this.options.pagination&&"server"===this.options.sidePagination&&(s.pageSize=this.options.pageSize===this.options.formatAllRows()?this.options.totalRows:this.options.pageSize,s.pageNumber=this.options.pageNumber),n||this.options.url||this.options.ajax){if("limit"===this.options.queryParamsType&&(s={search:s.searchText,sort:s.sortName,order:s.sortOrder},this.options.pagination&&"server"===this.options.sidePagination&&(s.offset=this.options.pageSize===this.options.formatAllRows()?0:this.options.pageSize*(this.options.pageNumber-1),s.limit=this.options.pageSize,0!==s.limit&&this.options.pageSize!==this.options.formatAllRows()||delete s.limit)),this.options.search&&"server"===this.options.sidePagination&&this.options.searchable&&this.columns.filter((function(t){return t.searchable})).length){s.searchable=[];var l,c=h(this.columns);try{for(c.s();!(l=c.n()).done;){var u=l.value;!u.checkbox&&u.searchable&&(this.options.visibleSearch&&u.visible||!this.options.visibleSearch)&&s.searchable.push(u.field)}}catch(t){c.e(t)}finally{c.f()}}if(ov.isEmptyObject(this.filterColumnsPartial)||(s.filter=JSON.stringify(this.filterColumnsPartial,null)),ov.extend(s,i||{}),!1!==(r=ov.calculateObjectValue(this.options,this.options.queryParams,[s],r))){e||this.showLoading();var d=ov.extend({},ov.calculateObjectValue(null,this.options.ajaxOptions),{type:this.options.method,url:n||this.options.url,data:"application/json"===this.options.contentType&&"post"===this.options.method?JSON.stringify(r):r,cache:this.options.cache,contentType:this.options.contentType,dataType:this.options.dataType,success:function(t,i,n){var r=ov.calculateObjectValue(o.options,o.options.responseHandler,[t,n],t);o.load(r),o.trigger("load-success",r,n&&n.status,n),e||o.hideLoading(),"server"===o.options.sidePagination&&o.options.pageNumber>1&&r[o.options.totalField]>0&&!r[o.options.dataField].length&&o.updatePagination()},error:function(t){if(t&&0===t.status&&o._xhrAbort)o._xhrAbort=!1;else{var i=[];"server"===o.options.sidePagination&&((i={})[o.options.totalField]=0,i[o.options.dataField]=[]),o.load(i),o.trigger("load-error",t&&t.status,t),e||o.hideLoading()}}});return this.options.ajax?ov.calculateObjectValue(this,this.options.ajax,[d],null):(this._xhr&&4!==this._xhr.readyState&&(this._xhrAbort=!0,this._xhr.abort()),this._xhr=t.ajax(d)),r}}}},{key:"initSearchText",value:function(){if(this.options.search&&(this.searchText="",""!==this.options.searchText)){var t=ov.getSearchInput(this);t.val(this.options.searchText),this.onSearch({currentTarget:t,firedByInitSearchText:!0})}}},{key:"getCaret",value:function(){var e=this;this.$header.find("th").each((function(i,n){t(n).find(".sortable").removeClass("desc asc").addClass(t(n).data("field")===e.options.sortName?e.options.sortOrder:"both")}))}},{key:"updateSelected",value:function(){var e=this.$selectItem.filter(":enabled").length&&this.$selectItem.filter(":enabled").length===this.$selectItem.filter(":enabled").filter(":checked").length;this.$selectAll.add(this.$selectAll_).prop("checked",e),this.$selectItem.each((function(e,i){t(i).closest("tr")[t(i).prop("checked")?"addClass":"removeClass"]("selected")}))}},{key:"updateRows",value:function(){var e=this;this.$selectItem.each((function(i,n){e.data[t(n).data("index")][e.header.stateField]=t(n).prop("checked")}))}},{key:"resetRows",value:function(){var t,e=h(this.data);try{for(e.s();!(t=e.n()).done;){var i=t.value;this.$selectAll.prop("checked",!1),this.$selectItem.prop("checked",!1),this.header.stateField&&(i[this.header.stateField]=!1)}}catch(t){e.e(t)}finally{e.f()}this.initHiddenRows()}},{key:"trigger",value:function(i){for(var n,o,r="".concat(i,".bs.table"),a=arguments.length,s=new Array(a>1?a-1:0),l=1;l<a;l++)s[l-1]=arguments[l];(n=this.options)[e.EVENTS[r]].apply(n,[].concat(s,[this])),this.$el.trigger(t.Event(r,{sender:this}),s),(o=this.options).onAll.apply(o,[r].concat([].concat(s,[this]))),this.$el.trigger(t.Event("all.bs.table",{sender:this}),[r,s])}},{key:"resetHeader",value:function(){var t=this;clearTimeout(this.timeoutId_),this.timeoutId_=setTimeout((function(){return t.fitHeader()}),this.$el.is(":hidden")?100:0)}},{key:"fitHeader",value:function(){var e=this;if(this.$el.is(":hidden"))this.timeoutId_=setTimeout((function(){return e.fitHeader()}),100);else{var i=this.$tableBody.get(0),n=this.hasScrollBar&&i.scrollHeight>i.clientHeight+this.$header.outerHeight()?ov.getScrollBarWidth():0;this.$el.css("margin-top",-this.$header.outerHeight());var o=this.$tableHeader.find(":focus");if(o.length>0){var r=o.parents("th");if(r.length>0){var a=r.attr("data-field");if(void 0!==a){var s=this.$header.find("[data-field='".concat(a,"']"));s.length>0&&s.find(":input").addClass("focus-temp")}}}this.$header_=this.$header.clone(!0,!0),this.$selectAll_=this.$header_.find('[name="btSelectAll"]'),this.$tableHeader.css("margin-right",n).find("table").css("width",this.$el.outerWidth()).html("").attr("class",this.$el.attr("class")).append(this.$header_),this.$tableLoading.css("width",this.$el.outerWidth());var l=t(".focus-temp:visible:eq(0)");l.length>0&&(l.focus(),this.$header.find(".focus-temp").removeClass("focus-temp")),this.$header.find("th[data-field]").each((function(i,n){e.$header_.find(ov.sprintf('th[data-field="%s"]',t(n).data("field"))).data(t(n).data())}));for(var c=this.getVisibleFields(),h=this.$header_.find("th"),u=this.$body.find(">tr:not(.no-records-found,.virtual-scroll-top)").eq(0);u.length&&u.find('>td[colspan]:not([colspan="1"])').length;)u=u.next();var d=u.find("> *").length;u.find("> *").each((function(i,n){var o=t(n);if(ov.hasDetailViewIcon(e.options)&&(0===i&&"right"!==e.options.detailViewAlign||i===d-1&&"right"===e.options.detailViewAlign)){var r=h.filter(".detail"),a=r.innerWidth()-r.find(".fht-cell").width();r.find(".fht-cell").width(o.innerWidth()-a)}else{var s=i-ov.getDetailViewIndexOffset(e.options),l=e.$header_.find(ov.sprintf('th[data-field="%s"]',c[s]));l.length>1&&(l=t(h[o[0].cellIndex]));var u=l.innerWidth()-l.find(".fht-cell").width();l.find(".fht-cell").width(o.innerWidth()-u)}})),this.horizontalScroll(),this.trigger("post-header")}}},{key:"initFooter",value:function(){if(this.options.showFooter&&!this.options.cardView){var t=this.getData(),e=[],i="";ov.hasDetailViewIcon(this.options)&&(i='<th class="detail"><div class="th-inner"></div><div class="fht-cell"></div></th>'),i&&"right"!==this.options.detailViewAlign&&e.push(i);var n,o=h(this.columns);try{for(o.s();!(n=o.n()).done;){var r,s,l=n.value,c=[],u={},d=ov.sprintf(' class="%s"',l.class);if(!(!l.visible||this.footerData&&this.footerData.length>0&&!(l.field in this.footerData[0]))){if(this.options.cardView&&!l.cardVisible)return;if(r=ov.sprintf("text-align: %s; ",l.falign?l.falign:l.align),s=ov.sprintf("vertical-align: %s; ",l.valign),(u=ov.calculateObjectValue(null,l.footerStyle||this.options.footerStyle,[l]))&&u.css)for(var p=0,f=Object.entries(u.css);p<f.length;p++){var g=a(f[p],2),v=g[0],b=g[1];c.push("".concat(v,": ").concat(b))}u&&u.classes&&(d=ov.sprintf(' class="%s"',l.class?[l.class,u.classes].join(" "):u.classes)),e.push("<th",d,ov.sprintf(' style="%s"',r+s+c.concat().join("; ")||void 0));var m=0;this.footerData&&this.footerData.length>0&&(m=this.footerData[0]["_".concat(l.field,"_colspan")]||0),m&&e.push(' colspan="'.concat(m,'" ')),e.push(">"),e.push('<div class="th-inner">');var y="";this.footerData&&this.footerData.length>0&&(y=this.footerData[0][l.field]||""),e.push(ov.calculateObjectValue(l,l.footerFormatter,[t,y],y)),e.push("</div>"),e.push('<div class="fht-cell"></div>'),e.push("</div>"),e.push("</th>")}}}catch(t){o.e(t)}finally{o.f()}i&&"right"===this.options.detailViewAlign&&e.push(i),this.options.height||this.$tableFooter.length||(this.$el.append("<tfoot><tr></tr></tfoot>"),this.$tableFooter=this.$el.find("tfoot")),this.$tableFooter.find("tr").length||this.$tableFooter.html("<table><thead><tr></tr></thead></table>"),this.$tableFooter.find("tr").html(e.join("")),this.trigger("post-footer",this.$tableFooter)}}},{key:"fitFooter",value:function(){var e=this;if(this.$el.is(":hidden"))setTimeout((function(){return e.fitFooter()}),100);else{var i=this.$tableBody.get(0),n=this.hasScrollBar&&i.scrollHeight>i.clientHeight+this.$header.outerHeight()?ov.getScrollBarWidth():0;this.$tableFooter.css("margin-right",n).find("table").css("width",this.$el.outerWidth()).attr("class",this.$el.attr("class"));var o=this.$tableFooter.find("th"),r=this.$body.find(">tr:first-child:not(.no-records-found)");for(o.find(".fht-cell").width("auto");r.length&&r.find('>td[colspan]:not([colspan="1"])').length;)r=r.next();var a=r.find("> *").length;r.find("> *").each((function(i,n){var r=t(n);if(ov.hasDetailViewIcon(e.options)&&(0===i&&"left"===e.options.detailViewAlign||i===a-1&&"right"===e.options.detailViewAlign)){var s=o.filter(".detail"),l=s.innerWidth()-s.find(".fht-cell").width();s.find(".fht-cell").width(r.innerWidth()-l)}else{var c=o.eq(i),h=c.innerWidth()-c.find(".fht-cell").width();c.find(".fht-cell").width(r.innerWidth()-h)}})),this.horizontalScroll()}}},{key:"horizontalScroll",value:function(){var t=this;this.$tableBody.off("scroll").on("scroll",(function(){var e=t.$tableBody.scrollLeft();t.options.showHeader&&t.options.height&&t.$tableHeader.scrollLeft(e),t.options.showFooter&&!t.options.cardView&&t.$tableFooter.scrollLeft(e),t.trigger("scroll-body",t.$tableBody)}))}},{key:"getVisibleFields",value:function(){var t,e=[],i=h(this.header.fields);try{for(i.s();!(t=i.n()).done;){var n=t.value,o=this.columns[this.fieldsColumnsIndex[n]];o&&o.visible&&(!this.options.cardView||o.cardVisible)&&e.push(n)}}catch(t){i.e(t)}finally{i.f()}return e}},{key:"initHiddenRows",value:function(){this.hiddenRows=[]}},{key:"getOptions",value:function(){var t=ov.extend({},this.options);return delete t.data,ov.extend(!0,{},t)}},{key:"refreshOptions",value:function(t){ov.compareObjects(this.options,t,!0)||(this.options=ov.extend(this.options,t),this.trigger("refresh-options",this.options),this.destroy(),this.init())}},{key:"getData",value:function(t){var e=this,i=this.options.data;if(!(this.searchText||this.options.customSearch||void 0!==this.options.sortName||this.enableCustomSort)&&ov.isEmptyObject(this.filterColumns)&&"function"!=typeof this.options.filterOptions.filterAlgorithm&&ov.isEmptyObject(this.filterColumnsPartial)||t&&t.unfiltered||(i=this.data),t&&!t.includeHiddenRows){var n=this.getHiddenRows();i=i.filter((function(t){return-1===ov.findIndex(n,t)}))}return t&&t.useCurrentPage&&(i=i.slice(this.pageFrom-1,this.pageTo)),t&&t.formatted&&i.forEach((function(t){for(var i=0,n=Object.entries(t);i<n.length;i++){var o=a(n[i],2),r=o[0],s=o[1],l=e.columns[e.fieldsColumnsIndex[r]];if(!l)return;t[r]=ov.calculateObjectValue(l,e.header.formatters[l.fieldIndex],[s,t,t.index,l.field],s)}})),i}},{key:"getSelections",value:function(){var t=this;return(this.options.maintainMetaData?this.options.data:this.data).filter((function(e){return!0===e[t.header.stateField]}))}},{key:"load",value:function(t){var e,i=t;this.options.pagination&&"server"===this.options.sidePagination&&(this.options.totalRows=i[this.options.totalField],this.options.totalNotFiltered=i[this.options.totalNotFilteredField],this.footerData=i[this.options.footerField]?[i[this.options.footerField]]:void 0),e=this.options.fixedScroll||i.fixedScroll,i=Array.isArray(i)?i:i[this.options.dataField],this.initData(i),this.initSearch(),this.initPagination(),this.initBody(e)}},{key:"append",value:function(t){this.initData(t,"append"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"prepend",value:function(t){this.initData(t,"prepend"),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"remove",value:function(t){for(var e=0,i=this.options.data.length-1;i>=0;i--){var n=this.options.data[i],o=ov.getItemField(n,t.field,this.options.escape,n.escape);void 0===o&&"$index"!==t.field||(!n.hasOwnProperty(t.field)&&"$index"===t.field&&t.values.includes(i)||t.values.includes(o))&&(e++,this.options.data.splice(i,1))}e&&("server"===this.options.sidePagination&&(this.options.totalRows-=e,this.data=s(this.options.data)),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))}},{key:"removeAll",value:function(){this.options.data.length>0&&(this.options.data.splice(0,this.options.data.length),this.initSearch(),this.initPagination(),this.initBody(!0))}},{key:"insertRow",value:function(t){t.hasOwnProperty("index")&&t.hasOwnProperty("row")&&(this.options.data.splice(t.index,0,t.row),this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0))}},{key:"updateRow",value:function(t){var e,i=h(Array.isArray(t)?t:[t]);try{for(i.s();!(e=i.n()).done;){var n=e.value;n.hasOwnProperty("index")&&n.hasOwnProperty("row")&&(n.hasOwnProperty("replace")&&n.replace?this.options.data[n.index]=n.row:ov.extend(this.options.data[n.index],n.row))}}catch(t){i.e(t)}finally{i.f()}this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0)}},{key:"getRowByUniqueId",value:function(t){var e,i,n=this.options.uniqueId,o=t,r=null;for(e=this.options.data.length-1;e>=0;e--){i=this.options.data[e];var a=ov.getItemField(i,n,this.options.escape,i.escape);if(void 0!==a&&("string"==typeof a?o=o.toString():"number"==typeof a&&(Number(a)===a&&a%1==0?o=parseInt(o,10):a===Number(a)&&0!==a&&(o=parseFloat(o))),a===o)){r=i;break}}return r}},{key:"updateByUniqueId",value:function(t){var e,i=null,n=h(Array.isArray(t)?t:[t]);try{for(n.s();!(e=n.n()).done;){var o=e.value;if(o.hasOwnProperty("id")&&o.hasOwnProperty("row")){var r=this.options.data.indexOf(this.getRowByUniqueId(o.id));-1!==r&&(o.hasOwnProperty("replace")&&o.replace?this.options.data[r]=o.row:ov.extend(this.options.data[r],o.row),i=o.id)}}}catch(t){n.e(t)}finally{n.f()}this.initSearch(),this.initPagination(),this.initSort(),this.initBody(!0,i)}},{key:"removeByUniqueId",value:function(t){var e=this.options.data.length,i=this.getRowByUniqueId(t);i&&this.options.data.splice(this.options.data.indexOf(i),1),e!==this.options.data.length&&("server"===this.options.sidePagination&&(this.options.totalRows-=1,this.data=s(this.options.data)),this.initSearch(),this.initPagination(),this.initBody(!0))}},{key:"_updateCellOnly",value:function(e,i){var n=this.initRow(this.options.data[i],i),o=this.getVisibleFields().indexOf(e);-1!==o&&(o+=ov.getDetailViewIndexOffset(this.options),this.$body.find(">tr[data-index=".concat(i,"]")).find(">td:eq(".concat(o,")")).replaceWith(t(n).find(">td:eq(".concat(o,")"))),this.initBodyEvent(),this.initFooter(),this.resetView(),this.updateSelected())}},{key:"updateCell",value:function(t){t.hasOwnProperty("index")&&t.hasOwnProperty("field")&&t.hasOwnProperty("value")&&(this.options.data[t.index][t.field]=t.value,!1!==t.reinit?(this.initSort(),this.initBody(!0)):this._updateCellOnly(t.field,t.index))}},{key:"updateCellByUniqueId",value:function(t){var e=this;(Array.isArray(t)?t:[t]).forEach((function(t){var i=t.id,n=t.field,o=t.value,r=e.options.data.indexOf(e.getRowByUniqueId(i));-1!==r&&(e.options.data[r][n]=o)})),!1!==t.reinit?(this.initSort(),this.initBody(!0)):this._updateCellOnly(t.field,this.options.data.indexOf(this.getRowByUniqueId(t.id)))}},{key:"showRow",value:function(t){this._toggleRow(t,!0)}},{key:"hideRow",value:function(t){this._toggleRow(t,!1)}},{key:"_toggleRow",value:function(t,e){var i;if(t.hasOwnProperty("index")?i=this.getData()[t.index]:t.hasOwnProperty("uniqueId")&&(i=this.getRowByUniqueId(t.uniqueId)),i){var n=ov.findIndex(this.hiddenRows,i);e||-1!==n?e&&n>-1&&this.hiddenRows.splice(n,1):this.hiddenRows.push(i),this.initBody(!0),this.initPagination()}}},{key:"getHiddenRows",value:function(t){if(t)return this.initHiddenRows(),this.initBody(!0),void this.initPagination();var e,i=[],n=h(this.getData());try{for(n.s();!(e=n.n()).done;){var o=e.value;this.hiddenRows.includes(o)&&i.push(o)}}catch(t){n.e(t)}finally{n.f()}return this.hiddenRows=i,i}},{key:"showColumn",value:function(t){var e=this;(Array.isArray(t)?t:[t]).forEach((function(t){e._toggleColumn(e.fieldsColumnsIndex[t],!0,!0)}))}},{key:"hideColumn",value:function(t){var e=this;(Array.isArray(t)?t:[t]).forEach((function(t){e._toggleColumn(e.fieldsColumnsIndex[t],!1,!0)}))}},{key:"_toggleColumn",value:function(t,e,i){if(-1!==t&&this.columns[t].visible!==e&&(this.columns[t].visible=e,this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns)){var n=this.$toolbar.find('.keep-open input:not(".toggle-all")').prop("disabled",!1);i&&n.filter(ov.sprintf('[value="%s"]',t)).prop("checked",e),n.filter(":checked").length<=this.options.minimumCountColumns&&n.filter(":checked").prop("disabled",!0)}}},{key:"getVisibleColumns",value:function(){var t=this;return this.columns.filter((function(e){return e.visible&&!t.isSelectionColumn(e)}))}},{key:"getHiddenColumns",value:function(){return this.columns.filter((function(t){return!t.visible}))}},{key:"isSelectionColumn",value:function(t){return t.radio||t.checkbox}},{key:"showAllColumns",value:function(){this._toggleAllColumns(!0)}},{key:"hideAllColumns",value:function(){this._toggleAllColumns(!1)}},{key:"_toggleAllColumns",value:function(e){var i,n=this,o=h(this.columns.slice().reverse());try{for(o.s();!(i=o.n()).done;){var r=i.value;if(r.switchable){if(!e&&this.options.showColumns&&this.getVisibleColumns().filter((function(t){return t.switchable})).length===this.options.minimumCountColumns)continue;r.visible=e}}}catch(t){o.e(t)}finally{o.f()}if(this.initHeader(),this.initSearch(),this.initPagination(),this.initBody(),this.options.showColumns){var a=this.$toolbar.find('.keep-open input[type="checkbox"]:not(".toggle-all")').prop("disabled",!1);e?a.prop("checked",e):a.get().reverse().forEach((function(i){a.filter(":checked").length>n.options.minimumCountColumns&&t(i).prop("checked",e)})),a.filter(":checked").length<=this.options.minimumCountColumns&&a.filter(":checked").prop("disabled",!0)}}},{key:"mergeCells",value:function(t){var e,i,n=t.index,o=this.getVisibleFields().indexOf(t.field),r=t.rowspan||1,a=t.colspan||1,s=this.$body.find(">tr[data-index]");o+=ov.getDetailViewIndexOffset(this.options);var l=s.eq(n).find(">td").eq(o);if(!(n<0||o<0||n>=this.data.length)){for(e=n;e<n+r;e++)for(i=o;i<o+a;i++)s.eq(e).find(">td").eq(i).hide();l.attr("rowspan",r).attr("colspan",a).show()}}},{key:"checkAll",value:function(){this._toggleCheckAll(!0)}},{key:"uncheckAll",value:function(){this._toggleCheckAll(!1)}},{key:"_toggleCheckAll",value:function(t){var e=this.getSelections();this.$selectAll.add(this.$selectAll_).prop("checked",t),this.$selectItem.filter(":enabled").prop("checked",t),this.updateRows(),this.updateSelected();var i=this.getSelections();t?this.trigger("check-all",i,e):this.trigger("uncheck-all",i,e)}},{key:"checkInvert",value:function(){var e=this.$selectItem.filter(":enabled"),i=e.filter(":checked");e.each((function(e,i){t(i).prop("checked",!t(i).prop("checked"))})),this.updateRows(),this.updateSelected(),this.trigger("uncheck-some",i),i=this.getSelections(),this.trigger("check-some",i)}},{key:"check",value:function(t){this._toggleCheck(!0,t)}},{key:"uncheck",value:function(t){this._toggleCheck(!1,t)}},{key:"_toggleCheck",value:function(t,e){var i=this.$selectItem.filter('[data-index="'.concat(e,'"]')),n=this.data[e];if(i.is(":radio")||this.options.singleSelect||this.options.multipleSelectRow&&!this.multipleSelectRowCtrlKey&&!this.multipleSelectRowShiftKey){var o,r=h(this.options.data);try{for(r.s();!(o=r.n()).done;){o.value[this.header.stateField]=!1}}catch(t){r.e(t)}finally{r.f()}this.$selectItem.filter(":checked").not(i).prop("checked",!1)}if(n[this.header.stateField]=t,this.options.multipleSelectRow){if(this.multipleSelectRowShiftKey&&this.multipleSelectRowLastSelectedIndex>=0)for(var s=a(this.multipleSelectRowLastSelectedIndex<e?[this.multipleSelectRowLastSelectedIndex,e]:[e,this.multipleSelectRowLastSelectedIndex],2),l=s[0],c=s[1],u=l+1;u<c;u++)this.data[u][this.header.stateField]=!0,this.$selectItem.filter('[data-index="'.concat(u,'"]')).prop("checked",!0);this.multipleSelectRowCtrlKey=!1,this.multipleSelectRowShiftKey=!1,this.multipleSelectRowLastSelectedIndex=t?e:-1}i.prop("checked",t),this.updateSelected(),this.trigger(t?"check":"uncheck",this.data[e],i)}},{key:"checkBy",value:function(t){this._toggleCheckBy(!0,t)}},{key:"uncheckBy",value:function(t){this._toggleCheckBy(!1,t)}},{key:"_toggleCheckBy",value:function(t,e){var i=this;if(e.hasOwnProperty("field")&&e.hasOwnProperty("values")){var n=[];this.data.forEach((function(o,r){if(!o.hasOwnProperty(e.field))return!1;if(e.values.includes(o[e.field])){var a=i.$selectItem.filter(":enabled").filter(ov.sprintf('[data-index="%s"]',r)),s=!!e.hasOwnProperty("onlyCurrentPage")&&e.onlyCurrentPage;if(!(a=t?a.not(":checked"):a.filter(":checked")).length&&s)return;a.prop("checked",t),o[i.header.stateField]=t,n.push(o),i.trigger(t?"check":"uncheck",o,a)}})),this.updateSelected(),this.trigger(t?"check-some":"uncheck-some",n)}}},{key:"refresh",value:function(t){t&&t.url&&(this.options.url=t.url),t&&t.pageNumber&&(this.options.pageNumber=t.pageNumber),t&&t.pageSize&&(this.options.pageSize=t.pageSize),this.trigger("refresh",this.initServer(t&&t.silent,t&&t.query,t&&t.url))}},{key:"destroy",value:function(){this.$el.insertBefore(this.$container),t(this.options.toolbar).insertBefore(this.$el),this.$container.next().remove(),this.$container.remove(),this.$el.html(this.$el_.html()).css("margin-top","0").attr("class",this.$el_.attr("class")||"");var e=ov.getEventName("resize.bootstrap-table",this.$el.attr("id"));t(window).off(e)}},{key:"resetView",value:function(t){var e=0;if(t&&t.height&&(this.options.height=t.height),this.$tableContainer.toggleClass("has-card-view",this.options.cardView),this.options.height){var i=this.$tableBody.get(0);this.hasScrollBar=i.scrollWidth>i.clientWidth}if(!this.options.cardView&&this.options.showHeader&&this.options.height?(this.$tableHeader.show(),this.resetHeader(),e+=this.$header.outerHeight(!0)+1):(this.$tableHeader.hide(),this.trigger("post-header")),!this.options.cardView&&this.options.showFooter&&(this.$tableFooter.show(),this.fitFooter(),this.options.height&&(e+=this.$tableFooter.outerHeight(!0))),this.$container.hasClass("fullscreen"))this.$tableContainer.css("height",""),this.$tableContainer.css("width","");else if(this.options.height){this.$tableBorder&&(this.$tableBorder.css("width",""),this.$tableBorder.css("height",""));var n=this.$toolbar.outerHeight(!0),o=this.$pagination.outerHeight(!0),r=this.options.height-n-o,a=this.$tableBody.find(">table"),s=a.outerHeight();if(this.$tableContainer.css("height","".concat(r,"px")),this.$tableBorder&&a.is(":visible")){var l=r-s-2;this.hasScrollBar&&(l-=ov.getScrollBarWidth()),this.$tableBorder.css("width","".concat(a.outerWidth(),"px")),this.$tableBorder.css("height","".concat(l,"px"))}}this.options.cardView?(this.$el.css("margin-top","0"),this.$tableContainer.css("padding-bottom","0"),this.$tableFooter.hide()):(this.getCaret(),this.$tableContainer.css("padding-bottom","".concat(e,"px"))),this.trigger("reset-view")}},{key:"showLoading",value:function(){this.$tableLoading.toggleClass("open",!0);var t=this.options.loadingFontSize;"auto"===this.options.loadingFontSize&&(t=.04*this.$tableLoading.width(),t=Math.max(12,t),t=Math.min(32,t),t="".concat(t,"px")),this.$tableLoading.find(".loading-text").css("font-size",t)}},{key:"hideLoading",value:function(){this.$tableLoading.toggleClass("open",!1)}},{key:"togglePagination",value:function(){this.options.pagination=!this.options.pagination;var t=this.options.showButtonIcons?this.options.pagination?this.options.icons.paginationSwitchDown:this.options.icons.paginationSwitchUp:"",e=this.options.showButtonText?this.options.pagination?this.options.formatPaginationSwitchUp():this.options.formatPaginationSwitchDown():"";this.$toolbar.find('button[name="paginationSwitch"]').html("".concat(ov.sprintf(this.constants.html.icon,this.options.iconsPrefix,t)," ").concat(e)),this.updatePagination(),this.trigger("toggle-pagination",this.options.pagination)}},{key:"toggleFullscreen",value:function(){this.$el.closest(".bootstrap-table").toggleClass("fullscreen"),this.resetView()}},{key:"toggleView",value:function(){this.options.cardView=!this.options.cardView,this.initHeader();var t=this.options.showButtonIcons?this.options.cardView?this.options.icons.toggleOn:this.options.icons.toggleOff:"",e=this.options.showButtonText?this.options.cardView?this.options.formatToggleOff():this.options.formatToggleOn():"";this.$toolbar.find('button[name="toggle"]').html("".concat(ov.sprintf(this.constants.html.icon,this.options.iconsPrefix,t)," ").concat(e)).attr("aria-label",e).attr("title",e),this.initBody(),this.trigger("toggle",this.options.cardView)}},{key:"resetSearch",value:function(t){var e=ov.getSearchInput(this),i=t||"";e.val(i),this.searchText=i,this.onSearch({currentTarget:e},!1)}},{key:"filterBy",value:function(t,e){this.filterOptions=ov.isEmptyObject(e)?this.options.filterOptions:ov.extend(this.options.filterOptions,e),this.filterColumns=ov.isEmptyObject(t)?{}:t,this.options.pageNumber=1,this.initSearch(),this.updatePagination()}},{key:"scrollTo",value:function(e){var n={unit:"px",value:0};"object"===i(e)?n=Object.assign(n,e):"string"==typeof e&&"bottom"===e?n.value=this.$tableBody[0].scrollHeight:"string"!=typeof e&&"number"!=typeof e||(n.value=e);var o=n.value;"rows"===n.unit&&(o=0,this.$body.find("> tr:lt(".concat(n.value,")")).each((function(e,i){o+=t(i).outerHeight(!0)}))),this.$tableBody.scrollTop(o)}},{key:"getScrollPosition",value:function(){return this.$tableBody.scrollTop()}},{key:"selectPage",value:function(t){t>0&&t<=this.options.totalPages&&(this.options.pageNumber=t,this.updatePagination())}},{key:"prevPage",value:function(){this.options.pageNumber>1&&(this.options.pageNumber--,this.updatePagination())}},{key:"nextPage",value:function(){this.options.pageNumber<this.options.totalPages&&(this.options.pageNumber++,this.updatePagination())}},{key:"toggleDetailView",value:function(t,e){this.$body.find(ov.sprintf('> tr[data-index="%s"]',t)).next().is("tr.detail-view")?this.collapseRow(t):this.expandRow(t,e),this.resetView()}},{key:"expandRow",value:function(t,e){var i=this.data[t],n=this.$body.find(ov.sprintf('> tr[data-index="%s"][data-has-detail-view]',t));if(this.options.detailViewIcon&&n.find("a.detail-icon").html(ov.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailClose)),!n.next().is("tr.detail-view")){n.after(ov.sprintf('<tr class="detail-view"><td colspan="%s"></td></tr>',n.children("td").length));var o=n.next().find("td"),r=e||this.options.detailFormatter,a=ov.calculateObjectValue(this.options,r,[t,i,o],"");1===o.length&&o.append(a),this.trigger("expand-row",t,i,o)}}},{key:"expandRowByUniqueId",value:function(t){var e=this.getRowByUniqueId(t);e&&this.expandRow(this.data.indexOf(e))}},{key:"collapseRow",value:function(t){var e=this.data[t],i=this.$body.find(ov.sprintf('> tr[data-index="%s"][data-has-detail-view]',t));i.next().is("tr.detail-view")&&(this.options.detailViewIcon&&i.find("a.detail-icon").html(ov.sprintf(this.constants.html.icon,this.options.iconsPrefix,this.options.icons.detailOpen)),this.trigger("collapse-row",t,e,i.next()),i.next().remove())}},{key:"collapseRowByUniqueId",value:function(t){var e=this.getRowByUniqueId(t);e&&this.collapseRow(this.data.indexOf(e))}},{key:"expandAllRows",value:function(){for(var e=this.$body.find("> tr[data-index][data-has-detail-view]"),i=0;i<e.length;i++)this.expandRow(t(e[i]).data("index"))}},{key:"collapseAllRows",value:function(){for(var e=this.$body.find("> tr[data-index][data-has-detail-view]"),i=0;i<e.length;i++)this.collapseRow(t(e[i]).data("index"))}},{key:"updateColumnTitle",value:function(e){e.hasOwnProperty("field")&&e.hasOwnProperty("title")&&(this.columns[this.fieldsColumnsIndex[e.field]].title=this.options.escape&&this.options.escapeTitle?ov.escapeHTML(e.title):e.title,this.columns[this.fieldsColumnsIndex[e.field]].visible&&(this.$header.find("th[data-field]").each((function(i,n){if(t(n).data("field")===e.field)return t(t(n).find(".th-inner")[0]).text(e.title),!1})),this.resetView()))}},{key:"updateFormatText",value:function(t,e){/^format/.test(t)&&this.options[t]&&("string"==typeof e?this.options[t]=function(){return e}:"function"==typeof e&&(this.options[t]=e),this.initToolbar(),this.initPagination(),this.initBody())}}]),e}();return dv.VERSION=hv.VERSION,dv.DEFAULTS=hv.DEFAULTS,dv.LOCALES=hv.LOCALES,dv.COLUMN_DEFAULTS=hv.COLUMN_DEFAULTS,dv.METHODS=hv.METHODS,dv.EVENTS=hv.EVENTS,t.BootstrapTable=dv,t.fn.bootstrapTable=function(e){for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];var a;return this.each((function(n,r){var s=t(r).data("bootstrap.table");if("string"==typeof e){var l;if(!hv.METHODS.includes(e))throw new Error("Unknown method: ".concat(e));if(!s)return;return a=(l=s)[e].apply(l,o),void("destroy"===e&&t(r).removeData("bootstrap.table"))}if(s)console.warn("You cannot initialize the table more than once!");else{var c=ov.extend(!0,{},dv.DEFAULTS,t(r).data(),"object"===i(e)&&e);s=new t.BootstrapTable(r,c),t(r).data("bootstrap.table",s),s.init()}})),void 0===a?this:a},t.fn.bootstrapTable.Constructor=dv,t.fn.bootstrapTable.theme=hv.THEME,t.fn.bootstrapTable.VERSION=hv.VERSION,t.fn.bootstrapTable.defaults=dv.DEFAULTS,t.fn.bootstrapTable.columnDefaults=dv.COLUMN_DEFAULTS,t.fn.bootstrapTable.events=dv.EVENTS,t.fn.bootstrapTable.locales=dv.LOCALES,t.fn.bootstrapTable.methods=dv.METHODS,t.fn.bootstrapTable.utils=ov,t((function(){t('[data-toggle="table"]').bootstrapTable()})),dv}));
