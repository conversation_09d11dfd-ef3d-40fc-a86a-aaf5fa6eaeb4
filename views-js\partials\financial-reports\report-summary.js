$("input[name='nonCurrentLiabilities[hasLiabilities]']").on('change', function () {
    const val = $(this).val();

    if (val === "YES") {
        $("#hasNonCurrentLiabilitiesRow").show(200);
    } else {
        $("#hasNonCurrentLiabilitiesRow").hide(200);
    }
});

$("input[name='nonCurrentLiabilities[includeLoans]']").on('change', function () {
    const val = $(this).val();

    if (val === "YES") {
        $("#totalNonCurrentLoansRow").show(200);
    } else {
        $("#totalNonCurrentLoansRow").hide(200);
    }
});

$('.returnLabel').click(function () {
    changeStep(parseInt($(this).attr('data-step')), 'RETURN')
})

$("input[name='nonCurrentLiabilities[hasOtherLiabilities]']").on('change', function () {
    const val = $(this).val();

    if (val === "YES") {
        $("#totalOtherNonCurrentLiabilitiesRow").show(200);
    } else {
        $("#totalOtherNonCurrentLiabilitiesRow").hide(200);
    }
});

$("input[name='finReportAccurate']").on('change', function () {
    const val = $(this).val();

    if (val === "NO") {
        $('.nextBtn').addClass('hidden')
        $("#totalNonCurrentLoansNoRow").show(200);
        $("#totalNonCurrentLoansYesRow").hide(200);
    } else {
        $('.nextBtn').removeClass('hidden')
        $("#totalNonCurrentLoansNoRow").hide(200);
        $("#totalNonCurrentLoansYesRow").show(200);
    }
});

