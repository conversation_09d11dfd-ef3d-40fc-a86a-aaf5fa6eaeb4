<div id="employeesContent">
    <!-- 4D.a -->
    <div class="row">
        <div class="col-md-8">
            <div class="form-group mb-3">
                <label class="mb-2" for="TotalEmployees">
                    Total number of employees of the corporate and legal entity:
    
                </label>
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group mb-3">
                <input id="TotalEmployees" name="TotalEmployees" class="form-control" data-toggle="touchspin" data-min="0"
                    data-max="1000000000" type="text" data-step="0.5"  data-decimals="1" data-firstclickvalueifempty="0"
                    placeholder="0.0"
                    value="{{data.full_total_employees}}">
            </div>
        </div>
    </div>

    <!-- 4D.b -->
    <div class="row">
        <div class="col-md-8">
            <div class="form-group mb-3">
                <label class="mb-2" for="TotalEmployeesEngaged" title="Refer to rule 12 to compute the number of employees"
                    data-placement="left"
                    data-toggle="tooltip">
                    Total number of employees engaged in the relevant activity:
    
                </label>
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group mb-3">
                <input id="TotalEmployeesEngaged" name="TotalEmployeesEngaged" class="form-control" data-toggle="touchspin"
                    data-min="0" data-max="1000000000" type="text" data-step="0.5"  data-decimals="1" placeholder="0.0"
                    data-firstclickvalueifempty="0"
                    value="{{data.total_employees_engaged}}">
            </div>
        </div>
    </div>
    
    <!-- 4D.c -->
    <div class="row">
        <div class="col-md-8">
            <div class="form-group mb-3">
                <label class="mb-2" for="TotalSuitableFTEInBVI"
                    title="Refer to rule 12 to compute the number of employees"
                    data-placement="left"
                    data-toggle="tooltip"
                >
                    Total number of employees engaged in the relevant activity physically present in the Virgin
                    Islands:
                </label>
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group mb-3">
                <input id="TotalSuitableFTEInBVI" name="TotalSuitableFTEInBVI" class="form-control" data-toggle="touchspin"
                    data-max="1000000000" type="text" data-step="0.5" data-decimals="1" data-min="0" placeholder="0.0"
                    data-firstclickvalueifempty="0"
                    value="{{data.total_employees}}">
            </div>
        </div>
    </div>
    
    <!-- 4D.d -->
    <div id="employeesDetailsTableRows"
     {{#ifCond data.total_employees ">" 0}} class="d-block" {{else}} class="hide-element" {{/ifCond}}
    >
        <div class="row">
            <div class="col-md-8">
                <div class="form-group mb-3">
                    <label class="mb-2">
                        Provide details on qualifications of the employees:
        
                    </label>
                </div>
            </div>
            <div class="col-md-4">
                <button type="button" class="btn solid royal-blue w-100" data-toggle="modal" data-target="#employeeModal"
                    data-activity-type={{activityType}} data-id="{{entry._id}}">
                    Add Employee
                </button>
            </div>
        </div>
        <div class="table-responsive ">
            <table class="table table-striped mb-0">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Qualification</th>
                        <th>Years of relevant expecience</th>
                        <th class="header-10-percent">
                        </th>
                    </tr>
                </thead>
                <tbody id="employeesTableBody">
                    {{#each data.employees}}
                    <tr id="employees-table-row-{{_id}}">
                        <td> {{name}} </td>
                        <td> {{qualification}} </td>
                        <td> {{experience_years}} </td>
                        <td class="justify-content-center d-flex">
                            <button type="button" class="btn btn-sm royal-blue solid mr-1"
                                data-activity-type="{{../activityType}}" data-id="{{../entryId}}" data-employee-id="{{_id}}"
                                data-toggle="modal" data-target="#employeeModal">
                                <i class="fa fa-pencil"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-danger deleteEmployee"
                                    data-id="{{../entryId}}" data-activity-type="{{../activityType}}"  data-employee-id="{{_id}}">
                                <i class="fa fa-times"></i>
                            </button>
                        </td>
                    </tr>
                    {{else}}
                    <tr>
                        <td colspan="6">
                            No employees found
                        </td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>
        </div>
    </div>

</div>
