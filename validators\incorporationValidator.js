exports.validateIncorporation = function (newValues, currentIncorporation) {
    const errors = [];
    // Step 1
    if (newValues.currentStep === 1) {
        if (!newValues.entityName ||
            !newValues.entityType ||
            (newValues.entityType === 'Special Instructions' && !newValues.specialInstructions) ||
            !newValues.partOfGroupControl) {
            errors.push('Please complete all of the fields');
        }
        if (newValues.partOfGroupControl === 'Yes' && (!currentIncorporation.files ||
            !currentIncorporation.files.structureChartFiles ||
            !currentIncorporation.files.structureChartFiles.length)) {
            errors.push('Please upload the structure chart');
        }
    }
    // Step 2
    if (newValues.currentStep === 2) {
        if (!currentIncorporation.relations || currentIncorporation.relations.length === 0) {
            errors.push('Please add at least one relation');
        }
    }
    // Step 3
    if (newValues.currentStep === 3) {
        if (!newValues.principalBusinessActivity ||
            (newValues.principalBusinessActivity === 'Other' && !newValues.principalBusinessActivityOther) ||
            !newValues.activityJurisdiction ||
            (newValues.taxResidenceControl === 'Foreign' && !newValues.taxResidencyJurisdiction) ||
            !newValues.taxResidenceControl) {
            errors.push('Please complete all of the fields');
        }
    }
    // Step 4
    if (newValues.currentStep === 4) {
        if (!newValues.companyOwnAssetsControl ||
            (newValues.companyOwnAssetsControl === 'Yes' && !newValues.bankAccountOwnerControl) ||
            (newValues.companyOwnAssetsControl === 'Yes' && !newValues.bankName) ||
            (newValues.companyOwnAssetsControl === 'Yes' && !newValues.bankAddress) ||
            (newValues.companyOwnAssetsControl === 'Yes' && !newValues.EstimatedValueOfAssets)) {
            errors.push('Please complete all of the fields');
        }
        if (newValues.companyOwnAssetsControl && newValues.companyOwnAssetsControl === 'Yes') {
            if (!currentIncorporation.assets || !currentIncorporation.assets.length) {
                errors.push('Please add at least one asset');
            }
            if (!currentIncorporation.funds || !currentIncorporation.funds.length) {
                errors.push('Please add at least one fund');
            }
        }
    }
    // Step 5
    if (newValues.currentStep === 5) {
        if (!newValues.records ||
            !newValues.records.recordHolder ||
            !newValues.records.primaryAddress ||
            !newValues.records.email ||
            !newValues.records.city ||
            !newValues.records.postalCode ||
            !newValues.records.operationCountry) {
            errors.push('Please complete all of the required fields');
        }
    }
    // Step 6
    if (newValues.currentStep === 6) {
        if (!newValues.additionalServicesControl) {
            errors.push('Please complete all of the required fields');
        }
        if (newValues.additionalServicesControl === 'Yes' && (!newValues.additionalServices || !newValues.additionalServices.length)) {
            errors.push('Please select at least one additional service');
        }
        if (newValues.additionalServicesControl === 'Yes' &&
            newValues.additionalServices.length &&
            newValues.additionalServices.includes('SIBA licence') &&
            !newValues.sibaLicenceType) {
            errors.push('Please complete all of the required fields');
        }
        if (newValues.additionalServicesControl === 'Yes' &&
            newValues.additionalServices.length &&
            newValues.additionalServices.includes('Trust licence') &&
            !newValues.trustLicenceType) {
            errors.push('Please complete all of the required fields');
        }
        if (newValues.additionalServicesControl === 'Yes' &&
            newValues.additionalServices.length &&
            newValues.additionalServices.includes('Other') &&
            !newValues.otherServicesDetails) {
            errors.push('Please complete all of the required fields');
        }
    }
    // Step 7
    if (newValues.currentStep === 7) {
        if (newValues.declarationInformation !== 'on' ||
            newValues.declarationAssets !== 'on' ||
            newValues.declarationTermsAndConditions !== 'on' ||
            !newValues.declarationName ||
            !newValues.declarationDate ||
            !newValues.declarationRelation ||
            !newValues.declarationPhoneNumber ||
            (newValues.declarationRelation === 'Other' && !newValues.declarationRelationOther)) {
            errors.push('Please complete all of the fields');
        }
    }
    return errors;
}