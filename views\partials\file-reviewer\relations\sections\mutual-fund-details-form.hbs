<div id="{{group}}-mutualFundForm">
    <div class="row">
        <div class="col-5">
            <h4>Fund Details</h4>
        </div>
        <div class="col-7 d-flex">
            <div class="custom-control custom-switch my-auto">
                <input type="checkbox" class="custom-control-input toggle-section-check"
                       id="mutual-fund-confirmation-{{group}}"
                       name="mutualFundDetails[active]"
                    {{#if mutualFund.active}} checked {{/if}}
                >
                <label class="custom-control-label" for="mutual-fund-confirmation-{{group}}"></label>
            </div>
        </div>
    </div>
    <div id="content-mutual-fund-confirmation-{{group}}" {{#unless mutualFund.active }} class="hide-element" {{/unless}}>
        {{>file-reviewer/shared/relation-file-table tableId="mutualFundTable"  name="mutualFundDetails"
                files=(ternary newRelation mutualFund mutualFund.files) relationId=relation._id}}
    </div>
</div>


