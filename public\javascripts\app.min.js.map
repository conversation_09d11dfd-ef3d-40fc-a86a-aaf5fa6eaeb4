{"version": 3, "sources": ["app.js"], "names": ["$", "Components", "prototype", "initTooltipPlugin", "fn", "tooltip", "initPopoverPlugin", "popover", "initToastPlugin", "toast", "initSlimScrollPlugin", "slimScroll", "height", "position", "size", "touchScrollStep", "color", "initFormValidation", "on", "event", "this", "addClass", "checkValidity", "preventDefault", "stopPropagation", "initCustomModalPlugin", "e", "Custombox", "modal", "content", "target", "attr", "effect", "overlay", "open", "initCounterUp", "each", "idx", "obj", "counterUp", "delay", "time", "initPeityCharts", "colors", "split", "width", "peity", "fill", "data", "initKnob", "knob", "initTippyTooltips", "length", "tippy", "init", "<PERSON><PERSON><PERSON><PERSON>", "window", "j<PERSON><PERSON><PERSON>", "Portlet", "$body", "$portletIdentifier", "$portletCloser", "$portletRefresher", "$this", "document", "ev", "$portlet", "closest", "$portlet_parent", "parent", "remove", "children", "append", "$pd", "find", "setTimeout", "fadeOut", "Math", "random", "App", "$window", "_resetSidebarScroll", "slimscroll", "wheelStep", "initMenu", "toggleClass", "removeClass", "metisMenu", "hasClass", "pageUrl", "location", "href", "prev", "slideToggle", "initLayout", "Waves"], "mappings": "CAUA,SAAAA,GACA,aAEA,IAAAC,EAAA,aAGAA,EAAAC,UAAAC,kBAAA,WACAH,EAAAI,GAAAC,SAAAL,EAAA,2BAAAK,WAIAJ,EAAAC,UAAAI,kBAAA,WACAN,EAAAI,GAAAG,SAAAP,EAAA,2BAAAO,WAIAN,EAAAC,UAAAM,gBAAA,WACAR,EAAAI,GAAAK,OAAAT,EAAA,yBAAAS,SAIAR,EAAAC,UAAAQ,qBAAA,WAEAV,EAAAI,GAAAO,YAAAX,EAAA,eAAAW,WAAA,CACAC,OAAA,OACAC,SAAA,QACAC,KAAA,MACAC,gBAAA,GACAC,MAAA,aAKAf,EAAAC,UAAAe,mBAAA,WACAjB,EAAA,qBAAAkB,GAAA,SAAA,SAAAC,GAEA,OADAnB,EAAAoB,MAAAC,SAAA,kBACA,IAAArB,EAAAoB,MAAA,GAAAE,kBACAH,EAAAI,iBACAJ,EAAAK,mBACA,MAOAvB,EAAAC,UAAAuB,sBAAA,WACAzB,EAAA,+BAAAkB,GAAA,QAAA,SAAAQ,GACAA,EAAAH,iBACA,IAAAI,UAAAC,MAAA,CACAC,QAAA,CACAC,OAAA9B,EAAAoB,MAAAW,KAAA,QACAC,OAAAhC,EAAAoB,MAAAW,KAAA,mBAEAE,QAAA,CACAjB,MAAAhB,EAAAoB,MAAAW,KAAA,wBAIAG,UAKAjC,EAAAC,UAAAiC,cAAA,WACAnC,EAAAoB,MAAAW,KAAA,eAAA/B,EAAAoB,MAAAW,KAAA,cACA/B,EAAAoB,MAAAW,KAAA,cAAA/B,EAAAoB,MAAAW,KAAA,aACA/B,EAAA,6BAAAoC,KAAA,SAAAC,EAAAC,GACAtC,EAAAoB,MAAAmB,UAAA,CACAC,MAAA,IACAC,KAAA,UAMAxC,EAAAC,UAAAwC,gBAAA,WACA1C,EAAA,6BAAAoC,KAAA,SAAAC,EAAAC,GACA,IAAAK,EAAA3C,EAAAoB,MAAAW,KAAA,eAAA/B,EAAAoB,MAAAW,KAAA,eAAAa,MAAA,KAAA,GACAC,EAAA7C,EAAAoB,MAAAW,KAAA,cAAA/B,EAAAoB,MAAAW,KAAA,cAAA,GACAnB,EAAAZ,EAAAoB,MAAAW,KAAA,eAAA/B,EAAAoB,MAAAW,KAAA,eAAA,GACA/B,EAAAoB,MAAA0B,MAAA,MAAA,CACAC,KAAAJ,EACAE,MAAAA,EACAjC,OAAAA,MAIAZ,EAAA,+BAAAoC,KAAA,SAAAC,EAAAC,GACA,IAAAK,EAAA3C,EAAAoB,MAAAW,KAAA,eAAA/B,EAAAoB,MAAAW,KAAA,eAAAa,MAAA,KAAA,GACAC,EAAA7C,EAAAoB,MAAAW,KAAA,cAAA/B,EAAAoB,MAAAW,KAAA,cAAA,GACAnB,EAAAZ,EAAAoB,MAAAW,KAAA,eAAA/B,EAAAoB,MAAAW,KAAA,eAAA,GACA/B,EAAAoB,MAAA0B,MAAA,QAAA,CACAC,KAAAJ,EACAE,MAAAA,EACAjC,OAAAA,MAIAZ,EAAA,mCAAAoC,KAAA,SAAAC,EAAAC,GACAtC,EAAAoB,MAAA0B,MAAA,WAIA9C,EAAA,8BAAAoC,KAAA,SAAAC,EAAAC,GACAtC,EAAAoB,MAAA0B,MAAA,OAAA9C,EAAAoB,MAAA4B,UAIAhD,EAAA,6BAAAoC,KAAA,SAAAC,EAAAC,GACA,IAAAK,EAAA3C,EAAAoB,MAAAW,KAAA,eAAA/B,EAAAoB,MAAAW,KAAA,eAAAa,MAAA,KAAA,GACAC,EAAA7C,EAAAoB,MAAAW,KAAA,cAAA/B,EAAAoB,MAAAW,KAAA,cAAA,GACAnB,EAAAZ,EAAAoB,MAAAW,KAAA,eAAA/B,EAAAoB,MAAAW,KAAA,eAAA,GACA/B,EAAAoB,MAAA0B,MAAA,MAAA,CACAC,KAAAJ,EACAE,MAAAA,EACAjC,OAAAA,OAKAX,EAAAC,UAAA+C,SAAA,WACAjD,EAAA,wBAAAoC,KAAA,SAAAC,EAAAC,GACAtC,EAAAoB,MAAA8B,UAIAjD,EAAAC,UAAAiD,kBAAA,WACA,EAAAnD,EAAA,yBAAAoD,QACAC,MAAA,0BAIApD,EAAAC,UAAAoD,KAAA,WAEAlC,KAAAjB,oBACAiB,KAAAd,oBACAc,KAAAZ,kBACAY,KAAAV,uBACAU,KAAAH,qBACAG,KAAAK,wBACAL,KAAAe,gBACAf,KAAAsB,kBACAtB,KAAA6B,WACA7B,KAAA+B,qBAGAnD,EAAAC,WAAA,IAAAA,EAAAD,EAAAC,WAAAsD,YAAAtD,EAnJA,CAqJAuD,OAAAC,QAEA,SAAAzD,GACA,aAKA,IAAA0D,EAAA,WACAtC,KAAAuC,MAAA3D,EAAA,QACAoB,KAAAwC,mBAAA,QACAxC,KAAAyC,eAAA,gCACAzC,KAAA0C,kBAAA,iCAIAJ,EAAAxD,UAAAoD,KAAA,WAEA,IAAAS,EAAA3C,KACApB,EAAAgE,UAAA9C,GAAA,QAAAE,KAAAyC,eAAA,SAAAI,GACAA,EAAA1C,iBACA,IAAA2C,EAAAlE,EAAAoB,MAAA+C,QAAAJ,EAAAH,oBACAQ,EAAAF,EAAAG,SACAH,EAAAI,SACA,GAAAF,EAAAG,WAAAnB,QACAgB,EAAAE,WAKAtE,EAAAgE,UAAA9C,GAAA,QAAAE,KAAA0C,kBAAA,SAAAG,GACAA,EAAA1C,iBACA,IAAA2C,EAAAlE,EAAAoB,MAAA+C,QAAAJ,EAAAH,oBAEAM,EAAAM,OAAA,6EACA,IAAAC,EAAAP,EAAAQ,KAAA,kBACAC,WAAA,WACAF,EAAAG,QAAA,OAAA,WACAH,EAAAH,YAEA,IAAA,EAAAO,KAAAC,SAAA,QAIA9E,EAAA0D,QAAA,IAAAA,EAAA1D,EAAA0D,QAAAH,YAAAG,EA1CA,CA4CAF,OAAAC,QAEA,SAAAzD,GACA,aAEA,IAAA+E,EAAA,WACA3D,KAAAuC,MAAA3D,EAAA,QACAoB,KAAA4D,QAAAhF,EAAAwD,SAMAuB,EAAA7E,UAAA+E,oBAAA,WAEAjF,EAAA,oBAAAkF,WAAA,CACAtE,OAAA,OACAC,SAAA,QACAC,KAAA,MACAE,MAAA,UACAmE,UAAA,EACApE,gBAAA,MAOAgE,EAAA7E,UAAAkF,SAAA,WACA,IAAArB,EAAA3C,KAGApB,EAAA,uBAAAkB,GAAA,QAAA,SAAAC,GACAA,EAAAI,iBACAwC,EAAAJ,MAAA0B,YAAA,kBACA,KAAAtB,EAAAiB,QAAAnC,QACAkB,EAAAJ,MAAA0B,YAAA,YAEAtB,EAAAJ,MAAA2B,YAAA,YAIAvB,EAAAkB,wBAIAjF,EAAA,cAAAuF,YAGAxB,EAAAkB,sBAGAjF,EAAA,qBAAAkB,GAAA,QAAA,SAAAQ,GACA1B,EAAA,QAAAqF,YAAA,uBAGArF,EAAAgE,UAAA9C,GAAA,QAAA,OAAA,SAAAQ,GACA,EAAA1B,EAAA0B,EAAAI,QAAAqC,QAAA,iCAAAf,QAIA,EAAApD,EAAA0B,EAAAI,QAAAqC,QAAA,8BAAAf,QAAApD,EAAA0B,EAAAI,QAAA0D,SAAA,uBACA,EAAAxF,EAAA0B,EAAAI,QAAAqC,QAAA,uBAAAf,SAIApD,EAAA,QAAAsF,YAAA,qBACAtF,EAAA,QAAAsF,YAAA,qBAKAtF,EAAA,gBAAAoC,KAAA,WACA,IAAAqD,EAAAjC,OAAAkC,SAAAC,KAAA/C,MAAA,QAAA,GACAxB,KAAAuE,MAAAF,IACAzF,EAAAoB,MAAAC,SAAA,UACArB,EAAAoB,MAAAiD,SAAAhD,SAAA,UACArB,EAAAoB,MAAAiD,SAAAA,SAAAhD,SAAA,MACArB,EAAAoB,MAAAiD,SAAAA,SAAAuB,OAAAvE,SAAA,UACArB,EAAAoB,MAAAiD,SAAAA,SAAAA,SAAAhD,SAAA,UACArB,EAAAoB,MAAAiD,SAAAA,SAAAA,SAAAA,SAAAhD,SAAA,MACArB,EAAAoB,MAAAiD,SAAAA,SAAAA,SAAAA,SAAAA,SAAAhD,SAAA,aAKArB,EAAA,kBAAAkB,GAAA,QAAA,SAAAC,GACAnB,EAAAoB,MAAAiE,YAAA,QACArF,EAAA,eAAA6F,YAAA,OAIA7F,EAAAwD,QAAAtC,GAAA,OAAA,WACAlB,EAAA,WAAA4E,UACA5E,EAAA,cAAAwC,MAAA,KAAAoC,QAAA,WAOAG,EAAA7E,UAAA4F,WAAA,WAEA,KAAA1E,KAAA4D,QAAAnC,SAAAzB,KAAA4D,QAAAnC,SAAA,KACAzB,KAAAuC,MAAAtC,SAAA,YAEA,GAAAD,KAAAuC,MAAAX,KAAA,kBACA5B,KAAAuC,MAAA2B,YAAA,aAMAP,EAAA7E,UAAAoD,KAAA,WACA,IAAAS,EAAA3C,KACAA,KAAA0E,aACA9F,EAAA0D,QAAAJ,OACAlC,KAAAgE,WACApF,EAAAC,WAAAqD,OAEAS,EAAAiB,QAAA9D,GAAA,SAAA,SAAAQ,GACAA,EAAAH,iBACAwC,EAAA+B,aACA/B,EAAAkB,yBAIAjF,EAAA+E,IAAA,IAAAA,EAAA/E,EAAA+E,IAAAxB,YAAAwB,EA7HA,CAgIAvB,OAAAC,QAEA,SAAAzD,GACA,aAEAwD,OAAAC,OADAsB,IAAAzB,OAFA,GAMAyC,MAAAzC", "file": "app.min.js", "sourcesContent": ["/*\r\nTemplate Name: Ubold - Responsive Bootstrap 4 Admin Dashboard\r\nAuthor: CoderThemes\r\nVersion: 3.0.0\r\nWebsite: https://coderthemes.com/\r\nContact: <EMAIL>\r\nFile: Main Js File\r\n*/\r\n\r\n\r\n!function ($) {\r\n    \"use strict\";\r\n\r\n    var Components = function () { };\r\n\r\n    //initializing tooltip\r\n    Components.prototype.initTooltipPlugin = function () {\r\n        $.fn.tooltip && $('[data-toggle=\"tooltip\"]').tooltip()\r\n    },\r\n\r\n    //initializing popover\r\n    Components.prototype.initPopoverPlugin = function () {\r\n        $.fn.popover && $('[data-toggle=\"popover\"]').popover()\r\n    },\r\n\r\n    //initializing toast\r\n    Components.prototype.initToastPlugin = function() {\r\n        $.fn.toast && $('[data-toggle=\"toast\"]').toast()\r\n    },\r\n\r\n    //initializing Slimscroll\r\n    Components.prototype.initSlimScrollPlugin = function () {\r\n        //You can change the color of scroll bar here\r\n        $.fn.slimScroll && $(\".slimscroll\").slimScroll({\r\n            height: 'auto',\r\n            position: 'right',\r\n            size: \"8px\",\r\n            touchScrollStep: 20,\r\n            color: '#9ea5ab'\r\n        });\r\n    },\r\n\r\n    //initializing form validation\r\n    Components.prototype.initFormValidation = function () {\r\n        $(\".needs-validation\").on('submit', function (event) {\r\n            $(this).addClass('was-validated');\r\n            if ($(this)[0].checkValidity() === false) {\r\n                event.preventDefault();\r\n                event.stopPropagation();\r\n                return false;\r\n            }\r\n            return true;\r\n        });\r\n    },\r\n\r\n    //initializing custom modal\r\n    Components.prototype.initCustomModalPlugin = function() {\r\n        $('[data-plugin=\"custommodal\"]').on('click', function(e) {\r\n            e.preventDefault();\r\n            var modal = new Custombox.modal({\r\n                content: {\r\n                    target: $(this).attr(\"href\"),\r\n                    effect: $(this).attr(\"data-animation\")\r\n                },\r\n                overlay: {\r\n                    color: $(this).attr(\"data-overlayColor\")\r\n                }\r\n            });\r\n            // Open\r\n            modal.open();\r\n        });\r\n    },\r\n\r\n    // Counterup\r\n    Components.prototype.initCounterUp = function() {\r\n        var delay = $(this).attr('data-delay')?$(this).attr('data-delay'):100; //default is 100\r\n        var time = $(this).attr('data-time')?$(this).attr('data-time'):1200; //default is 1200\r\n         $('[data-plugin=\"counterup\"]').each(function(idx, obj) {\r\n            $(this).counterUp({\r\n                delay: 100,\r\n                time: 1200\r\n            });\r\n         });\r\n    },\r\n\r\n    //peity charts\r\n    Components.prototype.initPeityCharts = function() {\r\n        $('[data-plugin=\"peity-pie\"]').each(function(idx, obj) {\r\n            var colors = $(this).attr('data-colors')?$(this).attr('data-colors').split(\",\"):[];\r\n            var width = $(this).attr('data-width')?$(this).attr('data-width'):20; //default is 20\r\n            var height = $(this).attr('data-height')?$(this).attr('data-height'):20; //default is 20\r\n            $(this).peity(\"pie\", {\r\n                fill: colors,\r\n                width: width,\r\n                height: height\r\n            });\r\n        });\r\n        //donut\r\n         $('[data-plugin=\"peity-donut\"]').each(function(idx, obj) {\r\n            var colors = $(this).attr('data-colors')?$(this).attr('data-colors').split(\",\"):[];\r\n            var width = $(this).attr('data-width')?$(this).attr('data-width'):20; //default is 20\r\n            var height = $(this).attr('data-height')?$(this).attr('data-height'):20; //default is 20\r\n            $(this).peity(\"donut\", {\r\n                fill: colors,\r\n                width: width,\r\n                height: height\r\n            });\r\n        });\r\n\r\n        $('[data-plugin=\"peity-donut-alt\"]').each(function(idx, obj) {\r\n            $(this).peity(\"donut\");\r\n        });\r\n\r\n        // line\r\n        $('[data-plugin=\"peity-line\"]').each(function(idx, obj) {\r\n            $(this).peity(\"line\", $(this).data());\r\n        });\r\n\r\n        // bar\r\n        $('[data-plugin=\"peity-bar\"]').each(function(idx, obj) {\r\n            var colors = $(this).attr('data-colors')?$(this).attr('data-colors').split(\",\"):[];\r\n            var width = $(this).attr('data-width')?$(this).attr('data-width'):20; //default is 20\r\n            var height = $(this).attr('data-height')?$(this).attr('data-height'):20; //default is 20\r\n            $(this).peity(\"bar\", {\r\n                fill: colors,\r\n                width: width,\r\n                height: height\r\n            });\r\n         });\r\n    },\r\n\r\n    Components.prototype.initKnob = function() {\r\n        $('[data-plugin=\"knob\"]').each(function(idx, obj) {\r\n           $(this).knob();\r\n        });\r\n    },\r\n\r\n    Components.prototype.initTippyTooltips = function () {\r\n        if($('[data-plugin=\"tippy\"]').length > 0)\r\n        tippy('[data-plugin=\"tippy\"]');\r\n    },\r\n\r\n    //initilizing\r\n    Components.prototype.init = function () {\r\n        var $this = this;\r\n        this.initTooltipPlugin(),\r\n        this.initPopoverPlugin(),\r\n        this.initToastPlugin(),\r\n        this.initSlimScrollPlugin(),\r\n        this.initFormValidation(),\r\n        this.initCustomModalPlugin(),\r\n        this.initCounterUp(),\r\n        this.initPeityCharts(),\r\n        this.initKnob();\r\n        this.initTippyTooltips();\r\n    },\r\n\r\n    $.Components = new Components, $.Components.Constructor = Components\r\n\r\n}(window.jQuery),\r\n\r\nfunction($) {\r\n    \"use strict\";\r\n\r\n    /**\r\n    Portlet Widget\r\n    */\r\n    var Portlet = function() {\r\n        this.$body = $(\"body\"),\r\n        this.$portletIdentifier = \".card\",\r\n        this.$portletCloser = '.card a[data-toggle=\"remove\"]',\r\n        this.$portletRefresher = '.card a[data-toggle=\"reload\"]'\r\n    };\r\n\r\n    //on init\r\n    Portlet.prototype.init = function() {\r\n        // Panel closest\r\n        var $this = this;\r\n        $(document).on(\"click\",this.$portletCloser, function (ev) {\r\n            ev.preventDefault();\r\n            var $portlet = $(this).closest($this.$portletIdentifier);\r\n                var $portlet_parent = $portlet.parent();\r\n            $portlet.remove();\r\n            if ($portlet_parent.children().length == 0) {\r\n                $portlet_parent.remove();\r\n            }\r\n        });\r\n\r\n        // Panel Reload\r\n        $(document).on(\"click\",this.$portletRefresher, function (ev) {\r\n            ev.preventDefault();\r\n            var $portlet = $(this).closest($this.$portletIdentifier);\r\n            // This is just a simulation, nothing is going to be reloaded\r\n            $portlet.append('<div class=\"card-disabled\"><div class=\"card-portlets-loader\"></div></div>');\r\n            var $pd = $portlet.find('.card-disabled');\r\n            setTimeout(function () {\r\n                $pd.fadeOut('fast', function () {\r\n                    $pd.remove();\r\n                });\r\n            }, 500 + 300 * (Math.random() * 5));\r\n        });\r\n    },\r\n    //\r\n    $.Portlet = new Portlet, $.Portlet.Constructor = Portlet\r\n    \r\n}(window.jQuery),\r\n\r\nfunction ($) {\r\n    'use strict';\r\n\r\n    var App = function () {\r\n        this.$body = $('body'),\r\n        this.$window = $(window)\r\n    };\r\n\r\n    /**\r\n    Resets the scroll\r\n    */\r\n    App.prototype._resetSidebarScroll = function () {\r\n        // sidebar - scroll container\r\n        $('.slimscroll-menu').slimscroll({\r\n            height: 'auto',\r\n            position: 'right',\r\n            size: \"8px\",\r\n            color: '#9ea5ab',\r\n            wheelStep: 5,\r\n            touchScrollStep: 20\r\n        });\r\n    },\r\n\r\n    /** \r\n     * Initlizes the menu - top and sidebar\r\n    */\r\n    App.prototype.initMenu = function () {\r\n        var $this = this;\r\n\r\n        // Left menu collapse\r\n        $('.button-menu-mobile').on('click', function (event) {\r\n            event.preventDefault();\r\n            $this.$body.toggleClass('sidebar-enable');\r\n            if ($this.$window.width() >= 768) {\r\n                $this.$body.toggleClass('enlarged');\r\n            } else {\r\n                $this.$body.removeClass('enlarged');\r\n            }\r\n\r\n            // sidebar - scroll container\r\n            $this._resetSidebarScroll();\r\n        });\r\n\r\n        // sidebar - main menu\r\n        $(\"#side-menu\").metisMenu();\r\n\r\n        // sidebar - scroll container\r\n        $this._resetSidebarScroll();\r\n\r\n        // right side-bar toggle\r\n        $('.right-bar-toggle').on('click', function (e) {\r\n            $('body').toggleClass('right-bar-enabled');\r\n        });\r\n\r\n        $(document).on('click', 'body', function (e) {\r\n            if ($(e.target).closest('.right-bar-toggle, .right-bar').length > 0) {\r\n                return;\r\n            }\r\n\r\n            if ($(e.target).closest('.left-side-menu, .side-nav').length > 0 || $(e.target).hasClass('button-menu-mobile')\r\n                || $(e.target).closest('.button-menu-mobile').length > 0) {\r\n                return;\r\n            }\r\n\r\n            $('body').removeClass('right-bar-enabled');\r\n            $('body').removeClass('sidebar-enable');\r\n            return;\r\n        });\r\n\r\n        // activate the menu in left side bar based on url\r\n        $(\"#side-menu a\").each(function () {\r\n            var pageUrl = window.location.href.split(/[?#]/)[0];\r\n            if (this.href == pageUrl) {\r\n                $(this).addClass(\"active\");\r\n                $(this).parent().addClass(\"active\"); // add active to li of the current link\r\n                $(this).parent().parent().addClass(\"in\");\r\n                $(this).parent().parent().prev().addClass(\"active\"); // add active class to an anchor\r\n                $(this).parent().parent().parent().addClass(\"active\");\r\n                $(this).parent().parent().parent().parent().addClass(\"in\"); // add active to li of the current link\r\n                $(this).parent().parent().parent().parent().parent().addClass(\"active\");\r\n            }\r\n        });\r\n\r\n        // Topbar - main menu\r\n        $('.navbar-toggle').on('click', function (event) {\r\n            $(this).toggleClass('open');\r\n            $('#navigation').slideToggle(400);\r\n        });\r\n\r\n        // Preloader\r\n        $(window).on('load', function () {\r\n            $('#status').fadeOut();\r\n            $('#preloader').delay(350).fadeOut('slow');\r\n        });\r\n    },\r\n\r\n    /** \r\n     * Init the layout - with broad sidebar or compact side bar\r\n    */\r\n    App.prototype.initLayout = function () {\r\n        // in case of small size, add class enlarge to have minimal menu\r\n        if (this.$window.width() >= 768 && this.$window.width() <= 1028) {\r\n            this.$body.addClass('enlarged');\r\n        } else {\r\n            if (this.$body.data('keep-enlarged') != true) {\r\n                this.$body.removeClass('enlarged');\r\n            }\r\n        }\r\n    },\r\n\r\n    //initilizing\r\n    App.prototype.init = function () {\r\n        var $this = this;\r\n        this.initLayout();\r\n        $.Portlet.init();\r\n        this.initMenu();\r\n        $.Components.init();\r\n        // on window resize, make menu flipped automatically\r\n        $this.$window.on('resize', function (e) {\r\n            e.preventDefault();\r\n            $this.initLayout();\r\n            $this._resetSidebarScroll();\r\n        });\r\n    },\r\n\r\n    $.App = new App, $.App.Constructor = App\r\n\r\n\r\n}(window.jQuery),\r\n//initializing main application module\r\nfunction ($) {\r\n    \"use strict\";\r\n    $.App.init();\r\n}(window.jQuery);\r\n\r\n// Waves Effect\r\nWaves.init();"]}