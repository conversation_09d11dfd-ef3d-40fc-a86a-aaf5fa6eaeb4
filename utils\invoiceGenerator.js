const moment = require('moment');
const ConfigurationModel = require('../models/configuration');

exports.generateInvoiceNumber = async function (config, type = null) {
  try {
    if (!config || !config.invoiceConfiguration) {
      throw new Error("Error, Substance configuration template not found");
    }

    const currentDate = new Date();
    if (moment.utc(config.invoiceConfiguration.currentYear).year() < moment.utc(currentDate).year()) {
      config.invoiceConfiguration.currentYear = new Date(moment.utc(currentDate).format('YYYY'));
      config.invoiceConfiguration.currentAvailableNumber = config.invoiceConfiguration.initialRangeNumber;
      config.invoiceConfiguration.currentAvailableIncorporationNumber = config.invoiceConfiguration.initialRangeIncorporationNumber;
      config.invoiceConfiguration.currentAvailableFinancialReportNumber = config.invoiceConfiguration.initialRangeFinancialReportNumber;
    }
    let invoiceNumber;
    if (type === "incorporation") {
      invoiceNumber = moment.utc(currentDate).format('YYMM') + "/" +
        config.invoiceConfiguration.currentAvailableIncorporationNumber;
      config.invoiceConfiguration.currentAvailableIncorporationNumber++;
    }
    else if (type === "financial-report") {
      invoiceNumber = moment.utc(currentDate).format('YYMM') + "/" +
        config.invoiceConfiguration.currentAvailableFinancialReportNumber;
      config.invoiceConfiguration.currentAvailableFinancialReportNumber++;
    }
    else {
      invoiceNumber = moment.utc(currentDate).format('YYMM') + "/" +
        config.invoiceConfiguration.currentAvailableNumber;
      config.invoiceConfiguration.currentAvailableNumber++;
    }

    await ConfigurationModel.findByIdAndUpdate(config._id, { invoiceConfiguration: config.invoiceConfiguration });
    return invoiceNumber;
  } catch (e) {
    console.log("error ", e);
    return null;
  }
};
