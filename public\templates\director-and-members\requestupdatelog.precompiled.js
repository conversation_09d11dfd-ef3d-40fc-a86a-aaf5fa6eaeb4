(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['requestupdatelog'] = template({"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data) {
    var helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "<div class=\"text-left\">\n\n    <div class=\"form-group mb-2\" >\n        <label for=\"changeType\">Type of request:</label>\n        <input name=\"changeType\" id=\"changeType\" disabled class=\"form-control w-100\" value=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"changeType") || (depth0 != null ? lookupProperty(depth0,"changeType") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"changeType","hash":{},"data":data,"loc":{"start":{"line":5,"column":92},"end":{"line":5,"column":106}}}) : helper)))
    + "\" >\n        </input>\n\n    </div>\n    <div  class=\"form-group w-100 \">\n        <label for=\"changeReason\">Additional information if necessary:</label>\n        <textarea name=\"changeReason\" class=\"form-control w-100\" disabled \n                  id=\"changeReason\" rows=\"3\" placeholder=\"...\" maxlength=\"500\">"
    + alias4(((helper = (helper = lookupProperty(helpers,"changeReason") || (depth0 != null ? lookupProperty(depth0,"changeReason") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"changeReason","hash":{},"data":data,"loc":{"start":{"line":12,"column":79},"end":{"line":12,"column":95}}}) : helper)))
    + "</textarea>\n    </div>\n</div>\n";
},"useData":true});
})();