(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['requestupdatepopup'] = template({"1":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                <option value=\"\" hidden>Select an option</option>\n\n"
    + ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"hasMissingData") : depth0),{"name":"if","hash":{},"fn":container.program(2, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":9,"column":16},"end":{"line":11,"column":23}}})) != null ? stack1 : "")
    + "                <option value=\"Change of Director(s)\" >Change of Director(s)</option>\n                <option value=\"Change of Director(s) Particulars\">Change of Director(s) Particulars</option>\n                <option value=\"Change of Director(s) Address\">Change of Director(s) Address</option>\n                <option value=\"Other update for Director(s)\">Other update for Director(s)</option>\n                <option value=\"Confirmation for Licensed Director to provide service\">Confirmation for Licensed Director to provide service</option>\n                <option value=\"Revert confirmation for Licensed Director to provide service\">Revert confirmation for Licensed Director to provide service</option>\n";
},"2":function(container,depth0,helpers,partials,data) {
    return "                    <option value=\"Missing Director(s) info\">Missing Director(s) info</option>\n";
},"4":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "                <option value=\"\" hidden>Select an option</option>\n\n"
    + ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"hasMissingData") : depth0),{"name":"if","hash":{},"fn":container.program(5, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":21,"column":16},"end":{"line":23,"column":23}}})) != null ? stack1 : "")
    + "                <option value=\"Change of Beneficial Owner(s)\" >Change of Beneficial Owner(s)</option>\n                <option value=\"Change of Beneficial Owner(s) Particulars\" >Change of Beneficial Owner(s) Particulars</option>\n                <option value=\"Change of Beneficial Owner(s) Address\" >Change of Beneficial Owner(s) Address</option>\n                <option value=\"Other update for Beneficial Owner(s)\">Other update for Beneficial Owner(s)</option>\n";
},"5":function(container,depth0,helpers,partials,data) {
    return "                    <option value=\"Missing Beneficial Owner(s) info\">Missing Beneficial Owner(s) info</option>\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "<div class=\"text-left\">\n\n    <div class=\"form-group mb-2\" >\n        <label for=\"changeType\">Type of request:</label>\n        <select name=\"changeType\" id=\"changeType\" class=\"form-control w-100\" >\n"
    + ((stack1 = lookupProperty(helpers,"if").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"isDirector") : depth0),{"name":"if","hash":{},"fn":container.program(1, data, 0),"inverse":container.program(4, data, 0),"data":data,"loc":{"start":{"line":6,"column":12},"end":{"line":28,"column":19}}})) != null ? stack1 : "")
    + "\n        </select>\n\n    </div>\n    <div  class=\"form-group w-100 \">\n        <label for=\"changeReason\">Additional information if necessary:</label>\n        <textarea name=\"changeReason\" class=\"form-control w-100\"\n                  id=\"changeReason\" rows=\"3\" placeholder=\"...\" maxlength=\"500\"></textarea>\n    </div>\n</div>\n";
},"useData":true});
})();