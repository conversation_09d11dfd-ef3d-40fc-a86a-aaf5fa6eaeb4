<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class='contour'>
                    <h2>
                        1. Details
                    </h2>



                    {{# if validationErrors }}
                        {{# each validationErrors }}
                           {{renderValidationMessage this.msg this.field}}
                        {{/each}}
                    {{/if}}


                    {{#if financialPeriodChanged}}
                        <h5 class="alert alert-info ">
                            <i class="fa fa-lg  fa-info-circle mr-1"></i>Note that your Financial Period has been changed
                            by a Trident Officer according to the ITA guidelines. <br>
                            Please make sure the information on the remaining pages of this submission is correct and in
                            accordance with the amended dates.
                        </h5>
                    {{else}}
                        {{#ifEquals isSameITADate true }}
                            <h5 class="alert alert-info ">
                                The Financial Period is reflected as prescribed by the Economic Substance Act and the
                                alteration approved by the ITA. If you believe this is incorrect, please contact your Trident Officer.
                            </h5>
                        {{else}}
                            <h5 class="alert alert-info">
                                The Financial Period is reflected as prescribed by the Economic Substance Act.
                                If you believe this is incorrect, please contact your Trident Officer.
                            </h5>
                        {{/ifEquals}}
                    {{/if}}


                    <form method="POST" class='enquiry' id="entityDetailsForm" autocomplete="off">
                        <input type="text" hidden name="csrf-token" value="{{csrfToken}}">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="card">
                                    <div class="card-body">
										<h3></h3>
                                        <div class="row">
                                            <div class="col-md-8">
                                                <div class="form-group mb-3">
                                                    <label class="mb-2" for="HasFinancialPeriodChanged">A. Has an application been made and confirmed with ITA to change your financial period?*</label>
                                                </div>
                                            </div>
                                            <div class="col-md-4" align="right">
                                                <div class="radio form-check-inline">
                                                    <input type="radio" id="HasFinancialPeriodChangedYes" name="HasFinancialPeriodChanged" value="Yes" readonly
                                                           {{#ifEquals isSameITADate true}}
                                                           checked
                                                           {{else}}
                                                               disabled
                                                           {{/ifEquals}}
                                                           data-toggle="tooltip" data-placement="top"
                                                           title="Choose only if an application to change the financial period has been presented and approved by the International Tax Authority">
                                                    <label for="HasFinancialPeriodChangedYes">Yes</label>
                                                </div>
                                                <div class="radio form-check-inline">
                                                    <input type="radio" id="HasFinancialPeriodChangedNo" name="HasFinancialPeriodChanged" value="No" readonly
                                                           {{#ifEquals isSameITADate false}}
                                                           checked
                                                           {{else}}
                                                               disabled
                                                           {{/ifEquals}}
                                                           data-toggle="tooltip" data-placement="top"
                                                           title="Default response for companies that have not presented a formal request to have the financial period changed">
                                                    <label for="HasFinancialPeriodChangedNo">No</label>
                                                </div>
                                            </div>
                                            <br>
                                        </div>

                                        <div id="showPeriod" >

                                            <div class="row">
                                                <div class="col-md-8">
                                                    <div class="form-group mb-3">
                                                        <label class="mb-2 disable-click" for="FinancialPeriodBegins">B. Financial Period begins:*</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div id="FinancialPeriodBeginsDiv" class="form-group mb-3"
                                                         data-toggle="tooltip" data-placement="top"
                                                         {{#ifEquals isSameITADate false}}
                                                            title="Date on which the financial period begins for the company.
                                                            Standard financial period for existing companies begins on June 30, 2019 and ends 12 months thereafter.
                                                            For a company incorporated on or after January 1, 2019, the financial period begins on the date of its incorporation and ends 12 months thereafter"
                                                         {{else}}
                                                            title="Approved ITA financial period start date"
                                                         {{/ifEquals}}>

                                                        <input type="text" name="FinancialPeriodBegins" id="FinancialPeriodBegins"
                                                          class="form-control datepicker periodDate"
                                                               value="{{formatDate entry.entity_details.financial_period_begins "YYYY-MM-DD"}}"

                                                               data-initial-value="{{formatDate entry.entity_details.financial_period_begins "YYYY-MM-DD"}}"
                                                               >
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <div class="form-group mb-3">
                                                        <label class="mb-2 disable-click" for="FinancialPeriodEnds">C. Financial Period ends:*</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div id="FinancialPeriodEndsDiv"  class="form-group mb-3"
                                                         data-toggle="tooltip" data-placement="top"
                                                        {{#ifEquals isSameITADate false}}
                                                         title="Date on which the financial period ends for the company.
                                                         Standard financial periods for existing companies end on June 30, 2020.
                                                         For a company incorporated on or after January 1, 2019, the financial period ends 12 months after the date of incorporation"
                                                        {{else}}
                                                         title="Approved ITA financial period end date"
                                                        {{/ifEquals}}>
                                                        <input type="text" name="FinancialPeriodEnds" id="FinancialPeriodEnds"
                                                          class="form-control datepicker periodDate"
                                                               value="{{formatDate entry.entity_details.financial_period_ends "YYYY-MM-DD"}}"
                                                               data-initial-value="{{formatDate entry.entity_details.financial_period_begins "YYYY-MM-DD"}}">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>


										<div id="Showrest" class="hide-element">

											<div class="row">
												<div class="col-md-8">
													<div class="form-group mb-3">
														<label id="PreviousEndDateLbl" class="mb-2" for="PreviousEndDate">D. Previous end date:</label>
													</div>
												</div>
												<div class="col-md-4">
													<div class="form-group mb-2"
                                                         data-toggle="tooltip" data-placement="top"
                                                         title="The end date of your previous submission if applicable. Otherwise this field will remain empty">
														<input type="text" name="PreviousEndDate" id="PreviousEndDate"
                                                          class="form-control datepicker"
                                                          {{#if previousEndDate}}
                                                              value="{{formatDate previousEndDate "YYYY-MM-DD"}}"
                                                            {{else}}
                                                              value=""
                                                          {{/if}}>
													</div>
												</div>
											</div>
											<div class="row">
												<div class="col-md-8">
													<div class="form-group mb-2">
														<label id="DateOfApplicationToITALbl" class="mb-2" for="DateOfApplicationToITA">E. Date of application to ITA under Rule 13, 15 or 16:</label>
													</div>
												</div>
												<div class="col-md-4">
													<div class="form-group mb-2"  data-toggle="tooltip" data-placement="top" title="Date of your approved ITA application.">
														<input type="text" name="DateOfApplicationToITA"
                                                        id="DateOfApplicationToITA" class="form-control datepicker"

                                                        {{#if hasApplicationITADate}}
                                                           value="{{formatDate applicationITADate "YYYY-MM-DD"}}"
                                                        {{else}}
                                                          value="{{formatDate entry.entity_details.date_of_application_ITA "YYYY-MM-DD"}}"
                                                        {{/if}}
                                                        >
													</div>
												</div>
											</div>
										</div>
										<div class="row">
											<div class="col-md-12">
												<div class="progress">
                                        			<div class="progress-bar width-16" role="progressbar" aria-valuenow="1" aria-valuemin="0" aria-valuemax="6">1 of 6</div>
                                                    
                                    			</div>
											</div>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-8">
										<div class="form-group mb-2">
											<input type="submit" name="submit"  value="Previous page"  class="btn btn-secondary waves-effect waves-light width-xl" />
										</div>
									</div>
									<div class="col-md-4">
										<div class="form-group mb-2" align="right">
											<input type="submit" name="submit" value="Save & next page"  class="btn btn-primary waves-effect waves-light width-xl" />
										</div>
									</div>
								</div>
                            </div>
                        </div>
                    </div>
					</form>
				</div>
			</div>
        </div>
    </div>
</main>
<script type="text/javascript" src="/views-js/entry/Financial-Period.js"></script>