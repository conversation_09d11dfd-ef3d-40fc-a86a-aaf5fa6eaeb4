const settings = require('../settings');
const HOLDING_TYPE = settings.substance_business_types.HOLDING;
const IP_TYPE = settings.substance_business_types.INTELLECTUAL_PROPERTY;

exports.validate = function(businessObj, businessType, version) {
    let minorDate = new Date();
    let errors = [];
    minorDate.setFullYear(minorDate.getFullYear() - 18);

    const entryVersion = parseFloat(version);

    if (entryVersion < 5){
        errors = validateBussinessDataV4(businessObj, businessType);
        return errors;
    }


    if (businessType !== HOLDING_TYPE && businessType !== IP_TYPE) {
        const generalErrors = validateBussinessDataV5(businessObj);
        const cigaErrors = validateCigaDataV5(businessObj);
        const outsourcingErrors = checkOutsourcingProviderV5(businessObj)
        errors = [...generalErrors, ...cigaErrors, ...outsourcingErrors];
    }

    if (businessType === HOLDING_TYPE) {
        errors = validateHoldingBussinessDataV5(businessObj);
        
    }

    if (businessType === IP_TYPE) {
        const generalErrors = validateBussinessDataV5(businessObj);
        const ipErrors = validateIntellectualBussinessDataV5(businessObj);
        errors = [...generalErrors, ...ipErrors];
    }

    return errors;
}

function validateBussinessDataV4(businessObj, activityType){
    const errors = [];
    if (activityType == IP_TYPE && businessObj.high_risk_ip == true && businessObj.evidence_high_risk_ip == true) {
        if (businessObj.high_risk_ip_evidence == null || businessObj.high_risk_ip_evidence.length == 0) {
            errors.push({ msg: "Provide evidence that would allow it to contest the rebuttable presumption introduced by ESA section 9(2)(b)" })
        }
    }
    if ((activityType != HOLDING_TYPE && activityType != IP_TYPE) ||
        (activityType == IP_TYPE && (businessObj.high_risk_ip == false || businessObj.evidence_high_risk_ip == true))) {
        if (businessObj.number_of_board_meetings == undefined) {
            errors.push({ msg: "Provide number of boardmeetings", field: "NumberOfBoardMeetings" })
        } else {
            if (businessObj.number_of_board_meetings < 0) {
                errors.push({ msg: "Invalid number of boardmeetings", field: "NumberOfBoardMeetings" })
            }
        }
        if (businessObj.number_or_board_meetings_outside_bvi == undefined) {
            errors.push({ msg: "Provide number of boardmeetings outside the BVI", field: "AmountOfBoardMeetingsOutsideBVI" })
        }
        if (businessObj.total_turnover == undefined) {
            errors.push({ msg: "Provide total turnover", field: "TotalTurnOverFinancialPeriod" })
        }
        checkExpenditure(businessObj, errors);

        if (businessObj.core_income_generating_outsourced) {
            if (!businessObj.outsourced_activity_undertaken_in_BVI && (businessObj.explanation_outsourced_activity_undertaken_in_BVI == undefined || businessObj.explanation_outsourced_activity_undertaken_in_BVI.length == 0)) {
                errors.push({ msg: "Provide explanation for outsourced activity", field: "ExplanationOutsourcedActivityundertakenInBVI" })
            }
            if (businessObj.demonstrate_monitoring_outsourced_activity && (businessObj.explanation_demonstrate_monitoring_outsourced_activity == undefined || businessObj.explanation_demonstrate_monitoring_outsourced_activity.length == 0)) {
                errors.push({ msg: "Provide explanation for monitoring outsourced activity", field: "ExplainMonitoring" })
            }
            if (businessObj.outsourcing_evidence == undefined || businessObj.outsourcing_evidence.length == 0) {
                errors.push({ msg: "Provide outsourcing evidence" })
            }
        }
        if (!businessObj.managers || businessObj.managers.length == 0) {
            errors.push({ msg: "Provide directors" })
        }
    }
    if ((activityType != HOLDING_TYPE && activityType != IP_TYPE) ||
        (activityType == IP_TYPE && (businessObj.high_risk_ip == false || businessObj.evidence_high_risk_ip == true)) ||
        (activityType == HOLDING_TYPE && businessObj.manage_equity_participations)) {
        if (!businessObj.premises || businessObj.premises.length == 0) {
            errors.push({ msg: "Provide premises" })
        }

        if (businessObj.total_employees == undefined) {
            errors.push({ msg: "Provide total number of suitable employees", field: "TotalSuitableFTEInBVI" })
        }

        if (businessObj.full_total_employees == undefined) {
            errors.push({ msg: "Provide total number of employees", field: "TotalEmployees" })
        }
    }
    return errors;

}

function validateBussinessDataV5(businessValues){
    const errors = [];
    
    if (businessValues.gross_income_total === undefined || businessValues.gross_income_total === null || businessValues.gross_income_total < 0) {
        errors.push({ msg: "Provide amount of gross income", field: "activityTotalGrossIncome" });
    }

    if (!businessValues.gross_income_type) {
        errors.push({ msg: "Provide type of gross income", field: "activityTypeOfGrossIncome" });
    }

    if ((businessValues.activity_assets_amount === undefined || businessValues.activity_assets_amount === null)  || !businessValues.activity_assets_type ) {
        errors.push({ msg: "Provide amount and type of assets and premises held", field: "activityAssetsAmount" });
    }

    if (businessValues.activity_netbook_values === undefined || businessValues.activity_netbook_values === null || businessValues.activity_netbook_values < 0) {
        errors.push({ msg: "Provide netbook values of tangible assets", field: "activityNetBookValues" });
    }

    if (businessValues.managers === undefined || businessValues.managers?.length === 0) {
        errors.push({ msg: "Provide at least one person responsible for direction and management" });
    }


    if (businessValues.number_of_board_meetings == undefined) {
        errors.push({ msg: "Provide number of board meetings", field: "NumberOfBoardMeetings" })
    } 
    else if (businessValues.number_of_board_meetings < 0) {
        errors.push({ msg: "Invalid number of board meetings", field: "NumberOfBoardMeetings" });
    }

    if (businessValues.number_of_board_meetings_in_bvi == undefined) {
        errors.push({
            msg: "Provide the number of board meetings that were held in the Virgin Islands where a quorum of directors was physically present.", field: "AmountOfBoardMeetingsInBVI" });
    } 
    else if (businessValues.number_of_board_meetings_in_bvi > 0 ){
        if (businessValues.number_of_board_meetings_in_bvi > businessValues.number_of_board_meetings ){
            errors.push({ msg: "Number of board meetings in the BVI cannot be greater than total number of board meetings", 
                field: "AmountOfBoardMeetingsInBVI" })
        }

        if (businessValues.board_meetings.length === 0) {
            errors.push({ msg: "Provide details of each board meeting" });
        }

        if (businessValues.are_minutes_for_board_meetings == undefined) {
            errors.push({ msg: "Select YES/NO if are minutes for these board meetings", field: "ActivityAreMinutesForBoardMeetings" });
        } 
    }


    if (businessValues.quorum_of_board_meetings === undefined || businessValues.quorum_of_board_meetings === null || businessValues.quorum_of_board_meetings < 0) {
        errors.push({ msg: "Provide a valid number for quorum of board meetings", field: "quorumOfBoardMeetings" });
    }

    if (businessValues.are_quorum_of_directors === undefined) {
        errors.push({ msg: "Select YES/NO if quorum of directors physically present in the Virgin Islands?", 
            field: "ActivityAreMinutesForBoardMeetings" });
    } 

    checkExpenditure(businessValues, errors);
    checkEmployeesV5(businessValues, errors);


    if (!businessValues.premises || businessValues.premises.length == 0) {
        errors.push({ msg: "Please state premises" });
    }else{
        const hasNonePremise = businessValues.premises.find((p) => p.address_line1?.toLowerCase() === 'none');
        const hasPremises = businessValues.premises.find((p) => p.address_line1?.toLowerCase() !== 'none');
        if (hasNonePremise === true && hasPremises === true){
            errors.push({ msg: "There is not allowed add premises if you added None" });
        } 

    }



    return errors;
}

function validateHoldingBussinessDataV5(businessValues) {
    const errors = [];

    if (businessValues.gross_income_total === undefined || businessValues.gross_income_total === null || businessValues.gross_income_total < 0) {
        errors.push({ msg: "Provide amount of gross income", field: "activityTotalGrossIncome" });
    }

    if (!businessValues.gross_income_type) {
        errors.push({ msg: "Provide type of gross income", field: "activityTypeOfGrossIncome" });
    }

    checkExpenditure(businessValues, errors);


    if (businessValues.manage_equity_participations === undefined){
        errors.push({ msg: "Select YES/NO if is the entity actively manage its equity participation", field: "manage_equity_participations" });
        return errors;
    }

    if (businessValues.manage_equity_participations === true) {
        checkEmployeesV5(businessValues, errors);
    }

    if (businessValues.manage_equity_participations === false) {
        if (businessValues.compliant_with_statutory_obligations === undefined) {
            errors.push({ msg: "Select YES/NO if entity comply with its statutory obligations", field: "compliant_with_statutory_obligations" });
        }
    }

    if (!businessValues.premises || businessValues.premises.length == 0) {
        errors.push({ msg: "Provide premises" });
    } else {
        const hasNonePremise = businessValues.premises.find((p) => p.address_line1?.toLowerCase() === 'none');
        const hasPremises = businessValues.premises.find((p) => p.address_line1?.toLowerCase() !== 'none');
        if (hasNonePremise === true && hasPremises === true) {
            errors.push({ msg: "There is not allowed add premises if you added None" });
        }

    }

    return errors;
}

function validateIntellectualBussinessDataV5(businessValues) {
    let errors = [];

    if (businessValues.high_risk_ip === undefined) {
        errors.push({ msg: "Select YES/NO if Is the entity a high-risk intellectual property entity", field: "entityHighRisk" });
    }

    if (businessValues.high_risk_ip === true){
        if (businessValues.evidence_high_risk_ip === undefined){
            errors.push({ msg: "Select YES/NO if the entity wish to provide evidence", field: "providedPresumptionEvidences" });
        } else if (businessValues.evidence_high_risk_ip === true){
            if (businessValues.high_risk_ip_evidence === null || businessValues.high_risk_ip_evidence.length === 0) {
                errors.push({ msg: "Provide evidence that would allow it to contest the introduced presumption as set out in ESA section 9(4)" })
            }
        }

        if (!businessValues.high_risk_gross_income_total){
            errors.push({ msg: "Provide total gross income through Royalties ", field: "highRiskGrossIncome" });
        }

        if (!businessValues.high_risk_gross_income_assets) {
            errors.push({ msg: "Provide total gross income through gains from sale of IP Assets ", field: "highRiskGrossIncomeAssets" });
        }    

        if (!businessValues.high_risk_gross_income_others) {
            errors.push({ msg: "Provide total gross income through others ", field: "highRiskGrossIncomeOthers" });
        }
        
        if (!businessValues.tangible_assets_name) {
            errors.push({ msg: "Provide relevant tangible assets name ", field: "tangibleAssetsName" });
        }

        if (!businessValues.tangible_assets_explanation) {
            errors.push({ msg: "Provide relevant tangible assets explanation ", field: "tangibleAssetsExplanation" });
        }

        if (!businessValues.intangible_assets_decisions) {
            errors.push({
                msg: "Provide the decisions for which each employee is responsible for in respect of the generation of income from the intangible asset ",
                field: "tangibleAssetsName"
            });
        }

        if (!businessValues.intangible_assets_nature) {
            errors.push({
                msg: "Provide the nature and history of strategic decisions ",
                field: "intangibleAssetsNature"
            });
        }

        if (!businessValues.intangible_assets_trading_nature) {
            errors.push({ msg: "Provide the nature and history of the trading activities of intangible assets ", field: "intangibleAssetsTradingNature" });
        }
    }
    
    if (businessValues.high_risk_ip === false){
        if (businessValues.is_other_ciga_legal_entity === undefined) {
            errors.push({
                msg: "Select YES/NO if the legal entity conduct CIGA other than those outlined in section 7(h) of the ESA", field: "otherCigaLegalEntityConductYesNo"
            });
        }

        if (businessValues.is_other_ciga_legal_entity === true) {
            if (businessValues.has_other_ciga_evidences === undefined) {
                errors.push({
                    msg: "Select YES/NO if the entity wish to provide evidence to rebut the presumption as set out in ESA section 9(3)",
                    field: "otherCigaProvideEvidencesYesNo"
                });
            }

            if (businessValues.has_other_ciga_evidences === true) {
                if (!businessValues.other_ciga_ip_asset) {
                    errors.push({
                        msg: "Provide other CIGA, relevant IP asset",
                        field: "otherCigaIPAsset"
                    });
                }

                if (!businessValues.other_ciga_business_details) {
                    errors.push({
                        msg: "Provide other CIGA detail business plans ",
                        field: "otherCigaDetailBusiness"
                    });
                }

                if (!businessValues.other_ciga_decisions) {
                    errors.push({
                        msg: "Provide other CIGA decisions for which each employee is responsible",
                        field: "otherCigaDecisions"
                    });
                }

                if (!businessValues.other_ciga_evidence_details) {
                    errors.push({
                        msg: "Provide other CIGA concrete evidence that decision making is taking place within the Virgin Islands",
                        field: "otherCigaEvidenceDetails"
                    });
                }

                if (businessValues.other_ciga_files === null || businessValues.other_ciga_files?.length === 0) {
                    errors.push({
                        msg: "Please upload evidence files for Other CIGA section"
                    });
                }

            }

        }

        if (businessValues.is_other_ciga_legal_entity === false){
            const cigaErrors = validateCigaDataV5(businessValues);
            errors = [...errors, ...cigaErrors];
        }
    }

    if (businessValues.high_risk_ip !== undefined || businessValues.is_other_ciga_legal_entity !== undefined){
        const outsourcingErrors = checkOutsourcingProviderV5(businessValues);
        errors = [...errors, ...outsourcingErrors];


        if (!businessValues.equipment_nature_description) {
            errors.push({
                msg: "Provide provide a description of the nature of any equipment located within the Virgin Islands used in connection with the relevant activity",
                field: "equipmentNatureDescription"
            });
        }
    }

    return errors;
}


function validateCigaDataV5(businessValues){
    const errors = [];
    if (!businessValues.activity_ciga_core) {
        errors.push({ msg: "Provide CIGA core income type", field: "activityCIGACore" });
    }
    else if (businessValues.activity_ciga_core === "0.2" && !businessValues.activity_ciga_core_other) {
        errors.push({ msg: "Provide CIGA core income other value", field: "activityCIGACoreOther" });
    }
    return errors;
}


function checkExpenditure(businessValues, errors){
    if (businessValues.total_expenditure == undefined || (businessValues.total_expenditure && businessValues.total_expenditure < 0)) {
        errors.push({ msg: "Provide a non-negative value for total of expenditure", field: "TotalExpenditureIncured" });
    }
    if (businessValues.total_expenditure_bvi == undefined || (businessValues.total_expenditure_bvi && businessValues.total_expenditure_bvi < 0)) {
        errors.push({ msg: "Provide a non-negative value for total of expenditure in the BVI", field: "TotalExpenditureIncurredInBVI" });
    }

    if (businessValues.total_expenditure >= 0 && businessValues.total_expenditure_bvi >= 0 && businessValues.total_expenditure_bvi > businessValues.total_expenditure ){
        errors.push({ msg: "The expediture in BVI cannot exceed total expenditure incurred in the operations", field: "TotalExpenditureIncurredInBVI" });
    }
}

function checkEmployeesV5(businessValues, errors){
    if (businessValues.full_total_employees == undefined || businessValues.full_total_employees < 0) {
        errors.push({ msg: "Provide valid total number of employees", field: "TotalEmployees" });
    }

    if (businessValues.total_employees_engaged == undefined || businessValues.total_employees_engaged < 0) {
        errors.push({ msg: "Provide total number of employees engaged", field: "TotalEmployeesEngaged" });
    } else {
        if (businessValues.full_total_employees >= 0 && businessValues.total_employees_engaged > businessValues.full_total_employees) {
            errors.push({
                msg: "The number of employees engaged in the relevant activity cannot exceed overall total number of employees of the entity",
                field: "TotalEmployeesEngaged"
            });
        }
    }

    if (businessValues.total_employees == undefined || businessValues.total_employees < 0) {
        errors.push({ msg: "Provide total number of suitable employees", field: "TotalSuitableFTEInBVI" });
    } else {
        if (businessValues.total_employees_engaged >= 0 && businessValues.total_employees > businessValues.total_employees_engaged) {
            errors.push({
                msg: "The number of employees in Virgin Islands cannot exceed the total number of employees engaged in the relevant activity",
                field: "TotalSuitableFTEInBVI"
            });
        }

        if (businessValues.full_total_employees >= 0 && businessValues.total_employees > businessValues.full_total_employees){
            errors.push({
                msg: "The number of employees in Virgin Islands cannot exceed overall total number of employees of the entity",
                field: "TotalSuitableFTEInBVI"
            });   
        }


        if (businessValues.total_employees >= 0) {
            if(businessValues.employees?.length < businessValues.total_employees) {
                errors.push({ msg: "Provide details of each employee engaged" });
            }

            const diffEmployeesTable = Math.abs(businessValues.employees?.length - businessValues.total_employees);
            if (diffEmployeesTable > 0.5){
                errors.push({ msg: "The number of details provided for each employee cannot exceed the number of engaged employees." });
            }
        }
    }
}

function checkOutsourcingProviderV5(businessValues){
    const errors = []
    if (businessValues.core_income_generating_outsourced === undefined) {
        errors.push({
            msg: "Select YES/NO if has any core income generating activity(CIGA) been outsourced?", field: "activityCIGACore"
        });
    }

    if (businessValues.core_income_generating_outsourced === true && businessValues.outsourcing_providers.length === 0) {
        errors.push({ msg: "Provide details of all outsourcing providers" });
    }

    if (businessValues.outsourcing_total_expenditure === undefined  || businessValues.outsourcing_total_expenditure === null || businessValues.outsourcing_total_expenditure < 0) {
        errors.push({ msg: "Provide outsourcing total expenditure", field: "outsourcingTotalExpenditure" });
    }


    return errors;
}