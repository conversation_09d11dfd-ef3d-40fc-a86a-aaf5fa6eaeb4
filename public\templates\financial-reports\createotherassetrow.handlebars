<tr id="asset-table-row-{{asset._id}}-{{asset.type}}" class="asset-row">
    <td>
        {{asset.description}}
    </td>
    <td {{#ifCond asset.type '===' 'AssetsEndPeriod'}}class="otherStartAssetsValue"{{/ifCond}}>
        {{decimalValue asset.value}}
    </td>
    <td class="text-right">
        <button type="button" class="btn btn-outline-secondary openEditAsset"
                data-id="{{asset._id}}" data-type="{{asset.type}}">
            <i class="fa fa-pencil"></i>
        </button>
    </td>
    <td class="text-left">
        <button type="button" class="delete btn btn-danger deleteAsset"
                data-id="{{asset._id}}" data-type="{{asset.type}}">
            <i class="fa fa-trash"></i>
        </button>
    </td>
</tr>