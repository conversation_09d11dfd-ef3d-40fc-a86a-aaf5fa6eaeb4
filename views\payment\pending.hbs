<script src="/javascripts/libs/bootstrap-table/bootstrap-table.min.js"></script>

<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class='contour'>
                    <form method="POST" class='enquiry' autocomplete="off">
                        <input type="text" hidden name="csrf-token" value="{{csrfToken}}">
                        <div class="container-fluid">
                            <div class="row">
                                <div class="col-12">
                                    <div class="card-box">
                                        <h4 class="mt-3">Overview of all pending payments to Master Client Code :
                                            <B>{{masterclientcode}}</B>
                                        </h4>
                                        <br>
                                        Select the pending payments to proceed
                                        <h5 class="mt-2">Company Incorporations</h5>
                                        <div class="table-responsive">
                                            <table class="table table-striped mb-0 w-100">
                                                <thead class="thead-light">
                                                    <tr>
                                                        <th data-checkbox="true" class="header-10-percent">Select to pay</th>
                                                        <th class="header-38-percent">Entity Name</th>
                                                        <th class="header-30-percent">Submitted</th>
                                                        <th class="header-12-percent">Amount</th>
                                                        <th class="header-10-percent">Download Invoice</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {{#each incorporationSubmissions}}
                                                    <tr>
                                                        <td>
                                                            <div class="custom-control custom-switch">
                                                                <input type="checkbox" class="custom-control-input"
                                                                    id="incorporation-{{@index}}"
                                                                    name='selected_incorporation' value='{{_id}}'
                                                                    data-index="{{@index}}">
                                                                <label class="custom-control-label"
                                                                    for="incorporation-{{@index}}"></label>
                                                            </div>
                                                        </td>
                                                        <td>{{name}}</td>
                                                        <td>{{formatDate submittedAt "MMMM D YYYY h:mm:ss a"}}</td>
                                                        <td style='text-align:right'>$ <span
                                                                id='amount-incorporation-{{@index}}'>{{payment.total}}</span> USD
                                                        </td>
                                                        <td>
                                                            {{#if invoiceNumber}}
                                                            <a href="/masterclients/{{masterClientCode}}/incorporate-company/{{_id}}/invoice.pdf"
                                                                target="_blank"
                                                                class="btn btn-primary waves-effect waves-light">Download</a>
                                                            {{/if}}
                                                        </td>
                                                    </tr>
                                                    {{/each}}
                                                    <tr>
                                                        <th colspan='4'>
                                                            Total
                                                        </th>
                                                        <th class='text-right'>
                                                            $ <span id='totalIncorporationAmount'>0</span> USD
                                                        </th>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div> <!-- end card-box-->
                                </div> <!-- end table-responsive-->
                            </div> <!-- end card-box -->
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="form-group mb-2">
                                        <a href="/masterclients" class="btn btn-secondary waves-effect waves-light">Back</a>

                                        <a href="./payments-history"
                                            class="btn btn-secondary waves-effect waves-light">View History</a>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group mb-2" align="right">
                                        <input type="submit" id='btnSubmit' name="submit" value="Proceed to payment"
                                            class="btn btn-primary waves-effect waves-light" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</main>


<script type="text/javascript" src="/views-js/payment/pending.js"></script>
