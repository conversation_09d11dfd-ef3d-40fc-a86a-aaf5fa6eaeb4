let boardMeetingId = '';
let isEditBoardMeeting = false;
let resetBoardMeetingForm = false;

$('#boardMeetingModal').on('show.bs.modal', function (event) {
    $('#loadingBoardMeeting').hide();
    const button = $(event.relatedTarget); // Button that triggered the modal
    const activityType = button.data('activity-type');
    const entityId = button.data('id');

    boardMeetingId = button.data('board-meeting-id');
    resetBoardMeetingForm = false;
    $("#activityType").val(activityType);

    if (boardMeetingId) {
        isEditBoardMeeting = true;

        const queryString = $.param({
            type: activityType
        });

        $.ajax({
            type: "GET",
            url: `/substance/entry/${entityId}/activity-business?${queryString}`,
            success: (response) => {
                if (response.status === 200) {
                    const boardMeetings = response.data?.board_meetings || [];
                    if(boardMeetings.length > 0){
                        const boardMeeting = boardMeetings.find((b) => b._id === boardMeetingId);
                        if(boardMeeting){
                            $("#meetingNumber").val(boardMeeting.meeting_number);
                            $("#meetingDirectorName").val(boardMeeting.name);
                            $("#meetingRelationToEntity").val(boardMeeting.relation_to_entity);
                            $("#meetingQualification").val(boardMeeting.qualification);

                            if(boardMeeting.physically_present === true){
                                $("#meetingPhysicallyPresentYes").prop('checked', true).trigger('change');
                            }else{
                                $("#meetingPhysicallyPresentNo").prop('checked', true).trigger('change');
                            }
                        }
                    }

                } else {
                    Swal.fire('Error', 'There was an error getting the information.', 'error').then(() => {
                        $('#boardMeetingModal').modal('hide');
                    });
                }
            },
            error: (err) => {
                Swal.fire('Error', 'There was an error getting the information.', 'error').then(() => {
                    $('#boardMeetingModal').modal('hide');
                });
            },
        });
    } else {
        isEditBoardMeeting = false;
    }

    if (isEditBoardMeeting) {
        $("#boardMeetingModalLbl").html('Edit Board Meeting');
    } else {
        $("#boardMeetingModalLbl").html('New Board Meeting');
    }
});

$('#boardMeetingModal').on('hidden.bs.modal', function (event) {
    resetBoardMeetingForm = true;
    $("#submitBoardMeeting").prop('disabled', false);
    $("#boardMeetingForm")[0].reset();
    $("#meetingNumber").val('');
});

$('#boardMeetingForm').on('submit', async function (event) {
    event.preventDefault();
    $("#submitBoardMeeting").prop('disabled', true);

    if (!this.checkValidity()) {
        this.classList.add('was-validated');
        $("#submitBoardMeeting").prop('disabled', false);
        return false;
    }

    $('#submitBoardMeeting').hide();
    $('#loadingBoardMeeting').show();

    const responseSave = await saveBoardMeeting();

    $('#submitBoardMeeting').show();
    $('#loadingBoardMeeting').hide();

    if (responseSave) {
        refreshBoardMeetingTable(responseSave.entryId, responseSave.activityType);

        Swal.fire('Success', 'Board Meeting saved successfully.', 'success').then(() => {
            $('#boardMeetingModal').modal('hide');
        });
    }
    $("#submitBoardMeeting").prop('disabled', false);
});

async function saveBoardMeeting() {
    try {
        const boardMeeting = {
            type: $("#activityType").val(),
            meetingNumber:  $("#meetingNumber").val(),
            directorName: $("#meetingDirectorName").val(),
            physicallyPresent: $("input[name='meetingPhysicallyPresent']:checked").val(),
            relationToEntity: $("#meetingRelationToEntity").val(),
            qualification: $("#meetingQualification").val(),
        };
        
        const response = await $.ajax({
            type: isEditBoardMeeting ? "PUT" : "POST",
            url: `./board-meetings${isEditBoardMeeting ? `/${boardMeetingId}` : ''}`,
            data: JSON.stringify(boardMeeting),
            dataType: "json",
            contentType: "application/json; charset=utf-8",
        });

        if (response.status === 200) {
            return response
        } else {
            const error = response.error || 'Board Meeting could not be saved, please try again later.';
            toastr["warning"](error, 'Error!');
            return false;
        }
    } catch (error) {
        toastr["warning"](error.responseJSON.error || 'Board Meeting could not be saved, please try again later.', 'Error!');
        return false;
    }
}

$(document).on('click', '.deleteBoardMeeting', async function () {
    await deleteBoardMeeting($(this).attr('data-id'),$(this).attr('data-activity-type'),$(this).attr('data-board-meeting-id') )
})

async function deleteBoardMeeting(entryId, activityType, boardMeetingId) {
    const result = await Swal.fire({
        icon: 'warning',
        title: 'Are you sure?',
        showCancelButton: true,
        confirmButtonText: 'Yes, delete it',
        reverseButtons: true,
        showLoaderOnConfirm: true,
        allowOutsideClick: () => !Swal.isLoading(),
        backdrop: true,
        preConfirm: async () => {
            try {
                
                const response = await $.ajax({
                    type: 'DELETE',
                    url: `/substance/entry/${entryId}/board-meetings/${activityType}/${boardMeetingId}`,
                    dataType: 'json'
                });

                if (response.status === 200) {
                   return  response;
                } else {
                    Swal.fire('Error', response.error ? response.error : 'Error deleting the Board Meeting', 'error');
                    return false;
                }
            } catch (error) {
                Swal.fire('Error', 'An error occurred while deleting the Board Meeting', 'error');
                return false;
            }
        }
    });

    if (result.isConfirmed && result.value) {
        refreshBoardMeetingTable(entryId, activityType);
        Swal.fire('Success', result.value?.message ? result.value.message : 'Board Meeting deleted successfully.', 'success');
    } 
}

function refreshBoardMeetingTable(entryId, activityType) {
    let template = Handlebars.templates.boardmeetings;
    const queryString = $.param({ type: activityType });

    
    $.ajax({
        type: "GET",
        url: `/substance/entry/${entryId}/activity-business?${queryString}`,
        success: (response) => {
            if (response.status === 200) {
                let rows = template({
                    boardMeetings: response.data?.board_meetings || [],
                    entryId: entryId,
                    activityType: activityType
                });

                $("#boardMeetingsTableBody").html(rows);
            } else {
                toastr["error"]('There was an error getting the Board Meetings.', 'Error');
            }
        },
        error: (err) => {
            toastr["error"]('There was an error getting the Board Meetings.', 'Error');
        },
    });
}