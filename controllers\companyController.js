const Company = require('../models/company').schema;
const EntryModel = require('../models/entry').EntryModel;
const ArchivedEntryModel = require('../models/entry').ArchivedEntryModel;
const MasterClientCode = require('../models/masterClientCode');
const appInsightsClient = require("applicationinsights").defaultClient;
const pdfController = require('../controllers/pdfController');
const settings = require('../settings');
const moment = require('moment');
const sessionUtils = require('../utils/sessionUtils');

exports.downloadPdf = async function (req, res, next) {
    try {
        const entry = await EntryModel.findById(req.params.entryId);
        if (entry && entry.company === req.params.companyCode) {
            pdfController.generatePdf(entry, res);
        } else {
            const archivedEntry = await ArchivedEntryModel.findById(req.params.entryId);
            if (archivedEntry && archivedEntry.company === req.params.companyCode) {
                pdfController.generatePdf(archivedEntry, res);
            } else {
                throw new Error('Entry not found');
            }
        }
    } catch (err) {
        return next(err);
    }
}

// Display list of all Companys.
exports.company_list = function (req, res, next) {
    //check select masterclient code belongs to logged in user
    MasterClientCode.findOne({ 'code': req.params.masterclientcode }, function (err, masterclientcode) {
        if (err) {
            return next(err);
        }
        if (masterclientcode == null || masterclientcode.owners.indexOf(req.user.email.toLowerCase()) == -1) {
            err = new Error('Masterclient not found');
            err.status = 404;
            return next(err);
        }
        let companyfilter = { masterclientcode: req.params.masterclientcode };
        if (req.query.filter_company && req.query.filter_company.length > 2) {
            companyfilter['name'] = { $regex: req.query.filter_company, $options: 'i' };
        }
        if (req.query.filter_company_code && req.query.filter_company_code.length > 2) {
            companyfilter['incorporationcode'] = { $regex: req.query.filter_company_code, $options: 'i' };
        }
        let query = Company.aggregate([
            {
                '$match': {
                    ...companyfilter,
                    'substanceModule.active': true
                },
            },
            {
                '$lookup': {
                    'from': 'entries',
                    'localField': 'code',
                    'foreignField': 'company',
                    'as': 'entries'
                }
            },
            {
                '$project': {
                    'name': 1,
                    'code': 1,
                    'incorporationcode': 1,
                    'masterclientcode': 1,
                    'entries': {
                        '_id': 1,
                        'scheduled_at': 1,
                        'status': 1,
                        'reopened':1
                    }
                }
            },
            {
                '$sort': { 'name': 1 }
            }
        ]);
        query.exec(function (err, companies) {
            if (err) {
                return next(err)
            }
            for (let i = 0; i < companies.length; i++) {
                companies[i].hasSubmissions = !!companies[i].entries.length;
                if (companies[i].hasSubmissions) {
                    companies[i].inProgressSubmission = companies[i].entries.find((s) => s.status === 'SAVED' || s.status === 'RE-OPEN');
                    companies[i].scheduledSubmission = companies[i].entries.find((s) => s.status === 'SCHEDULED');
                    companies[i].informationRequestSubmission = companies[i].entries.find((s) => s.status === 'INFORMATION REQUEST');

                    if (companies[i].inProgressSubmission && companies[i].inProgressSubmission.status === "RE-OPEN"){
                        const reopened = companies[i].inProgressSubmission.reopened.details.sort((a,b)=>b.date_reopened.getTime()-a.date_reopened.getTime());
                        companies[i].inProgressSubmission.reopenedId =reopened[0]._id;
                    }
                }
            }

            res.render('substance/companies', { title: 'Companies', masterclientcode: req.params.masterclientcode, companies: companies, user: req.user, messages: req.session.messages });
        });
    });
};

exports.select_company = function (req, res, next) {
    //check companycode belongs to logged in user, company.masterclientcode.owners contains loggedin user email
    //select the company from the companylist to store the code in the session for further processing
    const sessData = req.session;
    Company.findOne({'code': req.query.companyCode}, function (err, company) {
        if (err) {
            return next(err);
        }
        if (company == null) {
            err = new Error('Company not found');
            err.status = 404;
            return next(err);
        }
        MasterClientCode.findOne({
            'code': company.masterclientcode,
            'owners': req.user.email.toLowerCase()
        }, function (err, masterclientcode) {
            if (err) {
                return next(err);
            }
            if (masterclientcode == null) {
                err = new Error('Masterclient not found');
                err.status = 404;
                return next(err);
            }

            sessData.company = company;

            if (req.query.entryId){
                res.redirect('/masterclients/' + company.masterclientcode + '/substance/companies/' + req.query.companyCode + '/forms/' + req.query.entryId + "/select");
            }else{
                res.redirect('/masterclients/' + company.masterclientcode + '/substance/companies/' + req.query.companyCode + '/forms');
            }


        });
    });
}

exports.open_company_submissions = function (req, res, next) {
    //check companycode belongs to logged in user, company.masterclientcode.owners contains loggedin user email
    //select the company from the companylist to store the code in the session for further processing
    const sessData = req.session;
    Company.findOne({'code': req.params.companycode}, function (err, company) {
        if (err) {
            return next(err);
        }
        if (company == null) {
            err = new Error('Company not found');
            err.status = 404;
            return next(err);
        }
        MasterClientCode.findOne({
            'code': company.masterclientcode,
            'owners': req.user.email.toLowerCase()
        }, function (err, masterclientcode) {
            if (err) {
                return next(err);
            }
            if (masterclientcode == null) {
                err = new Error('Masterclient not found');
                err.status = 404;
                return next(err);
            }

            sessData.company = company;

            res.redirect('/masterclients/' + company.masterclientcode + '/substance/companies/' + req.params.companycode + '/forms');

        });
    });
}

exports.forms_list = async function (req, res, next) {
    try {
        const dbForms = await EntryModel.find({ 'company': req.params.companyCode }, ['updatedAt', 'status', 'entity_details', 'payment', 'invoice_number', 'reopened', 'createdAt', 'submitted_at'], { sort: '-updatedAt' });
        const dbArchivedForms = await ArchivedEntryModel.find({ 'company': req.params.companyCode }, ['updatedAt', 'status', 'entity_details', 'payment', 'invoice_number', 'reopened', 'createdAt', 'submitted_at'], { sort: '-updatedAt' });
        const forms = [...dbForms, ...dbArchivedForms];
        const sessData = req.session;
        if (forms.length > 0) {
            for (let dbForm of forms) {
                dbForm.allowContinue = false;
                dbForm.allowInvoice = false;
                dbForm.allowEdit = false;
                dbForm.allowDownload = true;
                dbForm.allowDelete = false;
                if (dbForm.entity_details?.financial_period_ends && dbForm.entity_details?.financial_period_ends < new Date(2022, 0, 1)) {
                    dbForm.pre2022Submission = true;
                }
                if (dbForm.status === undefined || dbForm.status === "" || dbForm.status === "SAVED" || dbForm.status === "RE-OPEN") {
                    dbForm.allowContinue = true;
                    dbForm.allowDownload = false;
                }
                if (dbForm.status === 'SCHEDULED') {
                    dbForm.allowEdit = true;
                }

                if (dbForm.invoice_number && dbForm.invoice_number !== '') {
                    dbForm.allowInvoice = true;
                }

                if (dbForm.status === "SAVED" && (!dbForm.payment?.payment_received_at && !dbForm.reopened?.details?.length > 0)){
                    dbForm.allowDelete = true;
                }

                if (dbForm.status === 'RE-OPEN' && dbForm.reopened?.details?.length > 0){
                    const reopened = dbForm.reopened.details.sort((a,b)=>b.date_reopened.getTime()-a.date_reopened.getTime());
                    dbForm.reopenedId =reopened[0]._id;
                }
            }
        }
        res.render('substance/forms', { title: 'Company overview', forms, company: sessData.company, user: req.user, messages: req.session.messages });
    } catch (err) {
        return next(err);

    }
};

exports.create_form = async function (req, res) {
    try {
        //check companycode belongs to logged in user, company.masterclientcode.owners contains loggedin user email
        const sessData = req.session;
        const companyCode = req.params.companycode;

        const company = await Company.findOne({'code': companyCode, 'masterclientcode': req.params.masterclientcode});

        if (!company) {
            return res.status(404).json({status: 404, error: "Company not found" });
        }

        const mcc = MasterClientCode.findOne({'code': company.masterclientcode, 'owners': req.user.email.toLowerCase()});

        if (!mcc) {
            return res.status(404).json({status: 404, error: "Masterclient not found" });
        }

        if (!company.incorporationdate) {
            return res.status(400).json({status: 400, error: "It seems amendments are needed in a prior submission. " +
                  "Please contact your Trident Officer for assistance." });
        }

        sessData.company = company;

        let previousSubmissions = await EntryModel.find({
            'status': {"$ne": "SAVED"},
            'company': companyCode
        }, {_id: 1, company: 1, status: 1, entity_details: 1});

        let latestSubmission;

        let newFinancialPeriodStartDate;
        let newFinancialPeriodEndDate;
        const validation_date = moment('01/01/2019', 'DD/MM/YYYY').toDate();


        // Case Has previous submissions

        if (previousSubmissions.length > 0) {
            previousSubmissions = previousSubmissions.sort((a, b) => {
                return new Date(b.entity_details.financial_period_ends) - new Date(a.entity_details.financial_period_ends);
            });
            latestSubmission = previousSubmissions[0];

            // Case company ITA date === true
            if (company.hasITADate === true) {
                const itaEndDate = moment(company.approvedITAEndDate).utc().format('DD/MM');
                const endDate = moment(latestSubmission.entity_details.financial_period_ends).utc()
                  .format('DD/MM');


                if (itaEndDate === endDate) {
                    newFinancialPeriodStartDate = getNewFinancialPeriodDateStr(latestSubmission.entity_details.financial_period_ends, true);
                    newFinancialPeriodEndDate = getNewFinancialPeriodDateStr(newFinancialPeriodStartDate, false);
                } else {
                    if(company.approvedITAStartDate > latestSubmission.entity_details.financial_period_ends){
                        newFinancialPeriodStartDate = company.approvedITAStartDate;
                        newFinancialPeriodEndDate = company.approvedITAEndDate;
                    }else{
                        return res.status(400).json({status: 400, error: "It seems amendments are needed in a prior submission. " +
                              "Please contact your Trident Officer for assistance." });
                    }
                }
            } else {
                // Case company ITA date === false and incorporation date < 01/01/2019
                if (company.incorporationdate < validation_date) {
                    const startDate = moment(latestSubmission.entity_details.financial_period_begins).utc()
                      .format('DD/MM');
                    const endDate = moment(latestSubmission.entity_details.financial_period_ends).utc()
                      .format('DD/MM');

                    if (startDate === '30/06' && endDate === '29/06') {
                        newFinancialPeriodStartDate = getNewFinancialPeriodDateStr(latestSubmission.entity_details.financial_period_ends, true);
                        newFinancialPeriodEndDate = getNewFinancialPeriodDateStr(newFinancialPeriodStartDate, false);
                    } else {
                        return res.status(400).json({status: 400, error: "It seems amendments are needed in a prior submission. " +
                              "Please contact your Trident Officer for assistance." });
                    }
                }
                // Case company ITA date === false and incorporation date >= 01/01/2019
                else {
                    const startDate = moment(latestSubmission.entity_details.financial_period_begins).utc()
                      .format('DD/MM');
                    const endDate = getNewFinancialPeriodDateStr(latestSubmission.entity_details.financial_period_begins, false);
                    const financialPeriodEndDate = moment(latestSubmission.entity_details.financial_period_ends).utc().format('YYYY-MM-DD');

                    if (startDate === moment(company.incorporationdate).utc().format('DD/MM') && financialPeriodEndDate === endDate) {
                        newFinancialPeriodStartDate = getNewFinancialPeriodDateStr(latestSubmission.entity_details.financial_period_ends, true);
                        newFinancialPeriodEndDate = getNewFinancialPeriodDateStr(newFinancialPeriodStartDate, false);
                    } else {
                        return res.status(400).json({status: 400, error: "It seems amendments are needed in a prior submission. " +
                              "Please contact your Trident Officer for assistance." });
                    }
                }
            }
        }
        else {
            // Case company ITA date === true
            if (company.hasITADate === true) {
                newFinancialPeriodStartDate = company.approvedITAStartDate;
                newFinancialPeriodEndDate = company.approvedITAEndDate;

            } else {
                // Case company ITA date === false and incorporation date < 01/01/2019
                if (company.incorporationdate < validation_date) {
                    newFinancialPeriodStartDate = '2019-06-30';
                    newFinancialPeriodEndDate = '2020-06-29' ;
                }
                // Case company ITA date === false and incorporation date >= 01/01/2019
                else {
                    newFinancialPeriodStartDate = company.incorporationdate;
                    newFinancialPeriodEndDate = getNewFinancialPeriodDateStr(company.incorporationdate, false);
                }
            }
        }

        const validationV5Date = moment('31/12/2022', 'DD/MM/YYYY').toDate()
        const isEndDateGt2022 = moment(newFinancialPeriodEndDate).utc() >= validationV5Date

        const entry = new EntryModel({
            company: companyCode,
            company_data: company,
            entity_details: {
                financial_period_begins: newFinancialPeriodStartDate,
                financial_period_ends: newFinancialPeriodEndDate,
            },
            status: 'SAVED',
            created_by: req.user.email,
            requested_information: {details: []},
            client_returned_information: { details: [] },
            financial_period_changes: { details: [] },
            reopened: { details: [] },
            version: isEndDateGt2022 === true ? "5.0" : "4.5"
        });
        // store generated database id in the session for further retrieval
        entry.save(function (err, savedEntry) {
            if (err) {
                console.log(err);
                throw new Error('Error creating new submission');
            }
            // Successful, so render.
            req.session.entryId = savedEntry.id;
            appInsightsClient.trackEvent({
                name: "new submission created",
                properties: {email: req.user.email, companycode: company.code, submissionId: savedEntry.id}
            });
            return res.status(200).json({status: 200, entryId: savedEntry.id });
        });

    }catch (e) {
        console.log(e);
        return res.status(500).json({status: 500, error: "Internal Server Error"})
    }

};

exports.select_form = async function (req, res, next) {
    try {
        let entry = await EntryModel.findById(req.params.entryId);

        if (entry == null || (entry.company !== req.params.companyCode)) { // No results.
            const err = new Error('Entry not found');
            err.status = 404;
            return next(err);
        }

        if (entry.status === 'SCHEDULED') {
            entry = await EntryModel.findByIdAndUpdate(req.params.entryId, {status: 'SAVED', scheduled_at: null, currentStepForm: 'financial-period' }, {new: true});
        }

        
        if (entry.entity_details?.financial_period_ends) {
            
            const newVersion = (entry.entity_details.financial_period_ends < moment.utc('2022-12-31').toDate()) ? "4.5" : "5.0";
            if(newVersion !== entry.version){
                const updatedObj = {
                    version: newVersion,
                    currentStepForm: null
                }
                
                entry = await EntryModel.findByIdAndUpdate(req.params.entryId, updatedObj, { new: true });

            }
            
        }

        if (entry.status === undefined || entry.status === "" || entry.status === "SAVED" || entry.status === "RE-OPEN") {


            const sessData = req.session;
            sessData.entryId = req.params.entryId;

            appInsightsClient.trackEvent({name: "submission re-opened",
                properties: {
                    email: req.user.email,
                    companycode: sessData.company.code,
                    submissionId: req.params.entryId
                }
            });

            //clear none relevant activity if has other activity for old versions
            if (entry.reopened?.details?.length > 0 && entry.version < '4.0') {
                let relevantActivities = entry.relevant_activities;

                if (relevantActivities && (relevantActivities.banking_business.selected || relevantActivities.insurance_business.selected ||
                  relevantActivities.fund_management_business.selected || relevantActivities.finance_leasing_business.selected  ||
                  relevantActivities.headquarters_business.selected  ||  relevantActivities.shipping_business.selected ||
                  relevantActivities.holding_business.selected ||  relevantActivities.intellectual_property_business.selected ||
                  relevantActivities.service_centre_business.selected)){
                    relevantActivities.none = {
                        selected: false,
                        part_of_financial_period: false,
                        financial_periods: []
                    };
                    relevantActivities.none_remarks = "";
                    relevantActivities.evidence_none_activities = []
                }

                await EntryModel.findByIdAndUpdate(req.params.entryId, {"$set": {"relevant_activities": relevantActivities}})
            }

            if (entry.currentStepForm !== null) {
                const existsCurrentStep = settings.validateSubstanceStep(entry.currentStepForm);
                
                if (parseFloat(entry.version) < 5 && entry.currentStepForm === 'entity-details'){
                    entry.currentStepForm = 'financial-period'
                }
                const url = '/substance/entry/' + req.params.entryId + "/" + (existsCurrentStep ? entry.currentStepForm : "financial-period");
                res.redirect(url);
            } else {
                res.redirect('/substance/entry/' + req.params.entryId + `/financial-period`);
            }

        }

    } catch (e) {
        console.log("err ", e);
        const err = new Error('Internal server error');
        err.status = 500;
        return next(err);
    }
};


exports.delete_form = function (req, res, next) {
    const sessData = req.session;
    EntryModel.findById(req.body.entryId, function (err, entry) {
        if (err) {
            return next(err);
        }
        if (entry == null || (entry.company !== sessData.company.code)) { // No results.
            err = new Error('Entry not found');
            err.status = 404;
            return next(err);
        }

        if (entry.status === undefined || entry.status === "" || entry.status === "SAVED") {

            if (entry.payment?.payment_received_at || entry.reopened?.details?.length > 0){
                err = new Error('Submission cannot be deleted');
                err.status = 400;
                return next(err);
            }

            EntryModel.deleteOne({_id: req.body.entryId, company: sessData.company.code}, function (err) {
                if (err) {
                    return next(err);
                }
                appInsightsClient.trackEvent({
                    name: "submission deleted",
                    properties: {
                        email: req.user.email,
                        companycode: sessData.company.code,
                        submissionId: req.body.entryId
                    }
                });
                return res.redirect('/masterclients/' + req.params.masterclientcode + '/substance/companies/' + sessData.company.code + '/forms');
            });
        } else {
            return res.redirect('/masterclients/' + req.params.masterclientcode + '/substance/companies/' + sessData.company.code + '/forms');
        }
    });
}

exports.ensureAuthenticated = function (req, res, next) {
    if ((req.user && req.session.id == req.user.sessionId) && req.session.auth2fa) {
        let sessData = req.session;
        //check if compancode in session is the same as the company code in the url
        if (!req.params.companyCode || req.params.companyCode == sessData.company.code) {
            next();
        } else {
            req.logout(function (err) {
                if (err) { return next(err) }
                req.session.destroy(function () {
                    // cannot access session here
                    sessionUtils.onSessionDestroyed(req, res);
                });
            });
        }
    } else if ((req.user && req.session.id == req.user.sessionId) && !req.session.auth2fa) {
        if (req.user.secret_2fa) {
            res.redirect('/users/2fa-code');
        } else {
            res.redirect('/users/2fa-setup');
        }
    } else {
        req.logout(function (err) {
            if (err) { return next(err) }
            req.session.destroy(function () {
                // cannot access session here
                sessionUtils.onSessionDestroyed(req, res);
            });
        });
    }
};


exports.validateMasterClient = async function (req, res, next) {
    const masterclient = await MasterClientCode.findOne({'code': req.params.masterclientcode});
    if (masterclient == null || masterclient.owners.indexOf(req.user.email.toLowerCase()) == -1) {
        let err = new Error('Masterclient not found');
        err.status = 404;
        return next(err);
    }
    return next();
}

exports.downloadInvoicePdf = async function (req, res, next) {
    try {
        const company = await Company.findOne({
            code: req.params.companycode,
            masterclientcode: req.params.masterclientcode
        });

        if (!company) {
            const err = new Error('Company not found');
            err.status = 404;
            return next(err);
        }


        const entry = await EntryModel.findOne({"_id": req.params.entryId, "company": company.code});
        if (!entry) {
            const archivedEntry = await ArchivedEntryModel.findById({"_id": req.params.entryId, "company": company.code});
            if (!archivedEntry) {
                const err = new Error('Entry not found');
                err.status = 404;
                return next(err);
            } else {
                pdfController.generateInvoicePdf(archivedEntry, res);
            }
        } else {
            pdfController.generateInvoicePdf(entry, res);
        }
    } catch (e) {
        console.log("error ", e);
        const err = new Error('Internal Server Error');
        err.status = 502;
        return next(err);
    }
};

exports.getReopenedInfo = async function(req, res) {
    try {
        const company = await Company.findOne({
            code: req.params.companycode,
            masterclientcode: req.params.masterclientcode
        });

        if (!company) {
            return res.status(404).json({status: 404, error: "Company not found"});
        }


        let entry = await EntryModel.findById(req.params.entryId);

        if (!entry) {
            return res.status(404).json({status: 404, error: "Entry not found"});
        }
        let reopenedData;

        if (entry.reopened?.details?.length > 0){
            reopenedData = entry.reopened.details.find((r) => r._id && r._id?.toString() === req.params.reopenedId);
        }

        if (reopenedData){
            return res.status(200).json({status: 200, reopenedData:reopenedData});

        }else{
            return res.status(404).json({status: 400, error: "Reopened data not found"});
        }
    } catch (e) {
        console.log(e);
        return res.status(500).json({status: 500, error: "Internal Server Error"});
    }
};


function  getNewFinancialPeriodDateStr(date, isStartDate) {
    let newDate;
    if (isStartDate){
        newDate = moment(date).utc().add('1', 'days').format('YYYY-MM-DD');
    }else{
        newDate = moment(date, 'YYYY-MM-DD').utc().add('1', 'years')
          .subtract(1, "days").format('YYYY-MM-DD');
    }
    return newDate;
}

