
let isEditBankAccount = false;
let bankAccountId = "";
let invalidClosingBalance = false;


$('#newCashTransactionModal').on('shown.bs.modal', async function (event) {
    const button = $(event.relatedTarget); // Button that triggered the modal
    
    bankAccountId = button.data('bank-account-id');
    isEditBankAccount = bankAccountId ? true : false;
    invalidClosingBalance = false;
    $('#loadingCashTransactionBtn').hide();
    const currency = $("#reportCurrency").val() || "USD";
    $("#cashTransactionCurrency").val(currency).trigger("change");

    if (bankAccountId) {
        const queryString = $.param({ bankAccountId: bankAccountId});
        $("#submitCashTransactionBtn").prop('disabled', true);
        await $.ajax({
            type: "GET",
            url: `${window.location.href}/bank-accounts?${queryString}`,
            success: (response) => {
               
                if (response.status === 200) {
                    
                    setCashTransactionFormValues(response.data.bankAccount)
                } else {
                    Swal.fire('Error', 'There was an error getting the information.', 'error').then(() => {
                        $('#newCashTransactionModal').modal('hide');
                    });
                }
                $("#submitCashTransactionBtn").prop('disabled', false);
            },
            error: (err) => {
                $("#submitCashTransactionBtn").prop('disabled', false);
                Swal.fire('Error', 'There was an error getting the information.', 'error').then(() => {
                    $('#boardMeetingModal').modal('hide');
                });
            },
        });
    }

    if (isEditBankAccount) {
        $("#bankAccountModalLbl").html('Edit Cash/Bank Account');
        if($('#cashTransactionCurrency').val() === $("#reportCurrency").val()) {
            $('#foreingExchangeRateRows').hide()
            $("#foreingExchangeRate").val('1.00');
        } else {
            $('#foreingExchangeRateRows').show()
            if($('#foreingExchangeRate').val() === '') {
                $("#foreingExchangeRate").val('');
            }
            
        }
    } else {
        $('#foreingExchangeRateRows').hide()
        $("#bankAccountModalLbl").html('New Cash/Bank Account');
    }
});


$('#newCashTransactionModal').on('hidden.bs.modal', function () {
    $("#submitCashTransactionBtn").prop('disabled', false);
    $("#closingBalancePerBankError").text(`The Cash in Bank is not reconciled by 0`);
    $("#closingBalancePerBankError").hide();
    $("#newCashTransactionForm").trigger("reset").removeClass('was-validated')
    isEditBankAccount =  false;
    invalidClosingBalance = false;
});

$('#newCashTransactionForm').on('submit', async function (event) {
    event.preventDefault();
    console.log('HOLAA')
    $("#submitCashTransactionBtn").prop('disabled', true);

    if (!this.checkValidity()) {
        this.classList.add('was-validated');
        toastr["warning"]('Missing values', 'Error!');
        $("#submitCashTransactionBtn").prop('disabled', false);
        return false;
    }
    calculateCashBalance();
    if(invalidClosingBalance){
        toastr["warning"]('The Cash in Bank is not reconciled with the closing amount', 'Error!');
        $("#submitCashTransactionBtn").prop('disabled', false);
        return false;
    }

    $('#submitCashTransactionBtn').hide();
    $('#loadingCashTransactionBtn').show();

    const responseSave = await saveBankAccount();


    $('#submitCashTransactionBtn').show();
    $('#loadingCashTransactionBtn').hide();

    if (responseSave && responseSave.status === 200) {
        refreshBankAccountsTable();

        Swal.fire('Success', 'Cash/Bank Account saved successfully.', 'success').then(() => {
            $('#newCashTransactionModal').modal('hide');
        });
    }
    $("#submitCashTransactionBtn").prop('disabled', false);
});


$("#cashTransactionCurrency").select2({
    dropdownParent: $('#newCashTransactionModal .modal-content')
});

$("#cashTransactionCurrency").on('change', function(){
    const val = $(this).val();
    if (val === $('#reportCurrency').val()) {
        $('#foreingExchangeRateRows').hide()
        $("#foreingExchangeRate").val('1.00');
    } else {
        $('#foreingExchangeRateRows').show()
        $("#foreingExchangeRate").val('');
    }
    calculateCashBalance();
})

$(document).on('change paste keyup', '.bank-transaction-value', function () {
    calculateCashBalance();
});

$(document).on('change paste keyup', '#foreingExchangeRate', function () {
    calculateCashBalance();
});

$(document).on('change paste keyup', '#closingAmount', function () {
    calculateCashBalance();
});

$(document).on('change paste keyup', '#openingAmount', function () {
    calculateCashBalance();
});

async function saveBankAccount() {
    try {
        const bankAccount = $("#newCashTransactionForm").serializeJSON();

        const response = await $.ajax({
            type: isEditBankAccount ? "PUT" : "POST",
            url: `${window.location.href}/bank-accounts${isEditBankAccount ? `/${bankAccountId}` : ''}`,
            data: JSON.stringify(bankAccount),
            dataType: "json",
            contentType: "application/json; charset=utf-8",
        });

        if (response.status === 200) {
            return response
        } else {
            const error = response.error || 'Cash/Bank Account could not be saved, please try again later.';
            toastr["warning"](error, 'Error!');
            return false;
        }
    } catch (error) {
        if (error.responseJSON?.formErrors) {
            error.responseJSON.formErrors.forEach((formError) => {
                toastr["warning"](formError, 'Error!');
            })
        }else{
            toastr["warning"](error.responseJSON.error || 'Cash/Bank Account  could not be saved, please try again later.', 'Error!');
            return false;
        }

    }
}

function setCashTransactionFormValues(bankAccount){
    $("#bankDescription").val(bankAccount.description);
    $("#bankAccountType").val(bankAccount.accountType).trigger('change');
    $("#openingAmount").val(showDecimalValue(bankAccount.openingAmount));
    $("#closingAmount").val(showDecimalValue(bankAccount.closingAmount));
    $("#assetsBankTransfers").val(showDecimalValue(bankAccount.assets?.bankTransfers));
    $("#assetsInvestmentsAcquisition").val(showDecimalValue(bankAccount.assets?.investmentsAcquisition));
    $("#assetsInvestmentsSale").val(showDecimalValue(bankAccount.assets?.investmentsSale));
    $("#assetsTangibleAcquisition").val(showDecimalValue(bankAccount.assets?.tangibleAcquisition));
    $("#assetsTangibleSale").val(showDecimalValue(bankAccount.assets?.tangibleSale));
    $("#assetsIntangibleAcquisition").val(showDecimalValue(bankAccount.assets?.intangibleAcquisition));
    $("#assetsIntangibleSale").val(showDecimalValue(bankAccount.assets?.intangibleSale));
    $("#assetsOtherAcquisition").val(showDecimalValue(bankAccount.assets?.otherAcquisition));
    $("#assetsOtherSale").val(showDecimalValue(bankAccount.assets?.otherSale));
    $("#assetsLoanReceivablePaid").val(showDecimalValue(bankAccount.assets?.loanReceivablePaid));
    $("#assetsLoanReceivableReceived").val(showDecimalValue(bankAccount.assets?.loanReceivableReceived));
    $("#assetsReceivablesPaid").val(showDecimalValue(bankAccount.assets?.receivablesPaid));
    $("#assetsReceivablesReceived").val(showDecimalValue(bankAccount.assets?.receivablesReceived));
    $("#liabilitiesAccountsPayableReceived").val(showDecimalValue(bankAccount.liabilities?.accountsPayableReceived));
    $("#liabilitiesAccountsPayablePaid").val(showDecimalValue(bankAccount.liabilities?.accountsPayablePaid));
    $("#liabilitiesLoansPayableReceived").val(showDecimalValue(bankAccount.liabilities?.loansPayableReceived));
    $("#liabilitiesLoansPayablePaid").val(showDecimalValue(bankAccount.liabilities?.loansPayablePaid));
    $("#expensesCompAdminFees").val(showDecimalValue(bankAccount.expenses?.compAdminFees));
    $("#expensesPortMngmntFees").val(showDecimalValue(bankAccount.expenses?.portMngmntFees));
    $("#expensesBankFeesCharges").val(showDecimalValue(bankAccount.expenses?.bankFees));
    $("#expensesLoanInterest").val(showDecimalValue(bankAccount.expenses?.loanInterest));
    $("#expensesIncomeTax").val(showDecimalValue(bankAccount.expenses?.incomeTax));
    $("#bankOtherExpenses").val(showDecimalValue(bankAccount.expenses?.otherExpenses));
    $("#incomeDividendReceived").val(showDecimalValue(bankAccount.income?.dividendReceived));
    $("#incomeCouponInterestReceived").val(showDecimalValue(bankAccount.income?.couponInterestReceived));
    $("#incomeLoanInterestReceived").val(showDecimalValue(bankAccount.income?.loanInterestReceived));
    $("#incomeBankInterestReceived").val(showDecimalValue(bankAccount.income?.bankInterestReceived));
    $("#bankOtherIncome").val(showDecimalValue(bankAccount.income?.otherIncome));
    $("#equityPaymentToShareholder").val(showDecimalValue(bankAccount.equity?.paymentToShareholder));
    $("#equityReceiptsFromShareholder").val(showDecimalValue(bankAccount.equity?.receiptsFromShareholder));
    $("#equityCapitalContribution").val(showDecimalValue(bankAccount.equity?.capitalContribution));
    $("#closingBalancePerBankStatement").val(showDecimalValue(bankAccount.closingBalancePerBank));
    $("#cashAtBankTotal").val(bankAccount.cashAtBank  !== null ?  showDecimalValue(bankAccount.cashAtBank) : '');
    $("#cashTransactionCurrency").val(bankAccount.transactionCurrency).trigger('change');
    $("#foreingExchangeRate").val( bankAccount.foreingExchangeRate !== null ? showDecimalValue(bankAccount.foreingExchangeRate, 4) : '').trigger('keyup');

}

function calculateCashBalance() {
    const openingAmount = parseFloat($("#openingAmount").val().replace(/,/g, '')) ?? 0;
    const closingAmount = parseFloat($("#closingAmount").val().replace(/,/g, '')) ?? null;
    const foreingExchangeRate = parseFloat($("#foreingExchangeRate").val().replace(/,/g, '')) ?? 0;
    let closingBalancePerBankStatement = 0;
    let cashAtBankTotal = 0;

    $(".bank-transaction-value").each(function () {
        const item = $(this)
        const itemValue = item.val()
        const value = parseFloat(itemValue.replace(/,/g, '')) || 0;
        closingBalancePerBankStatement += value;
    });
    closingBalancePerBankStatement = closingBalancePerBankStatement +openingAmount;
    cashAtBankTotal = closingBalancePerBankStatement * foreingExchangeRate;

    $("#closingBalancePerBankStatement").val(showDecimalValue(closingBalancePerBankStatement));
    $("#cashAtBankTotal").val(showDecimalValue(cashAtBankTotal));

    if (isNaN(closingAmount) || closingAmount === "" || closingAmount !== closingBalancePerBankStatement){
        const diffValue = closingAmount ? closingAmount - closingBalancePerBankStatement : closingBalancePerBankStatement;
        invalidClosingBalance = true;
        $("#closingBalancePerBankError").text(`The Cash in Bank is not reconciled by ${ showDecimalValue(diffValue) }`);
        $("#closingBalancePerBankError").show()
    }else{
        invalidClosingBalance = false;
        $("#closingBalancePerBankError").hide()
    }
}


