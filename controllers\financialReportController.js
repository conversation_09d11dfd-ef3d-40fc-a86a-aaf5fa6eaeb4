const httpConstants = require('http2').constants;
const MasterClientCode = require('../models/masterClientCode');
const Company = require('../models/company').schema;
const FinancialReportModel = require('../models/financial-report');
const ConfigurationModel = require('../models/configuration');
const uploadController = require('./uploadController');
const pdfController = require('./pdfController');
const { CURRENCIES } = require('../utils/constants');
const { REPORT_STATUS, ACCOUNTING_SERVICE_TYPES, ACCOUNTING_FORM_STEPS } = require('../utils/financialReportConstants');
const utils = require('../utils/utils')
const invoiceGenerator = require('../utils/invoiceGenerator');
const financialReportValidator = require('../validators/financialReportValidator').validateFinancialReport;
const bankAccountValidator = require('../validators/financialReportValidator').validateBankAccountDetails;
const moment = require('moment');
const { v4: uuidv4 } = require('uuid');
const verifyExtFile = require('../utils/verifyExtensionFile');

exports.validateMccAndCompany = async function (req, res, next) {
  try {
    const sessData = req.session;
    const masterClient = await MasterClientCode.findOne({ 'code': req.params.masterclientcode });

    if (masterClient == null || masterClient.owners.indexOf(req.user.email.toLowerCase()) === -1) {
      return res.render('error', { status: 404, message: "Masterclient not found" });
    }

    if (req.params.companyCode) {
      const company = await Company.findOne({ 'code': req.params.companyCode, 'masterclientcode': req.params.masterclientcode });
      if (!company) {
        return res.render('error', { status: 404, message: "Company not found" });
      }
      else {
        sessData.company = company;
      }
    }
    return next();
  } catch (e) {
    console.log(e);
    return res.render('error', { status: 500, message: "Internal server error" });
  }
};

// Display list of all Companys.
exports.getDashboard = async function (req, res, next) {
  try {
    let companyfilter = { masterclientcode: req.params.masterclientcode };
    if (req.query.filter_company && req.query.filter_company.length > 2) {
      companyfilter['name'] = { $regex: req.query.filter_company, $options: 'i' };
    }
    if (req.query.filter_company_code && req.query.filter_company_code.length > 2) {
      companyfilter['incorporationcode'] = { $regex: req.query.filter_company_code, $options: 'i' };
    }


    let companies = await Company.aggregate([
      {
        '$match': {
          ...companyfilter,
          'accountingRecordsModule.active': true
        },
      },
      {
        '$project': {
          'name': 1,
          'code': 1,
          'incorporationCode': '$incorporationcode',          
          'masterClientCode': '$masterclientcode',
          'incorporationdate': 1
        }
      },
      {
        '$sort': { 'name': 1 }
      },
      {
        '$lookup': {
          from: 'financialreportsbspls',
          localField: '_id',
          foreignField: 'companyData._id',
          as: 'financialReports'
        }
      }
    ]);

    for (let i = 0; i < companies.length; i++) {
      const reports = companies[i].financialReports.filter((r) => r.status !== REPORT_STATUS.DELETED);
      companies[i].hasFinancialReport = reports.length > 0;

      if (companies[i].hasFinancialReport) {
        const inProgressReport = reports.find((r) => r.status === REPORT_STATUS.SAVED || r.status === REPORT_STATUS.IN_PROGRESS || r.status === REPORT_STATUS.RE_OPEN);
        const requestInfoReport = reports.find((r) => r.status === REPORT_STATUS.REQUEST_INFO);
        const helpInProgress = reports.find((r) => r.status === REPORT_STATUS.HELP_PROGRESS ||  r.status === REPORT_STATUS.REQUEST_HELP);

        if (requestInfoReport) {
          companies[i].rfiReport = requestInfoReport._id;
        }

        if (helpInProgress) {
          companies[i].helpInProgress = {
            _id: helpInProgress._id,
            status: helpInProgress.status
          }
        }

        let latestReport;
        companies[i].lockedByPenalty = reports.find((r) => r.status === REPORT_STATUS.IN_PENALTY);

        if (inProgressReport) {
          companies[i].inProgressReport = inProgressReport;
          
          latestReport = {
            _id: inProgressReport._id,
            financialPeriod: inProgressReport.financialPeriod,
            status: inProgressReport.status

          }


          if (companies[i].inProgressReport && companies[i].inProgressReport.status === REPORT_STATUS.RE_OPEN && companies[i].inProgressReport.reopened?.details?.length > 0 ) {
            const reopened = companies[i].inProgressReport.reopened?.details.sort((a, b) => b.reopenedAt.getTime() - a.reopenedAt.getTime());
            companies[i].inProgressReport.reopenedId = reopened[0]._id;
          }

        } else {
          reports.sort((a, b) => b.financialPeriod.end - a.financialPeriod.end);
          latestReport = {
            _id: reports[0]._id,
            financialPeriod: reports[0].financialPeriod,
            status: reports[0].status
          }
        }

        if( latestReport?.status === REPORT_STATUS.HELP_COMPLETED){
          companies[i].helpCompleted = latestReport._id;
        }

        companies[i].latestReport = latestReport;

      }
    }

    res.render('financial-reports/dashboard', {
      title: 'Financial reports',
      masterClientCode: req.params.masterclientcode,
      companies: companies,
      user: req.user,
      messages: req.session.messages
    });
  } catch (e) {
    console.log(e);
    return next(e);
  }
};


exports.createNewFinancialReport = async function (req, res, next) {
  try {
    const companyCode = req.params.companyCode;
    const company = await Company.findOne({ 'code': companyCode, 'masterclientcode': req.params.masterclientcode });
    if (!company) {
      let err = new Error('Company not found');
      err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
      return next(err);
    }

    const isFirstReport = req.body.isFirstReport
    let startPeriod;
    let endPeriod;
    let invalidPeriodErrors = [];
    let updateCompany = false;
    let companyAccountingModule = company.accountingRecordsModule ? company.accountingRecordsModule.toObject() : {};
    let previousReports = await FinancialReportModel.find({
      masterClientCode: company.masterclientcode,
      status: { "$ne": REPORT_STATUS.DELETED },
      "companyData.code": company.code
    }, { _id: 1, financialPeriod: 1 });

    if (!company.accountingRecordsModule || !company.accountingRecordsModule?.selfServiceCompleteAnnualReturnAmount ||
      !company.accountingRecordsModule?.selfServicePrepareAnnualReturnAmount ||
      !company.accountingRecordsModule?.tridentServiceCompleteAnnualReturnAmount ||
      !company.accountingRecordsModule?.tridentServiceDropAccountingRecordsAmount
    ) {
      let configuration = await ConfigurationModel.findOne({});
      companyAccountingModule = {
        ...companyAccountingModule,
        active: company.accountingRecordsModule?.active ?? false,
        selfServiceCompleteAnnualReturnAmount:  company.accountingRecordsModule?.selfServiceCompleteAnnualReturnAmount? company.accountingRecordsModule?.selfServiceCompleteAnnualReturnAmount : configuration.financialReportConfiguration?.selfServiceCompleteAnnualReturnAmount,
        selfServicePrepareAnnualReturnAmount:  company.accountingRecordsModule?.selfServicePrepareAnnualReturnAmount? company.accountingRecordsModule?.selfServicePrepareAnnualReturnAmount : configuration.financialReportConfiguration?.selfServicePrepareAnnualReturnAmount,
        tridentServiceCompleteAnnualReturnAmount:  company.accountingRecordsModule?.tridentServiceCompleteAnnualReturnAmount? company.accountingRecordsModule?.tridentServiceCompleteAnnualReturnAmount : configuration.financialReportConfiguration?.tridentServiceCompleteAnnualReturnAmount,
        tridentServiceDropAccountingRecordsAmount:  company.accountingRecordsModule?.tridentServiceDropAccountingRecordsAmount? company.accountingRecordsModule?.tridentServiceDropAccountingRecordsAmount : configuration.financialReportConfiguration?.tridentServiceDropAccountingRecordsAmount,
        firstFinancialPeriodStart: company.accountingRecordsModule?.firstFinancialPeriodStart ?? null,
        firstFinancialPeriodEnd: company.accountingRecordsModule?.firstFinancialPeriodEnd ?? null,
        currentDeadline: company.accountingRecordsModule?.currentDeadline ?? null,
      }
      updateCompany = true;
    }

    if (isFirstReport === true || previousReports.length === 0) {
      startPeriod = moment(req.body.financialPeriodStart).isValid() ? moment.utc(moment(req.body.financialPeriodStart, 'YYYY-MM-DD')) : null;
      endPeriod = moment(req.body.financialPeriodEnd).isValid() ? moment.utc(moment(req.body.financialPeriodEnd, 'YYYY-MM-DD')) : null;
      

      if (!startPeriod || startPeriod === "") {
        invalidPeriodErrors.push("Provide start of financial period");
      }
      if (!endPeriod || endPeriod === "") {
        invalidPeriodErrors.push("Provide end of financial period");
      }

      if (startPeriod && endPeriod) {
        if (endPeriod <= startPeriod) {
          invalidPeriodErrors.push("End of financial period must be maximum 12 months after the start of the financial period");
        }
        const minDate = moment("2023-01-01", "YYYY-MM-DD").utc();
        let maxDate
        const incorporationDate = moment(company.incorporationdate).format('YYYY-MM-DD')
        if (startPeriod < minDate) {
          invalidPeriodErrors.push("Financial period cannot be earlier than 2023-01-01");
        }

        if(moment(startPeriod).format('YYYY-MM-DD') === incorporationDate) {
          maxDate = moment.utc(moment(req.body.financialPeriodStart, 'YYYY-MM-DD')).add(18, 'months');
          if (endPeriod >= maxDate) {
            invalidPeriodErrors.push("End of financial period must be maximum 18 months after the start of the financial period");
          }
        } else {
          maxDate = moment.utc(moment(req.body.financialPeriodStart, 'YYYY-MM-DD')).add(12, 'months');
          if (endPeriod >= maxDate) {
            invalidPeriodErrors.push("End of financial period must be maximum 12 months after the start of the financial period");
          }
        }

      }

      if (invalidPeriodErrors.length > 0) {
        return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
          status: httpConstants.HTTP_STATUS_BAD_REQUEST,
          error: invalidPeriodErrors.join('. ')
        });
      }
      
      const nextFPEndDate = endPeriod.clone();
      const nextFPStartDate = startPeriod.clone();
      let newDeadline = moment(nextFPEndDate).utc().add(9, 'months');
      let newFilingDeadline = moment(nextFPEndDate).utc().add(9, 'months');

      if (moment(nextFPStartDate).isBefore(moment("2024-01-01").utc().startOf('day'))) {        
        newDeadline = moment(nextFPEndDate).utc().add(18, 'months');
        newFilingDeadline = moment(nextFPEndDate).utc().add(18, 'months');
      }
      

      startPeriod = startPeriod.format('YYYY-MM-DD');
      endPeriod = endPeriod.format('YYYY-MM-DD');
      
      companyAccountingModule = {
        ...companyAccountingModule,
        firstFinancialPeriodStart: startPeriod,
        firstFinancialPeriodEnd: endPeriod,
        currentDeadline: newDeadline,
        currentFilingDeadline: newFilingDeadline
      };
      updateCompany = true;
    } else {
      previousReports = previousReports.sort((a, b) => {
        return new Date(b.financialPeriod.end) - new Date(a.financialPeriod.end);
      });
      const latestReport = previousReports[0];
      const startDate = moment(latestReport.financialPeriod.end).utc().add('1', 'days');
      const currentDate = moment().utc();
      
      startPeriod = startDate.clone().format('YYYY-MM-DD');
      endPeriod = moment(startPeriod, 'YYYY-MM-DD').utc().add(1, 'years').subtract(1, "days").format('YYYY-MM-DD');

      if(startDate.startOf('day').isAfter(currentDate)){
        return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
          status: httpConstants.HTTP_STATUS_BAD_REQUEST,
          error: "The financial period has not yet commenced, as the start date is in the future. Preparing the return is not possible yet"
        });
      }

      const newReportStartDate = moment(startPeriod, 'YYYY-MM-DD').utc();
      const newReportEndDate = moment(endPeriod, 'YYYY-MM-DD').utc();

      const hasOverlap = previousReports.some(report => {
          const reportStartDate = moment(report.financialPeriod.start, 'YYYY-MM-DD').utc();
          const reportEndDate = moment(report.financialPeriod.end, 'YYYY-MM-DD').utc();

          return (newReportStartDate.isBetween(reportStartDate, reportEndDate, undefined, '[]') ||
                  newReportEndDate.isBetween(reportStartDate, reportEndDate, undefined, '[]'));
      });

      if (hasOverlap) {
          return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({
              status: httpConstants.HTTP_STATUS_BAD_REQUEST,
              error: "The new financial period overlaps with an existing period. Please choose different dates."
          });
      }

    }

    if (updateCompany) {
      company.accountingRecordsModule = companyAccountingModule
      await company.save()
    }

    let report = new FinancialReportModel({
      masterClientCode: req.params.masterclientcode,
      companyData: company,
      status: REPORT_STATUS.SAVED,
      createdBy: req.user.email,
      createdAt: new Date(),
      currency: "USD",
      financialPeriod: {
        start: startPeriod,
        end: endPeriod
      },
      files: {
        exemptEvidenceFiles: [],
        copyResolutionFiles: [],
        accountingRecordFiles: [],
        annualReturnFiles: []
      },
      version: "2.0"
    });

    await report.save();
    req.session.company = company;
    return res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      message: 'Financial Period submitted successfully.',
      services: process.env.FINANCIAL_REPORTS_SEVICES,
      reportId: report._id,
      companyId: company.code
    });

  } catch (e) {
    console.log("Error creating new financial report: ", e);
    res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      error: 'Internal server error'
    });
  }
};

exports.getCompanyForms = async function (req, res, next) {
  try {
    const sessData = req.session;
    const companyCode = req.params.companyCode;
    let reportForms = [];
    const company = await Company.findOne({ 'code': companyCode, 'masterclientcode': req.params.masterclientcode });
    if (!company) {
      let err = new Error('Company not found');
      err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
      return next(err);
    }
    sessData.company = company;
    let reports = await FinancialReportModel.find({
      'companyData.code': companyCode,
      "masterClientCode": req.params.masterclientcode,
      status: { "$ne": REPORT_STATUS.DELETED }
    },
      {
        '_id': 1,
        'name': 1,
        'status': 1,
        'updatedAt': 1,
        "financialPeriod": 1,
        "invoiceNumber": 1,
        'managementStatus': 1,
        'reportDetails': 1,
        'payment': 1,
        "reopened": 1, 
        "initialSubmitDate": 1
      }).limit(100); //.sort({ 'updatedAt': 'desc'}).exec();

    reports.sort((a, b) => {
      const date1 = a.financialPeriod && a.financialPeriod.start ? a.financialPeriod.start : 0;
      const date2 = b.financialPeriod && b.financialPeriod.start ? b.financialPeriod.start : 0;
      return date1 > date2 ? 1 : date1 < date2 ? -1 : 0;
    });

    if (reports.length > 0) {
      for (let report of reports) {
        let reportForm = report;
        reportForm.allowContinue = (reportForm.status === REPORT_STATUS.SAVED || reportForm.status === REPORT_STATUS.IN_PROGRESS ||  reportForm.status === REPORT_STATUS.RE_OPEN);
        reportForm.allowDownload = reportForm.status && (reportForm.status !== REPORT_STATUS.SAVED && reportForm.status !== REPORT_STATUS.IN_PROGRESS && reportForm.status !== REPORT_STATUS.RE_OPEN);
        reportForm.allowInvoice = reportForm.status && (reportForm.status !== REPORT_STATUS.SAVED && reportForm.status !== REPORT_STATUS.IN_PROGRESS) &&
          reportForm.invoiceNumber && reportForm.invoiceNumber !== '';
        reportForm.allowDelete = (reportForm.status === REPORT_STATUS.SAVED || reportForm.status === REPORT_STATUS.IN_PROGRESS) && !report.initialSubmitDate;

        if (reportForm.status === REPORT_STATUS.RE_OPEN && reportForm.reopened?.details?.length > 0) {
          const reopened = reportForm.reopened.details.sort((a, b) => b.reopenedAt.getTime() - a.reopenedAt.getTime());
          reportForm.reopenedId = reopened[0]._id;
        }

        reportForms.push(reportForm)
      }
    }

    reportForms.sort((a, b) => {
      const date1 = a.updatedAt ? a.updatedAt : 0;
      const date2 = b.updatedAt ? b.updatedAt : 0;
      return date1 > date2 ? -1 : date1 < date2 ? 1 : 0;
    });


    res.render('financial-reports/company-forms-list', {
      title: 'Company overview',
      user: req.user,
      messages: req.session.messages,
      masterClientCode: req.params.masterclientcode,
      company: company,
      reportForms: reportForms
    });
  } catch (e) {
    console.log(e);
    let err = new Error('Internal Server Error');
    err.status = 502;
    return next(err);
  }

};

exports.loadFinancialReportForm = async function (req, res, next) {
  try {
    const report = await FinancialReportModel.findOne({ _id: req.params.reportId, masterClientCode: req.params.masterclientcode });
    const invalidSubmitPeriod =  report.reportDetails?.isExemptCompany !== true && moment(report.financialPeriod.end).utc() > moment().utc().startOf('day');
 
    if (report.status !== REPORT_STATUS.SAVED && report.status !== REPORT_STATUS.IN_PROGRESS && report.status !== REPORT_STATUS.RE_OPEN) {
      return res.render('error', { message: 'This submission can not longer be edited' });
    } else {

      let companyReports = await FinancialReportModel.find({
        'companyData.code': report.companyData.code,
        "masterClientCode": req.params.masterclientcode,
        "status": { "$ne": REPORT_STATUS.DELETED },
        "_id": { "$ne": report._id }
      }, { _id: 1, financialPeriod: 1 })
      report.isFirstReport = true;


      if (companyReports.length > 0) {
        const previousReports = companyReports.filter((r) => r.financialPeriod.end < report.financialPeriod.end);
        if (previousReports.length > 0) {
          report.isFirstReport = false;
        }

      }

      let totalLoansReceived = 0;
      let totalLoansPaid = 0;

      if (report.cashTransactions?.bankAccounts?.length > 0) {
        const liabilitiesMap = report.cashTransactions.bankAccounts.map((ba) => ba.liabilities);

        totalLoansReceived = sumTotalArrayValues(liabilitiesMap, "loansPayableReceived");
        totalLoansPaid = sumTotalArrayValues(liabilitiesMap, "loansPayablePaid");

        report.totalLoansAmountReceived = totalLoansReceived;
        report.totalLoansAmountPaid = totalLoansPaid;
      }

      const mappedReport = report.getMappedSchema()

      res.render('financial-reports/report-form',
        {
          title: 'Financial Return',
          user: req.user,
          messages: req.session.messages,
          masterClientCode: req.params.masterclientcode,
          reportId: req.params.reportId,
          report,
          companyCode: req.params.companyCode,
          currencies: CURRENCIES,
          ACCOUNTING_FORM_STEPS,
          services: process.env.FINANCIAL_REPORTS_SEVICES,
          mappedReport: mappedReport,
          invalidSubmitPeriod
        });
    }
  } catch (e) {
    console.log(e);
    return next(e);
  }
};

exports.saveFinancialReportForm = async function (req, res) {
  try {
    let newValues = {
      status: REPORT_STATUS.IN_PROGRESS
    };

    let updatedValues;
    const currentReport = await FinancialReportModel.findOne({ _id: req.params.reportId, masterClientCode: req.params.masterclientcode });
    const company = await Company.findOne({
      code: currentReport.companyData.code, masterclientcode: currentReport.companyData.masterclientcode
    });
    const errors = await financialReportValidator(req.body, currentReport);

    if (errors.length) {
      return res.json({ status: httpConstants.HTTP_STATUS_BAD_REQUEST, errors });
    }

    let invalidSubmitPeriod = currentReport.reportDetails?.isExemptCompany === false && moment(currentReport.financialPeriod.end).utc() > moment().utc().startOf('day');

    // Step 1 REPORT DETAILS
    if (req.body.currentStep === ACCOUNTING_FORM_STEPS.DETAILS) {
      const isExemptCompany = utils.formatYesNoBoolean(req.body.isExemptCompany);
      const isThereFinancialYearChange = utils.formatYesNoBoolean(req.body.isThereFinancialYearChange)
      let financialPeriod = {}
      let originalFinancialPeriod = {}

      if (isThereFinancialYearChange) {
          financialPeriod = {
            start: req.body.financialPeriod && req.body.financialPeriod.start ? moment(req.body.financialPeriod.start).utc() : undefined,
            end: req.body.financialPeriod && req.body.financialPeriod.end ? moment(req.body.financialPeriod.end).utc() : undefined,
          }
          // If already has values, do not overwrite  
          if (!currentReport.originalFinancialPeriod.start && !currentReport.originalFinancialPeriod.end) {
              originalFinancialPeriod = currentReport.financialPeriod;
          }
        } else {
          if (currentReport.originalFinancialPeriod && currentReport.originalFinancialPeriod.start && currentReport.originalFinancialPeriod.end) {
            // If there was previously a financial period, use it and clear backup
            financialPeriod = currentReport.originalFinancialPeriod
            originalFinancialPeriod = {}
          } else {
            // If there was no original financial period, use the current one
            financialPeriod = currentReport.financialPeriod;
          }
      }
      newValues = {
        reportDetails: {
          isExemptCompany: isExemptCompany,
          exemptCompanyType: isExemptCompany ? req.body.exemptCompanyType : "",
          exemptCompanyExplanation: isExemptCompany ? req.body.exemptCompanyExplanation : "",
          serviceType: req.body.serviceType,
          isFirstYearOperation: utils.formatYesNoBoolean(req.body.isFirstYearOperation),
          isThereFinancialYearChange: isThereFinancialYearChange,
          assistanceRequired: req.body.assistanceRequired

        },
        financialPeriod: financialPeriod,
        originalFinancialPeriod: originalFinancialPeriod
      }
      invalidSubmitPeriod = newValues.reportDetails.isExemptCompany === false &&  moment(financialPeriod.end).utc() > moment().utc().startOf('day');
    }


    //step 2 CASH TRANSACTIONS
    if (req.body.currentStep === ACCOUNTING_FORM_STEPS.CASH) {
      const companyOwnsCashOrEquivalents = utils.formatYesNoBoolean(req.body.companyOwnsCashOrEquivalents)
      newValues = {
        currency: req.body.reportCurrency,
        cashTransactions: {
          companyOwnsCashOrEquivalents: companyOwnsCashOrEquivalents,
          bankAccounts: companyOwnsCashOrEquivalents === true && currentReport.cashTransactions?.bankAccounts?.length > 0 ?
            currentReport.cashTransactions?.bankAccounts : [],
          totalBankAccounts: companyOwnsCashOrEquivalents === true && currentReport.cashTransactions?.totalBankAccounts ?
            currentReport.cashTransactions.totalBankAccounts : null
        }
      }
    }

    // Step 3 ASSETS
    if (req.body.currentStep === ACCOUNTING_FORM_STEPS.ASSETS) {
      const assets = parsePrepareAssets(req.body, currentReport);
      const tangibleAssetsPeriod = assets.isFixedAssetsCotributed === true ? assets.valueTangibleAssetsEndPeriod : assets.tangibleAssetsEndPeriod;

      newValues = {
        assets: assets,
        calculatedValues: {
          totalAssets: (assets.valueOfOtherAssetsEndPeriod +  assets.intangibleAssetsEndFinancialPeriod + 
            tangibleAssetsPeriod + assets.totalAmountOfInvestments + assets.balanceOfLoansReceivables + currentReport.cashTransactions.totalBankAccounts)
        }
      }
    }

    // Step 4 LIABILITIES
    if (req.body.currentStep === ACCOUNTING_FORM_STEPS.LIABILITIES) {
      const liabilities = parsePrepareLiabilities(req.body, currentReport)
      currentReport.calculatedValues.shareholderEquality = currentReport.calculatedValues.totalAssets - liabilities.totalLiabilities
      currentReport.calculatedValues.dividedIncome = currentReport.cashTransactions.bankAccounts.map((bankAccount) => (bankAccount.income.dividendReceived * bankAccount.foreingExchangeRate))
      .reduce((sum, val) => {
        return sum + val
      }, 0) + (currentReport.assets?.declaredDividedIncome ? currentReport.assets?.declaredDividedIncome : 0)

      const { couponInterestIncomeReceived, investmentAdquisition, investmentSales } = currentReport.cashTransactions.bankAccounts.reduce((acc, account) => {
        const fxRate = account.foreingExchangeRate;
    
        acc.couponInterestIncomeReceived += account.income.couponInterestReceived * fxRate;
        acc.investmentAdquisition += account.assets.investmentsAcquisition * fxRate;
        acc.investmentSales += account.assets.investmentsSale * fxRate;
    
        return acc;
      }, {
        couponInterestIncomeReceived: 0,
        investmentAdquisition: 0,
        investmentSales: 0,
      });

      const calculationOfNetInvestment = currentReport.assets.totalAmountOfInvestments - currentReport.assets.otherFinancialAssets + investmentAdquisition + investmentSales - currentReport.assets.investmentsTransferredByShareholder - currentReport.assets.investmentTransferredToTheShareholder; 
      currentReport.calculatedValues.otherIncome = currentReport.cashTransactions.bankAccounts.map((bankAccount) => (bankAccount.income.bankInterestReceived * bankAccount.foreingExchangeRate)).reduce((sum, val) => {
        return sum + val
      }, 0) + (!currentReport.assets.isTangibleFixAssets && currentReport.assets.tangibleFixedAssets > 0 && currentReport.assets.balanceOfTangibleAssets > 0? currentReport.assets.balanceOfTangibleAssets : 0) +  (!currentReport.assets.isIntangibleAssets && currentReport.assets.intangibleAssets > 0 && currentReport.assets.balanceOfIntangibleAssets > 0? currentReport.assets.balanceOfIntangibleAssets : 0)

      currentReport.calculatedValues.netGainInvestment = calculationOfNetInvestment >= 0 ? couponInterestIncomeReceived + calculationOfNetInvestment : 0;

      currentReport.calculatedValues.netLossInvestment = calculationOfNetInvestment < 0 ?  couponInterestIncomeReceived : 0;
      
      currentReport.calculatedValues.totalIncome = currentReport.calculatedValues.dividedIncome + currentReport.assets?.interestReceivableOnTheLoan  + currentReport.calculatedValues.netGainInvestment + currentReport.calculatedValues.otherIncome + currentReport.assets.invoicesIssued
      currentReport.calculatedValues.investmentAsset = currentReport.assets.otherFinancialAssets - currentReport.cashTransactions.bankAccounts.map((bankAccount) => 
        (bankAccount.assets.investmentsAcquisition * bankAccount.foreingExchangeRate) - (bankAccount.assets.investmentsSale * bankAccount.foreingExchangeRate))
      .reduce((sum, val) => {
        return sum - val
      }, 0)
      currentReport.calculatedValues.portfolioFees = currentReport.cashTransactions.bankAccounts.map((bankAccount) => (bankAccount.expenses.portMngmntFees * bankAccount.foreingExchangeRate))
      .reduce((sum, expense) => {
        return sum - expense
      }, 0)
      currentReport.calculatedValues.bankFees = currentReport.cashTransactions.bankAccounts.map((bankAccount) => (bankAccount.expenses.bankFees * bankAccount.foreingExchangeRate))
      .reduce((sum, expense) => {
        return sum - expense
      }, 0)
      currentReport.calculatedValues.incomeTaxExpenses = currentReport.cashTransactions.bankAccounts.map((bankAccount) => (bankAccount.expenses.incomeTax * bankAccount.foreingExchangeRate))
      .reduce((sum, expense) => {
        return sum - expense
      }, 0)

      currentReport.calculatedValues.assetsTangible = currentReport.assets.tangibleFixedAssets + currentReport.cashTransactions.bankAccounts.map((bankAccount) => 
        (bankAccount.assets.tangibleAcquisition * bankAccount.foreingExchangeRate) + (bankAccount.assets.tangibleSale * bankAccount.foreingExchangeRate))
      .reduce((sum, val) => {
        return sum + val
      }, 0)

    currentReport.calculatedValues.assetsintangible = currentReport.assets.tangibleFixedAssets + currentReport.cashTransactions.bankAccounts.map((bankAccount) => 
      (bankAccount.assets.intangibleAcquisition * bankAccount.foreingExchangeRate) + (bankAccount.assets.intangibleSale * bankAccount.foreingExchangeRate))
      .reduce((sum, val) => {
        return sum + val
      }, 0)

    currentReport.calculatedValues.assetsOtherAssets = currentReport.assets.valueOfOtherAssetsStartPeriod + currentReport.assets.valueOfOtherAssetsEndPeriod + currentReport.assets.tangibleFixedAssets + currentReport.cashTransactions.bankAccounts.map((bankAccount) => 
        (bankAccount.assets.otherAcquisition * bankAccount.foreingExchangeRate) + (bankAccount.assets.otherSale * bankAccount.foreingExchangeRate))
        .reduce((sum, val) => {
          return sum + val
        }, 0)
      
    currentReport.calculatedValues.otherExpenses = (liabilities.otherExpenses??0) + (liabilities.totalInvoicesAndAccruedExpenses??0) - (currentReport.assets.deprecationExpenses??0) - (currentReport.assets.amortisationExpenses??0) + 
      (currentReport.assets.balanceOfIntangibleAssets < 0 ? currentReport.assets.balanceOfIntangibleAssets : 0) + 
      (currentReport.assets.balanceOfTangibleAssets < 0 ? currentReport.assets.balanceOfTangibleAssets : 0)
    currentReport.calculatedValues.totalExpenses = currentReport.calculatedValues.netLossInvestment + liabilities.compAdminFees + currentReport.calculatedValues.portfolioFees +currentReport.calculatedValues.bankFees + liabilities.loanInterestAccrued +currentReport.calculatedValues.incomeTaxExpenses + currentReport.calculatedValues.otherExpenses + liabilities.costOfGoods
    currentReport.calculatedValues.netProfit = currentReport.calculatedValues.totalIncome - currentReport.calculatedValues.totalExpenses
    currentReport.calculatedValues._revenue =  currentReport.calculatedValues.dividedIncome + currentReport.assets.interestReceivableOnTheLoan + currentReport.calculatedValues.netGainInvestment + currentReport.calculatedValues.otherIncome + currentReport.assets.invoicesIssued
    currentReport.calculatedValues._costOfSales =  liabilities.costOfGoods + currentReport.calculatedValues.netLossInvestment
    currentReport.calculatedValues._profit = currentReport.calculatedValues._revenue - currentReport.calculatedValues._costOfSales
    currentReport.calculatedValues._operationExpenses = liabilities.compAdminFees + currentReport.calculatedValues.portfolioFees  + (liabilities?.loanInterestAccrued??0)
    currentReport.calculatedValues._otherExpenses = currentReport.calculatedValues.bankFees + currentReport.calculatedValues.otherExpenses
    currentReport.calculatedValues._totalExpenses = currentReport.calculatedValues._operationExpenses + currentReport.calculatedValues.incomeTaxExpenses +  currentReport.calculatedValues._otherExpenses
    currentReport.calculatedValues._netIncome = currentReport.calculatedValues._profit - currentReport.calculatedValues._totalExpenses 
    newValues = {
      liabilities: liabilities,
      calculatedValues:  currentReport.calculatedValues
    }
  }
    // Step 5 SUMMARY
    if (req.body.currentStep === ACCOUNTING_FORM_STEPS.SUMMARY) {
      newValues = {
        finReportAccurate: utils.formatYesNoBoolean(req.body.finReportAccurate)
      }

    }

    // Step 6 COMPLETE INCOME AND EXPENSES
    if (req.body.currentStep === ACCOUNTING_FORM_STEPS.COMP_INCOME_EXPENSES) {
      newValues = parseCompleteDetailsIncomeAndExpenses(req.body, currentReport);
      newValues.currency = req.body.completeReportCurrency;
    }

    // Step 7 COMPLETE ASSETS AND LIABILITIES
    if (req.body.currentStep === ACCOUNTING_FORM_STEPS.COMP_ASSETS_LBT) {
      newValues = parseCompleteDetailsAssetsAndLiabilities(req.body, currentReport);
    }

    // Step 8 Preview
    if (req.body.currentStep === ACCOUNTING_FORM_STEPS.DECLARATION) {
      if (req.body.isCompleted && invalidSubmitPeriod){
        const errors = [
					"The financial period has not yet concluded, as the end date is forthcoming. Submission is currently not possible.",
				];
        return res.json({ status: httpConstants.HTTP_STATUS_BAD_REQUEST, errors });
      }

      newValues = await parseDeclarationValues(req.body, currentReport, company, req.user.email);
    }

    updatedValues = await FinancialReportModel.findByIdAndUpdate(req.params.reportId,
      newValues, { upsert: true, new: true, setDefaultsOnInsert: true });

    // calculate deadline for company if not reopened
    if ((updatedValues.status === REPORT_STATUS.PAID || updatedValues.status === REPORT_STATUS.CONFIRMED || updatedValues.status === REPORT_STATUS.UNDER_REVIEW) 
      && !updatedValues.reopened?.details?.length){
      const nextFPEndDate = moment(currentReport.financialPeriod.end).utc().add(1, 'years'); 
      const nextFPStartDate = moment(currentReport.financialPeriod.end).utc().add('1', 'days');

      let newDeadline = moment(nextFPEndDate).utc().add(9, 'months');
      let newFilingDeadline = moment(nextFPEndDate).utc().add(9, 'months');
      
      if (moment(nextFPStartDate).isBefore(moment("2024-01-01").utc().startOf('day'))) {        
        newDeadline = moment(nextFPEndDate).utc().add(18, 'months');
        newFilingDeadline = moment(nextFPEndDate).utc().add(18, 'months');
      }      

      await Company.findOneAndUpdate({ code: currentReport.companyData.code, masterclientcode: currentReport.companyData.masterclientcode },{
        $set: {
          "accountingRecordsModule.currentDeadline": newDeadline,
          "accountingRecordsModule.currentFilingDeadline": newFilingDeadline
        }
      });
      
    }
    else if (updatedValues.status === REPORT_STATUS.IN_PENALTY){
      //const nextFPEndDate = moment(currentReport.financialPeriod.end).utc();
      //let newDeadline = nextFPEndDate.add(12, 'months').format('YYYY-MM-DD');
      //update to 18 months for 1st submission, otherwise 9 months for subsequent submissions

      await Company.findOneAndUpdate({ code: currentReport.companyData.code, masterclientcode: currentReport.companyData.masterclientcode },{
        $set: {
          //"accountingRecordsModule.currentDeadline": newDeadline, // DO NOT SET NEW DEADLINE AS THE COMPANY IS ALREDY IN PENALTY
          "accountingRecordsModule.compliantStatus": "NON-COMPLIANT",
          "accountingRecordsModule.inPenalty": true,
          "accountingRecordsModule.currentPenaltyReport": currentReport._id,
        }
      })
    }

    const response = {
      noGenerateInvoice: updatedValues.reportDetails?.isExemptCompany === true,
      serviceType: updatedValues.reportDetails.serviceType,
      invalidSubmitPeriod: invalidSubmitPeriod,
      companyInPenalty: updatedValues.status === REPORT_STATUS.IN_PENALTY,
      newFinancialPeriod: updatedValues.financialPeriod
    }


    return res.json({ status: updatedValues ? httpConstants.HTTP_STATUS_OK : httpConstants.HTTP_STATUS_BAD_REQUEST, response});

  } catch (e) {
    console.log("E ", e);
    return res.json({ status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: "Internal Server Error" });
  }
};

exports.deleteFinancialReport = async function (req, res) {
  try {
    const report = await FinancialReportModel.findOne({ _id: req.params.reportId, masterClientCode: req.params.masterclientcode });
    if (!report) {
      return res.json({ status: httpConstants.HTTP_STATUS_NOT_FOUND, message: "Financial report not found" });
    }
    if (report.status === REPORT_STATUS.SAVED || (report.status === REPORT_STATUS.IN_PROGRESS && !report.initialSubmitDate)) {
      await FinancialReportModel.findByIdAndUpdate(req.params.reportId, { status: REPORT_STATUS.DELETED, deletedAt: moment().utc(), deletedBy: req.user.email });
      return res.json({ status: httpConstants.HTTP_STATUS_OK });
    } else {
      return res.json({ status: 402, message: "This financial report can't be deleted" });
    }
  } catch (e) {
    console.log(e);
    return res.json({ status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: "Internal Server Error" });
  }
};

exports.saveFinancialReportFiles = async function (req, res) {
  try {
    const report = await FinancialReportModel.findOne({ _id: req.params.reportId, masterClientCode: req.params.masterclientcode });

    if (report.files) {
      const files = report.files[req.body.fieldName];
      if (files.length >= Number(req.body.maxFileAllowed)) {
        let errorMsg = 'Max file records exceeded, cannot upload more than ' + Number(req.body.maxFileAllowed) + ' files.';
        console.log(errorMsg);
        return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).send({ message: errorMsg });
      }
    }
    const validFieldNames = ["exemptEvidenceFiles", "copyResolutionFiles", "accountingRecordFiles", "annualReturnFiles",]
    if (!validFieldNames.includes(req.body.fieldName)) {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).send({ message: 'Invalid field name type' });
    }

    let uploadedFiles = req.files['fileUploaded'];

    if (uploadedFiles && uploadedFiles.length > 0) {

      for (let i = 0; i < uploadedFiles.length; i++) {
        const newFile = uploadedFiles[i];
        if (newFile.mimetype !== 'application/pdf' ||
          !verifyExtFile.isValidExtensionFile(newFile.originalname) ||
          !verifyExtFile.isValidExtensionFile(newFile.blob) ||
          !verifyExtFile.isValidExtensionFile(newFile.blobName)
        ) {
          return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).send({ message: 'Incorrect file type' });
        }
        if (newFile.size > 5000000) {
          return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).send({ message: 'Max files size exceeded, cannot upload more than 5Mb' });
        }

        let errorUploading = false;
        uploadController.moveUploadFile(req.params.reportId, newFile, req.body.fieldName,
          process.env.AZURE_STORAGE_CONTAINER_FINANCIAL_REPORTS).catch((reason => {
            if (reason) {
              console.log(reason);
              errorUploading = true;
            }
          }));

        if (errorUploading) {
          return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).send({ message: 'Error uploading the file, try again later' });
        }
        uploadedFiles[i] = {
          fileId: uuidv4(),
          fieldName: newFile.fieldname.replace(/fileUploaded/i, req.body.fieldName),
          blob: newFile.blob.replace(/fileUploaded/i, req.body.fieldName),
          blobName: newFile.blobName.replace(/fileUploaded/i, req.body.fieldName),
          url: newFile.url.replace(/fileUploaded/i, req.params.reportId + '/' + req.body.fieldName),
          originalName: newFile.originalname,
          encoding: newFile.encoding,
          mimeType: newFile.mimetype,
          container: newFile.container,
          blobType: newFile.blobType,
          size: newFile.size,
          etag: newFile.etag
        };
      }



      if (!report.files) {
        report.files = {
          exemptEvidenceFiles: [],
          copyResolutionFiles: [],
          accountingRecordFiles: [],
          annualReturnFiles: []
        }
      }

      report.files[req.body.fieldName] = [...report.files[req.body.fieldName], ...uploadedFiles];
      await report.save();
    }
    return res.json({ result: true });

  } catch (error) {
    console.log("error: ", error);
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
  }
};

exports.getFinancialReportFiles = async function (req, res) {
  try {
    const report = await FinancialReportModel.findOne({ _id: req.params.reportId, masterClientCode: req.params.masterclientcode });
    let filesToReturn = [];

    if (report.files) {
      const files = report.files[req.query.fieldName];
      filesToReturn = files && files.length > 0 ? files : [];

    }
    return res.json({ result: true, files: filesToReturn });

  } catch (error) {
    console.log("error: ", error);
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
  }
};

exports.deleteFinancialReportFiles = async function (req, res) {
  try {
    const report = await FinancialReportModel.findOne({ _id: req.params.reportId, masterClientCode: req.params.masterclientcode });
    if (report.files) {
      const files = report.files[req.body.fieldName];
      if (files) {
        const fileIndex = files.findIndex((f) => f.fileId && f.fileId.toString() === req.body.fileId);
        if (fileIndex > -1) {
          files.splice(fileIndex, 1);
          report.files[req.body.fieldName] = files;
          await report.save();
        }

      }
    }
    return res.json({ result: true });

  } catch (error) {
    console.log("error: ", error);
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
  }
};


exports.createBankAccount = async function (req, res) {
  try {
    const report = await FinancialReportModel.findOne({ _id: req.params.reportId, masterClientCode: req.params.masterclientcode });

    let totalBankAccountsAmount = 0;
    const bankAccounts = report.cashTransactions?.bankAccounts || [];

    const errors = bankAccountValidator(req.body);

    if (errors.length) {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({ status: httpConstants.HTTP_STATUS_BAD_REQUEST, formErrors: errors });
    }

    const newBankAccount = parseBankAccountValues(req.body);
    // pending validations

    bankAccounts.push(newBankAccount);
    totalBankAccountsAmount = sumTotalArrayValues(bankAccounts, "cashAtBank")
    report.cashTransactions = {
      companyOwnsCashOrEquivalents: report.cashTransactions?.companyOwnsCashOrEquivalents ?? null,
      bankAccounts: bankAccounts,
      totalBankAccounts: totalBankAccountsAmount
    }

    await report.save();
    return res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      message: "Cash/Bank account created successfully"
    });
  } catch (error) {
    console.log("E ", error);
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      error: "Internal Server Error"
    });
  }
};

exports.getBankAccounts = async function (req, res) {
  try {
    const report = await FinancialReportModel.findOne({ _id: req.params.reportId, masterClientCode: req.params.masterclientcode });

    const bankAccounts = report.cashTransactions && report.cashTransactions.bankAccounts?.length > 0 ? report.cashTransactions.bankAccounts : [];

    if (req.query.bankAccountId) {
      const bankAccount = bankAccounts.find((bankAccount) => bankAccount._id.toString() === req.query.bankAccountId);
      return res.json({
        status: httpConstants.HTTP_STATUS_OK, data: {
          reportId: req.params.reportId,
          bankAccount: bankAccount
        }
      });
    }

    const liabilitiesMap = bankAccounts.map((ba) => ba.liabilities);

    const totalLoansReceived = sumTotalArrayValues(liabilitiesMap, "loansPayableReceived");
    const totalLoansPaid = sumTotalArrayValues(liabilitiesMap, "loansPayablePaid");

    return res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      data: {
        reportId: req.params.reportId,
        bankAccounts: bankAccounts,
        totalBankAccounts: report.cashTransactions?.totalBankAccounts ?? "",
        totalLoansAmountReceived: totalLoansReceived,
        totalLoansAmountPaid: totalLoansPaid
      }
    });
  } catch (error) {
    console.log("E ", error);
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      message: "Internal Server Error"
    });
  }
};

exports.editBankAccount = async function (req, res) {
  try {
    const report = await FinancialReportModel.findOne({ _id: req.params.reportId, masterClientCode: req.params.masterclientcode });

    const errors = bankAccountValidator(req.body);

    if (errors.length) {
      return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({ status: httpConstants.HTTP_STATUS_BAD_REQUEST, formErrors: errors });
    }


    let editBankAccount = parseBankAccountValues(req.body);

    let index = report.cashTransactions.bankAccounts.findIndex((bankAccount) =>
      bankAccount._id.toString() === req.params.bankAccountId);


    if (index === -1) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        error: "Cash/Bank account not found"
      });
    }


    let currentBankAccount = report.cashTransactions.bankAccounts[index];
    editBankAccount._id = currentBankAccount._id;

    report.cashTransactions.bankAccounts[index] = editBankAccount;
    report.cashTransactions.totalBankAccounts = sumTotalArrayValues(report.cashTransactions.bankAccounts, "cashAtBank")

    await report.save();
    return res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      message: "Cash/Bank account updated successfully"
    });
  } catch (error) {
    console.log("E ", error);
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      message: "Internal Server Error"
    });
  }
};

exports.deleteBankAccount = async function (req, res) {
  try {
    const report = await FinancialReportModel.findOne({ _id: req.params.reportId, masterClientCode: req.params.masterclientcode });

    let index = report.cashTransactions.bankAccounts.findIndex((bankAccount) =>
      bankAccount._id.toString() === req.params.bankAccountId);

    if (index === -1) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({
        status: httpConstants.HTTP_STATUS_NOT_FOUND,
        error: "Cash/Bank account not found"
      });
    }

    report.cashTransactions.bankAccounts.splice(index, 1);
    report.cashTransactions.totalBankAccounts = sumTotalArrayValues(report.cashTransactions.bankAccounts, "cashAtBank")

    await report.save();
    return res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      message: "Cash/Bank account deleted successfully"
    });
  } catch (error) {
    console.log("E ", error);
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      message: "Internal Server Error"
    });
  }
};

exports.deleteAllBankAccounts = async function (req, res) {
  try {
    const report = await FinancialReportModel.findOne({ _id: req.params.reportId, masterClientCode: req.params.masterclientcode });

    if (report.cashTransactions && report.cashTransactions?.bankAccounts?.length > 0 ) {
      report.cashTransactions.bankAccounts = [];
      report.cashTransactions.totalBankAccounts = sumTotalArrayValues(report.cashTransactions.bankAccounts, "cashAtBank")
    }
   

    await report.save();
    return res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      message: "Cash/Bank accounts deleted successfully"
    });
  } catch (error) {
    console.log("E ", error);
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      message: "Internal Server Error"
    });
  }
};

exports.invalidateBankAccounts = async function (req, res) {
  try {
    const report = await FinancialReportModel.findOne({ _id: req.params.reportId, masterClientCode: req.params.masterclientcode });
    report.currency = req.body.newCurrency;

    if (report.cashTransactions && report.cashTransactions?.bankAccounts?.length > 0) {
      report.cashTransactions.bankAccounts.forEach((bankAccount) => {
        bankAccount.invalid = true;
        bankAccount.foreingExchangeRate = null;
        bankAccount.cashAtBank = null;
      })

      report.cashTransactions.totalBankAccounts = sumTotalArrayValues(report.cashTransactions.bankAccounts, "cashAtBank");
    }
    await report.save();
    return res.status(httpConstants.HTTP_STATUS_OK).json({
      status: httpConstants.HTTP_STATUS_OK,
      message: "Cash/Bank accounts have been invalidate successfully"
    });
  } catch (error) {
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({
      status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
      message: "Internal Server Error"
    });
  }
}

exports.downloadSummaryPdf = async function (req, res) {
  try {
    const report = await FinancialReportModel.findOne({ _id: req.params.reportId, masterClientCode: req.params.masterclientcode });

    pdfController.generateFinancialReportPdf(report, res);
  } catch (error) {
    console.log("E ", error);
    return res.render('error', { status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: "Internal Server Error" });
  }
};

exports.downloadInvoicePdf = async function (req, res) {
  try {
    const report = await FinancialReportModel.findOne({ _id: req.params.reportId, masterClientCode: req.params.masterclientcode });

    const company = await Company.findOne({ code: req.params.companyCode });
    pdfController.generateFinancialBSPLInvoicePdf(report, company, res);
  } catch (error) {
    console.log("E ", error);
    return res.render('error', { status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR, message: "Internal Server Error" });
  }
};

exports.validateFinancialReport = async function (req, res, next) {
  const companyCode = req.params.companyCode;
  const company = await Company.findOne({ 'code': companyCode, 'masterclientcode': req.params.masterclientcode }, { code: 1, _id: 1, masterclientcode: 1 });
  if (!company) {
    let err = new Error('Company not found');
    err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
    return next(err);
  }

  let reportComp = await FinancialReportModel.findOne({ '_id': req.params.reportId, 'companyData.code': companyCode, 'masterClientCode': company.masterclientcode }, { _id: 1, status: 1 });
  if (!reportComp) {
    let err = new Error('Financial report not related to this company');
    err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
    return next(err);
  } else {
    if (reportComp.status === REPORT_STATUS.DELETED) {
      let err = new Error('You do not have permission to access this financial report as it has been marked as DELETED.');
      err.status = httpConstants.HTTP_STATUS_BAD_REQUEST;
      return next(err);
    }

  }
  return next();
};

function parseCompleteDetailsIncomeAndExpenses(formValues, currentReport) {
  // income values
  const totalRevenue = utils.parseStringNumberToFloat(formValues.completeDetails.revenue);
  const costOfSales = utils.parseStringNumberToFloat(formValues.completeDetails.costOfSales);
  const grossProfit = totalRevenue - costOfSales

  /// expenses values
  const operatingExpenses = utils.parseStringNumberToFloat(formValues.completeDetails.operatingExpenses); 
  const incomeTax = utils.parseStringNumberToFloat(formValues.completeDetails.incomeTax);
  const totalOtherExpenses = utils.parseStringNumberToFloat(formValues.completeDetails.totalOtherExpenses);

  const totalExpenses = operatingExpenses + incomeTax + totalOtherExpenses;
  const netIncome = grossProfit - totalExpenses
  const newValues = {
    completeDetails: {
      income: {
        total: totalRevenue,
        costOfSales: costOfSales
      },
      expenses: {
        incomeTax: incomeTax,
        operatingExpenses: operatingExpenses,
        totalOtherExpenses: totalOtherExpenses,
        totalOfExpenses: totalExpenses
      },
      assets: currentReport.completeDetails?.assets ?? {},
      liabilities: currentReport.completeDetails?.liabilities ?? {},
      shareholderEquity: currentReport.completeDetails?.shareholderEquity ?? null,
      grossProfit: grossProfit,
      netIncome: netIncome
    }
  };
  return newValues;
}


function parseCompleteDetailsAssetsAndLiabilities(formValues, currentReport) {
  // assets values 
  const cashAmount = utils.parseStringNumberToFloat(formValues.completeDetails.cashAmount);
  const loansAndReceivables = utils.parseStringNumberToFloat(formValues.completeDetails.loansAndReceivables);
  const investmentsAssetsAmount = utils.parseStringNumberToFloat(formValues.completeDetails.investmentsAssetsAmount);
  const fixedAssetsAmount = utils.parseStringNumberToFloat(formValues.completeDetails.fixedAssetsAmount);
  const intangibleAssetsAmount = utils.parseStringNumberToFloat(formValues.completeDetails.intangibleAssetsAmount);
  const totalOtherAssets = utils.parseStringNumberToFloat(formValues.completeDetails.totalOtherAssets);

  // liabilities values
  const accountsPayable = utils.parseStringNumberToFloat(formValues.completeDetails.accountsPayable);
  const longTermDebts = utils.parseStringNumberToFloat(formValues.completeDetails.longTermDebts);
  const totalOtherLiabilities = utils.parseStringNumberToFloat(formValues.completeDetails.totalOtherLiabilities);


  const totalAssets = cashAmount + loansAndReceivables + investmentsAssetsAmount + fixedAssetsAmount + intangibleAssetsAmount + totalOtherAssets;
  const totalLiabilities = accountsPayable + longTermDebts + totalOtherLiabilities;

  const totalShareholdersEquity = (totalAssets - totalLiabilities);

  const newValues = {
    completeDetails: {
      income: currentReport.completeDetails?.income ?? {},
      expenses: currentReport.completeDetails?.expenses ?? {},
      assets: {
        cashAmount: cashAmount,
        loansAndReceivables: loansAndReceivables,
        investmentsAssetsAmount: investmentsAssetsAmount,
        fixedAssetsAmount: fixedAssetsAmount,
        intangibleAssetsAmount: intangibleAssetsAmount,
        totalOtherAssets: totalOtherAssets,
        total: totalAssets,
      },
      liabilities: {
        accountsPayable: accountsPayable,
        longTermDebts: longTermDebts,
        totalOtherLiabilities: totalOtherLiabilities,
        total: totalLiabilities,
      },
      shareholderEquity: totalShareholdersEquity,
      grossProfit: currentReport.completeDetails.grossProfit ?? null,
      netIncome: currentReport.completeDetails.netIncome ?? null,
    }
  };

  return newValues;
}


async function parseDeclarationValues(formValues, currentReport, company, userEmail) {
  let status;

  if (formValues.isCompleted !== true){
    return  {
      declaration: {
        information: formValues.information === 'on',
        assetsLiabilities: formValues.assetsLiabilities === 'on',
        tridentService: formValues.tridentService === 'on',
        clientPurpose: formValues.clientPurpose === 'on',
        legalAdviseObtain: formValues.legalAdviseObtain === 'on',
        termsAndConditions: formValues.termsAndConditions === 'on',
        declarationAmount: formValues.declarationAmount === 'on',
        name: formValues.declarationName,
        relation: formValues.declarationRelation,
        relationOther: formValues.declarationRelationOther,
        phone: formValues.declarationPhoneNumber,
        email: formValues.declarationEmail,
      }
    }
  }
  
  const limitDate = company.accountingRecordsModule.currentDeadline;//  moment(currentReport.financialPeriod.end).utc().add(12, 'months').toDate();

  //12 months for 1st submission, 9 months for subsequent submissions
  
  const currentDate = moment().utc().add(-1,'day').toDate();
  const inPenalty = !currentReport.initialSubmitDate && currentDate > limitDate ? true : false;
  let penaltyDetails = currentReport.penaltyDetails ?? {};

  if (inPenalty === true){
    status = REPORT_STATUS.IN_PENALTY;
    penaltyDetails = {
      penaltyStartedAt: moment().utc(),
      penaltyCompletedAt: null,
      penaltyAmount: null,
      penaltyDays:  null,
    }
  }
  else if (currentReport.payment?.paidAt && currentReport.payment?.reference && currentReport.reopened?.details?.length > 0){
    status = REPORT_STATUS.PAID;
  }
  else if (currentReport.reportDetails.isExemptCompany === true || currentReport.reportDetails.isThereFinancialYearChange === true){
    status = REPORT_STATUS.UNDER_REVIEW;
  }
  else {
    status = formValues.serviceType === ACCOUNTING_SERVICE_TYPES.TRIDENT_SERVICE_COMPLETE || formValues.serviceType === ACCOUNTING_SERVICE_TYPES.TRIDENT_SERVICE_DROP ?
      REPORT_STATUS.REQUEST_HELP :
      REPORT_STATUS.CONFIRMED
  }


  const newValues = {
    submittedBy: userEmail,
    submittedAt: moment().utc(),
    initialSubmitDate: currentReport.initialSubmitDate ? currentReport.initialSubmitDate : moment().utc(),
    status: status,
    declaration: {
      information: formValues.information === 'on',
      assetsLiabilities: formValues.assetsLiabilities === 'on',
      tridentService: formValues.tridentService === 'on',
      clientPurpose: formValues.clientPurpose === 'on',
      legalAdviseObtain: formValues.legalAdviseObtain === 'on',
      termsAndConditions: formValues.termsAndConditions === 'on',
      declarationAmount: formValues.declarationAmount === 'on',
      name: formValues.declarationName,
      relation: formValues.declarationRelation,
      relationOther: formValues.declarationRelationOther,
      phone: formValues.declarationPhoneNumber,
      email: formValues.declarationEmail,
    },
    payment: currentReport.payment,
    penaltyDetails: penaltyDetails,
  }
  if (currentReport.status === REPORT_STATUS.RE_OPEN && currentReport.reopened?.details?.length > 0) {
    const reopened = currentReport.reopened.details[currentReport.reopened.details.length - 1];

    reopened.resubmittedAt = moment().utc();
    reopened.resubmittedBy = userEmail;
    currentReport.reopened.details[currentReport.reopened.details.length - 1] = reopened;
    newValues.reopened = currentReport.reopened;
  }

  const generateInvoice = currentReport.reportDetails?.isExemptCompany !== true;

  if (!currentReport.invoiceNumber && generateInvoice === true) {
    const configuration = await ConfigurationModel.findOne({});


    if (currentReport.payment?.paidAt && currentReport.payment?.reference && currentReport.reopened?.details?.length > 0) {
      newValues.payment = currentReport.payment;
    } else {
      let invoiceValue = 0;

      if (currentReport.reportDetails?.serviceType === ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_COMPLETE) {
        invoiceValue = currentReport.companyData?.accountingRecordsModule?.selfServiceCompleteAnnualReturnAmount;
      }

      if (currentReport.reportDetails?.serviceType === ACCOUNTING_SERVICE_TYPES.SELF_SERVICE_PREPARE) {
        invoiceValue = currentReport.companyData?.accountingRecordsModule?.selfServicePrepareAnnualReturnAmount;
      }
      if (currentReport.reportDetails?.serviceType === ACCOUNTING_SERVICE_TYPES.TRIDENT_SERVICE_COMPLETE) {
        invoiceValue = currentReport.companyData?.accountingRecordsModule?.tridentServiceCompleteAnnualReturnAmount;
      }
      if (currentReport.reportDetails?.serviceType === ACCOUNTING_SERVICE_TYPES.TRIDENT_SERVICE_DROP) {
        invoiceValue = currentReport.companyData?.accountingRecordsModule?.tridentServiceDropAccountingRecordsAmount;
      }

      newValues.payment = {
        ...newValues.payment,
        total: invoiceValue,
      };
    }
    newValues.invoiceNumber = await invoiceGenerator.generateInvoiceNumber(configuration, 'financial-report');
  }

  return newValues;
}


function parseBankAccountValues(formValues) {
  let newBankAccount = {
    description: formValues.bankDescription,
    accountType: formValues.bankAccountType,
    openingAmount: utils.parseStringNumberToFloat(formValues.openingAmount),
    closingAmount: utils.parseStringNumberToFloat(formValues.closingAmount),
    transactionCurrency: formValues.cashTransactionCurrency,
    foreingExchangeRate: utils.parseStringNumberToFloat(formValues.foreingExchangeRate),
    invalid: false,
    income: {
      dividendReceived: utils.parseStringNumberToFloat(formValues.incomeDividendReceived),
      couponInterestReceived: utils.parseStringNumberToFloat(formValues.incomeCouponInterestReceived),
      loanInterestReceived: utils.parseStringNumberToFloat(formValues.incomeLoanInterestReceived),
      bankInterestReceived: utils.parseStringNumberToFloat(formValues.incomeBankInterestReceived),
      otherIncome: utils.parseStringNumberToFloat(formValues.bankOtherIncome),
    },
    expenses: {
      compAdminFees: utils.parseStringNumberToFloat(formValues.expensesCompAdminFees),
      portMngmntFees: utils.parseStringNumberToFloat(formValues.expensesPortMngmntFees),
      loanInterest: utils.parseStringNumberToFloat(formValues.expensesLoanInterest),
      bankFees: utils.parseStringNumberToFloat(formValues.expensesBankFeesCharges),
      incomeTax: utils.parseStringNumberToFloat(formValues.expensesIncomeTax),
      otherExpenses: utils.parseStringNumberToFloat(formValues.bankOtherExpenses),
    },
    assets: {
      bankTransfers: utils.parseStringNumberToFloat(formValues.assetsBankTransfers),
      investmentsAcquisition: utils.parseStringNumberToFloat(formValues.assetsInvestmentsAcquisition),
      investmentsSale: utils.parseStringNumberToFloat(formValues.assetsInvestmentsSale),
      tangibleAcquisition: utils.parseStringNumberToFloat(formValues.assetsTangibleAcquisition),
      tangibleSale: utils.parseStringNumberToFloat(formValues.assetsTangibleSale),
      intangibleAcquisition: utils.parseStringNumberToFloat(formValues.assetsIntangibleAcquisition),
      intangibleSale: utils.parseStringNumberToFloat(formValues.assetsIntangibleSale),
      otherAcquisition: utils.parseStringNumberToFloat(formValues.assetsOtherAcquisition),
      otherSale: utils.parseStringNumberToFloat(formValues.assetsOtherSale),
      loanReceivablePaid: utils.parseStringNumberToFloat(formValues.assetsLoanReceivablePaid),
      loanReceivableReceived: utils.parseStringNumberToFloat(formValues.assetsLoanReceivableReceived),
      receivablesPaid: utils.parseStringNumberToFloat(formValues.assetsReceivablesPaid),
      receivablesReceived: utils.parseStringNumberToFloat(formValues.assetsReceivablesReceived),
    },
    liabilities: {
      accountsPayableReceived: utils.parseStringNumberToFloat(formValues.liabilitiesAccountsPayableReceived),
      accountsPayablePaid: utils.parseStringNumberToFloat(formValues.liabilitiesAccountsPayablePaid),
      loansPayableReceived: utils.parseStringNumberToFloat(formValues.liabilitiesLoansPayableReceived),
      loansPayablePaid: utils.parseStringNumberToFloat(formValues.liabilitiesLoansPayablePaid),
    },
    equity: {
      paymentToShareholder: utils.parseStringNumberToFloat(formValues.equityPaymentToShareholder),
      receiptsFromShareholder: utils.parseStringNumberToFloat(formValues.equityReceiptsFromShareholder),
      capitalContribution: utils.parseStringNumberToFloat(formValues.equityCapitalContribution),
    }
  };

  const totalIncome = sumTotalObjValues(newBankAccount.income);
  const totalExpenses = sumTotalObjValues(newBankAccount.expenses);
  const totalAssets = sumTotalObjValues(newBankAccount.assets);
  const totalLiabilities = sumTotalObjValues(newBankAccount.liabilities);
  const totalEquity = sumTotalObjValues(newBankAccount.equity);

  newBankAccount.closingBalancePerBank = newBankAccount.openingAmount + (totalIncome + totalExpenses + totalAssets + totalLiabilities + totalEquity);
  newBankAccount.cashAtBank = newBankAccount.closingBalancePerBank * newBankAccount.foreingExchangeRate;
  return newBankAccount;
}

function parsePrepareLiabilities(formValues, currentReport) {
  const isFirstYearOperation = utils.formatYesNoBoolean(formValues.isFirstYearOperation);
  const anyCompanyAccPayable = utils.formatYesNoBoolean(formValues.anyCompanyAccPayable);
  const didCompanyOweLongTermDebts = utils.formatYesNoBoolean(formValues.didCompanyOweLongTermDebts);
  const otherLiabilitiesOwed = utils.formatYesNoBoolean(formValues.otherLiabilitiesOwed);
  const longTermDebts = utils.parseStringNumberToFloat(formValues.longTermDebts);
  const loanInterestAccrued = utils.parseStringNumberToFloat(formValues.loanInterestAccrued);
  const ltDebtsReceived = utils.parseStringNumberToFloat(formValues.ltDebtsReceivedByShareholder);
  const ltDebtsRepayments = utils.parseStringNumberToFloat(formValues.ltDebtsRepayments);

  let ltDebtsClosingBalance = 0;

  if (didCompanyOweLongTermDebts === true) {
    const loansCalc = currentReport.cashTransactions.bankAccounts.map(bankAccount => 
      (bankAccount.liabilities.loansPayableReceived * bankAccount.foreingExchangeRate) + (bankAccount.liabilities.loansPayablePaid * bankAccount.foreingExchangeRate))
    .reduce((sum, value) => sum + value, 0);

    ltDebtsClosingBalance = longTermDebts + loanInterestAccrued + ltDebtsReceived + ltDebtsRepayments - loansCalc;
  }

  const totalLiabilities = utils.parseStringNumberToFloat(formValues.accountsPayable) + ltDebtsClosingBalance + utils.parseStringNumberToFloat(formValues.valueOfOtherLiabilitiesEndPeriod)

  return {
    isFirstYearOperation: isFirstYearOperation,
    valueOfOtherLiabilitiesStartPeriod: !isFirstYearOperation ? utils.parseStringNumberToFloat(formValues.valueOfOtherLiabilitiesStartPeriod): 0,
    accountsPayable: !isFirstYearOperation ? utils.parseStringNumberToFloat(formValues.accountsPayable): 0,
    longTermDebts: !isFirstYearOperation ? longTermDebts : 0,
    accruedExpenses: !isFirstYearOperation ? utils.parseStringNumberToFloat(formValues.accruedExpenses) : 0,
    anyCompanyAccPayable: anyCompanyAccPayable,
    accountsPayableBalance: anyCompanyAccPayable ? utils.parseStringNumberToFloat(formValues.accountsPayableBalance) : 0,
    otherExpenses: utils.parseStringNumberToFloat(formValues.liabilitiesOtherExpenses) ,
    invoicesIssued: anyCompanyAccPayable ? utils.formatYesNoBoolean(formValues.lbtInvoicesIssued) : 0,
    compAdminFees: utils.parseStringNumberToFloat(formValues.liabilitiesAdminFees),
    didCompanyOweLongTermDebts: didCompanyOweLongTermDebts,
    ltDebtsReceived: didCompanyOweLongTermDebts === true ? ltDebtsReceived : 0,
    loanInterestAccrued: loanInterestAccrued ?? 0,
    ltDebtsRepayments: didCompanyOweLongTermDebts ? ltDebtsRepayments : 0,
    ltDebtsClosingBalance: ltDebtsClosingBalance,
    otherLiabilitiesOwed: otherLiabilitiesOwed,
    totalInvoicesAndAccruedExpenses: otherLiabilitiesOwed ? utils.parseStringNumberToFloat(formValues.totalInvoicesAndAccruedExpenses) : null,
    valueOfOtherLiabilitiesEndPeriod: !isFirstYearOperation ? utils.parseStringNumberToFloat(formValues.valueOfOtherLiabilitiesEndPeriod): 0,
    totalLiabilities: totalLiabilities,
    costOfGoods: utils.parseStringNumberToFloat(formValues.liabilitiesCostOfGoods)
  };
}

function parsePrepareAssets(formValues, report) {
  const isFirstYearOperation = utils.formatYesNoBoolean(formValues.isFirstYearOperation)
  const interestReceivableOnTheLoan = utils.parseStringNumberToFloat(formValues.interestReceivableOnTheLoan)
  const loansAndReceivables = utils.parseStringNumberToFloat(formValues.loansAndReceivables)
  const loansReceivedByShareholder = utils.parseStringNumberToFloat(formValues.loansReceivedByShareholder)
  const paymentsOfLoansByShareholder = utils.parseStringNumberToFloat(formValues.paymentsOfLoansByShareholder)
  const invoicesIssued = utils.parseStringNumberToFloat(formValues.invoicesIssued)
  const declaredDividedIncome = utils.parseStringNumberToFloat(formValues.declaredDividedIncome)
  const isLoansAndReceivablesEndPeriod = utils.formatYesNoBoolean(formValues.isLoansAndReceivablesEndPeriod);
  const thereIsAnyInvestments = utils.formatYesNoBoolean(formValues.anyInvestments)
  const thereIsTangibleFixAssets = utils.formatYesNoBoolean(formValues.isTangibleFixAssets)
  const isFixedAssetsCotributed = utils.formatYesNoBoolean(formValues.isFixedAssetsCotributed)
  const isIntangibleAssets = utils.formatYesNoBoolean(formValues.isIntangibleAssets)
  const isIntagibleAssetsContributed = utils.formatYesNoBoolean(formValues.isIntagibleAssetsContributed)
  const investmentsTransferredByShareholder = utils.parseStringNumberToFloat(formValues.investmentsTransferredByShareholder)
  const investmentTransferredToTheShareholder = utils.parseStringNumberToFloat(formValues.investmentTransferredToTheShareholder)

  const isOtherAssets = utils.formatYesNoBoolean(formValues.isOtherAssets)
  const bankAccountsLoans = report.cashTransactions.bankAccounts.map((account) => {
    return (-(account.assets.loanReceivablePaid * account.foreingExchangeRate) - (account.assets.loanReceivableReceived * account.foreingExchangeRate) - 
    (account.assets.receivablesPaid * account.foreingExchangeRate) - (account.assets.receivablesReceived * account.foreingExchangeRate))
  }).reduce((total, sum) => {
    return total - sum
  }, 0)
  const balanceOfLoansReceivables = loansAndReceivables - bankAccountsLoans + interestReceivableOnTheLoan + loansReceivedByShareholder + paymentsOfLoansByShareholder + invoicesIssued + declaredDividedIncome

  const bankAccountInvestmentsCalc = report.cashTransactions.bankAccounts.map((account) => {
    return ((account.assets.investmentsAcquisition * account.foreingExchangeRate) + (account.assets.investmentsSale * account.foreingExchangeRate))
  }).reduce((sum, total) => {
    return sum + total
  }, 0)

  const balanceOfInvestment = utils.parseStringNumberToFloat(formValues.totalAmountOfInvestments) - 
    utils.parseStringNumberToFloat(formValues.otherFinancialAssets) + bankAccountInvestmentsCalc - investmentsTransferredByShareholder - investmentTransferredToTheShareholder
 

  const { tangibleAcquisitionCalc, tangibleSalesCalc, intangibleAcquisition, intangibleSale } = report.cashTransactions.bankAccounts.reduce((acc, account) => {
    const fxRate = account.foreingExchangeRate;

    acc.tangibleAcquisitionCalc += account.assets.tangibleAcquisition * fxRate;
    acc.tangibleSalesCalc += account.assets.tangibleSale * fxRate;
    acc.intangibleAcquisition += account.assets.intangibleAcquisition * fxRate;
    acc.intangibleSale += account.assets.intangibleSale * fxRate;

    return acc;
  }, {
    tangibleAcquisitionCalc: 0,
    tangibleSalesCalc: 0,
    intangibleAcquisition: 0,
    intangibleSale: 0
  });

 
  const tangibleAssetsEndPeriod = utils.parseStringNumberToFloat(formValues.tangibleFixedAssets) + 
    utils.parseStringNumberToFloat(formValues.deprecationExpenses) - tangibleAcquisitionCalc - tangibleSalesCalc;
  const valueTangibleAssetsEndPeriod = tangibleAssetsEndPeriod + utils.parseStringNumberToFloat(formValues.tangibleAssetsContributed);
  const balanceOfTangibleAssets = tangibleSalesCalc + tangibleAcquisitionCalc  - utils.parseStringNumberToFloat(formValues.tangibleFixedAssets);


  const intangibleAssetsEndReportPeriod = utils.parseStringNumberToFloat(formValues.intangibleAssets) + 
    utils.parseStringNumberToFloat(formValues.amortisationExpenses) - intangibleAcquisition -intangibleSale;
  const intangibleAssetsEndFinancialPeriod =  intangibleAssetsEndReportPeriod + utils.parseStringNumberToFloat(formValues.intangibleAssetsContributed) ;
  const balanceOfIntangibleAssets = intangibleAssetsEndFinancialPeriod - utils.parseStringNumberToFloat(formValues.intangibleAssets) + 
    intangibleAcquisition - intangibleSale + utils.parseStringNumberToFloat(formValues.amortisationExpenses)


  return {
    isFirstYearReporting: utils.formatYesNoBoolean(formValues.isFirstYearReporting),
    valueOfOtherAssetsStartPeriod: utils.parseStringNumberToFloat(formValues.valueOfOtherAssetsStartPeriod),
    loansAndReceivables: !isFirstYearOperation ? loansAndReceivables : 0,
    otherFinancialAssets: !isFirstYearOperation ? utils.parseStringNumberToFloat(formValues.otherFinancialAssets) : 0 ,
    tangibleFixedAssets: !isFirstYearOperation ? utils.parseStringNumberToFloat(formValues.tangibleFixedAssets) : 0,
    intangibleAssets: !isFirstYearOperation ? utils.parseStringNumberToFloat(formValues.intangibleAssets) : 0,
    isLoansAndReceivablesEndPeriod: isLoansAndReceivablesEndPeriod,
    amountsOfLoansReceivables: !isLoansAndReceivablesEndPeriod? utils.parseStringNumberToFloat(formValues.amountsOfLoansReceivables) : 0,
    interestReceivableOnTheLoan: isLoansAndReceivablesEndPeriod !== null ? interestReceivableOnTheLoan : 0, 
    loansReceivedByShareholder: isLoansAndReceivablesEndPeriod ? loansReceivedByShareholder : 0, 
    paymentsOfLoansByShareholder:isLoansAndReceivablesEndPeriod ?  paymentsOfLoansByShareholder : 0, 
    invoicesIssued: isLoansAndReceivablesEndPeriod !== null ? invoicesIssued : 0, 
    declaredDividedIncome:isLoansAndReceivablesEndPeriod !== null? declaredDividedIncome : 0,  
    balanceOfLoansReceivables: isLoansAndReceivablesEndPeriod !== null ?  balanceOfLoansReceivables : 0, 
    anyInvestments: thereIsAnyInvestments,
    totalAmountOfInvestments:  thereIsAnyInvestments ? utils.parseStringNumberToFloat(formValues.totalAmountOfInvestments) : 0,
    investmentsTransferredByShareholder: thereIsAnyInvestments ? investmentsTransferredByShareholder : 0,
    investmentTransferredToTheShareholder: thereIsAnyInvestments ? investmentTransferredToTheShareholder : 0, 
    balanceOfInvestment: thereIsAnyInvestments ? balanceOfInvestment : 0,
    isTangibleFixAssets: thereIsTangibleFixAssets, 
    deprecationExpenses:  thereIsTangibleFixAssets ? utils.parseStringNumberToFloat(formValues.deprecationExpenses) : 0,
    isFixedAssetsCotributed: thereIsTangibleFixAssets ? isFixedAssetsCotributed : 0,
    tangibleAssetsEndPeriod:  tangibleAssetsEndPeriod >= 0 && thereIsTangibleFixAssets && isFixedAssetsCotributed === false  ? tangibleAssetsEndPeriod : 0,
    tangibleAssetsContributed: isFixedAssetsCotributed ? utils.parseStringNumberToFloat(formValues.tangibleAssetsContributed) : 0, 
    valueTangibleAssetsEndPeriod: isFixedAssetsCotributed ? valueTangibleAssetsEndPeriod : 0,
    balanceOfTangibleAssets:  balanceOfTangibleAssets,
    isIntangibleAssets: isIntangibleAssets,
    amortisationExpenses: isIntangibleAssets ?  utils.parseStringNumberToFloat(formValues.amortisationExpenses) : 0,
    isIntagibleAssetsContributed: isIntangibleAssets ? isIntagibleAssetsContributed : 0,
    intangibleAssetsEndReportPeriod: !isIntagibleAssetsContributed ?  intangibleAssetsEndReportPeriod : 0,
    intangibleAssetsContributed: isIntagibleAssetsContributed ?  utils.parseStringNumberToFloat(formValues.intangibleAssetsContributed) : 0,
    intangibleAssetsEndFinancialPeriod: isIntagibleAssetsContributed ? intangibleAssetsEndFinancialPeriod : 0,
    balanceOfIntangibleAssets:   balanceOfIntangibleAssets ,
    isOtherAssets: isOtherAssets,
    valueOfOtherAssetsEndPeriod: isOtherAssets ? utils.parseStringNumberToFloat(formValues.valueOfOtherAssetsEndPeriod) : 0
  }
}


exports.getFinancialReportReopenedInfo = async function (req, res) {
  try {
    let report = await FinancialReportModel.findById(req.params.reportId);

    let reopenedData;
    if (report.reopened?.details?.length > 0) {
      reopenedData = report.reopened.details.find((r) => r._id && r._id?.toString() === req.params.reopenedId);
    }

    if (reopenedData) {
      return res.status(200).json({ status: 200, reopenedData: reopenedData });

    } else {
      return res.status(404).json({ status: 400, error: "Reopened data not found" });
    }
  } catch (e) {
    console.log(e);
    return res.status(500).json({ status: 500, error: "Internal Server Error" });
  }
};

function sumTotalObjValues(obj) {
  return Object.values(obj).reduce(function (acc, currentVal) {
    if (typeof currentVal === 'number' && !isNaN(currentVal)) {
      return acc + currentVal;
    } else {
      return acc;
    }
  }, 0);
}

function sumTotalArrayValues(itemlist, prop) {
  return itemlist.reduce((a, b) => a + (b[prop] || 0), 0);
}

