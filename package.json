{"name": "locallibrary", "version": "0.0.0", "private": true, "scripts": {"start": "node server.js", "devstart": "nodemon -r dotenv/config server.js", "build": "echo 'build script executed'", "lint": "npx eslint ./"}, "dependencies": {"@azure/identity": "^4.2.1", "@azure/storage-blob": "^12.17.0", "@azure/storage-file-share": "^12.17.0", "@handlebars/allow-prototype-access": "^1.0.5", "applicationinsights": "^2.9.0", "axios": "^1.6.7", "bcryptjs": "^2.4.3", "connect-flash": "^0.1.1", "connect-redis": "^6.1.3", "cookie-parser": "^1.4.6", "csrf-csrf": "^3.0.3", "debug": "^4.3.4", "express": "^4.19.2", "express-async-errors": "^3.1.1", "express-fileupload": "^1.4.3", "express-handlebars": "^7.1.2", "express-session": "^1.18.0", "express-validator": "^6.6.1", "handlebars": "^4.7.8", "hbs": "^4.2.0", "http-errors": "^1.7.3", "moment": "^2.29.4", "mongoose": "^6.13.6", "morgan": "^1.9.1", "multer": "^1.4.5-lts.1", "node-fetch": "^2.6.1", "nodemailer": "^6.9.9", "passport": "0.6.0", "passport-local": "^1.0.0", "pdfmake": "^0.2.9", "qrcode": "^1.5.3", "redis": "^3.1.2", "sequelize": "^6.37.1", "speakeasy": "^2.0.0", "tedious": "^18.2.1", "uuid": "^9.0.1", "xml2js": "^0.6.2"}, "devDependencies": {"acorn": "^7.1.0", "dotenv": "^8.2.0", "eslint": "^8.2.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-node": "^4.1.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.0.0", "nodemon": "^3.0.3", "prettier": "^2.6.2", "rollup": "^4.37.0"}, "nodemonConfig": {"ignore": ["email_check.json"]}}