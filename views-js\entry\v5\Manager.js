$(document).ready(function () {
    const val = $('input[name=is_corporate_director]:checked').val()

    if (val === "Yes") {
        $("#MiddleNameDirector").prop('disabled', true);
        $("#MiddleNameDirector").val('');
        $("#LastNameDirector").prop('disabled', true);
        $("#LastNameDirector").val('');
    } else {
        $("#MiddleNameDirector").prop('disabled', false);
        $("#LastNameDirector").prop('disabled', false);
    }

    const hasError = $('#hasErrorVal').val();
    if (hasError === 'false') {
        window.parent.closeDirectorIFrame();
    }
});


$("input[name=is_corporate_director]").on('change', function () {
    const val = $('input[name=is_corporate_director]:checked').val()

    if (val === "Yes") {
        $("#MiddleNameDirector").prop('disabled', true);
        $("#MiddleNameDirector").val('');
        $("#LastNameDirector").prop('disabled', true);
        $("#LastNameDirector").val('');

    } else {
        $("#MiddleNameDirector").prop('disabled', false);

        $("#LastNameDirector").prop('disabled', false);

    }
})