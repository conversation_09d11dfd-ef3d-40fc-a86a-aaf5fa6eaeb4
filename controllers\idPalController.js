const ConfigurationModel = require('../models/configuration');

const fetch = require("node-fetch");

exports.generateUuid = async function (idPalAccountId) {
  try {

    const configuration = await ConfigurationModel.findOne({});
    const idPalURL = process.env.ID_PAL_SEND_APPLICATION_URL;
    const idPalClientKey = process.env.ID_PAL_CLIENT_KEY;
    const idPalAccessKey = process.env.ID_PAL_ACCESS_KEY;
    const idPalProfileId = process.env.ID_PAL_PROFILE_ID;

    if (!configuration || (configuration && !configuration.idPalConfiguration)) {
      return {status: 500, message: "ID-PAL token configuration not found"};
    }

    if (!idPalURL || !idPalClientKey || !idPalAccessKey || !idPalProfileId || !idPalAccountId) {
      return {status: 500, message: "ID-PAL has missing values"};
    }

    let body = {
      "client_key": idPal<PERSON><PERSON><PERSON>ey,
      "access_key": idPalAccess<PERSON>ey,
      "profile_id": idPalProfileId,
      "account_id": idPalAccountId,
      "language": "en",
    };

    let options = {
      method: 'post',
      body: JSON.stringify(body),
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': "Bearer " + configuration.idPalConfiguration.accessToken
      },
    };

    let response = await fetch(idPalURL, options);
    let content = await response.json();

    if (!response.status || response.status !== 200) {
        return {status: response.status || 500, message: content}
    }

    if (content && (content.status === 0 || content.status === 25)) {
      return {
        status: 200,
        message: "UUID generated",
        uuid: content.uuid
      }
    } else {
      return {
        status: 500,
        message: content.message,
      }
    }

  }catch (e) {
    console.log(e);
    return {
      status: 500,
      message: "Internal Server Error"
    }
  }
};

exports.redirectToIdPalApp = async function (req, res, next) {
  if (req.query.uuid) {
    res.redirect(process.env.ID_PAL_REDIRECT_URL + "?uuid=" +req.query.uuid);
  } else {
    let err = new Error('ID Pal page not found');
    err.status = 404;
    return next(err);
  }
};
