const COUNTRIES_LIST = [
    {
        "num_code": "4",
        "alpha_2_code": "AF",
        "alpha_3_code": "AFG",
        "name": "Afghanistan",
        "nationality": ["afghan"]
    },
    {
        "num_code": "248",
        "alpha_2_code": "AX",
        "alpha_3_code": "ALA",
        "name": "Åland Islands",
        "nationality": ["aland island"]
    },
    {
        "num_code": "8",
        "alpha_2_code": "AL",
        "alpha_3_code": "ALB",
        "name": "Albania",
        "nationality": ["albanian"]
    },
    {
        "num_code": "12",
        "alpha_2_code": "DZ",
        "alpha_3_code": "DZA",
        "name": "Algeria",
        "nationality": ["algerian"]
    },
    {
        "num_code": "16",
        "alpha_2_code": "AS",
        "alpha_3_code": "ASM",
        "name": "American Samoa",
        "nationality": ["american samoan"]
    },
    {
        "num_code": "20",
        "alpha_2_code": "AD",
        "alpha_3_code": "AND",
        "name": "Andorra",
        "nationality": ["andorran"]
    },
    {
        "num_code": "24",
        "alpha_2_code": "AO",
        "alpha_3_code": "AGO",
        "name": "Angola",
        "nationality": ["angolan"]
    },
    {
        "num_code": "660",
        "alpha_2_code": "AI",
        "alpha_3_code": "AIA",
        "name": "Anguilla",
        "nationality": ["anguillan"]
    },
    {
        "num_code": "10",
        "alpha_2_code": "AQ",
        "alpha_3_code": "ATA",
        "name": "Antarctica",
        "nationality": ["antarctic"]
    },
    {
        "num_code": "28",
        "alpha_2_code": "AG",
        "alpha_3_code": "ATG",
        "name": "Antigua and Barbuda",
        "nationality": ["antiguan", "barbudan"]
    },
    {
        "num_code": "32",
        "alpha_2_code": "AR",
        "alpha_3_code": "ARG",
        "name": "Argentina",
        "nationality": ["argentinian"]
    },
    {
        "num_code": "51",
        "alpha_2_code": "AM",
        "alpha_3_code": "ARM",
        "name": "Armenia",
        "nationality": ["armenian"]
    },
    {
        "num_code": "533",
        "alpha_2_code": "AW",
        "alpha_3_code": "ABW",
        "name": "Aruba",
        "nationality": ["aruban"]
    },
    {
        "num_code": "36",
        "alpha_2_code": "AU",
        "alpha_3_code": "AUS",
        "name": "Australia",
        "nationality": ["australian"]
    },
    {
        "num_code": "40",
        "alpha_2_code": "AT",
        "alpha_3_code": "AUT",
        "name": "Austria",
        "nationality": ["austrian"]
    },
    {
        "num_code": "31",
        "alpha_2_code": "AZ",
        "alpha_3_code": "AZE",
        "name": "Azerbaijan",
        "nationality": ["azerbaijani", "azeri"]
    },
    {
        "num_code": "44",
        "alpha_2_code": "BS",
        "alpha_3_code": "BHS",
        "name": "Bahamas",
        "nationality": ["bahamian"]
    },
    {
        "num_code": "48",
        "alpha_2_code": "BH",
        "alpha_3_code": "BHR",
        "name": "Bahrain",
        "nationality": ["bahraini"]
    },
    {
        "num_code": "50",
        "alpha_2_code": "BD",
        "alpha_3_code": "BGD",
        "name": "Bangladesh",
        "nationality": ["bangladeshi"]
    },
    {
        "num_code": "52",
        "alpha_2_code": "BB",
        "alpha_3_code": "BRB",
        "name": "Barbados",
        "nationality": ["barbadian"]
    },
    {
        "num_code": "112",
        "alpha_2_code": "BY",
        "alpha_3_code": "BLR",
        "name": "Belarus",
        "nationality": ["belarusian"]
    },
    {
        "num_code": "56",
        "alpha_2_code": "BE",
        "alpha_3_code": "BEL",
        "name": "Belgium",
        "nationality": ["belgian"]
    },
    {
        "num_code": "84",
        "alpha_2_code": "BZ",
        "alpha_3_code": "BLZ",
        "name": "Belize",
        "nationality": ["belizean"]
    },
    {
        "num_code": "204",
        "alpha_2_code": "BJ",
        "alpha_3_code": "BEN",
        "name": "Benin",
        "nationality": ["beninese", "beninois"]
    },
    {
        "num_code": "60",
        "alpha_2_code": "BM",
        "alpha_3_code": "BMU",
        "name": "Bermuda",
        "nationality": ["bermudian", "bermudan"]
    },
    {
        "num_code": "64",
        "alpha_2_code": "BT",
        "alpha_3_code": "BTN",
        "name": "Bhutan",
        "nationality": ["bhutanese"]
    },
    {
        "num_code": "68",
        "alpha_2_code": "BO",
        "alpha_3_code": "BOL",
        "name": "Bolivia",
        "nationality": ["bolivian"]
    },
    {
        "num_code": "535",
        "alpha_2_code": "BQ",
        "alpha_3_code": "BES",
        "name": "Bonaire",
        "nationality": ["bonaire"]
    },
    {
        "num_code": "70",
        "alpha_2_code": "BA",
        "alpha_3_code": "BIH",
        "name": "Bosnia and Herzegovina",
        "nationality": ["bosnian", "herzegovinian"]
    },
    {
        "num_code": "72",
        "alpha_2_code": "BW",
        "alpha_3_code": "BWA",
        "name": "Botswana",
        "nationality": ["motswana", "botswanan"]
    },
    {
        "num_code": "74",
        "alpha_2_code": "BV",
        "alpha_3_code": "BVT",
        "name": "Bouvet Island",
        "nationality": ["bouvet island"]
    },
    {
        "num_code": "76",
        "alpha_2_code": "BR",
        "alpha_3_code": "BRA",
        "name": "Brazil",
        "nationality": ["brazilian"]
    },
    {
        "num_code": "86",
        "alpha_2_code": "IO",
        "alpha_3_code": "IOT",
        "name": "British Indian Ocean Territory",
        "nationality": ["biot"]
    },
    {
        "num_code": "96",
        "alpha_2_code": "BN",
        "alpha_3_code": "BRN",
        "name": "Brunei Darussalam",
        "nationality": ["bruneian"]
    },
    {
        "num_code": "100",
        "alpha_2_code": "BG",
        "alpha_3_code": "BGR",
        "name": "Bulgaria",
        "nationality": ["bulgarian"]
    },
    {
        "num_code": "854",
        "alpha_2_code": "BF",
        "alpha_3_code": "BFA",
        "name": "Burkina Faso",
        "nationality": ["burkinan"]
    },
    {
        "num_code": "108",
        "alpha_2_code": "BI",
        "alpha_3_code": "BDI",
        "name": "Burundi",
        "nationality": ["burundian"]
    },
    {
        "num_code": "132",
        "alpha_2_code": "CV",
        "alpha_3_code": "CPV",
        "name": "Cape Verde",
        "nationality": ["cabo verdean"]
    },
    {
        "num_code": "116",
        "alpha_2_code": "KH",
        "alpha_3_code": "KHM",
        "name": "Cambodia",
        "nationality": ["cambodian"]
    },
    {
        "num_code": "120",
        "alpha_2_code": "CM",
        "alpha_3_code": "CMR",
        "name": "Cameroon",
        "nationality": ["cameroonian"]
    },
    {
        "num_code": "124",
        "alpha_2_code": "CA",
        "alpha_3_code": "CAN",
        "name": "Canada",
        "nationality": ["canadian"]
    },
    {
        "num_code": "136",
        "alpha_2_code": "KY",
        "alpha_3_code": "CYM",
        "name": "Cayman Islands",
        "nationality": ["caymanian"]
    },
    {
        "num_code": "140",
        "alpha_2_code": "CF",
        "alpha_3_code": "CAF",
        "name": "Central African Republic",
        "nationality": ["central african"]
    },
    {
        "num_code": "148",
        "alpha_2_code": "TD",
        "alpha_3_code": "TCD",
        "name": "Chad",
        "nationality": ["chadian"]
    },
    {
        "num_code": "152",
        "alpha_2_code": "CL",
        "alpha_3_code": "CHL",
        "name": "Chile",
        "nationality": ["chilean"]
    },
    {
        "num_code": "156",
        "alpha_2_code": "CN",
        "alpha_3_code": "CHN",
        "name": "China",
        "nationality": ["chinese"]
    },
    {
        "num_code": "162",
        "alpha_2_code": "CX",
        "alpha_3_code": "CXR",
        "name": "Christmas Island",
        "nationality": ["christmas island"]
    },
    {
        "num_code": "166",
        "alpha_2_code": "CC",
        "alpha_3_code": "CCK",
        "name": "Cocos (Keeling) Islands",
        "nationality": ["cocos island"]
    },
    {
        "num_code": "170",
        "alpha_2_code": "CO",
        "alpha_3_code": "COL",
        "name": "Colombia",
        "nationality": ["colombian"]
    },
    {
        "num_code": "174",
        "alpha_2_code": "KM",
        "alpha_3_code": "COM",
        "name": "Comoros",
        "nationality": ["comoran", "comorian"]
    },
    {
        "num_code": "178",
        "alpha_2_code": "CG",
        "alpha_3_code": "COG",
        "name": "Congo",
        "nationality": ["congolese"]
    },
    {
        "num_code": "180",
        "alpha_2_code": "CD",
        "alpha_3_code": "COD",
        "name": "Congo, The Democratic Republic of The",
        "nationality": ["congolese"]
    },
    {
        "num_code": "184",
        "alpha_2_code": "CK",
        "alpha_3_code": "COK",
        "name": "Cook Islands",
        "nationality": ["cook island"]
    },
    {
        "num_code": "188",
        "alpha_2_code": "CR",
        "alpha_3_code": "CRI",
        "name": "Costa Rica",
        "nationality": ["costa rican"]
    },
    {
        "num_code": "384",
        "alpha_2_code": "CI",
        "alpha_3_code": "CIV",
        "name": "Cote d'Ivoire",
        "nationality": ["ivorian"]
    },
    {
        "num_code": "191",
        "alpha_2_code": "HR",
        "alpha_3_code": "HRV",
        "name": "Croatia",
        "nationality": ["croatian"]
    },
    {
        "num_code": "192",
        "alpha_2_code": "CU",
        "alpha_3_code": "CUB",
        "name": "Cuba",
        "nationality": ["cuban"]
    },
    {
        "num_code": "531",
        "alpha_2_code": "CW",
        "alpha_3_code": "CUW",
        "name": "Curacao",
        "nationality": ["curaçaoan"]
    },
    {
        "num_code": "196",
        "alpha_2_code": "CY",
        "alpha_3_code": "CYP",
        "name": "Cyprus",
        "nationality": ["cypriot"]
    },
    {
        "num_code": "203",
        "alpha_2_code": "CZ",
        "alpha_3_code": "CZE",
        "name": "Czech Republic",
        "nationality": ["czech"]
    },
    {
        "num_code": "208",
        "alpha_2_code": "DK",
        "alpha_3_code": "DNK",
        "name": "Denmark",
        "nationality": ["danish"]
    },
    {
        "num_code": "262",
        "alpha_2_code": "DJ",
        "alpha_3_code": "DJI",
        "name": "Djibouti",
        "nationality": ["djiboutian"]
    },
    {
        "num_code": "212",
        "alpha_2_code": "DM",
        "alpha_3_code": "DMA",
        "name": "Dominica",
        "nationality": ["dominican"]
    },
    {
        "num_code": "214",
        "alpha_2_code": "DO",
        "alpha_3_code": "DOM",
        "name": "Dominican Republic",
        "nationality": ["dominican"]
    },
    {
        "num_code": "218",
        "alpha_2_code": "EC",
        "alpha_3_code": "ECU",
        "name": "Ecuador",
        "nationality": ["ecuadorian"]
    },
    {
        "num_code": "818",
        "alpha_2_code": "EG",
        "alpha_3_code": "EGY",
        "name": "Egypt",
        "nationality": ["egyptian"]
    },
    {
        "num_code": "222",
        "alpha_2_code": "SV",
        "alpha_3_code": "SLV",
        "name": "El Salvador",
        "nationality": ["salvadoran"]
    },
    {
        "num_code": "226",
        "alpha_2_code": "GQ",
        "alpha_3_code": "GNQ",
        "name": "Equatorial Guinea",
        "nationality": ["equatorial guinean", "equatoguinean"]
    },
    {
        "num_code": "232",
        "alpha_2_code": "ER",
        "alpha_3_code": "ERI",
        "name": "Eritrea",
        "nationality": ["eritrean"]
    },
    {
        "num_code": "233",
        "alpha_2_code": "EE",
        "alpha_3_code": "EST",
        "name": "Estonia",
        "nationality": ["estonian"]
    },
    {
        "num_code": "231",
        "alpha_2_code": "ET",
        "alpha_3_code": "ETH",
        "name": "Ethiopia",
        "nationality": ["ethiopian"]
    },
    {
        "num_code": "238",
        "alpha_2_code": "FK",
        "alpha_3_code": "FLK",
        "name": "Falkland Islands (Malvinas)",
        "nationality": ["falkland island"]
    },
    {
        "num_code": "234",
        "alpha_2_code": "FO",
        "alpha_3_code": "FRO",
        "name": "Faroe Islands",
        "nationality": ["faroese"]
    },
    {
        "num_code": "242",
        "alpha_2_code": "FJ",
        "alpha_3_code": "FJI",
        "name": "Fiji",
        "nationality": ["fijian"]
    },
    {
        "num_code": "246",
        "alpha_2_code": "FI",
        "alpha_3_code": "FIN",
        "name": "Finland",
        "nationality": ["finnish"]
    },
    {
        "num_code": "250",
        "alpha_2_code": "FR",
        "alpha_3_code": "FRA",
        "name": "France",
        "nationality": ["french"]
    },
    {
        "num_code": "254",
        "alpha_2_code": "GF",
        "alpha_3_code": "GUF",
        "name": "French Guiana",
        "nationality": ["french guianese"]
    },
    {
        "num_code": "258",
        "alpha_2_code": "PF",
        "alpha_3_code": "PYF",
        "name": "French Polynesia",
        "nationality": ["french polynesian"]
    },
    {
        "num_code": "260",
        "alpha_2_code": "TF",
        "alpha_3_code": "ATF",
        "name": "French Southern Territories",
        "nationality": ["french southern territories"]
    },
    {
        "num_code": "266",
        "alpha_2_code": "GA",
        "alpha_3_code": "GAB",
        "name": "Gabon",
        "nationality": ["gabonese"]
    },
    {
        "num_code": "270",
        "alpha_2_code": "GM",
        "alpha_3_code": "GMB",
        "name": "Gambia",
        "nationality": ["gambian"]
    },
    {
        "num_code": "268",
        "alpha_2_code": "GE",
        "alpha_3_code": "GEO",
        "name": "Georgia",
        "nationality": ["georgian"]
    },
    {
        "num_code": "276",
        "alpha_2_code": "DE",
        "alpha_3_code": "DEU",
        "name": "Germany",
        "nationality": ["german"]
    },
    {
        "num_code": "288",
        "alpha_2_code": "GH",
        "alpha_3_code": "GHA",
        "name": "Ghana",
        "nationality": ["ghanaian"]
    },
    {
        "num_code": "292",
        "alpha_2_code": "GI",
        "alpha_3_code": "GIB",
        "name": "Gibraltar",
        "nationality": ["gibraltar"]
    },
    {
        "num_code": "300",
        "alpha_2_code": "GR",
        "alpha_3_code": "GRC",
        "name": "Greece",
        "nationality": ["greek", "hellenic"]
    },
    {
        "num_code": "304",
        "alpha_2_code": "GL",
        "alpha_3_code": "GRL",
        "name": "Greenland",
        "nationality": ["greenlandic"]
    },
    {
        "num_code": "308",
        "alpha_2_code": "GD",
        "alpha_3_code": "GRD",
        "name": "Grenada",
        "nationality": ["grenadian"]
    },
    {
        "num_code": "312",
        "alpha_2_code": "GP",
        "alpha_3_code": "GLP",
        "name": "Guadeloupe",
        "nationality": ["guadeloupe"]
    },
    {
        "num_code": "316",
        "alpha_2_code": "GU",
        "alpha_3_code": "GUM",
        "name": "Guam",
        "nationality": ["guamanian", "guambat"]
    },
    {
        "num_code": "320",
        "alpha_2_code": "GT",
        "alpha_3_code": "GTM",
        "name": "Guatemala",
        "nationality": ["guatemalan"]
    },
    {
        "num_code": "831",
        "alpha_2_code": "GG",
        "alpha_3_code": "GGY",
        "name": "Guernsey",
        "nationality": ["channel island"]
    },
    {
        "num_code": "324",
        "alpha_2_code": "GN",
        "alpha_3_code": "GIN",
        "name": "Guinea",
        "nationality": ["guinean"]
    },
    {
        "num_code": "624",
        "alpha_2_code": "GW",
        "alpha_3_code": "GNB",
        "name": "Guinea-bissau",
        "nationality": ["bissau-guinean"]
    },
    {
        "num_code": "328",
        "alpha_2_code": "GY",
        "alpha_3_code": "GUY",
        "name": "Guyana",
        "nationality": ["guyanese"]
    },
    {
        "num_code": "332",
        "alpha_2_code": "HT",
        "alpha_3_code": "HTI",
        "name": "Haiti",
        "nationality": ["haitian"]
    },
    {
        "num_code": "334",
        "alpha_2_code": "HM",
        "alpha_3_code": "HMD",
        "name": "Heard Island and McDonald Islands",
        "nationality": ["heard island", "mcDonald islands"]
    },
    {
        "num_code": "336",
        "alpha_2_code": "VA",
        "alpha_3_code": "VAT",
        "name": "Holy See (Vatican City State)",
        "nationality": ["vatican"]
    },
    {
        "num_code": "340",
        "alpha_2_code": "HN",
        "alpha_3_code": "HND",
        "name": "Honduras",
        "nationality": ["honduran"]
    },
    {
        "num_code": "344",
        "alpha_2_code": "HK",
        "alpha_3_code": "HKG",
        "name": "Hong Kong",
        "nationality": ["hong kong", "hong kongese"]
    },
    {
        "num_code": "348",
        "alpha_2_code": "HU",
        "alpha_3_code": "HUN",
        "name": "Hungary",
        "nationality": ["hungarian", "magyar"]
    },
    {
        "num_code": "352",
        "alpha_2_code": "IS",
        "alpha_3_code": "ISL",
        "name": "Iceland",
        "nationality": ["icelandic"]
    },
    {
        "num_code": "356",
        "alpha_2_code": "IN",
        "alpha_3_code": "IND",
        "name": "India",
        "nationality": ["indian"]
    },
    {
        "num_code": "360",
        "alpha_2_code": "ID",
        "alpha_3_code": "IDN",
        "name": "Indonesia",
        "nationality": ["indonesian"]
    },
    {
        "num_code": "364",
        "alpha_2_code": "IR",
        "alpha_3_code": "IRN",
        "name": "Iran, Islamic Republic of",
        "nationality": ["iranian", "persian"]
    },
    {
        "num_code": "368",
        "alpha_2_code": "IQ",
        "alpha_3_code": "IRQ",
        "name": "Iraq",
        "nationality": ["iraqi"]
    },
    {
        "num_code": "372",
        "alpha_2_code": "IE",
        "alpha_3_code": "IRL",
        "name": "Ireland",
        "nationality": ["irish"]
    },
    {
        "num_code": "833",
        "alpha_2_code": "IM",
        "alpha_3_code": "IMN",
        "name": "Isle of Man",
        "nationality": ["manx"]
    },
    {
        "num_code": "376",
        "alpha_2_code": "IL",
        "alpha_3_code": "ISR",
        "name": "Israel",
        "nationality": ["israeli"]
    },
    {
        "num_code": "380",
        "alpha_2_code": "IT",
        "alpha_3_code": "ITA",
        "name": "Italy",
        "nationality": ["italian"]
    },
    {
        "num_code": "388",
        "alpha_2_code": "JM",
        "alpha_3_code": "JAM",
        "name": "Jamaica",
        "nationality": ["jamaican"]
    },
    {
        "num_code": "392",
        "alpha_2_code": "JP",
        "alpha_3_code": "JPN",
        "name": "Japan",
        "nationality": ["japanese"]
    },
    {
        "num_code": "832",
        "alpha_2_code": "JE",
        "alpha_3_code": "JEY",
        "name": "Jersey",
        "nationality": ["channel island"]
    },
    {
        "num_code": "400",
        "alpha_2_code": "JO",
        "alpha_3_code": "JOR",
        "name": "Jordan",
        "nationality": ["jordanian"]
    },
    {
        "num_code": "398",
        "alpha_2_code": "KZ",
        "alpha_3_code": "KAZ",
        "name": "Kazakhstan",
        "nationality": ["kazakhstani", "kazakh"]
    },
    {
        "num_code": "404",
        "alpha_2_code": "KE",
        "alpha_3_code": "KEN",
        "name": "Kenya",
        "nationality": ["kenyan"]
    },
    {
        "num_code": "296",
        "alpha_2_code": "KI",
        "alpha_3_code": "KIR",
        "name": "Kiribati",
        "nationality": ["i-kiribati"]
    },
    {
        "num_code": "408",
        "alpha_2_code": "KP",
        "alpha_3_code": "PRK",
        "name": "Korea, Democratic People's Republic of",
        "nationality": ["north korean"]
    },
    {
        "num_code": "410",
        "alpha_2_code": "KR",
        "alpha_3_code": "KOR",
        "name": "Korea, Republic of",
        "nationality": ["south korean"]
    },
    {
        "num_code": "414",
        "alpha_2_code": "KW",
        "alpha_3_code": "KWT",
        "name": "Kuwait",
        "nationality": ["kuwaiti"]
    },
    {
        "num_code": "417",
        "alpha_2_code": "KG",
        "alpha_3_code": "KGZ",
        "name": "Kyrgyzstan",
        "nationality": ["kyrgyzstani", "kyrgyz", "kirgiz", "kirghiz"]
    },
    {
        "num_code": "418",
        "alpha_2_code": "LA",
        "alpha_3_code": "LAO",
        "name": "Lao People's Democratic Republic",
        "nationality": ["lao", "laotian"]
    },
    {
        "num_code": "428",
        "alpha_2_code": "LV",
        "alpha_3_code": "LVA",
        "name": "Latvia",
        "nationality": ["latvian"]
    },
    {
        "num_code": "422",
        "alpha_2_code": "LB",
        "alpha_3_code": "LBN",
        "name": "Lebanon",
        "nationality": ["lebanese"]
    },
    {
        "num_code": "426",
        "alpha_2_code": "LS",
        "alpha_3_code": "LSO",
        "name": "Lesotho",
        "nationality": ["basotho"]
    },
    {
        "num_code": "430",
        "alpha_2_code": "LR",
        "alpha_3_code": "LBR",
        "name": "Liberia",
        "nationality": ["liberian"]
    },
    {
        "num_code": "434",
        "alpha_2_code": "LY",
        "alpha_3_code": "LBY",
        "name": "Libyan Arab Jamahiriya",
        "nationality": ["libyan"]
    },
    {
        "num_code": "438",
        "alpha_2_code": "LI",
        "alpha_3_code": "LIE",
        "name": "Liechtenstein",
        "nationality": ["liechtenstein"]
    },
    {
        "num_code": "440",
        "alpha_2_code": "LT",
        "alpha_3_code": "LTU",
        "name": "Lithuania",
        "nationality": ["lithuanian"]
    },
    {
        "num_code": "442",
        "alpha_2_code": "LU",
        "alpha_3_code": "LUX",
        "name": "Luxembourg",
        "nationality": ["luxembourg", "luxembourgish"]
    },
    {
        "num_code": "446",
        "alpha_2_code": "MO",
        "alpha_3_code": "MAC",
        "name": "Macao",
        "nationality": ["macanese", "chinese"]
    },
    {
        "num_code": "807",
        "alpha_2_code": "MK",
        "alpha_3_code": "MKD",
        "name": "Macedonia, The Former Yugoslav Republic of",
        "nationality": ["macedonian"]
    },
    {
        "num_code": "450",
        "alpha_2_code": "MG",
        "alpha_3_code": "MDG",
        "name": "Madagascar",
        "nationality": ["malagasy"]
    },
    {
        "num_code": "454",
        "alpha_2_code": "MW",
        "alpha_3_code": "MWI",
        "name": "Malawi",
        "nationality": ["malawian"]
    },
    {
        "num_code": "458",
        "alpha_2_code": "MY",
        "alpha_3_code": "MYS",
        "name": "Malaysia",
        "nationality": ["malaysian"]
    },
    {
        "num_code": "462",
        "alpha_2_code": "MV",
        "alpha_3_code": "MDV",
        "name": "Maldives",
        "nationality": ["maldivian"]
    },
    {
        "num_code": "466",
        "alpha_2_code": "ML",
        "alpha_3_code": "MLI",
        "name": "Mali",
        "nationality": ["malian", "malinese"]
    },
    {
        "num_code": "470",
        "alpha_2_code": "MT",
        "alpha_3_code": "MLT",
        "name": "Malta",
        "nationality": ["maltese"]
    },
    {
        "num_code": "584",
        "alpha_2_code": "MH",
        "alpha_3_code": "MHL",
        "name": "Marshall Islands",
        "nationality": ["marshallese"]
    },
    {
        "num_code": "474",
        "alpha_2_code": "MQ",
        "alpha_3_code": "MTQ",
        "name": "Martinique",
        "nationality": ["martiniquais", "martinican"]
    },
    {
        "num_code": "478",
        "alpha_2_code": "MR",
        "alpha_3_code": "MRT",
        "name": "Mauritania",
        "nationality": ["mauritanian"]
    },
    {
        "num_code": "480",
        "alpha_2_code": "MU",
        "alpha_3_code": "MUS",
        "name": "Mauritius",
        "nationality": ["mauritian"]
    },
    {
        "num_code": "175",
        "alpha_2_code": "YT",
        "alpha_3_code": "MYT",
        "name": "Mayotte",
        "nationality": ["mahoran"]
    },
    {
        "num_code": "484",
        "alpha_2_code": "MX",
        "alpha_3_code": "MEX",
        "name": "Mexico",
        "nationality": ["mexican"]
    },
    {
        "num_code": "583",
        "alpha_2_code": "FM",
        "alpha_3_code": "FSM",
        "name": "Micronesia, Federated States of",
        "nationality": ["micronesian"]
    },
    {
        "num_code": "498",
        "alpha_2_code": "MD",
        "alpha_3_code": "MDA",
        "name": "Moldova, Republic of",
        "nationality": ["moldovan"]
    },
    {
        "num_code": "492",
        "alpha_2_code": "MC",
        "alpha_3_code": "MCO",
        "name": "Monaco",
        "nationality": ["monegasque", "monacan"]
    },
    {
        "num_code": "496",
        "alpha_2_code": "MN",
        "alpha_3_code": "MNG",
        "name": "Mongolia",
        "nationality": ["mongolian"]
    },
    {
        "num_code": "499",
        "alpha_2_code": "ME",
        "alpha_3_code": "MNE",
        "name": "Montenegro",
        "nationality": ["montenegrin"]
    },
    {
        "num_code": "500",
        "alpha_2_code": "MS",
        "alpha_3_code": "MSR",
        "name": "Montserrat",
        "nationality": ["montserratian"]
    },
    {
        "num_code": "504",
        "alpha_2_code": "MA",
        "alpha_3_code": "MAR",
        "name": "Morocco",
        "nationality": ["moroccan"]
    },
    {
        "num_code": "508",
        "alpha_2_code": "MZ",
        "alpha_3_code": "MOZ",
        "name": "Mozambique",
        "nationality": ["mozambican"]
    },
    {
        "num_code": "104",
        "alpha_2_code": "MM",
        "alpha_3_code": "MMR",
        "name": "Myanmar",
        "nationality": ["burmese"]
    },
    {
        "num_code": "516",
        "alpha_2_code": "NA",
        "alpha_3_code": "NAM",
        "name": "Namibia",
        "nationality": ["namibian"]
    },
    {
        "num_code": "520",
        "alpha_2_code": "NR",
        "alpha_3_code": "NRU",
        "name": "Nauru",
        "nationality": ["nauruan"]
    },
    {
        "num_code": "524",
        "alpha_2_code": "NP",
        "alpha_3_code": "NPL",
        "name": "Nepal",
        "nationality": ["nepali", "nepalese"]
    },
    {
        "num_code": "528",
        "alpha_2_code": "NL",
        "alpha_3_code": "NLD",
        "name": "Netherlands",
        "nationality": ["dutch", "netherlandic"]
    },
    {
        "num_code": "540",
        "alpha_2_code": "NC",
        "alpha_3_code": "NCL",
        "name": "New Caledonia",
        "nationality": ["new caledonian"]
    },
    {
        "num_code": "554",
        "alpha_2_code": "NZ",
        "alpha_3_code": "NZL",
        "name": "New Zealand",
        "nationality": ["new zealand", "nz"]
    },
    {
        "num_code": "558",
        "alpha_2_code": "NI",
        "alpha_3_code": "NIC",
        "name": "Nicaragua",
        "nationality": ["nicaraguan"]
    },
    {
        "num_code": "562",
        "alpha_2_code": "NE",
        "alpha_3_code": "NER",
        "name": "Niger",
        "nationality": ["nigerien"]
    },
    {
        "num_code": "566",
        "alpha_2_code": "NG",
        "alpha_3_code": "NGA",
        "name": "Nigeria",
        "nationality": ["nigerian"]
    },
    {
        "num_code": "570",
        "alpha_2_code": "NU",
        "alpha_3_code": "NIU",
        "name": "Niue",
        "nationality": ["niuean"]
    },
    {
        "num_code": "574",
        "alpha_2_code": "NF",
        "alpha_3_code": "NFK",
        "name": "Norfolk Island",
        "nationality": ["norfolk island"]
    },
    {
        "num_code": "580",
        "alpha_2_code": "MP",
        "alpha_3_code": "MNP",
        "name": "Northern Mariana Islands",
        "nationality": ["northern marianan"]
    },
    {
        "num_code": "578",
        "alpha_2_code": "NO",
        "alpha_3_code": "NOR",
        "name": "Norway",
        "nationality": ["norwegian"]
    },
    {
        "num_code": "512",
        "alpha_2_code": "OM",
        "alpha_3_code": "OMN",
        "name": "Oman",
        "nationality": ["omani"]
    },
    {
        "num_code": "586",
        "alpha_2_code": "PK",
        "alpha_3_code": "PAK",
        "name": "Pakistan",
        "nationality": ["pakistani"]
    },
    {
        "num_code": "585",
        "alpha_2_code": "PW",
        "alpha_3_code": "PLW",
        "name": "Palau",
        "nationality": ["palauan"]
    },
    {
        "num_code": "275",
        "alpha_2_code": "PS",
        "alpha_3_code": "PSE",
        "name": "Palestinian Territory, Occupied",
        "nationality": ["palestinian"]
    },
    {
        "num_code": "591",
        "alpha_2_code": "PA",
        "alpha_3_code": "PAN",
        "name": "Panama",
        "nationality": ["panamanian"]
    },
    {
        "num_code": "598",
        "alpha_2_code": "PG",
        "alpha_3_code": "PNG",
        "name": "Papua New Guinea",
        "nationality": ["papua new guinean", "papuan"]
    },
    {
        "num_code": "600",
        "alpha_2_code": "PY",
        "alpha_3_code": "PRY",
        "name": "Paraguay",
        "nationality": ["paraguayan"]
    },
    {
        "num_code": "604",
        "alpha_2_code": "PE",
        "alpha_3_code": "PER",
        "name": "Peru",
        "nationality": ["peruvian"]
    },
    {
        "num_code": "608",
        "alpha_2_code": "PH",
        "alpha_3_code": "PHL",
        "name": "Philippines",
        "nationality": ["philippine", "filipino"]
    },
    {
        "num_code": "612",
        "alpha_2_code": "PN",
        "alpha_3_code": "PCN",
        "name": "Pitcairn",
        "nationality": ["pitcairn island"]
    },
    {
        "num_code": "616",
        "alpha_2_code": "PL",
        "alpha_3_code": "POL",
        "name": "Poland",
        "nationality": ["polish"]
    },
    {
        "num_code": "620",
        "alpha_2_code": "PT",
        "alpha_3_code": "PRT",
        "name": "Portugal",
        "nationality": ["portuguese"]
    },
    {
        "num_code": "630",
        "alpha_2_code": "PR",
        "alpha_3_code": "PRI",
        "name": "Puerto Rico",
        "nationality": ["puerto rican"]
    },
    {
        "num_code": "634",
        "alpha_2_code": "QA",
        "alpha_3_code": "QAT",
        "name": "Qatar",
        "nationality": ["qatari"]
    },
    {
        "num_code": "638",
        "alpha_2_code": "RE",
        "alpha_3_code": "REU",
        "name": "Reunion",
        "nationality": ["reunionese"]
    },
    {
        "num_code": "642",
        "alpha_2_code": "RO",
        "alpha_3_code": "ROU",
        "name": "Romania",
        "nationality": ["romanian"]
    },
    {
        "num_code": "643",
        "alpha_2_code": "RU",
        "alpha_3_code": "RUS",
        "name": "Russian Federation",
        "nationality": ["russian"]
    },
    {
        "num_code": "646",
        "alpha_2_code": "RW",
        "alpha_3_code": "RWA",
        "name": "Rwanda",
        "nationality": ["rwandan"]
    },
    {
        "num_code": "652",
        "alpha_2_code": "BL",
        "alpha_3_code": "BLM",
        "name": "Saint Barthelemy",
        "nationality": ["barthelemois"]
    },
    {
        "num_code": "654",
        "alpha_2_code": "SH",
        "alpha_3_code": "SHN",
        "name": "Saint Helena",
        "nationality": ["saint helenian"]
    },
    {
        "num_code": "659",
        "alpha_2_code": "KN",
        "alpha_3_code": "KNA",
        "name": "Saint Kitts and Nevis",
        "nationality": ["kittitian", "nevisian"]
    },
    {
        "num_code": "662",
        "alpha_2_code": "LC",
        "alpha_3_code": "LCA",
        "name": "Saint Lucia",
        "nationality": ["saint lucian"]
    },
    {
        "num_code": "663",
        "alpha_2_code": "MF",
        "alpha_3_code": "MAF",
        "name": "Saint Martin (French part)",
        "nationality": ["saint-martinoise"]
    },
    {
        "num_code": "666",
        "alpha_2_code": "PM",
        "alpha_3_code": "SPM",
        "name": "Saint Pierre and Miquelon",
        "nationality": ["saint-pierrais", "miquelonnais"]
    },
    {
        "num_code": "670",
        "alpha_2_code": "VC",
        "alpha_3_code": "VCT",
        "name": "Saint Vincent and the Grenadines",
        "nationality": ["saint vincentian", "vincentian"]
    },
    {
        "num_code": "882",
        "alpha_2_code": "WS",
        "alpha_3_code": "WSM",
        "name": "Samoa",
        "nationality": ["samoan"]
    },
    {
        "num_code": "674",
        "alpha_2_code": "SM",
        "alpha_3_code": "SMR",
        "name": "San Marino",
        "nationality": ["sammarinese"]
    },
    {
        "num_code": "678",
        "alpha_2_code": "ST",
        "alpha_3_code": "STP",
        "name": "Sao Tome and Principe",
        "nationality": ["sao tomean"]
    },
    {
        "num_code": "682",
        "alpha_2_code": "SA",
        "alpha_3_code": "SAU",
        "name": "Saudi Arabia",
        "nationality": ["saudi", "saudi arabian"]
    },
    {
        "num_code": "686",
        "alpha_2_code": "SN",
        "alpha_3_code": "SEN",
        "name": "Senegal",
        "nationality": ["senegalese"]
    },
    {
        "num_code": "688",
        "alpha_2_code": "RS",
        "alpha_3_code": "SRB",
        "name": "Serbia",
        "nationality": ["serbian"]
    },
    {
        "num_code": "690",
        "alpha_2_code": "SC",
        "alpha_3_code": "SYC",
        "name": "Seychelles",
        "nationality": ["seychellois"]
    },
    {
        "num_code": "694",
        "alpha_2_code": "SL",
        "alpha_3_code": "SLE",
        "name": "Sierra Leone",
        "nationality": ["sierra leonean"]
    },
    {
        "num_code": "702",
        "alpha_2_code": "SG",
        "alpha_3_code": "SGP",
        "name": "Singapore",
        "nationality": ["singaporean"]
    },
    {
        "num_code": "534",
        "alpha_2_code": "SX",
        "alpha_3_code": "SXM",
        "name": "Sint Maarten (Dutch part)",
        "nationality": ["sint maarten"]
    },
    {
        "num_code": "703",
        "alpha_2_code": "SK",
        "alpha_3_code": "SVK",
        "name": "Slovakia",
        "nationality": ["slovak"]
    },
    {
        "num_code": "705",
        "alpha_2_code": "SI",
        "alpha_3_code": "SVN",
        "name": "Slovenia",
        "nationality": ["slovenian", "slovene"]
    },
    {
        "num_code": "90",
        "alpha_2_code": "SB",
        "alpha_3_code": "SLB",
        "name": "Solomon Islands",
        "nationality": ["solomon island"]
    },
    {
        "num_code": "706",
        "alpha_2_code": "SO",
        "alpha_3_code": "SOM",
        "name": "Somalia",
        "nationality": ["somali", "somalian"]
    },
    {
        "num_code": "710",
        "alpha_2_code": "ZA",
        "alpha_3_code": "ZAF",
        "name": "South Africa",
        "nationality": ["south african"]
    },
    {
        "num_code": "239",
        "alpha_2_code": "GS",
        "alpha_3_code": "SGS",
        "name": "South Georgia and the South Sandwich Islands",
        "nationality": ["south georgia", "south sandwich islands"]
    },
    {
        "num_code": "728",
        "alpha_2_code": "SS",
        "alpha_3_code": "SSD",
        "name": "South Sudan",
        "nationality": ["south sudanese"]
    },
    {
        "num_code": "724",
        "alpha_2_code": "ES",
        "alpha_3_code": "ESP",
        "name": "Spain",
        "nationality": ["spanish"]
    },
    {
        "num_code": "144",
        "alpha_2_code": "LK",
        "alpha_3_code": "LKA",
        "name": "Sri Lanka",
        "nationality": ["sri lankan"]
    },
    {
        "num_code": "729",
        "alpha_2_code": "SD",
        "alpha_3_code": "SDN",
        "name": "Sudan",
        "nationality": ["sudanese"]
    },
    {
        "num_code": "740",
        "alpha_2_code": "SR",
        "alpha_3_code": "SUR",
        "name": "Suriname",
        "nationality": ["surinamese"]
    },
    {
        "num_code": "744",
        "alpha_2_code": "SJ",
        "alpha_3_code": "SJM",
        "name": "Svalbard and Jan Mayen",
        "nationality": ["svalbard"]
    },
    {
        "num_code": "748",
        "alpha_2_code": "SZ",
        "alpha_3_code": "SWZ",
        "name": "Swaziland",
        "nationality": ["swazi"]
    },
    {
        "num_code": "752",
        "alpha_2_code": "SE",
        "alpha_3_code": "SWE",
        "name": "Sweden",
        "nationality": ["swedish"]
    },
    {
        "num_code": "756",
        "alpha_2_code": "CH",
        "alpha_3_code": "CHE",
        "name": "Switzerland",
        "nationality": ["swiss"]
    },
    {
        "num_code": "760",
        "alpha_2_code": "SY",
        "alpha_3_code": "SYR",
        "name": "Syrian Arab Republic",
        "nationality": ["syrian"]
    },
    {
        "num_code": "158",
        "alpha_2_code": "TW",
        "alpha_3_code": "TWN",
        "name": "Taiwan, Province of China",
        "nationality": ["taiwanese"]
    },
    {
        "num_code": "762",
        "alpha_2_code": "TJ",
        "alpha_3_code": "TJK",
        "name": "Tajikistan",
        "nationality": ["tajikistani"]
    },
    {
        "num_code": "834",
        "alpha_2_code": "TZ",
        "alpha_3_code": "TZA",
        "name": "Tanzania, United Republic of",
        "nationality": ["tanzanian"]
    },
    {
        "num_code": "764",
        "alpha_2_code": "TH",
        "alpha_3_code": "THA",
        "name": "Thailand",
        "nationality": ["thai"]
    },
    {
        "num_code": "626",
        "alpha_2_code": "TL",
        "alpha_3_code": "TLS",
        "name": "Timor-Leste",
        "nationality": ["timorese"]
    },
    {
        "num_code": "768",
        "alpha_2_code": "TG",
        "alpha_3_code": "TGO",
        "name": "Togo",
        "nationality": ["togolese"]
    },
    {
        "num_code": "772",
        "alpha_2_code": "TK",
        "alpha_3_code": "TKL",
        "name": "Tokelau",
        "nationality": ["tokelauan"]
    },
    {
        "num_code": "776",
        "alpha_2_code": "TO",
        "alpha_3_code": "TON",
        "name": "Tonga",
        "nationality": ["tongan"]
    },
    {
        "num_code": "780",
        "alpha_2_code": "TT",
        "alpha_3_code": "TTO",
        "name": "Trinidad and Tobago",
        "nationality": ["trinidadian", "tobagonian"]
    },
    {
        "num_code": "788",
        "alpha_2_code": "TN",
        "alpha_3_code": "TUN",
        "name": "Tunisia",
        "nationality": ["tunisian"]
    },
    {
        "num_code": "792",
        "alpha_2_code": "TR",
        "alpha_3_code": "TUR",
        "name": "Turkey",
        "nationality": ["turkish"]
    },
    {
        "num_code": "795",
        "alpha_2_code": "TM",
        "alpha_3_code": "TKM",
        "name": "Turkmenistan",
        "nationality": ["turkmen"]
    },
    {
        "num_code": "796",
        "alpha_2_code": "TC",
        "alpha_3_code": "TCA",
        "name": "Turks and Caicos Islands",
        "nationality": ["turks and caicos Island"]
    },
    {
        "num_code": "798",
        "alpha_2_code": "TV",
        "alpha_3_code": "TUV",
        "name": "Tuvalu",
        "nationality": ["tuvaluan"]
    },
    {
        "num_code": "800",
        "alpha_2_code": "UG",
        "alpha_3_code": "UGA",
        "name": "Uganda",
        "nationality": ["ugandan"]
    },
    {
        "num_code": "804",
        "alpha_2_code": "UA",
        "alpha_3_code": "UKR",
        "name": "Ukraine",
        "nationality": ["ukrainian"]
    },
    {
        "num_code": "784",
        "alpha_2_code": "AE",
        "alpha_3_code": "ARE",
        "name": "United Arab Emirates",
        "nationality": ["emirati", "emirian", "emiri"]
    },
    {
        "num_code": "826",
        "alpha_2_code": "GB",
        "alpha_3_code": "GBR",
        "name": "United Kingdom",
        "nationality": ["british", "uk"]
    },
    {
        "num_code": "581",
        "alpha_2_code": "UM",
        "alpha_3_code": "UMI",
        "name": "United States Minor Outlying Islands",
        "nationality": ["american islander"]
    },
    {
        "num_code": "840",
        "alpha_2_code": "US",
        "alpha_3_code": "USA",
        "name": "United States",
        "nationality": ["american"]
    },
    {
        "num_code": "858",
        "alpha_2_code": "UY",
        "alpha_3_code": "URY",
        "name": "Uruguay",
        "nationality": ["uruguayan"]
    },
    {
        "num_code": "860",
        "alpha_2_code": "UZ",
        "alpha_3_code": "UZB",
        "name": "Uzbekistan",
        "nationality": ["uzbekistani"]
    },
    {
        "num_code": "548",
        "alpha_2_code": "VU",
        "alpha_3_code": "VUT",
        "name": "Vanuatu",
        "nationality": ["vanuatuan"]
    },
    {
        "num_code": "862",
        "alpha_2_code": "VE",
        "alpha_3_code": "VEN",
        "name": "Venezuela",
        "nationality": ["venezuelan"]
    },
    {
        "num_code": "704",
        "alpha_2_code": "VN",
        "alpha_3_code": "VNM",
        "name": "Viet Nam",
        "nationality": ["vietnamese"]
    },
    {
        "num_code": "92",
        "alpha_2_code": "VG",
        "alpha_3_code": "VGB",
        "name": "Virgin Islands, British",
        "nationality": ["british virgin island"]
    },
    {
        "num_code": "850",
        "alpha_2_code": "VI",
        "alpha_3_code": "VIR",
        "name": "Virgin Islands, American",
        "nationality": ["u.s. virgin island"]
    },
    {
        "num_code": "876",
        "alpha_2_code": "WF",
        "alpha_3_code": "WLF",
        "name": "Wallis and Futuna",
        "nationality": ["wallis and futuna", "wallisian", "futunan"]
    },
    {
        "num_code": "732",
        "alpha_2_code": "EH",
        "alpha_3_code": "ESH",
        "name": "Western Sahara",
        "nationality": ["sahrawian"]
    },
    {
        "num_code": "887",
        "alpha_2_code": "YE",
        "alpha_3_code": "YEM",
        "name": "Yemen",
        "nationality": ["yemeni"]
    },
    {
        "num_code": "894",
        "alpha_2_code": "ZM",
        "alpha_3_code": "ZMB",
        "name": "Zambia",
        "nationality": ["zambian"]
    },
    {
        "num_code": "716",
        "alpha_2_code": "ZW",
        "alpha_3_code": "ZWE",
        "name": "Zimbabwe",
        "nationality": ["zimbabwean"]
    }
];

function getCountryName(countryCode) {
    if (!countryCode) {
        return "";
    }
    const country = COUNTRIES_LIST.find((country) => country.alpha_2_code === countryCode || country.alpha_3_code === countryCode);

    return new Handlebars.SafeString(country?.name || countryCode);
}