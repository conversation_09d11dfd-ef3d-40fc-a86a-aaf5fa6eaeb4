# Node.js
# Build a general Node.js project with npm.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript
trigger:
    - develop
pool:
  vmImage: windows-latest

steps:
- task: NodeTool@0
  inputs:
    versionSpec: '14.16.0'
  displayName: 'Install Node.js'

- script: |
    npm install
  displayName: 'npm install'


- task: Npm@1
  inputs:
    command: custom
    customCommand: 'run lint'
  displayName: Run ESLint


- script: |
    npm run build
  displayName: 'npm build'


- task: CopyFiles@2
  inputs:
    sourceFolder: '$(Build.SourcesDirectory)'
    contents: '**' 
    targetFolder: $(Build.ArtifactStagingDirectory)/npm
  displayName: 'Copy package.json'   
 

- task: PublishPipelineArtifact@1
  inputs:
    targetPath: '$(Build.ArtifactStagingDirectory)/npm'
    artifactName: npm
  displayName: 'Publish npm artifact'

- task: AzureRmWebAppDeployment@4
  inputs:
    ConnectionType: 'AzureRM'
    azureSubscription: 'NPGDev-AZDevOps-Connection-Trident - Substance'
    appType: 'webApp'
    WebAppName: 'tbvi-clientportal-test'
    ResourceGroupName: 'TBVI-FileReview-test-rg'
    packageForLinux:  'D:\a\1\a\npm'
