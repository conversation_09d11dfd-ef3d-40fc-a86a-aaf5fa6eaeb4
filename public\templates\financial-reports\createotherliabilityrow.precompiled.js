(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['createotherliabilityrow'] = template({"1":function(container,depth0,helpers,partials,data) {
    return " class=\"otherLiabilityValue\" ";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data) {
    var stack1, alias1=container.lambda, alias2=container.escapeExpression, alias3=depth0 != null ? depth0 : (container.nullContext || {}), alias4=container.hooks.helperMissing, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "<tr id=\"liability-table-row-"
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"liability") : depth0)) != null ? lookupProperty(stack1,"_id") : stack1), depth0))
    + "-"
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"liability") : depth0)) != null ? lookupProperty(stack1,"type") : stack1), depth0))
    + "\" class=\"liability-row\">\r\n    <td>\r\n        "
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"liability") : depth0)) != null ? lookupProperty(stack1,"description") : stack1), depth0))
    + "\r\n    </td>\r\n    <td "
    + ((stack1 = (lookupProperty(helpers,"ifEquals")||(depth0 && lookupProperty(depth0,"ifEquals"))||alias4).call(alias3,((stack1 = (depth0 != null ? lookupProperty(depth0,"liability") : depth0)) != null ? lookupProperty(stack1,"type") : stack1),"LiabilityEndPeriod",{"name":"ifEquals","hash":{},"fn":container.program(1, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":5,"column":8},"end":{"line":5,"column":99}}})) != null ? stack1 : "")
    + ">\r\n        "
    + alias2((lookupProperty(helpers,"decimalValue")||(depth0 && lookupProperty(depth0,"decimalValue"))||alias4).call(alias3,((stack1 = (depth0 != null ? lookupProperty(depth0,"liability") : depth0)) != null ? lookupProperty(stack1,"value") : stack1),{"name":"decimalValue","hash":{},"data":data,"loc":{"start":{"line":6,"column":8},"end":{"line":6,"column":40}}}))
    + "\r\n    </td>\r\n    <td class=\"text-right\">\r\n        <button type=\"button\" class=\"btn btn-outline-secondary openEditLiability\"\r\n                data-id=\""
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"liability") : depth0)) != null ? lookupProperty(stack1,"_id") : stack1), depth0))
    + "\" data-type=\""
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"liability") : depth0)) != null ? lookupProperty(stack1,"type") : stack1), depth0))
    + "\">\r\n            <i class=\"fa fa-pencil\"></i>\r\n        </button>\r\n    </td>\r\n    <td class=\"text-left\">\r\n        <button type=\"button\" class=\"delete btn btn-danger deleteLiability\"\r\n                data-id=\""
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"liability") : depth0)) != null ? lookupProperty(stack1,"_id") : stack1), depth0))
    + "\" data-type=\""
    + alias2(alias1(((stack1 = (depth0 != null ? lookupProperty(depth0,"liability") : depth0)) != null ? lookupProperty(stack1,"type") : stack1), depth0))
    + "\">\r\n            <i class=\"fa fa-trash\"></i>\r\n        </button>\r\n    </td>\r\n</tr>";
},"useData":true});
})();