(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['premises'] = template({"1":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    "
    + ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"premises") : stack1),{"name":"each","hash":{},"fn":container.program(2, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":2,"column":4},"end":{"line":10,"column":13}}})) != null ? stack1 : "");
},"2":function(container,depth0,helpers,partials,data) {
    var helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return " <tr>\r\n        <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"address_line1") || (depth0 != null ? lookupProperty(depth0,"address_line1") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"address_line1","hash":{},"data":data,"loc":{"start":{"line":3,"column":12},"end":{"line":3,"column":29}}}) : helper)))
    + "</td>\r\n        <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"address_line2") || (depth0 != null ? lookupProperty(depth0,"address_line2") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"address_line2","hash":{},"data":data,"loc":{"start":{"line":4,"column":12},"end":{"line":4,"column":29}}}) : helper)))
    + "</td>\r\n        <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"city") || (depth0 != null ? lookupProperty(depth0,"city") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"city","hash":{},"data":data,"loc":{"start":{"line":5,"column":12},"end":{"line":5,"column":20}}}) : helper)))
    + "</td>\r\n        <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"country") || (depth0 != null ? lookupProperty(depth0,"country") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"country","hash":{},"data":data,"loc":{"start":{"line":6,"column":12},"end":{"line":6,"column":23}}}) : helper)))
    + "</td>\r\n        <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"postalcode") || (depth0 != null ? lookupProperty(depth0,"postalcode") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"postalcode","hash":{},"data":data,"loc":{"start":{"line":7,"column":12},"end":{"line":7,"column":26}}}) : helper)))
    + "</td>\r\n        <td class=\"text-center\"><i class=\"fa fa-pencil btn btn-sm royal-blue solid editpremises\" data-id=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":8,"column":106},"end":{"line":8,"column":113}}}) : helper)))
    + "\"></i></td>\r\n        <td class=\"text-center\"><i class=\"fa fa-times  btn btn-sm btn-danger deletepremises\" data-id=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":9,"column":102},"end":{"line":9,"column":109}}}) : helper)))
    + "\"></i></td>\r\n";
},"4":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = (depth0 != null ? lookupProperty(depth0,"data") : depth0)) != null ? lookupProperty(stack1,"premises") : stack1),{"name":"each","hash":{},"fn":container.program(5, data, 0),"inverse":container.program(7, data, 0),"data":data,"loc":{"start":{"line":12,"column":4},"end":{"line":32,"column":13}}})) != null ? stack1 : "");
},"5":function(container,depth0,helpers,partials,data) {
    var helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "        <tr>\r\n            <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"address_line1") || (depth0 != null ? lookupProperty(depth0,"address_line1") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"address_line1","hash":{},"data":data,"loc":{"start":{"line":14,"column":16},"end":{"line":14,"column":33}}}) : helper)))
    + "</td>\r\n            <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"address_line2") || (depth0 != null ? lookupProperty(depth0,"address_line2") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"address_line2","hash":{},"data":data,"loc":{"start":{"line":15,"column":16},"end":{"line":15,"column":33}}}) : helper)))
    + "</td>\r\n            <td>"
    + alias4(((helper = (helper = lookupProperty(helpers,"country") || (depth0 != null ? lookupProperty(depth0,"country") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"country","hash":{},"data":data,"loc":{"start":{"line":16,"column":16},"end":{"line":16,"column":27}}}) : helper)))
    + "</td>\r\n            <td class=\"justify-content-center d-flex d-flex-inline\">\r\n                <button type=\"button\" class=\"btn btn-sm royal-blue solid mr-1 editpremises\" data-id=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":18,"column":101},"end":{"line":18,"column":108}}}) : helper)))
    + "\">\r\n                    <i class=\"fa fa-pencil\"></i>\r\n                </button>\r\n                <button type=\"button\" class=\"btn btn-sm btn-danger deletepremises\" data-id=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":21,"column":92},"end":{"line":21,"column":99}}}) : helper)))
    + "\">\r\n                    <i class=\"fa fa-times\"></i>\r\n                </button>\r\n            </td>\r\n        </tr>\r\n";
},"7":function(container,depth0,helpers,partials,data) {
    return "        <tr>\r\n            <td colspan=\"4\">\r\n                No premises found\r\n            </td>\r\n        </tr>>\r\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = (lookupProperty(helpers,"ifCond")||(depth0 && lookupProperty(depth0,"ifCond"))||container.hooks.helperMissing).call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"version") : depth0),"<",5,{"name":"ifCond","hash":{},"fn":container.program(1, data, 0),"inverse":container.program(4, data, 0),"data":data,"loc":{"start":{"line":1,"column":0},"end":{"line":33,"column":11}}})) != null ? stack1 : "");
},"useData":true});
})();