let deleteInfo = {
    companyIncorporationId: '',
      relationId: ''
  };
  $('#deleteRelationModal').on('show.bs.modal', function (event) {
      let button = $(event.relatedTarget); // Button that triggered the modal
      deleteInfo = {
          companyIncorporationId: button.data('incorporation-id'),
          relationId: button.data('relation-id'),
      };

  });

  $('#deleteRelationButton').click(async function () {
    
    const mcc = window.location.pathname.split('/')[2]

    $.ajax({
        type: 'DELETE',
        url: '/masterclients/'+mcc+'/incorporate-company/' + deleteInfo["companyIncorporationId"] + '/relations/' + deleteInfo["relationId"],
        success: function (data) {
        if (data.status === 200){
            Swal.fire(
                    'Success',
                    'The relation has been deleted successfully',
                    'success'
            ).then(() => {
                reloadRelationTable(data.incorporationId, data.masterClientCode, data.relations ? data.relations : []);
                $("#deleteRelationModal").modal('hide');
            });
        }
        },
        error: function () {
            Swal.fire(
                    'Error',
                    'There was an error while trying to delete the relation',
                    'error'
            );
        },
    });
  });