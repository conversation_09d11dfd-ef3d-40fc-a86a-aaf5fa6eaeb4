let isEditLiability = false;
let openedLiabilityId = '';
let liabilityType = '';
let isEditIntangibleAsset = false;
let openedIntangibleAssetId = '';
let bankAccLoans = 0
let loansCalc = 0

let anyCompanyAccPayableSaved = $("input[name='anyCompanyAccPayable']:checked")
let didCompanyOweLongTermDebtsSaved = $("input[name='didCompanyOweLongTermDebts']:checked")
let otherLiabilitiesOwedSaved = $("input[name='otherLiabilitiesOwed']:checked")

$(document).on('show.bs.modal', '.modal', function () {
    var zIndex = 1040 + (10 * $('.modal:visible').length);
    $(this).css('z-index', zIndex);
    setTimeout(function () {
        $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
    }, 0);
});

$(document).on('change paste keyup', '.liabilities-long-term', function () {
    calculateClosingBalanceLongTermDebts();
});

$(document).on('change paste keyup', "#longTermDebts", function () {
    calculateClosingBalanceLongTermDebts();
});

$(document).on('click', '.addOtherLiabilityBtn', function(){
    $('#newLiabilityModal').modal('show')
    $('#newLiabilityModal #liabilityType').val($(this).data('type'))

})

$("input[name='companyFirstOperationLiability']").on('change', function () {
    const val = $(this).val();
    if (val === "YES"){
        $("#companyFirstOperationYesRows").hide(200);
    }
    else{
        $("#companyFirstOperationYesRows").show();
    }
});


$("input[name='anyCompanyAccPayable']").on('change', function () {
    const val = $(this).val();
    if (anyCompanyAccPayableSaved && anyCompanyAccPayableSaved.length > 0 && val === 'NO') {
        Swal.fire({
            title: 'Are you sure?',
            text: "This will wipe out the values for the questions in this section",
            showCancelButton: true,
            icon: 'info',
            backdrop: true,
            cancelButtonColor: "#6c757d",
            cancelButtonText: "No",
            confirmButtonColor: "#0081B4",
            confirmButtonText: 'Yes',
            reverseButtons: true,
        }).then((result) => {
            if (result.isConfirmed) {
                if (val === "YES") {
                    $("#haveAccountsPayableYesRows").show(200);
                }
                else {
                    $("#haveAccountsPayableYesRows").hide(200);
                    $("#haveAccountsPayableYesRows input[type='text']").val(null);
                }
                anyCompanyAccPayableSaved = $(this)
            } else {
                $("input[name='anyCompanyAccPayable'][value='" + anyCompanyAccPayableSaved.val() + "']").prop('checked', true);
            }
        })
    } else {
        if (val === "YES") {
            $("#haveAccountsPayableYesRows").show(200);
        }
        else {
            $("#haveAccountsPayableYesRows").hide(200);
            $("#haveAccountsPayableYesRows input[type='text']").val(null);
        }
        anyCompanyAccPayableSaved = $(this)
    }
    
});

$("input[name='didCompanyOweLongTermDebts']").on('change', function () {
    const val = $(this).val();
    if (didCompanyOweLongTermDebtsSaved && didCompanyOweLongTermDebtsSaved.length > 0) {
        Swal.fire({
            title: 'Are you sure?',
            text: "This will wipe out the values for the questions in this section",
            showCancelButton: true,
            icon: 'info',
            backdrop: true,
            cancelButtonColor: "#6c757d",
            cancelButtonText: "No",
            confirmButtonColor: "#0081B4",
            confirmButtonText: 'Yes',
            reverseButtons: true,
        }).then((result) => {
            if (result.isConfirmed) {
                if (val === "YES") {
                    $("#companyOwnLongTermYesRows").show(200);
                }
                else if (val === "NO") {
                    $("#companyOwnLongTermNoRows").show(200).css("display", "flex");
                    $("#companyOwnLongTermYesRows").hide();
                    $("#companyOwnLongTermYesRows input[type='text']").val(null);
                }
                else {
                    $("#companyOwnLongTermYesRows").hide();
                    $("#companyOwnLongTermYesRows input[type='text']").val(null);
                }
                didCompanyOweLongTermDebtsSaved = $(this)
            } else {
                $("input[name='didCompanyOweLongTermDebts'][value='" + didCompanyOweLongTermDebtsSaved.val() + "']").prop('checked', true);
            }
        })
    } else {
        if (val === "YES") {
            $("#companyOwnLongTermYesRows").show(200);
        }
        else if (val === "NO") {
            $("#companyOwnLongTermNoRows").show(200).css("display", "flex");
            $("#companyOwnLongTermYesRows").hide();
        }
        else {
            $("#companyOwnLongTermYesRows").hide();
        }
        didCompanyOweLongTermDebtsSaved = $(this)
    }

});

$("input[name='otherLiabilitiesOwed']").on('change', function () {
    const val = $(this).val();
    if (otherLiabilitiesOwedSaved && otherLiabilitiesOwedSaved.length > 0 && val === 'NO') {
        Swal.fire({
            title: 'Are you sure?',
            text: "This will delete all the other liabilities at the end of the financial period created",
            showCancelButton: true,
            icon: 'info',
            backdrop: true,
            cancelButtonColor: "#6c757d",
            cancelButtonText: "No",
            confirmButtonColor: "#0081B4",
            confirmButtonText: 'Yes',
            reverseButtons: true,
        }).then(function (result) {
            if (result.isConfirmed) {
                    if (val === "YES") {
                        $("#longTermOtherLiabilitiesYesRows").show(200);
                    }
                    else {
                        $("#longTermOtherLiabilitiesYesRows").hide();
                        $("#longTermOtherLiabilitiesYesRows input[type='text']").val(null);
                        if ($('#LiabilityEndPeriodTable tr').length > 0) {
                            $('#LiabilityEndPeriodTable tr').remove();
                            $(`#LiabilityEndPeriodTableContainer`).css('display', 'none')
                        }
                    }
                    otherLiabilitiesOwedSaved = $(this)

            } else {
                $("input[name='otherLiabilitiesOwed'][value='" + otherLiabilitiesOwedSaved.val() + "']").prop('checked', true);
            }
        })

    } else {
        if (val === "YES") {
            $("#longTermOtherLiabilitiesYesRows").show(200);
        }
        else {
            $("#longTermOtherLiabilitiesYesRows").hide();
            $("#longTermOtherLiabilitiesYesRows input[type='text']").val(null);
        }
        otherLiabilitiesOwedSaved = $(this)
    }
    
});

function calculateClosingBalanceLongTermDebts(){
    
    const longTermStartPeriod = $("#longTermDebts").val();
    const ltDebtsRepayments = $('#ltDebtsRepayments').val()
    const longTermStartPeriodNum = parseFloat(longTermStartPeriod.replace(/,/g, '')) || 0;
    const ltDebtsRepaymentsNum = parseFloat(ltDebtsRepayments.replace(/,/g, '')) || 0;
    const exchanges = $("input[id^='exchangeRate_banckAcc-']")
    const loansPayableReceived = $("input[id^='loansPayableReceived_banckAcc-']")
    const loansPayablePaid = $("input[id^='loansPayablePaid_banckAcc-']")
    const ltDebtsReceivedByShareholder = parseNumber($('#ltDebtsReceivedByShareholder').val())
    
    if(bankAccLoans === 0) {
        for (let index = 0; index < $("div[id^='bankAccount-']").length; index++){
            const accountExchange = exchanges[index].value;
            loansCalc += (loansPayableReceived[index].value * accountExchange) + (loansPayablePaid[index].value * accountExchange) 
            bankAccLoans++
        }
    }
    
    let total = longTermStartPeriodNum + ltDebtsReceivedByShareholder +  ltDebtsRepaymentsNum - loansCalc
    $("#ltDebtsClosingBalance").val(showDecimalValue(total)).trigger('keyup');
}


$(document).on('click', '.openEditLiability', function () {
    openEditStartLiabilityModal($(this).data('id'), $(this).data('type'))
})

$(document).on('click', '.deleteLiability', function () {
    deleteStartLiability($(this).data('id'), $(this).data('type'))
})

function openEditStartLiabilityModal (liabilityId, liabilityType) {
    $.ajax({
        type: "GET",
        url: document.location.href + "/liabilities/" + liabilityId + '/' + liabilityType,
        contentType: "application/json; charset=utf-8",
        success: function (data) {
            openedLiabilityId = liabilityId
            isEditLiability = true
            $('#newLiabilityModal').modal();
            $('#newLiabilityModal #liabilityDescription').val(data.liability.description)
            $('#newLiabilityModal #liabilityValue').val(showDecimalValue(data.liability.value.toFixed(2)))
            $('#newLiabilityModal #liabilityType').val(liabilityType)
        },
        error: function () {
            toastr["warning"]('Liability could not be edited, please try again later.', 'Error!');
        }
    })
}

$(document).on('click', '#submitLiabilityBtn', function () {
    if ($('#newLiabilityModal #liabilityValue').val() > 0){
        $('#newLiabilityModal input[required]:visible').trigger('keyup');
        const liability = {
            type: $('#newLiabilityModal #liabilityType').val(),
            description: $('#newLiabilityModal #liabilityDescription').val(),
            value: $('#newLiabilityModal #liabilityValue').val()
        }
        if ($("#newLiabilityModal .is-invalid:visible").length === 0) {
            $.ajax({
                type: isEditLiability ? "PUT" :"POST",
                url: "./" +  window.location.pathname.split('/')[6] + "/liabilities" + (isEditLiability ? `/${openedLiabilityId}/${liability.type}` : ''),
                data: JSON.stringify(liability),
                contentType: "application/json; charset=utf-8",
                success: function (data) {
                    if (data.status === 200) {
                        data.liability.type = liability.type;
                        let template = Handlebars.templates.createotherliabilityrow;
                        let html = template({liability: data.liability});
                        if($(`#${liability.type}Table tr`).length === 0) {
                            $(`#${liability.type}TableContainer`).css('display', 'block')
                        }
                        if (isEditLiability) {
                            $(`#liability-table-row-${data.liability._id}-${liability.type}`).replaceWith(html)
                        } else {
                            $(`#${liability.type}Table`).append(html);
                        }
                        calculateOtherLiabilitiesTotal()
                        $('#newLiabilityModal').modal('hide');
                        $('#newLiabilityModal input').val('')
                        openedLiabilityId = ''
                        isEditLiability = false
                    } else {
                        toastr["warning"](data.message, 'Error!');
                    }
                },
                error: function (err) {
                    toastr["warning"]('Liability could not be saved, please try again later.', 'Error!');
                }
            });
        }
    } else {
        toastr["warning"]('Please provide only positive values.', 'Error!');
    }
})

function deleteStartLiability (id, liabilityType) {
    Swal.fire(
        {
            title: "Delete?",
            text: "Are you sure you want to delete this liability?",
            icon: "question",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            cancelButtonText: "Cancel",
            confirmButtonText: "Ok"
        }).then(async function (t) {
            if (t.value) {
                $.ajax({
                    type: "DELETE",
                    url: document.location.href + "/liabilities/" + id + "/" + liabilityType,
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                        if (data.status === 200) {
                            $('#liability-table-row-' + id + '-' + liabilityType).remove();
                            if($(`#${liabilityType}Table tr`).length === 0) {
                                $(`#${liabilityType}TableContainer`).css('display', 'none')
                            }
                            calculateOtherLiabilitiesTotal()
                        } else {
                            Swal.fire(
                                {
                                    title: "Error!",
                                    text: data.message,
                                    icon: "warning",
                                    showCancelButton: false,
                                    confirmButtonColor: "#3085d6",
                                    confirmButtonText: "Ok"
                                });
                        }
                    },
                    error: function () {
                        toastr["warning"]('Liability could not be deleted, please try again later.', 'Error!');
                    }
                });
            }
        });
}

function calculateOtherLiabilitiesTotal () {
    let value = 0
    $('.otherLiabilityValue').each(function () {
        value += parseFloat(($(this).text()).replace(/,/g, ''))
    })
    $('#otherLiabilitiesTotal').val(showDecimalValue(value.toFixed(2)))
}
