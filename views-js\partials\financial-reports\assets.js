let totalLoandInCashAcc = 0
let totalInvestmentCashAcc = 0
let fixedAssetsCashAcc = 0
let balanceOfTangibleAssets = 0
let intangibleAdquisitionCalc = 0
let intangibleSalesCalc = 0
let intangibleBalance = 0
let otherAssetsValue = 0
let otherAssetsBalance = 0
let acquisitionCalc = 0
let saleCalc = 0
let acquisitionCalc2 = 0
let saleCalc2 = 0
// conts
let loansCont = 0
let InvestmentCont = 0
let fixedAssetsCont = 0
let TangibleAssetsCont = 0
let intangibleValueCont = 0
let intangibleBalanceCont = 0
let otherAssetsCont = 0
let otherAssetsBalanceCont = 0
let valueOfTangible = 0

let isEditAsset = false
let openedAssetId = ''

let isLoansAndReceivablesSaved = $("input[name='isLoansAndReceivablesEndPeriod']:checked")
let anyInvestmentsSaved = $("input[name='anyInvestments']:checked")
let isTangibleFixAssetsSaved = $("input[name='isTangibleFixAssets']:checked")
let isFixedAssetsCotributedSaved = $("input[name='isFixedAssetsCotributed']:checked")
let isOtherAssetsSaved = $("input[name='isOtherAssets']:checked")
let isIntangibleAssetsSaved = $("input[name='isIntangibleAssets']:checked")
let isIntagibleAssetsContributedSaved = $("input[name='isIntagibleAssetsContributed']:checked")


$("input[name='isLoansAndReceivablesEndPeriod']").on('change', function () {
    const val = $(this).val();
    if (isLoansAndReceivablesSaved && isLoansAndReceivablesSaved.length > 0) {
        Swal.fire({
            title: 'Are you sure?',
            text: "This will wipe out the values for the questions in this section",
            showCancelButton: true,
            icon: 'info',
            backdrop: true,
            cancelButtonColor: "#6c757d",
            cancelButtonText: "No",
            confirmButtonColor: "#0081B4",
            confirmButtonText: 'Yes',
            reverseButtons: true,
        }).then((result) => {
            if (result.isConfirmed) {
                if (val === "YES") {
                    $(".loansInputYes").show(200).css('display', 'flex');
                    $("#loansRowsNo").hide();
                    $("#loansRowsNo input").val(null);
                } else {
                    $("#loansRowsNo").show(200).css('display', 'flex');
                    $(".loansInputYes").hide();                    
                    $(".loansInputYes input").val(null);
                }
                isLoansAndReceivablesSaved = $(this)
            } else {
                $("input[name='isLoansAndReceivablesEndPeriod'][value='" + isLoansAndReceivablesSaved.val() + "']").prop('checked', true);
            }
        })
    } else {
        if (val === "YES") {
            $(".loansInputYes").show(200).css('display', 'flex');
            $("#loansRowsNo").hide();
            $("#loansRowsNo input").val(null);
        } else {
            $("#loansRowsNo").show(200).css('display', 'flex');
            $(".loansInputYes").hide();                    
            $(".loansInputYes input").val(null);
        }
        isLoansAndReceivablesSaved = $(this)
    }
    
});

$("input[name='anyInvestments']").on('change', function () {
    const val = $(this).val();
    if (anyInvestmentsSaved && anyInvestmentsSaved.length > 0 && val === 'NO') {
        Swal.fire({
            title: 'Are you sure?',
            text: "This will wipe out the values for the questions in this section",
            showCancelButton: true,
            icon: 'info',
            backdrop: true,
            cancelButtonColor: "#6c757d",
            cancelButtonText: "No",
            confirmButtonColor: "#0081B4",
            confirmButtonText: 'Yes',
            reverseButtons: true,
        }).then((result) => {
            if (result.isConfirmed) {
                if (val === "YES") {
                    $("#investmentRows").show(200);
                } else {
                    $("#investmentRows").hide(200);
                    $("#investmentRows input").val(null);
                }
                anyInvestmentsSaved = $(this)
            } else {
                $("input[name='anyInvestments'][value='" + anyInvestmentsSaved.val() + "']").prop('checked', true);
            }
        })
    } else {
        if (val === "YES") {
            $("#investmentRows").show(200);
        } else {
            $("#investmentRows").hide(200);
            $("#investmentRows input").val(null);
        }
        anyInvestmentsSaved = $(this)
    }
});

$("input[name='isTangibleFixAssets']").on('change', function () {
    const val = $(this).val();
    if (isTangibleFixAssetsSaved && isTangibleFixAssetsSaved.length > 0 && val === 'NO') {
        Swal.fire({
            title: 'Are you sure?',
            text: "This will wipe out the values for the questions in this section",
            showCancelButton: true,
            icon: 'info',
            backdrop: true,
            cancelButtonColor: "#6c757d",
            cancelButtonText: "No",
            confirmButtonColor: "#0081B4",
            confirmButtonText: 'Yes',
            reverseButtons: true,
        }).then((result) => {
            if (result.isConfirmed) {
                if (val === "YES"){
                    $("#tangibleFixesRows").show(200);
                } else {
                    $("#tangibleFixesRows").hide(200);
                    $("#tangibleFixesRows input[type='text']").val(null);
                    $("#tangibleFixesRows input[type='radio']").prop('checked', false);
                    $('#fixedAssetsContributedNo').hide()
                    $('#fixedAssetsContributedYes').hide()
                    isFixedAssetsCotributedSaved = $("input[name='isFixedAssetsCotributed']:checked")
                }
                isTangibleFixAssetsSaved = $(this)
            } else {
                $("input[name='isTangibleFixAssets'][value='" + isTangibleFixAssetsSaved.val() + "']").prop('checked', true);
            }
        })
    } else {
        if (val === "YES"){
            $("#tangibleFixesRows").show(200);
        } else {
            $("#tangibleFixesRows").hide(200);
            $("#tangibleFixesRows input").val(null);
        }
        isTangibleFixAssetsSaved = $(this)
    }
});

$("input[name='isFixedAssetsCotributed']").on('change', function () {
    const val = $(this).val();
    if (isFixedAssetsCotributedSaved && isFixedAssetsCotributedSaved.length > 0) {
        Swal.fire({
            title: 'Are you sure?',
            text: "This will wipe out the values for the questions in this section",
            showCancelButton: true,
            icon: 'info',
            backdrop: true,
            cancelButtonColor: "#6c757d",
            cancelButtonText: "No",
            confirmButtonColor: "#0081B4",
            confirmButtonText: 'Yes',
            reverseButtons: true,
        }).then((result) => {
            if (result.isConfirmed) {
                $("#deprecationExpenses").trigger('keyup')
                if (val === "YES"){
                    $("#fixedAssetsContributedYes").show(200);
                    $("#fixedAssetsContributedNo").hide(200);
                    $("#fixedAssetsContributedNo input").val(null)
                } else {
                    $("#fixedAssetsContributedYes").hide(200);
                    $("#fixedAssetsContributedNo").show(200);
                    $("#fixedAssetsContributedYes input").val(null)
                }
                isFixedAssetsCotributedSaved = $(this)
            } else {
                $("input[name='isFixedAssetsCotributed'][value='" + isFixedAssetsCotributedSaved.val() + "']").prop('checked', true);
            }
        })
    } else {
        $("#deprecationExpenses").trigger('keyup')
        if (val === "YES"){
            $("#fixedAssetsContributedYes").show(200);
            $("#fixedAssetsContributedNo").hide(200);
            $("#fixedAssetsContributedNo input").val(null)
        } else {
            $("#fixedAssetsContributedYes").hide(200);
            $("#fixedAssetsContributedNo").show(200);
            $("#fixedAssetsContributedYes input").val(null)
        }
        isFixedAssetsCotributedSaved = $(this)
    }
    
});

$("input[name='isOtherAssets']").on('change', async function () {
    const val = $(this).val();

    if (isOtherAssetsSaved && isOtherAssetsSaved.length > 0 && val === 'NO') {
        Swal.fire({
            title: 'Are you sure?',
            text: "This will wipe out the values for the questions in this section",
            showCancelButton: true,
            icon: 'info',
            backdrop: true,
            cancelButtonColor: "#6c757d",
            cancelButtonText: "No",
            confirmButtonColor: "#0081B4",
            confirmButtonText: 'Yes',
            reverseButtons: true,
        }).then(function (result) {
            if (result.isConfirmed) {
                    if (val === "YES"){
                        $("#otherAssetsRows").show(200);
                    } else {
                        $("#otherAssetsRows").hide(200);
                        $("#otherAssetsRows input").val(null);
                    }
                isOtherAssetsSaved = $(this)
            } else {
                $("input[name='isOtherAssets'][value='" + isOtherAssetsSaved.val() + "']").prop('checked', true);
            }
        })
    } else {
        if (val === "YES"){
            $("#otherAssetsRows").show(200);
        } else {
            $("#otherAssetsRows").hide(200);
            $("#otherAssetsRows input").val(null);
        }
        isOtherAssetsSaved = $(this)
    }
});

$("input[name='isIntangibleAssets']").on('change', function () {
    const val = $(this).val();
    if (isIntangibleAssetsSaved && isIntangibleAssetsSaved.length > 0 && val === 'NO') {
        Swal.fire({
            title: 'Are you sure?',
            text: "This will wipe out the values for the questions in this section",
            showCancelButton: true,
            icon: 'info',
            backdrop: true,
            cancelButtonColor: "#6c757d",
            cancelButtonText: "No",
            confirmButtonColor: "#0081B4",
            confirmButtonText: 'Yes',
            reverseButtons: true,
        }).then((result) => {
            if (result.isConfirmed) {
                if (val === "YES"){
                    $("#intangibleAssetsRows").show(200);
                } else {
                    $("#intangibleAssetsRows").hide(200);
                    $("#intangibleAssetsRows input[type='text']").val(null);
                    $("#intangibleAssetsRows input[type='radio']").prop('checked', false);
                    $("#intagibleAssetsContributedRowsYes").hide();
                    $("#intagibleAssetsContributedRowsNo").hide();
                    isIntagibleAssetsContributedSaved = $("input[name='isIntagibleAssetsContributed']:checked")
                }
                isIntangibleAssetsSaved = $(this)
            } else {
                $("input[name='isIntangibleAssets'][value='" + isIntangibleAssetsSaved.val() + "']").prop('checked', true);
            }
        })
    } else {
        if (val === "YES"){
            $("#intangibleAssetsRows").show(200);
        } else {
            $("#intangibleAssetsRows").hide(200);
        }
        isIntangibleAssetsSaved = $(this)
    }

});

$("input[name='isIntagibleAssetsContributed']").on('change', function () {
    const val = $(this).val();
    if (isIntagibleAssetsContributedSaved && isIntagibleAssetsContributedSaved.length > 0) {
        Swal.fire({
            title: 'Are you sure?',
            text: "This will wipe out the values for the questions in this section",
            showCancelButton: true,
            icon: 'info',
            backdrop: true,
            cancelButtonColor: "#6c757d",
            cancelButtonText: "No",
            confirmButtonColor: "#0081B4",
            confirmButtonText: 'Yes',
            reverseButtons: true,
        }).then((result) => {
            if (result.isConfirmed) {
                if (val === "YES"){
                    $("#intagibleAssetsContributedRowsYes").show(200);
                    $("#intagibleAssetsContributedRowsNo").hide(200);
                    $("#intagibleAssetsContributedRowsNo input").val(null)
                } else {
                    $("#intagibleAssetsContributedRowsYes").hide(200);
                    $("#intagibleAssetsContributedRowsNo").show(200);
                    $("#intagibleAssetsContributedRowsYes input").val(null)
                }
                isIntagibleAssetsContributedSaved = $(this)
            } else {
                $("input[name='isIntagibleAssetsContributed'][value='" + isIntagibleAssetsContributedSaved.val() + "']").prop('checked', true);
            }
        })
    } else {
        if (val === "YES"){
            $("#intagibleAssetsContributedRowsYes").show(200);
            $("#intagibleAssetsContributedRowsNo").hide(200);
            $("#intagibleAssetsContributedRowsNo input").val(null)
        } else {
            $("#intagibleAssetsContributedRowsYes").hide(200);
            $("#intagibleAssetsContributedRowsNo").show(200);
            $("#intagibleAssetsContributedRowsYes input").val(null)
        }
        isIntagibleAssetsContributedSaved = $(this)
    }
});

$(document).on('click', '.addOtherAssetBtn', function () {
    const AssetType = $(this).data('type')
    const newPosition = $(`input[name="other${AssetType}[]"]`).length
    let template = Handlebars.templates.createotherdescriptionrow;

    let newOtherRow = template({
        type: AssetType,
        typeLbl: "Asset",
        position: newPosition,
        positionLbl: newPosition + 1
    });
    $(`#other${AssetType}List`).append(newOtherRow);
})

$(document).on('change paste keyup','.closing_balance', function() {
    calculateClosingBalance()
})

$(document).on('change paste keyup', '.investment_balance', function() {
    calculateInvestmentGainLoss()
})

$(document).on('change paste keyup', 'input[name="isFixedAssetsCotributed"], #tangibleAssetsContributed, #tangibleFixedAssets, #deprecationExpenses', function() {
    calculateValueOfTangibleAssets()
    calculateBalanceOfTangibleAssets()
})

$(document).on('change paste keyup', 'input[name="isIntagibleAssetsContributed"], #amortisationExpenses, #intangibleAssets, #intangibleAssetsContributed', function() {
    calculateValueOfIntangibleEndReportPeriod()
    calculateBalanceOfIntangibeAssets()
})


$(document).on('change paste keyup', "#isOtherAssetsAcquiredYes, input[id^='otherAssetsStartPeriod']", function() {
    calculateValueOfOtherAssets()
})

$(document).on('change paste keyup', "#otherAssetsContributed", function() {
    calculateValueOfOtherAssetsEndPeriod()
})

function  calculateValueOfOtherAssets() {
    let valueOfOtherAssets = 0
    $(".otherStartAssetsValue").each(function () {
        valueOfOtherAssets += parseFloat(($(this).text()).replace(/,/g, ''))
    })
    $('#valueOfOtherAssetsEndPeriod').val(showDecimalValue(valueOfOtherAssets.toFixed(2)))
}

function calculateClosingBalance () {
    const loanReceivablePaid = $("input[id^='loanReceivablePaid_banckAcc-']")
    const loanReceivableReceived = $("input[id^='loanReceivableReceived_banckAcc-']")
    const receivablesPaid = $("input[id^='receivablesPaid_banckAcc-']")
    const receivablesReceived = $("input[id^='receivablesReceived_banckAcc-']")
    const exchanges = $("input[id^='exchangeRate_banckAcc-']")
    let totalLoanInbankAccounts = 0;

    for (let index = 0; index < $("div[id^='bankAccount-']").length; index++) {
        const accountExchange = exchanges[index].value;
        const calc = -(loanReceivablePaid[index].value * accountExchange) - (loanReceivableReceived[index].value * accountExchange) -
            (receivablesPaid[index].value * accountExchange) - (receivablesReceived[index].value * accountExchange);
        loansCont++
        totalLoanInbankAccounts -= calc
    }

    if ($('input[name="isLoansAndReceivablesEndPeriod"]:checked').length > 0 && $('#declaredDividedIncome').val() !== '') {
        const closingBalanceTotal = parseNumber($('#loansAndReceivables').val()) - parseFloat(totalLoanInbankAccounts) + parseNumber($("#interestReceivableOnTheLoan").val())
        - parseNumber($("#loansReceivedByShareholder").val()) + parseNumber($('#paymentsOfLoansByShareholder').val()) + parseNumber($("#invoicesIssued").val()) + parseNumber($("#declaredDividedIncome").val())
        $("#balanceOfLoansReceivables").val(showDecimalValue(closingBalanceTotal.toFixed(2)))
    }
    
}

function calculateInvestmentGainLoss () {
    const investmentsAcquisition = $("input[id^='investmentsAcquisition_banckAcc-']")
    const investmentsSale = $("input[id^='investmentsSale_banckAcc-']")
    const exchanges = $("input[id^='exchangeRate_banckAcc-']")
    

    if(InvestmentCont === 0) {
        for (let index = 0; index < $("div[id^='bankAccount-']").length; index++){
            const accountExchange = exchanges[index].value;
            const calc = (investmentsAcquisition[index].value * accountExchange) + (investmentsSale[index].value * accountExchange) 
            InvestmentCont++
            totalInvestmentCashAcc += calc
        }
    }

    if($('input[name="anyInvestments"]:checked').length > 0 && $("#totalAmountOfInvestments").val() !== '') {
        const balanceOfInvestment =  parseNumber($("#totalAmountOfInvestments").val()) - parseNumber($("#otherFinancialAssets").val()) + parseFloat(totalInvestmentCashAcc) 
        - parseNumber($("#investmentsTransferredByShareholder").val()) - parseNumber($("#investmentTransferredToTheShareholder").val())
        $('#balanceOfInvestment').val(showDecimalValue(balanceOfInvestment.toFixed(2)))
    }
}


function calculateValueOfTangibleAssets () {
    const tangibleAcquisition = $("input[id^='tangibleAcquisition_banckAcc-']")
    const tangibleSale = $("input[id^='tangibleSale_banckAcc-']")
    const exchanges = $("input[id^='exchangeRate_banckAcc-']")
    const isFixedAssetsCotributed = $('input[name="isFixedAssetsCotributed"]:checked').val();


    if($('input[name="isTangibleFixAssets"]:checked').length === 0){
        return null;
    }

    const tangibleFixedAssets = parseNumber($("#tangibleFixedAssets").val());
    const deprecationExpenses =  parseNumber($("#deprecationExpenses").val())
    
    if(valueOfTangible === 0) {
        for (let index = 0; index < $("div[id^='bankAccount-']").length; index++){
            const accountExchange = exchanges[index].value;
            acquisitionCalc += (tangibleAcquisition[index].value * accountExchange)
            saleCalc += (tangibleSale[index].value * accountExchange)
            valueOfTangible++
        }
    }
    let tangibleAssetsCalc = tangibleFixedAssets + deprecationExpenses - acquisitionCalc - saleCalc
    if( isFixedAssetsCotributed ==="YES" && $("#tangibleAssetsContributed").val() !== '') {
        tangibleAssetsCalc +=  parseNumber($("#tangibleAssetsContributed").val())
        $("#valueTangibleAssetsEndPeriod").val(showDecimalValue(tangibleAssetsCalc.toFixed(2)))
    }
    else if (isFixedAssetsCotributed ==="NO"){
        $("#tangibleAssetsEndPeriod").val(showDecimalValue(tangibleAssetsCalc > 0 ? tangibleAssetsCalc.toFixed(2) : 0))    
    }
}

function calculateBalanceOfTangibleAssets () {
    const tangibleAcquisition = $("input[id^='tangibleAcquisition_banckAcc-']")
    const tangibleSale = $("input[id^='tangibleSale_banckAcc-']")
    const exchanges = $("input[id^='exchangeRate_banckAcc-']")

    if(TangibleAssetsCont === 0) {
        for (let index = 0; index < $("div[id^='bankAccount-']").length; index++){
            const accountExchange = exchanges[index].value;
            acquisitionCalc2 += (tangibleAcquisition[index].value * accountExchange)
            saleCalc2 += (tangibleSale[index].value * accountExchange)
            TangibleAssetsCont++
        }
    }
    
    if(parseNumber($("#tangibleFixedAssets").val()) > 0  && $("#deprecationExpenses").val() !== '' && $("#tangibleAssetsContributed").val() !== '') {
        const balance = saleCalc2 + acquisitionCalc2 - parseNumber($("#tangibleFixedAssets").val())
        $('#balanceOfTangibleAssets').val(showDecimalValue(balance.toFixed(2)))
    } else if (parseNumber($("#tangibleFixedAssets").val()) === 0) {
        $('#balanceOfTangibleAssets').val(showDecimalValue(0))
    }
}

function calculateValueOfIntangibleEndReportPeriod () {
    const intangibleAcquisition = $("input[id^='intangibleAcquisition_banckAcc-']")
    const intangibleSale = $("input[id^='intangibleSale_banckAcc-']")
    const exchanges = $("input[id^='exchangeRate_banckAcc-']")
    const isIntangibleAssetsCotributed = $('input[name="isIntagibleAssetsContributed"]:checked').val();
 
    if(intangibleValueCont === 0) {
        for (let index = 0; index < $("div[id^='bankAccount-']").length; index++){
            const accountExchange = exchanges[index].value;
            intangibleAdquisitionCalc += (intangibleAcquisition[index].value * accountExchange)
            intangibleSalesCalc += (intangibleSale[index].value * accountExchange)
            intangibleValueCont++
        }
    }

    let value =  parseNumber($('#intangibleAssets').val()) + parseNumber($('#amortisationExpenses').val()) - intangibleAdquisitionCalc -intangibleSalesCalc;
    if(isIntangibleAssetsCotributed === "YES"){
        value += parseNumber($("#intangibleAssetsContributed").val())
        $("#intangibleAssetsEndFinancialPeriod").val(showDecimalValue(isNaN(value) ? 0.00 : value.toFixed(2)))
    }
    else if(isIntangibleAssetsCotributed === "NO" && $("#amortisationExpenses").val() !== '') {
        $("#intangibleAssetsEndReportPeriod").val(showDecimalValue(isNaN(value) ? 0.00 : value.toFixed(2)))
    }
    
}


function calculateBalanceOfIntangibeAssets () {
    const intangibleAcquisition = $("input[id^='intangibleAcquisition_banckAcc-']")
    const intangibleSale = $("input[id^='intangibleSale_banckAcc-']")
    const exchanges = $("input[id^='exchangeRate_banckAcc-']")
    
    if(intangibleBalanceCont === 0) {
        for (let index = 0; index < $("div[id^='bankAccount-']").length; index++){
            const accountExchange = exchanges[index].value;
            const calc = (intangibleAcquisition[index].value * accountExchange) - (intangibleSale[index].value * accountExchange)
            intangibleBalanceCont++
            intangibleBalance += calc
        }
    }

    if ($("#amortisationExpenses").val() !== '') {
        const balance = parseNumber($("#intangibleAssetsEndFinancialPeriod").val()) - parseNumber($('#intangibleAssets').val())
                        + parseFloat(intangibleBalance) + parseNumber($('#amortisationExpenses').val())
        $('#balanceOfIntangibleAssets').val(showDecimalValue(balance.toFixed(2)))
    }
    
}

$(document).on('click', '.addOtherAssetBtn', function () {
    $('#newAssetModalReport').modal('show')
    $('#newAssetModalReport #assetType').val($(this).data('type'))
})

$(document).on('click', '.openEditAsset', function () {
    openEditStartAssetModal($(this).data('id'), $(this).data('type'))
})

$(document).on('click', '.deleteAsset', function () {
    deleteStartAsset($(this).data('id'), $(this).data('type'))
})

$(document).on('click', '#submitAssetBtn', async function () {
    $('#newAssetModalReport input[required]:visible').trigger('keyup');
    const asset = {
        type: $('#newAssetModalReport #assetType').val(),
        description: $('#assetDescription').val(),
        value: $('#assetValue').val()
    }
    if ($("#newAssetModalReport .is-invalid:visible").length === 0) {
        
        $.ajax({
            type: isEditAsset ? "PUT" :"POST",
            url: "./" +  window.location.pathname.split('/')[6] + "/assets" + (isEditAsset ? `/${openedAssetId}/${asset.type}` : ''),
            data: JSON.stringify(asset),
            contentType: "application/json; charset=utf-8",
            success: function (data) {
                if (data.status === 200) {
                    data.asset.type = asset.type;
                    let template = Handlebars.templates.createotherassetrow;
                    let html = template({asset: data.asset});
                    if($(`#${asset.type}Table tr`).length === 0) {
                        $(`#${asset.type}TableContainer`).css('display', 'block')
                    }
                    if (isEditAsset) {
                        $(`#asset-table-row-${data.asset._id}-${asset.type}`).replaceWith(html)
                    } else {
                        $(`#${asset.type}Table`).append(html);
                    }
                    $('#newAssetModalReport').modal('hide');
                    $('#newAssetModalReport input').val('')
                    openedAssetId = ''
                    isEditAsset = false
                    calculateValueOfOtherAssets()
                } else {
                    toastr["warning"](data.message, 'Error!');
                }
            },
            error: function (err) {
                toastr["warning"]('Asset could not be saved, please try again later.', 'Error!');
            }
        });
    }
});

function openEditStartAssetModal (assetId, assetType) {
    $.ajax({
        type: "GET",
        url: document.location.href + "/assets/" + assetId + '/' + assetType,
        contentType: "application/json; charset=utf-8",
        success: function (data) {
            openedAssetId = assetId
            isEditAsset = true
            $('#newAssetModalReport').modal();
            $('#newAssetModalReport #assetDescription').val(data.asset.description)
            $('#newAssetModalReport #assetValue').val(showDecimalValue(data.asset.value.toFixed(2)))
            $('#newAssetModalReport #assetType').val(assetType)
        },
        error: function () {
            toastr["warning"]('Asset could not be edited, please try again later.', 'Error!');
        }
    })
}

function deleteStartAsset(id, assetType) {
    Swal.fire(
        {
            title: "Delete?",
            text: "Are you sure you want to delete this asset?",
            icon: "question",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            cancelButtonText: "Cancel",
            confirmButtonText: "Ok"
        }).then(async function (t) {
            if (t.value) {
                
                $.ajax({
                    type: "DELETE",
                    url: document.location.href + "/assets/" + id + "/" + assetType,
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                        if (data.status === 200) {
                            $('#asset-table-row-' + id + '-' + assetType).remove();
                            if($(`#${assetType}Table tr`).length === 0) {
                                $(`#${assetType}TableContainer`).css('display', 'none')
                            }
                            calculateValueOfOtherAssets()
                        } else {
                            Swal.fire(
                                {
                                    title: "Error!",
                                    text: data.message,
                                    icon: "warning",
                                    showCancelButton: false,
                                    confirmButtonColor: "#3085d6",
                                    confirmButtonText: "Ok"
                                });
                        }
                    },
                    error: function () {
                        toastr["warning"]('Asset could not be deleted, please try again later.', 'Error!');
                    }
                });
            }
        });
}
