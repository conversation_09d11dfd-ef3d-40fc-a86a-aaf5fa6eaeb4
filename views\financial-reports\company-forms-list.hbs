<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <h4 class="mt-3">Overview of all financial reports for entity: <B>{{company.name}}</B>
                        ({{company.code}})</h4>
                    <br>
                    <div class="table-responsive">
                        <table id="company-reports-table" class="table table-striped mb-0 ">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Fiscal Period</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                    <th>Download PDF</th>
                                    {{!-- <th>Download PL PDF</th> --}}
                                    <th>Delete</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each reportForms}}
                                <tr>
                                    <td>{{formatDate updatedAt "MMMM D YYYY h:mm:ss a"}}</td>
                                    <td>
                                        {{#if financialPeriod.start}}
                                        {{formatDate financialPeriod.start "YYYY/MM/DD"}} - {{formatDate
                                        financialPeriod.end "YYYY/MM/DD"}} </td>
                                    {{/if}}


                                    <td>
                                        <div class="d-flex">
                                            <div class="d-flex align-items-center">{{getStatusLabel}}</div>
                                            <div class="ml-2">
                                                {{#ifCond status '===' 'HELP IN PROGRESS'}}
                                                    <i class="fa fa-hand-paper-o font-size-logo color-green" aria-hidden="true" data-toggle="tooltip" placement="top" 
                                                                title="Your help request is in progress."></i>
                                                {{else}}
                                                    {{#ifCond status '===' 'INFORMATION REQUEST'}}
                                                        <a href="/masterclients/{{../masterClientCode}}/company-files"><i class="fa fa-hand-paper-o information-request font-size-logo color-orange cursor-pointer" data-toggle="tooltip" placement="top" 
                                                                    title="A TridentTrust officer has sent a Request for Information."></i></a>
                                                    {{else}}
                                                        {{#ifCond status '===' 'HELP COMPLETED'}}
                                                            <i class="fa fa-hand-paper-o font-size-logo font-size-logo color-gray" aria-hidden="true" data-toggle="tooltip" placement="top" 
                                                                    title="The request for assistance is complete. You may download your completed PDF file at this time"></i>
                                                        {{else}} 
                                                            {{#ifCond status '===' 'HELP REQUEST'}}
                                                                <i class="fa fa-hand-paper-o font-size-logo color-orange"  aria-hidden="true" data-toggle="tooltip" placement="top" 
                                                                    title="You have requested assistance from Trident, a Trident Trust employee will be in contact with you."></i>
                                                            {{/ifCond}}
                                                        {{/ifCond}}
                                                    {{/ifCond}}           
                                                {{/ifCond}} 
                                            </div>          
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {{#if allowContinue}}
                                            {{#ifEquals status 'RE-OPEN'}}
                                                <button data-mcc="{{../masterClientCode}}" data-code="{{../company.code}}" data-id="{{_id}}"
                                                    data-reopened-id="{{reopenedId}}" class="btn solid royal-blue  showReopenContinueModal">
                                                    Continue
                                                </button>
                                            {{else}}
                                                <a href="/masterclients/{{../masterClientCode}}/financial-reports/companies/{{../company.code}}/{{_id}}"
                                                    class="btn btn-primary waves-effect waves-light">Continue</a>
                                            {{/ifEquals}}


                                        {{/if}}

                                        {{#if lockedByPeriodLimit}}
                                            <strong data-toggle="tooltip" data-placement="top"
                                                title="The report cannot be edited, nine months have passed since the fiscal year end date.">
                                                Locked <i class="fa fa fa-lock  fa-lg"></i>
                                            </strong>
                                        {{/if}}
                                    </td>
                                    <td>
                                        {{#if allowDownload}}
                                        <a href="/masterclients/{{../masterClientCode}}/financial-reports/companies/{{../company.code}}/{{_id}}/report.pdf"
                                            target="_blank" class="btn solid royal-blue">Download</a>
                                        {{/if}}
                                    </td>
                                    <td>
                                        {{#if allowDelete}}
                                        <button id="deleteFinancialReport" class="btn solid btn-danger"
                                            data-mcc="{{../masterClientCode}}" data-company="{{../company.code}}"
                                            data-id="{{_id}}">
                                            <i class="fa fa-times"></i>
                                        </button>
                                        {{/if}}
                                    </td>
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                    </div> <!-- end .padding -->
                    <br>

                    <div class="row">
                        <div class="col-md-12">
                            <a href="/masterclients/{{masterClientCode}}/financial-reports"
                                class="btn btn-secondary waves-effect waves-light width-xl">Back</a>
                        </div>
                    </div>

                </div> <!-- end card-box-->
            </div> <!-- end col -->
        </div> <!-- end table-responsive-->
    </div> <!-- end card-box -->
    </div><!-- end col-->
</main>

<script type="text/javascript" src="/views-js/financial-reports/company-forms-list.js"></script>