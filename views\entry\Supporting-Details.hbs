<main class="">
  <div class="container">
    <div class="row">
      <div class="col-12">
        <div class='contour'>
          {{#ifCond entry.version '<' 5}} 
            <h2>5. Supporting Details</h2>
          {{else}}
            <h2>6. Supporting Details</h2>
          {{/ifCond}}
          {{# if validationErrors }}
          {{# each validationErrors }}
          {{renderValidationMessage this.msg this.field}}
          {{/each}}
          {{/if}}
          <form method="POST" class='enquiry' autocomplete="off" id="submitForm">
            <input type="text" hidden name="csrf-token" value="{{csrfToken}}">
            <div class="container-fluid">
              <h4 class="page-title"></h4>
              <div class="row">
                <div class="col-lg-12">
                  <div class="card">
                    <div class="card-body">
                      <div class="row">
                        <div class="col-md-12">
                          <div class="form-group mb-3">
                            <label class="mb-2" for="supportComment">
                              {{#if entry.relevant_activities.none.selected}} 
                                Please provide any comment to support your Economic Substance Declaration:
                              {{else}}
                                Please provide any comment to support your Economic Substance Declaration (Optional):
                              {{/if}} 
                            </label>
                          </div>
                        </div>
                        <br>
                        <div class="col-md-12">
                          <div class="form-group mb-3">
                            <textarea class="form-control" id="supportComment" name="supportComment"
                                {{#if entry.relevant_activities.none.selected}} 
                                  title="Provide an explanation for your statement as you have selected 'none' for your relevant activities."
                                  data-toggle="tooltip" data-placement="top"
                                {{/if}}
                              rows="5">{{entry.supporting_details.support_comment}}</textarea>
                          </div>
                        </div>
                      </div>
                      <div class="row">
                        <div class="col-md-8">
                          <div class="form-group mb-3">
                            <label for="evidenceType">
                              Supporting Attachments
                            </label>
                          </div>
                        </div>
                        <div class="col-md-4">
                          <div class="text-right">
                            <button type="button" class="btn px-4 solid royal-blue" data-toggle="modal"
                              data-target="#upload-modal" id="supportingDetailsAttachmentUploadBtn"
                              name="SubmitSupportingDetailsAttachment" data-field="SupportingDetailsAttachment-"
                              data-entry="{{entry._id}}"> Upload
                              document(s)</button>
                          </div>
                        </div>
                      </div>
                      <div id="uploaded_attachments" class="my-2">
                      </div>

                      <!-- PAGE-->
                      <div class="row">
                        <div class="col-md-12">
                          <div class="progress">
                            {{#ifCond entry.version '<' 5}} 
                              <div id="progressBarSupportingV4" class="progress-bar" role="progressbar" aria-valuenow="5" aria-valuemin="0" aria-valuemax="6">5 of 6 </div>
                            {{else}}
                              <div id="progressBarSupportingV5" class="progress-bar" role="progressbar" aria-valuenow="6" aria-valuemin="0" aria-valuemax="7">6 of 7</div>
                            {{/ifCond}}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-md-8">
                      <div class="form-group mb-2">
                        <input type="submit" name="submit" value="Previous page"
                          class="btn btn-secondary waves-effect waves-light width-xl" />
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="form-group mb-2" align="right">
                        <input type="submit" name="submit" value="Save & next page"
                          class="btn btn-primary waves-effect waves-light width-xl" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
  {{> uploadModal entryId=entry._id}}
</main>
<script type='text/javascript' src='/templates/uploadedfiles.precompiled.js'></script>
<script type='text/javascript' src="/views-js/entry/Suporting-Details.js"></script>
