let unassignId;

$('#unassignModal').on('show.bs.modal', function (event) {
    let button = $(event.relatedTarget); // Button that triggered the modal
    const statusPerOfficer = {
      "fr": ['SUBMITTED','ASSIGNED', 'ON HOLD', 'SEND TO FR BY QA',
          'SEND TO FR BY CO'],
        "qa": ['REVIEWED', 'IN PROGRESS BY QA', 'SEND TO QA BY CO', 'ASSIGNED QA BY CO'],
        "co": ['COMPLIANCE', 'VALIDATED QA', 'COMPLIANCE BY FR', 'COMPLIANCE BY QA']
    }
    unassignId = button.data('id');
    const reviewStatus = button.data('review-status');
    ;

    if (statusPerOfficer['fr'].includes(reviewStatus)){
      $("#fileReviewerOfficer").show();
    }
    else if(statusPerOfficer['qa'].includes(reviewStatus)){
        $("#fileReviewerOfficer").show();
        $("#qualityOfficer").show();
    }
    else if(statusPerOfficer['co'].includes(reviewStatus)){
        $("#fileReviewerOfficer").show();
        $("#qualityOfficer").show();
        $("#complianceOfficer").show();
    }
});

$("#unassignOfficerForm").submit(function (e) {
    e.preventDefault();
    $("#unassignBtn").prop('disabled', true);
    
    $.ajax({
        type: 'POST',
        url: '/file-reviewer/compliances/' + unassignId + '/unassign-officer',
        data: $(this).serialize(),
        success: (response) => {
          if (response.status === 200){
              Swal.fire('Success', response.message, 'success').then(() => {
                $("#unassignModal").modal('hide');
                  $("#unassignBtn").prop('disabled', false);
                  location.reload();
              });
          }
          else{
              Swal.fire('Error', response.message, 'error').then(() => {
                  $("#unassignModal").modal('hide');
                  $("#unassignBtn").prop('disabled', false);
              });
          }

        },
        error: (err) => {
          console.log(err);
            Swal.fire('Error', 'There was an error at moment to unassign the officer.', 'error');
            $("#unassignBtn").prop('disabled', false);
        },
    });
});

$('#unassignModal').on('hidden.bs.modal', function (event) {
    $(".officerCheck").prop('checked', false);
    $("#unassignBtn").prop('disabled', false);
    $("#fileReviewerOfficer").hide();
    $("#qualityOfficer").hide();
    $("#complianceOfficer").hide();
});