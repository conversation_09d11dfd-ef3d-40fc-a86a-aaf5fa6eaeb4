<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class='contour'>
                    <h2>
                        2. Entity Details
                    </h2>



                    {{#if validationErrors }}
                    {{#each validationErrors }}
                    {{renderValidationMessage this.msg this.field}}
                    {{/each}}
                    {{/if}}

                    <form method="POST" class='enquiry' id="entityDetailsForm" autocomplete="off">
                        <input type="text" hidden name="csrf-token" value="{{csrfToken}}">
                        <div class="container-fluid">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="card">
                                        <div class="card-body">
                                            <!-- ENTITY TAXPAYER IDENTIFICATION FIELD-->
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <div class="form-group mb-3">
                                                        <label class="mb-2" for="TIN">D. Entity Taxpayer
                                                            Identification Number (TIN)</label>
                                                    </div>
                                                </div>

                                                <div class="col-md-4">
                                                    <input type="text" name="TIN" id="TIN" class="form-control" placeholder="TIN" 
                                                        value="{{entry.entity_details.TIN}}" />
                                                </div>

                                            </div>

                                            <!-- GROSS TOTAL ANNUAL INCOME -->
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <label id="totalGrossEntityLbl"  for="totalGrossEntity">
                                                        E. Gross total annual income of the entity
                                                        <span class="fa-stack tooltip-wrapper tooltip-top-margin" data-toggle="tooltip" data-placement="top"
                                                            data-html="true"
                                                            title="The currency selected will be the currency used throughout the submission">
                                                            <i class="fa fa-info-circle fa-stack fa-lg"></i>
                                                        </span>
                                                    </label >
                                                </div>

                                                <div class="col-md-4">

                                                    <select name="totalAnnualGrossCurrency" id="totalAnnualGrossCurrency" class="form-control w-100" data-toggle="select2"
                                                     data-value="{{entry.entity_details.totalAnnualGrossCurrency}}">
                                                        {{#each currencies}}
                                                        <option value="{{cc}}" {{#if ../entry.entity_details.totalAnnualGrossCurrency}} {{#ifEquals cc
                                                            ../entry.entity_details.totalAnnualGrossCurrency }} selected {{/ifEquals}} {{else}} {{#ifEquals cc 'USD'
                                                            }} selected {{/ifEquals}} {{/if}}>
                                                            {{cc}} - {{name}}
                                                        </option>
                                                        {{/each}}

                                                    </select>

                                                    <input type="text" id="totalAnnualGross" class="form-control mt-3 autonumber" data-a-sep="," data-m-dec="2"
                                                        name="totalAnnualGross" value="{{entry.entity_details.totalAnnualGross}}" placeholder="0.0" />

                                                </div>
                                            </div>

                                            <!-- BUSINESS ADDRESS IS SAME-->
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <div class="form-group mb-3">
                                                        <label class="mb-2" for="isSameBusinessAddress">F. Business
                                                            address is same as registered address?*</label>
                                                    </div>
                                                </div>

                                                <div class="col-md-4" align="right">
                                                    <div class="radio form-check-inline">
                                                        <input type="radio" id="isSameBusinessAddressYes" 
                                                            name="isSameBusinessAddress" value="Yes" {{#ifCond
                                                            entry.entity_details.isSameBusinessAddress "===" true }}
                                                            checked {{/ifCond}}>
                                                        <label for="isSameBusinessAddressYes">Yes</label>
                                                    </div>
                                                    <div class="radio form-check-inline">
                                                        <input type="radio" id="isSameBusinessAddressNo" 
                                                            name="isSameBusinessAddress" value="No" {{#ifCond
                                                            entry.entity_details.isSameBusinessAddress "===" false }}
                                                            checked {{/ifCond}}>
                                                        <label for="isSameBusinessAddressNo">No</label>
                                                    </div>
                                                </div>
                                            </div>

                                            <div id="showBusinessAddressRows" class="pl-3
                                            {{#ifCond entry.entity_details.isSameBusinessAddress "===" undefined }} hide-element {{/ifCond}}">
                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <label id="businessAddress1Lbl" for="businessAddress1">
                                                            Address Line 1 (street #, name & city):
                                                        </label>
                                                    </div>

                                                    <div class="col-md-4 pl-1">
                                                        <input type="text" name="businessAddress1" id="businessAddress1"
                                                            class="form-control" maxlength="100"
                                                            {{#ifCond entry.entity_details.isSameBusinessAddress "===" true }} disabled {{/ifCond}}
                                                            value="{{entry.entity_details.businessAddress.address_line1}}" />
                                                    </div>
                                                </div>


                                                <div class="row">
                                                    <div class="col-md-8">
                                                        <label id="businessAddress2Lbl" for="businessAddress2">
                                                            Address Line 2:
                                                        </label>
                                                    </div>

                                                    <div class="col-md-4 pl-1">
                                                        <input type="text" name="businessAddress2" id="businessAddress2"
                                                            class="form-control" maxlength="100"
                                                            {{#ifCond entry.entity_details.isSameBusinessAddress "===" true }} disabled {{/ifCond}}
                                                            value="{{entry.entity_details.businessAddress.address_line2}}" />
                                                    </div>
                                                </div>

                                                <div class="row mb-3">
                                                    <div class="col-md-8">
                                                        <label id="businessAddressCountryLbl"
                                                            for="businessAddressCountry" maxlength="100">
                                                            Country:
                                                        </label>
                                                    </div>

                                                    <div class="col-md-4 pl-1">
                                                        <select class="form-control w-100" id="businessAddressCountry"
                                                            name="businessAddressCountry"
                                                            {{#ifCond entry.entity_details.isSameBusinessAddress "===" true }} disabled {{/ifCond}}
                                                            data-toggle="select2"
                                                            data-value="{{entry.entity_details.businessAddress.country}}">
                                                            <option value="">Select country</option>
                                                            {{#each countries}}
                                                            <option value="{{alpha_3_code}}"
                                                                {{#ifCond alpha_3_code '===' ../entry.entity_details.businessAddress.country }} 
                                                                    selected 
                                                                {{/ifCond}}
                                                            >
                                                                {{name}}
                                                            </option>
                                                            {{/each}}
                                                        </select>
                                                    </div>
                                                </div>

                                            </div>

                                            <div class="row">
                                                <div class="col-md-8">
                                                    <label id="nameOfMNEGrouplbl" for="nameOfMNEGroup">
                                                        G. Name of MNE Group (if applicable):
                                                    </label>
                                                </div>
                                            
                                                <div class="col-md-4 pl-2">
                                                    <input type="text" name="nameOfMNEGroup" id="nameOfMNEGroup" class="form-control" maxlength="100"
                                                        value="{{entry.entity_details.nameOfMNEGroup}}" />
                                                </div>
                                            </div>

                                            <!-- Does entity have an ultimate parent entity-->
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <div class="form-group mb-3">
                                                        <label class="mb-2" for="hasUltimateParents">
                                                            H. Does entity have an ultimate parent entity?
                                                            <span class="fa-stack tooltip-wrapper tooltip-top-margin" data-toggle="tooltip"
                                                                data-placement="top" data-html="true" 
                                                                title="<small>A “ultimate parent” means an entity that meets the following criteria:
                                                                    <br>
                                                                    (a) it owns directly or indirectly a sufficient interest in the corporate and legal entity such that it is required to
                                                                    prepare consolidated financial statements under accounting principles generally applied in its jurisdiction of
                                                                    residence, or would be so required if its equity interest were traded on a public securities exchange in its
                                                                    jurisdiction of residence; and
                                                                    <br>
                                                                    (b) here is no other entity that owns directly or indirectly an interest described in paragraph (a) above in the first
                                                                    mentioned entity.</small>"><i class="fa fa-info-circle fa-stack fa-lg"></i>
                                                            </span>
                                                        </label>
                                                    </div>
                                                </div>

                                                <div class="col-md-4" align="right">
                                                    <div class="radio form-check-inline"> 
                                                        <input type="radio" id="hasUltimateParentsYes" 
                                                            name="hasUltimateParents" value="Yes" {{#ifCond
                                                            entry.entity_details.hasUltimateParents "===" true }}
                                                            checked {{/ifCond}}>
                                                        <label for="hasUltimateParentsYes">Yes</label>
                                                    </div>
                                                    <div class="radio form-check-inline">
                                                        <input type="radio" id="hasUltimateParentsNo" 
                                                            name="hasUltimateParents" value="No" {{#ifCond
                                                            entry.entity_details.hasUltimateParents "===" false }}
                                                            checked {{/ifCond}}>
                                                        <label for="hasUltimateParentsNo">No</label>
                                                    </div>
                                                </div>
                                            </div>

                                            <div id="showUltimateParentRows" class="pl-3 hide-element">
                                                <div class="row mb-2">
                                                    <div class="col-12">
                                                        <button type="button" class="btn solid royal-blue"
                                                            data-toggle="modal" data-target="#entityParentModal"
                                                            data-parent-type="ultimate" data-id="{{entry._id}}">
                                                            <i class="fa fa-plus pr-2"></i>Add Ultimate Parent
                                                        </button>
                                                    </div>
                                                </div>

                                                <div>
                                                    <div class="table-responsive">
                                                        <table class="table table-striped">
                                                            <thead>
                                                                <tr>
                                                                    <th>Name</th>
                                                                    <th>Alternative Name</th>
                                                                    <th>Jurisdiction of Formation</th>
                                                                    <th>Incorporation Number</th>
                                                                    <th>TIN or other number</th>
                                                                    <th></th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="ultimateParentsTableBody">
                                                                {{#each entry.entity_details.ultimateParents}}
                                                                <tr id="ultimate-parent-table-row-{{_id}}"
                                                                    class="ultimate-parent-row">
                                                                    <td> {{parentName}} </td>
                                                                    <td> {{alternativeName}} </td>
                                                                    <td> {{getCountryName jurisdiction}} </td>
                                                                    <td> {{incorporationNumber}} </td>
                                                                    <td> {{TIN}} </td>
                                                                    <td class="justify-content-end text-right d-flex">
                                                                        <button type="button"
                                                                            class="btn btn-sm royal-blue solid mr-1"
                                                                            data-parent-type="ultimate"
                                                                            data-id="{{../entry._id}}"
                                                                            data-parent-id="{{_id}}" data-toggle="modal"
                                                                            data-target="#entityParentModal">
                                                                            <i class="fa fa-pencil"></i>
                                                                        </button>
                                                                        <button type="button"
                                                                            class="btn btn-sm btn-danger deleteParentEntity"
                                                                            data-id="{{../entry._id}}" data-type="ultimate"
                                                                            data-parent-id="{{_id}}">
                                                                            <i class="fa fa-times"></i>
                                                                        </button>
                                                                    </td>
                                                                </tr>
                                                                {{else}}
                                                                <tr>
                                                                    <td colspan="6">
                                                                        No ultimate entity parents found
                                                                    </td>
                                                                </tr>
                                                                {{/each}}
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Does entity have a Immediate parent entity?-->
                                            <div class="row">
                                                <div class="col-md-8">
                                                    <div class="form-group mb-3">
                                                        <label class="mb-2" for="hasImmediateParents">
                                                            I. Does entity have a Immediate parent entity? <span class="fa-stack tooltip-wrapper tooltip-top-margin" data-toggle="tooltip"
                                                                data-placement="top" data-html="true"
                                                                title="<small>A “immediate parent” means any entity(ies) that own(s) directly 25% or more of the ownership or voting interests in the
                                                                corporate and legal entity and the immediate parent may be a corporate or a non-corporate entity, for example a
                                                                partnership</small>"><i class="fa fa-info-circle fa-stack fa-lg"></i>
                                                            </span>
                                                        </label>
                                                    </div>
                                                </div>

                                                <div class="col-md-4" align="right">
                                                    <div class="radio form-check-inline">
                                                        <input type="radio" id="hasImmediateParentsYes" 
                                                            name="hasImmediateParents" value="Yes" {{#ifCond
                                                            entry.entity_details.hasImmediateParents "===" true }}
                                                            checked {{/ifCond}}>
                                                        <label for="hasImmediateParentsYes">Yes</label>
                                                    </div>
                                                    <div class="radio form-check-inline">
                                                        <input type="radio" id="hasImmediateParentsNo" 
                                                            name="hasImmediateParents" value="No" {{#ifCond
                                                            entry.entity_details.hasImmediateParents "===" false }}
                                                            checked {{/ifCond}}>
                                                        <label for="hasImmediateParentsNo">No</label>
                                                    </div>
                                                </div>
                                            </div>

                                            <div id="showImmediateParentRows" class="pl-3 hide-element">
                                                <div class="row mb-2">
                                                    <div class="col-12">
                                                        <button type="button" class="btn solid royal-blue"
                                                            data-toggle="modal" data-target="#entityParentModal"
                                                            data-parent-type="immediate" data-id="{{entry._id}}">
                                                            <i class="fa fa-plus pr-2"></i>Add Immediate Parent
                                                        </button>
                                                    </div>
                                                </div>

                                                <div>
                                                    <div class="table-responsive">
                                                        <table class="table table-striped">
                                                            <thead>
                                                                <tr>
                                                                    <th>Name</th>
                                                                    <th>Alternative Name</th>
                                                                    <th>Jurisdiction of Formation</th>
                                                                    <th>Incorporation Number</th>
                                                                    <th>TIN or other number</th>
                                                                    <th></th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="immediateParentsTableBody">
                                                                {{#each entry.entity_details.immediateParents}}
                                                                <tr id="immediate-parent-table-row-{{_id}}"
                                                                    class="immediate-parent-row">
                                                                    <td> {{parentName}} </td>
                                                                    <td> {{alternativeName}} </td>
                                                                    <td> {{getCountryName jurisdiction}} </td>
                                                                    <td> {{incorporationNumber}} </td>
                                                                    <td> {{TIN}} </td>
                                                                    <td class="justify-content-end text-right d-flex">
                                                                        <button type="button"
                                                                            class="btn btn-sm royal-blue solid mr-1"
                                                                            data-parent-type="immediate"
                                                                            data-id="{{../entry._id}}"
                                                                            data-parent-id="{{_id}}" data-toggle="modal"
                                                                            data-target="#entityParentModal">
                                                                            <i class="fa fa-pencil"></i>
                                                                        </button>
                                                                        <button type="button"
                                                                            class="btn btn-sm btn-danger deleteParentEntity"
                                                                            data-id="{{../entry._id}}" data-type="immediate"
                                                                            data-parent-id="{{_id}}">
                                                                            <i class="fa fa-times"></i>
                                                                        </button>
                                                                    </td>
                                                                </tr>
                                                                {{else}}
                                                                <tr>
                                                                    <td colspan="6">
                                                                        No immediate entity parents found
                                                                    </td>
                                                                </tr>
                                                                {{/each}}
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-12">
                                                    <div class="progress">
                                                        <div class="progress-bar width-28" role="progressbar" aria-valuenow="2" aria-valuemin="0"
                                                            aria-valuemax="7">2 of 7</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="form-group mb-2">
                                                <input type="submit" name="submit" value="Previous page"
                                                    class="btn btn-secondary waves-effect waves-light width-xl" />
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group mb-2" align="right">
                                                <input type="submit" name="submit" value="Save & next page"
                                                    class="btn btn-primary waves-effect waves-light width-xl" />
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</main>
<!-- MODAL FOR ULTIMATE AND IMMEDIATE PARENTS -->
{{>substance/modals/entity-parent-modal countries=countries}}

<!-- Plugins js -->
<script src="/javascripts/libs/jquery-mask-plugin/jquery.mask.min.js"></script>
<script src="/javascripts/libs/autonumeric/autoNumeric-min.js"></script>

<!-- Init js-->
<script src="/javascripts/form-masks.init.js"></script>

<script type="text/javascript" src="/views-js/entry/v5/Entity-Details.js"></script>
