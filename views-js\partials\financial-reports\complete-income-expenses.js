$(document).ready(function () {
    calculateGrossProfit();
    calculateCompleteTotalValues($('.complete-expenses'), $('#completeDetailsExpensesTotal'));
    calculateNetIncome();

});

$(document).on('change paste keyup', '.complete-income', function () {
    calculateGrossProfit();
    calculateNetIncome();
});

$(document).on('change paste keyup', '.complete-expenses', function () {
    calculateCompleteTotalValues($('.complete-expenses'), $('#completeDetailsExpensesTotal'));
});


$('#completeDetailsGrossProfit').on('keyup', function () {
    calculateNetIncome();
})

$('#completeDetailsExpensesTotal').on('keyup', function () {
    calculateNetIncome();
})

$("#completeReportCurrency").on('change', function () {
    const val = $(this).val();
    $(".complete-currency").html(`${val}`)
})

function calculateGrossProfit(){
    const revenueStr = $('#completeDetailsRevenue').val();
    const costOfSalesStr = $('#completeDetailsCostOfSales').val();

    const revenueTotal = parseFloat(revenueStr.replace(/,/g, '')) || 0;
    const costOfSalesTotal = parseFloat(costOfSalesStr.replace(/,/g, '')) || 0;

    $('#completeDetailsGrossProfit').val(showDecimalValue(revenueTotal - costOfSalesTotal))
}


function calculateNetIncome() {
    const incomeTotalStr = $('#completeDetailsGrossProfit').val();
    const expensesTotalStr = $('#completeDetailsExpensesTotal').val();

    const incomeTotal = parseFloat(incomeTotalStr.replace(/,/g, '')) || 0;
    const expensesTotal = parseFloat(expensesTotalStr.replace(/,/g, '')) || 0;


    $('.net-income-value').val(showDecimalValue(incomeTotal - expensesTotal))
}