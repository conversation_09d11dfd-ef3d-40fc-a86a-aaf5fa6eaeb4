function refreshUploadedFiles() {
  const entityId = window.location.pathname
  $.get('/substance/entry/'+entityId.split('/')[3]+'/uploaded-files', {}, function (data) {

    const template = Handlebars.templates.uploadedfiles;

    const files = [...data?.support_attachments || []];

    let d = {
      title: "Supporting Attachments",
      files: files,
      field: "SupportingDetailsAttachment-" + urlPart
    };
    const html1 = template(d);

    $("#uploaded_attachments").html(html1);
  });

}

$(document).ready(function () {
  refreshUploadedFiles();
});