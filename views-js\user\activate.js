$("#activateBtn").on('click', async function (e) {
    e.preventDefault();
    $("#activateBtn").prop('disabled', true);

    try {
        const recaptcha_site = $("#activateBtn").data('sitekey');
        const isTokenSet = await setCaptchaToken(recaptcha_site);
        if (isTokenSet) {
            $("#activateForm").submit();
        } else {
            $("#activateBtn").prop('disabled', false);
        }
    }
    catch (e) {
        $("#activateBtn").prop('disabled', false);
    }
});