<div id="directionAndManagementContent">
    <!-- 4b.a -->
    <div class="row ">
        <div class="col-md-8">
            <div class="form-group mb-3">
                <label class="mb-2" for="ActivityDirectedinBVI">Is the
                    relevant activity directed and managed in the
                    Virgin Islands?</label>
            </div>
        </div>
        <div class="col-md-4" align="right">
            <div class="radio form-check-inline">
                <input type="radio" id="ActivityDirectedinBVIYes" name="ActivityDirectedinBVI" value="Yes" {{#if
                    data.management_in_bvi}}checked{{/if}} data-toggle="tooltip" data-placement="top"
                    title="For a legal entity to be directed and managed from the BVI, it must conduct an adequate number of board meetings in the BVI (i.e., the quorum of directors must be physically present in the territory for the board meetings)" />
                <label for="ActivityDirectedinBVIYes">Yes</label>
            </div>
            <div class="radio form-check-inline">
                <input type="radio" id="ActivityDirectedinBVINo" name="ActivityDirectedinBVI" value="No" {{#unless
                    data.management_in_bvi}}checked{{/unless}} data-toggle="tooltip" data-placement="top"
                    title="For a legal entity to be directed and managed from the BVI, it must conduct an adequate number of board meetings in the BVI (i.e., the quorum of directors must be physically present in the territory for the board meetings)" />
                <label for="ActivityDirectedinBVINo">No</label>
            </div>
        </div>
    </div>
    
    <!-- 4b.b -->
    <div class="row ">
        <div class="col-md-8">
            <div class="form-group mb-3">
                <label class="mb-2" for="ListOfPersons">
                    Please provide details of the persons responsible for direction and management of the relevant
                    activity:</label>
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group mb-3">
                <input type="button" class="btn solid royal-blue width-xs" value="Add Person" id="addManager">
            </div>
        </div>
    </div>
    
    <div id="directorsDetailsTable" class="table-responsive
        {{#ifCond data.managers ">" 0}} d-block {{else}} hide-element {{/ifCond}}"
        >
        <table class="table table-striped mb-0 ">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Corporate director?</th>
                    <th>Resident in Virgin Islands</th>
                    <th>Relation to the entity</th>
                    <th class="header-10-percent">
                    </th>
                </tr>
            </thead>
            <tbody id="tbl_managers">
    
            </tbody>
        </table>
    </div>
    <br>
    
    <!-- 4b.c -->
    <div class="row ">
        <div class="col-md-8">
            <div class="form-group mb-3">
                <label class="mb-2" for="NumberOfBoardMeetings">
                    Number of board meetings the entity held during the financial period with relation to this
                    activity:
                </label>
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group mb-3">
                <input id="NumberOfBoardMeetings" class="form-control" name="NumberOfBoardMeetings" data-toggle="touchspin"
                    type="text" data-max="1000000000" data-step="1" data-decimals="1" data-min="0" data-firstclickvalueifempty="0"
                    placeholder="0.0"
                    value="{{data.number_of_board_meetings}}">
            </div>
        </div>
    </div>
    
    <!-- 4b.d -->
    <div class="row ">
        <div class="col-md-8">
            <div class="form-group mb-3">
                <label class="mb-2" for="AmountOfBoardMeetingsInBVI">Of those board meetings, how many were held in the Virgin Islands where a quorum of directors was
                    physically present?:
                </label>
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group mb-3">
                <input id="AmountOfBoardMeetingsInBVI" class="form-control" name="AmountOfBoardMeetingsInBVI"
                    data-toggle="touchspin" type="text" data-firstclickvalueifempty="0" data-min="0" data-max="1000000000" data-step="1"
                    placeholder="0.0"
                    data-decimals="1" value="{{data.number_of_board_meetings_in_bvi}}">
            </div>
        </div>
    </div>

    <div id="AmountOfBoardMeetingsOutsideBVIRows" 
        {{#ifCond data.number_of_board_meetings_in_bvi '>' 0}} class="d-block" {{else}}class="hide-element"{{/ifCond}}>
        <!-- 4b.e -->
        <div class="row ">
            <div class="col-md-8">
                <div class="form-group mb-3">
                    <label class="mb-2" for="ListOfPersons">Please provide a list of persons responsible for direction and
                        management that attended each of the meetings</label>
                </div>
            </div>
            <div class="col-md-4">
                <button type="button" class="btn solid royal-blue w-100" data-toggle="modal" data-target="#boardMeetingModal"
                    data-activity-type={{activityType}} data-id="{{entry._id}}">
                    Add Board Meeting
                </button>
            </div>
        </div>
        <div class="table-responsive ">
            <table class="table table-striped mb-0">
                <thead>
                    <tr>
                        <th>Meeting #</th>
                        <th>Name</th>
                        <th>Physically Present</th>
                        <th>Relation to Entity</th>
                        <th>Qualification</th>
                        <th class="header-10-percent">
                        </th>
                    </tr>
                </thead>
                <tbody id="boardMeetingsTableBody">
                    {{#each data.board_meetings}}
                    <tr id="board-meetings-table-row-{{_id}}" class="board-meeting-row">
                        <td> {{meeting_number}} </td>
                        <td> {{name}} </td>
                        <td> {{#if physically_present}} Yes {{else}} No {{/if}} </td>
                        <td> {{relation_to_entity}} </td>
                        <td> {{qualification}} </td>
                        <td class="justify-content-center  d-flex">
                            <button type="button" class="btn btn-sm royal-blue solid mr-1"
                                data-activity-type="{{../activityType}}" data-id="{{../entryId}}"
                                data-board-meeting-id="{{_id}}" data-toggle="modal" data-target="#boardMeetingModal">
                                <i class="fa fa-pencil"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-danger deleteBoardMeeting"
                                data-activity-type="{{../activityType}}" data-id="{{../entryId}}"
                                data-board-meeting-id="{{_id}}">
                                <i class="fa fa-times"></i>
                            </button>
                        </td>
                    </tr>
                    {{else}}
                    <tr>
                        <td colspan="6">
                            No board meetings found
                        </td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>
        </div>
        <br>
        
        <!-- 4B.f -->
        <div class="row ">
            <div class="col-md-8">
                <div class="form-group mb-3">
                    <label class="mb-2" for="ActivityAreMinutesForBoardMeetings">
                        Are the minutes for these board meetings being held in the Virgin Islands?
                    </label>
                </div>
            </div>
            <div class="col-md-4" align="right">
                <div class="radio form-check-inline">
                    <input type="radio" id="ActivityAreMinutesForBoardMeetingsYes" name="ActivityAreMinutesForBoardMeetings"
                        value="Yes" {{#if data.are_minutes_for_board_meetings}}checked{{/if}} />
                    <label for="ActivityAreMinutesForBoardMeetingsYes">Yes</label>
                </div>
                <div class="radio form-check-inline">
                    <input type="radio" id="ActivityAreMinutesForBoardMeetingsNo" name="ActivityAreMinutesForBoardMeetings"
                        value="No" {{#unless data.are_minutes_for_board_meetings}}checked{{/unless}} />
                    <label for="ActivityAreMinutesForBoardMeetingsNo">No</label>
                </div>
            </div>
        </div>
    </div>
    

    <!-- 4B.g -->
    <div class="row ">
        <div class="col-md-8">
            <div class="form-group mb-3">
                <label class="mb-2" for="quorumOfBoardMeetings">
                    Quorum of board meetings:
                </label>
            </div>
        </div>
        <div class="col-md-4">
            <div class="form-group mb-3">
                <input id="quorumOfBoardMeetings" class="form-control" name="quorumOfBoardMeetings" data-toggle="touchspin" type="text"
                    data-max="1000000000" data-step="1" data-decimals="1" data-firstclickvalueifempty="0" data-min="0" placeholder="0.0"
                    value="{{data.quorum_of_board_meetings}}">
            </div>
        </div>
    </div>
    
    
    <!-- 4B.h -->
    <div class="row">
        <div class="col-md-8">
            <div class="form-group mb-3">
                <label class="mb-2" for="areQuorumOfDirectors">
                    Quorum of directors physically present in the Virgin Islands?
                </label>
            </div>
        </div>
        <div class="col-md-4" align="right">
            <div class="radio form-check-inline">
                <input type="radio" id="areQuorumOfDirectorsYes" name="areQuorumOfDirectors" value="Yes" {{#if
                    data.are_quorum_of_directors}}checked{{/if}} />
                <label for="areQuorumOfDirectorsYes">Yes</label>
            </div>
            <div class="radio form-check-inline">
                <input type="radio" id="areQuorumOfDirectorsNo" name="areQuorumOfDirectors" value="No" {{#unless
                    data.are_quorum_of_directors}}checked{{/unless}} />
                <label for="areQuorumOfDirectorsNo">No</label>
            </div>
        </div>
    </div>
</div>