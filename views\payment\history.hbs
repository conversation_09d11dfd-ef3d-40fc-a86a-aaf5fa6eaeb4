<script src="/javascripts/libs/bootstrap-table/bootstrap-table.min.js"></script>

<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class='contour'>
                    <form method="POST" class='enquiry' autocomplete="off">
                        <input type="text" hidden name="csrf-token" value="{{csrfToken}}">
                        <div class="container-fluid">
                            <div class="row">
                                <div class="col-12">
                                    <div class="card-box">
                                        <h4 class="mt-3">Overview of all completed payments to Master Client Code :
                                            <B>{{masterclientcode}}</B>
                                        </h4>
                                        <br>
                                        <h5 class="mt-2">Submissions</h5>
                                        <div class="table-responsive">
                                            <table class="table table-striped mb-0 w-100">
                                                <thead class="thead-light">
                                                    <tr>
                                                        <th class="header-15-percent">Transaction Id</th>
                                                        <th class="header-16-percent">Entity Name</th>
                                                        <th class="header-14-percent">Company Code </th>
                                                        <th class="header-15-percent">Financial Period</th>
                                                        <th class="header-15-percent">Paid</th>
                                                        <th class="header-12-percent">Total amount</th>
                                                        <th class="header-13-percent">Download Invoice</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {{#each entries}}
                                                    <tr>
                                                        <td>{{payment.batchpayment_transactionId}}</td>
                                                        <td>{{company_data.name}}</td>
                                                        <td>{{company_data.incorporationcode}} </td>
                                                        <td>{{formatDate entity_details.financial_period_begins
                                                            "YYYY/MM/DD"}} - {{formatDate
                                                            entity_details.financial_period_ends "YYYY/MM/DD"}}</td>
                                                        <td>{{formatDate payment.payment_received_at
                                                            "MMMM D YYYY h:mm:ss a"}}</td>
                                                        <td>${{payment.amount}}USD</td>
                                                        <td>
                                                            {{#if allowInvoice}}
                                                            <a href="/masterclients/{{company_data.masterclientcode}}/substance/companies/{{company_data.code}}/forms/{{_id}}/invoice/invoice.pdf"
                                                                target="_blank"
                                                                class="btn btn-primary waves-effect waves-light">Download</a>
                                                            {{/if}}
                                                        </td>
                                                    </tr>
                                                    {{/each}}
                                                </tbody>
                                            </table>
                                        </div>
                                        <h5 class="mt-2">Company Incorporations</h5>
                                        <div class="table-responsive">
                                            <table class="table table-striped mb-0 w-100">
                                                <thead class="thead-light">
                                                    <tr>
                                                        <th class="header-15-percent">Transaction Id</th>
                                                        <th class="header-45-percent">Entity Name</th>
                                                        <th class="header-15-percent">Paid</th>
                                                        <th class="header-12-percent">Total amount</th>
                                                        <th class="header-13-percent">Download Invoice</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {{#each incorporations}}
                                                    <tr>
                                                        <td>{{payment.batchpaymentTransactionId}}</td>
                                                        <td>{{name}}</td>
                                                        <td>{{formatDate payment.paidAt
                                                            "MMMM D YYYY h:mm:ss a"}}</td>
                                                        <td>${{payment.total}}USD</td>
                                                        <td>
                                                            {{#if invoiceNumber}}
                                                            <a href="/masterclients/{{masterClientCode}}/incorporate-company/{{_id}}/invoice.pdf"
                                                                target="_blank"
                                                                class="btn btn-primary waves-effect waves-light">Download</a>
                                                            {{/if}}
                                                        </td>
                                                    </tr>
                                                    {{/each}}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="form-group mb-2">
                                        <a href="/masterclients/{{masterclientcode}}/payments"
                                            class="btn btn-secondary waves-effect waves-light width-xl">Back</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</main>
