const mongoose = require('mongoose');

const invoiceItemSchema = new mongoose.Schema({
  title: { type: String, required: false },
  description: { type: String, required: true },
  value: { type: Number, required: true },
});

const incorporationSchema = new mongoose.Schema({
  fees: [invoiceItemSchema],
  disbursements : [invoiceItemSchema],
});

const idPalConfigurationSchema = new mongoose.Schema({
  accessToken: { type: String, required: true },
  refreshToken: { type: String, required: true },
});

 
const invoiceConfigurationSchema = new mongoose.Schema({
  initialRangeNumber: { type: Number, required: true },
  currentAvailableNumber: { type: Number, required: true },
  initialRangeIncorporationNumber: { type: Number, required: true, default: 50000 },
  currentAvailableIncorporationNumber: { type: Number, required: true, default: 50000 },
  initialRangeFinancialReportNumber: { type: Number, required: true, default: 100000 },
  currentAvailableFinancialReportNumber: { type: Number, required: true, default: 100000 },
  currentYear: { type: Date, required: true }

});

const penaltyRangesSchema = new mongoose.Schema({
  startRange: { type: Number, required: false },
  endRange: { type: Number, required: false },
  amount: { type: Number, required: false },
})


const financialReportConfigurationSchema = new mongoose.Schema({
  selfServiceCompleteAnnualReturnAmount: { type: Number, required: false, default: 190 },
  selfServicePrepareAnnualReturnAmount: { type: Number, required: false, default: 190 },
  tridentServiceCompleteAnnualReturnAmount: { type: Number, required: false, default: 350 },
  tridentServiceDropAccountingRecordsAmount: { type: Number, required: false, default: 600 },
  lastUpdateCompanyCompliantStatusFunction: { type: Date, required: false },
  penaltyRanges: [penaltyRangesSchema]
})


const ConfigurationSchema = new mongoose.Schema({
  configType: { type: String, required: true },
  idPalConfiguration: {    type: idPalConfigurationSchema, required: false  },
  invoiceConfiguration: { type: invoiceConfigurationSchema, required: false },
  incorporation: { type: incorporationSchema, required: true },
  isMovingMccFiles: { type: Boolean, required: false, default: false },
  financialReportConfiguration: { type: financialReportConfigurationSchema, required: false },
});

//Export model
module.exports = mongoose.model('substanceconfigurations', ConfigurationSchema);
