<div class="col-lg-12">
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <h4>
                        <label for="reportCurrency" class="mb-2 lbl-read-only color-black cursor-default">
                            Reporting currency:
                        </label>
                    </h4>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3 input-group">
                        <select name="reportCurrency" id="reportCurrency" class="form-control w-100" data-toggle="select2" data-value="{{report.currency}}">
                            {{#each currencies}}
                            <option value="{{cc}}" 
                                {{#if ../report.currency}}  
                                    {{#ifEquals cc ../report.currency }} selected {{/ifEquals}}
                                {{else}}
                                    {{#ifEquals cc 'USD' }}  selected {{/ifEquals}} 
                                {{/if}}>
                                {{cc}} - {{name}}
                            </option>
                            {{/each}}
                        
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <h4>
                        <label class="mb-2 lbl-read-only color-black cursor-default">
                            2. Cash transactions:
                        </label>
                    </h4>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <div class="form-group mb-3">
                        <label class="mb-2" for="isSameBusinessAddress">2.1 Does the company own any cash or cash equivalents*</label>
                    </div>
                </div>
                
                <div class="col-md-4" align="right">
                    <div class="radio form-check-inline">
                        <input type="radio" id="companyOwnsCashOrEquivalentsYes" name="companyOwnsCashOrEquivalents" value="YES" 
                        {{#ifCond report.cashTransactions.companyOwnsCashOrEquivalents "===" true }} checked {{/ifCond}}>
                        <label for="companyOwnsCashOrEquivalentsYes">Yes</label>
                    </div>
                    <div class="radio form-check-inline">
                        <input type="radio" id="companyOwnsCashOrEquivalentsNo" name="companyOwnsCashOrEquivalents" value="NO"
                         {{#ifCond report.cashTransactions.companyOwnsCashOrEquivalents "===" false }} checked {{/ifCond}}>
                        <label for="companyOwnsCashOrEquivalentsNo">No</label>
                    </div>
                </div>
            </div>

            <div id="showCashBankRows" 
                {{#ifCond report.cashTransactions.companyOwnsCashOrEquivalents "===" true }} class="d-block;" {{else}}  class="hide-element" {{/ifCond}}
            >
                <div class="row">
                    <div class="col-12">
                        <button type="button" class="btn solid royal-blue" data-toggle="modal" data-target="#newCashTransactionModal"
                            data-report-id="{{ report._id }}">
                            <i class="fa fa-plus pr-2"></i>Add Cash/Bank Account
                        </button>
                    </div>
                </div>
                <br>
                <div class="table-responsive ">
                    <table id="cashTransactionsTable" class="table table-striped mb-0">
                        <thead>
                            <tr>
                                <th class="header-20-percent" >Description</th>
                                <th>Account Type</th>
                                <th>Opening Amount</th>
                                <th>Closing Amount</th>
                                <th>Currency</th>
                                <th id="cashATBankHeader">Cash At Bank USD</th>
                                <th class="header-10-percent">
                                </th>
                            </tr>
                        </thead>
                        <tbody id="cashTransactionsTableBody">
                            {{#each report.cashTransactions.bankAccounts}}
                            <tr id="bank-account-row-{{_id}}" class="bank-account-row">
                                <td> {{description}} </td>
                                <td> {{accountType}} </td>
                                <td>{{decimalValue openingAmount}} </td>
                                <td> {{decimalValue closingAmount}}</td>
                                <td> {{transactionCurrency}}</td>
                                <td>{{decimalValue cashAtBank}}</td>
                                <td class="justify-content-center  d-flex">
                                    <button type="button" class="btn btn-sm royal-blue solid mr-1" data-report-id="{{../reportId}}"
                                        data-bank-account-id="{{_id}}" data-toggle="modal" data-target="#newCashTransactionModal">
                                        <i class="fa fa-pencil"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger deleteBankAccount" data-report-id="{{../reportId}}"
                                        data-bank-account-id="{{_id}}">
                                        <i class="fa fa-times"></i>
                                    </button>
                                </td>
                            </tr>
                            {{else}}
                            <tr>
                                <td colspan="7">
                                    No Cash/Bank Accounts found
                                </td>
                            </tr>
                            {{/each}}
                        </tbody>
                    </table>
                </div>

                <br>

                <div class="row">
                    <div class="col-md-8">
                        <label for="totalBankAccounts">2.2. Total Cash and cash equivalent</label>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group input-group">
                            <input type="text" id="totalBankAccounts" class="form-control autonumber text-right" data-a-sep="," data-m-dec="2"
                                name="totalBankAccounts" value="{{report.cashTransactions.totalBankAccounts}}" readonly>
                        </div>
                    </div>
                </div>

                <input type="text" id="lbtTotalLoansAmountReceived" class="form-control autonumber text-right liabilities-long-term" hidden readonly
                    value="{{report.totalLoansAmountReceived}}" data-a-sep=",">
                
                <input type="text" id="lbtTotalLoansAmountPaid" class="form-control autonumber text-right liabilities-long-term" hidden readonly
                    value="{{report.totalLoansAmountPaid}}" data-a-sep=",">

            </div>
            

            <div class="row mt-2">
                <div class="col-md-12">
                    <div class="progress">
                        <div class="progress-bar width-28" role="progressbar" aria-valuenow="2"
                             aria-valuemin="0" aria-valuemax="7">2 of 7
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript" src="/views-js/partials/financial-reports/cash-transactions.js"></script>