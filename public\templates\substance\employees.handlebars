{{#each employees}}
    <tr id="employees-table-row-{{_id}}" >
        <td> {{name}} </td>
        <td> {{qualification}} </td>
        <td> {{experience_years}} </td>
        <td class="justify-content-center d-flex d-flex-inline">
            <button type="button" class="btn btn-sm royal-blue solid mr-1" data-activity-type="{{../activityType}}"
                data-id="{{../entryId}}" data-employee-id="{{_id}}" data-toggle="modal" data-target="#employeeModal">
                <i class="fa fa-pencil"></i>
            </button>
            <button type="button" class="btn btn-sm btn-danger deleteEmployee"
                    data-activity-type="{{../activityType}}"
                    data-id="{{../entryId}}" data-employee-id="{{_id}}">
                <i class="fa fa-times"></i>
            </button>
        </td>
    </tr>
    {{else}}
        <tr>
            <td colspan="4">
                No employees found
            </td>
        </tr>
{{/each}}


