(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['createothervaluerow'] = template({"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data) {
    var helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "<div class=\"row\" id=\"completeDetailsOther"
    + alias4(((helper = (helper = lookupProperty(helpers,"type") || (depth0 != null ? lookupProperty(depth0,"type") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"type","hash":{},"data":data,"loc":{"start":{"line":1,"column":41},"end":{"line":1,"column":49}}}) : helper)))
    + "Row"
    + alias4(((helper = (helper = lookupProperty(helpers,"position") || (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"position","hash":{},"data":data,"loc":{"start":{"line":1,"column":52},"end":{"line":1,"column":64}}}) : helper)))
    + "\">\r\n    <div class=\"col-md-8\">\r\n        <div class=\"form-group ml-2\">\r\n            <label class=\"lbl-read-only\" for=\"completeDetailsOther"
    + alias4(((helper = (helper = lookupProperty(helpers,"type") || (depth0 != null ? lookupProperty(depth0,"type") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"type","hash":{},"data":data,"loc":{"start":{"line":4,"column":66},"end":{"line":4,"column":74}}}) : helper)))
    + alias4(((helper = (helper = lookupProperty(helpers,"position") || (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"position","hash":{},"data":data,"loc":{"start":{"line":4,"column":74},"end":{"line":4,"column":86}}}) : helper)))
    + "\">Other "
    + alias4(((helper = (helper = lookupProperty(helpers,"type") || (depth0 != null ? lookupProperty(depth0,"type") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"type","hash":{},"data":data,"loc":{"start":{"line":4,"column":94},"end":{"line":4,"column":102}}}) : helper)))
    + " ("
    + alias4(((helper = (helper = lookupProperty(helpers,"positionLbl") || (depth0 != null ? lookupProperty(depth0,"positionLbl") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"positionLbl","hash":{},"data":data,"loc":{"start":{"line":4,"column":104},"end":{"line":4,"column":119}}}) : helper)))
    + ")</label>\r\n        </div>\r\n    </div>\r\n    <div class=\"col-md-4\">\r\n        <div class=\"form-group input-group mb-1\">\r\n            <div class=\"input-group-prepend\">\r\n                <span class=\"input-group-text prepend-currency\"> "
    + alias4(((helper = (helper = lookupProperty(helpers,"currency") || (depth0 != null ? lookupProperty(depth0,"currency") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"currency","hash":{},"data":data,"loc":{"start":{"line":10,"column":65},"end":{"line":10,"column":77}}}) : helper)))
    + "</span>\r\n            </div>\r\n            <input type=\"text\" id=\"completeDetailsOther"
    + alias4(((helper = (helper = lookupProperty(helpers,"type") || (depth0 != null ? lookupProperty(depth0,"type") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"type","hash":{},"data":data,"loc":{"start":{"line":12,"column":55},"end":{"line":12,"column":63}}}) : helper)))
    + alias4(((helper = (helper = lookupProperty(helpers,"position") || (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"position","hash":{},"data":data,"loc":{"start":{"line":12,"column":63},"end":{"line":12,"column":75}}}) : helper)))
    + "\"\r\n                class=\"form-control autonumber text-right "
    + alias4(((helper = (helper = lookupProperty(helpers,"typeClass") || (depth0 != null ? lookupProperty(depth0,"typeClass") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"typeClass","hash":{},"data":data,"loc":{"start":{"line":13,"column":58},"end":{"line":13,"column":71}}}) : helper)))
    + "\" data-a-sep=\",\" placeholder=\"0.00\"\r\n                data-m-dec=\"2\" name=\"completeDetails[other"
    + alias4(((helper = (helper = lookupProperty(helpers,"type") || (depth0 != null ? lookupProperty(depth0,"type") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"type","hash":{},"data":data,"loc":{"start":{"line":14,"column":58},"end":{"line":14,"column":66}}}) : helper)))
    + "[][value]]\" > \r\n        </div>\r\n        <div>\r\n            <input type=\"text\" id=\"completeDetailsOther"
    + alias4(((helper = (helper = lookupProperty(helpers,"type") || (depth0 != null ? lookupProperty(depth0,"type") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"type","hash":{},"data":data,"loc":{"start":{"line":17,"column":55},"end":{"line":17,"column":63}}}) : helper)))
    + "Desc"
    + alias4(((helper = (helper = lookupProperty(helpers,"position") || (depth0 != null ? lookupProperty(depth0,"position") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"position","hash":{},"data":data,"loc":{"start":{"line":17,"column":67},"end":{"line":17,"column":79}}}) : helper)))
    + "\" name=\"completeDetails[other"
    + alias4(((helper = (helper = lookupProperty(helpers,"type") || (depth0 != null ? lookupProperty(depth0,"type") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"type","hash":{},"data":data,"loc":{"start":{"line":17,"column":108},"end":{"line":17,"column":116}}}) : helper)))
    + "[][description]]\"\r\n                class=\"form-control text-left \" placeholder=\"Description\" >\r\n        </div>\r\n    </div>\r\n</div>";
},"useData":true});
})();