
const appInsightsClient = require("applicationinsights").defaultClient;
const EntryModel = require('../models/entry').EntryModel;
const ArchivedEntryModel = require('../models/entry').ArchivedEntryModel;
const CompanyIncorporationModel = require('../models/clientIncorporation');
const FinancialReportModel = require('../models/financial-report');
const BatchPaymentModel = require('../models/batchpayment');
const MasterClientModel = require('../models/masterClientCode');
const userModel = require("../models/user");
const xml2js = require('xml2js');
const axios = require('axios');
const netencrypt = require("../netproencrypt");
const sessionUtils = require('../utils/sessionUtils');

exports.getPendingPayments = async function (req, res, next) {
    try{
        let incorporationSubmissions = await CompanyIncorporationModel.find({ 'status': 'SUBMITTED', 'masterClientCode': req.params.masterclientcode, 'payment.total': { $gt: 0 } },
            { '_id': 1, 'name': 1, 'payment': 1, 'submittedAt': 1, 'invoiceNumber': 1, 'masterClientCode': 1 });

        res.render('payment/pending',
            {
                title: 'Pending Payments',
                user: req.user,
                messages: req.session.messages,
                masterclientcode: req.params.masterclientcode,
                incorporationSubmissions
            });
    }catch(e){
        console.log(e);
        next(e);
    }

};

exports.getPaymentsHistory = async function (req, res, next) {
    try {
        let payments = await EntryModel.find({ 'status': 'PAID', 'company_data.masterclientcode': req.params.masterclientcode }, ['_id', 'payment', 'company_data', 'entity_details', 'invoice_number'], { sort: { 'payment.payment_received_at': -1 } });
        let archivedEntries = await ArchivedEntryModel.find({ 'status': 'PAID', 'company_data.masterclientcode': req.params.masterclientcode }, ['_id', 'payment', 'company_data', 'entity_details', 'invoice_number'], { sort: { 'payment.payment_received_at': -1 } });
        let incorporations = await CompanyIncorporationModel.find({ 'status': 'PAID', 'masterClientCode': req.params.masterclientcode }, ['_id', 'payment', 'invoiceNumber', 'name', 'masterClientCode'], { sort: { 'payment.paidAt': -1 } });
        let financialReports = await FinancialReportModel.find({ 'status': 'PAID', 'masterClientCode': req.params.masterclientcode },
            ['_id', 'payment', 'invoiceNumber', 'companyData', 'financialPeriod', 'masterClientCode'], { sort: { 'payment.paidAt': -1 } });
        const totalEntries = [...payments, ...archivedEntries];
        if (totalEntries.length > 0) {
            for (let dbForm of totalEntries) {
                dbForm.allowInvoice = !!(dbForm.invoice_number && dbForm.invoice_number !== '');
            }
        }
        res.render(
            'payment/history',
            {
                title: 'Payments history',
                user: req.user, messages: req.session.messages,
                masterclientcode: req.params.masterclientcode,
                entries: totalEntries,
                incorporations,
                financialReports,
            });
    }catch(e){
        console.log(e);
        next(e);
    }

};

exports.processStatus = function (req, res) {
    if (req.query['token-id'] && req.query['token-id'].length > 0) {
        //now call the cxpay api to get a form url (stage 1)
        let cxPayCompleteActionObject = {
            'complete-action': {
                'api-key': process.env.CXPAY_API_KEY,
                'token-id': req.query['token-id']
            }
        }

        //conver to xml
        let xmlBuilder = new xml2js.Builder();
        let xmlCompleteAction = xmlBuilder.buildObject(cxPayCompleteActionObject);

        console.log(xmlCompleteAction);
        let gatewayURL = 'https://cxpay.transactiongateway.com/api/v2/three-step';

        let options = {
            url: gatewayURL,
            method: 'POST',
            data: xmlCompleteAction,
            headers: {
                'Content-type': 'text/xml',
                'Content-Length': Buffer.byteLength(xmlCompleteAction).toString()
            },
        };

        axios(options).then(function (response) {

            const body = response.data
            xml2js.parseStringPromise(body).then(async function (result) {
                console.log('ORDER ID');
                console.log(result);
                console.log('Done');
                let pendingPaymentsLink = `${process.env.SUBSTANCE_APP_HOST}/masterclients/${req.params.masterclientcode}/payments`
                if (result.response.result[0] == '1') {
                    try {
                        let batchPayment = await BatchPaymentModel.findOneAndUpdate({ 'code': result.response['order-id'][0], 'transactionId': result.response['transaction-id'][0] }, { status: 'PAID' }).exec();
                        if (batchPayment) {
                            console.log(batchPayment);
                            console.log('BATCH UPDATED');
                            for (let payment of batchPayment.payment_objects) {
                                if (payment.type_object === 'CLIENT_INCORPORATION') {
                                    let incorporation = await CompanyIncorporationModel.findById(payment.objectid).exec();
                                    if (incorporation) {
                                        const currentDate = new Date();
                                        console.log('upading incorporation id  ' + payment.objectid)
                                        incorporation.status = 'PAID';
                                        incorporation.payment = {
                                            total: incorporation.payment.total,
                                            fees: incorporation.payment.fees ,
                                            disbursements: incorporation.payment.disbursements,
                                            paid: true,
                                            paidAt: new Date(new Date().toUTCString()),
                                            type: 'CREDITCARD',
                                            reference: incorporation.masterClientCode + '-' + currentDate.getFullYear() + (currentDate.getMonth()+1).toString() + currentDate.getDate().toString(),                                            
                                            batchpaymentId: batchPayment._id,
                                            batchpaymentTransactionId: batchPayment.transactionId,
                                            batchpaymentCode: batchPayment.code,
                                        }
                                        let savedIncorporation = await CompanyIncorporationModel.findByIdAndUpdate(payment.objectid, incorporation, {}).exec();
                                        if (savedIncorporation) {
                                            appInsightsClient.trackEvent({ name: "batch payment status processced", properties: { id: payment.objectid, batchpayment: batchPayment._id } });
                                            console.log(savedIncorporation);
                                            console.log('INCORPORATION UPDATED');
                                        }
                                    }
                                }
                            }
                        }
                    } catch (err) {
                        appInsightsClient.trackException({ exception: new Error("Error processing the cxPay response transaction.") });
                        console.log(err);
                    }
                    res.render('payment/status', { title: 'Payment Status', status: 'Confirmed', paymentsUrl: pendingPaymentsLink });

                } else if (result.response.result[0] == '2') {
                    appInsightsClient.trackException({ exception: new Error("The transaction to cxPay API failed.") });
                    res.render('payment/status', { title: 'Payment Status', status: 'Declined', paymentsUrl: pendingPaymentsLink });
                    //todo go back to creditcard form
                } else {
                    res.render('payment/status', { title: 'Payment Status', status: 'Error: ' + result.response['result-text'], paymentsUrl: pendingPaymentsLink });
                    //todo go back to creditcard form
                }

            }).catch(function (err) {
                // Failed
                console.log(err);
                appInsightsClient.trackException({ exception: new Error("Errow with the transaction: ", err) });
        
            });
        });

    } else {
        res.render('payment/status', { title: 'Payment Status', status: 'Invalid token. Payment cancelled' });
    }
}

exports.startBatchPayment = async function (req, res, next) {
    try {
        let payments = [];
        let totalAmount = 0;
        let masterclient = req.params.masterclientcode;
        let batchCode = masterclient + (new Date()).getTime();

        if (req.body.selected_incorporation && Array.isArray(req.body.selected_incorporation)) {
            for (const item of req.body.selected_incorporation) {
                let incorporation = await CompanyIncorporationModel.findById(item);
                payments.push({
                    type_object: 'CLIENT_INCORPORATION',
                    objectid: item,
                    amount: incorporation.payment.total
                });
                totalAmount += incorporation.payment.total;
            }
        } else if (req.body.selected_incorporation) {
            let incorporation = await CompanyIncorporationModel.findById(req.body.selected_incorporation);
            payments.push({
                type_object: 'CLIENT_INCORPORATION',
                objectid: req.body.selected_incorporation,
                amount: incorporation.payment.total
            });
            totalAmount += incorporation.payment.total;
        }


        let userSessionid = netencrypt.netencrypt(req.user.sessionId);
        //now call the cxpay api to get a form url (stage 1)
        let cxPaySaleObject = {
            'sale': {
                'api-key': process.env.CXPAY_API_KEY,
                'redirect-url': process.env.SUBSTANCE_APP_HOST +
                    '/masterclients/' +
                    req.params.masterclientcode +
                    '/payments/process-status/' + userSessionid,
                'amount': totalAmount,
                'order-id': batchCode,
                'merchant-receipt-email': req.user.email,
                'customer-receipt': true,
                'billing': {
                    'company': req.params.masterclientcode,
                    'email': req.user.email,
                }
            }
        }

        console.log(cxPaySaleObject);
        //conver to xml
        let xmlBuilder = new xml2js.Builder();
        let xmlSale = xmlBuilder.buildObject(cxPaySaleObject);


        let gatewayURL = 'https://cxpay.transactiongateway.com/api/v2/three-step';

        let options = {
            url: gatewayURL,
            method: 'post',
            data: xmlSale,
            headers: {
                'Content-type': 'text/xml',
                'Content-Length': Buffer.byteLength(xmlSale).toString()
            },
        };

        axios(options).then(function (response) {
            const body = response.data
            xml2js.parseStringPromise(body).then(function (result) {
                console.log(result);
                console.log('Done');
                let batch = new BatchPaymentModel(
                    {
                        code: batchCode,
                        masterclientcode: req.params.masterclientcode,
                        created_by: req.user.email,
                        status: "NEW",
                        payment_objects: payments,
                        total_amount: totalAmount,
                        transactionId: result.response['transaction-id'][0]
                    });
                batch.save(function (err, savedBatch) {
                    if (err) return console.error(err);
                    console.log(savedBatch)
                    // Successful, so render.        
                    req.session.batchPaymentId = savedBatch.id;
                    appInsightsClient.trackEvent({ name: "new batch payment created", properties: { email: req.user.email, masterclientcode: req.params.masterclientcode, batchpayment: savedBatch._id } });
                    res.render('payment/creditcard', { title: 'Payment', user: req.user, messages: req.session.messages, masterclientcode: req.params.masterclientcode, batch_payment: savedBatch, formUrl: result.response['form-url'][0] });
                });
            }).catch(function (err) {
                console.log(err);
                let pendingPaymentsLink = `${process.env.SUBSTANCE_APP_HOST}/masterclients/${req.params.masterclientcode}/payments`
                res.render('payment/status', { title: 'Payment Status', status: 'Error', paymentsUrl: pendingPaymentsLink });
            });
        });
    }catch(e){
        console.log(e);
        next(e);
    }

}


exports.ensureAuthenticated_ProcessStatus = async function (req, res, next) {
    try {
        let userSessionid = netencrypt.netdecrypt(req.params.usssid);
        const user = await userModel.findOne({ sessionId: userSessionid });
        if (user != null) {

            //passport login
            req.logIn(user, async function (error) {
                if (error) { return next(error); }
                
                user.sessionId = req.session.id;
                await user.save();
            
                req.session.auth2fa = true;
                let masterclient = await MasterClientModel.findOne({
                    code: req.params.masterclientcode,
                });

                if (
                    masterclient == null ||
                    masterclient.owners.indexOf(req.user.email.toLowerCase()) == -1
                ) {
                    var err = new Error("Masterclient not found");
                    err.status = 404;
                    return next(err);
                }
                next();

            });
        }
        else {
            req.logout(function (err) {
                if (err) { return next(err) }
                req.session.destroy(function () {
                    // cannot access session here
                    sessionUtils.onSessionDestroyed(req, res);
                });
            });
        }
    }catch(e){
        console.log(e);
        next(e);
    }

}


exports.ensureAuthenticated = async function (req, res, next) {
    if ((req.user && req.session.id == req.user.sessionId) && req.session.auth2fa) {
        let masterclient = await MasterClientModel.findOne({ 'code': req.params.masterclientcode });
        if (masterclient == null || masterclient.owners.indexOf(req.user.email.toLowerCase()) == -1) {
            const err = new Error('Masterclient not found');
            err.status = 404;
            return next(err);
        }
        next();
    } else if ((req.user && req.session.id == req.user.sessionId) && !req.session.auth2fa) {
        if (req.user.secret_2fa) {
            res.redirect('/users/2fa-code');
        } else {
            res.redirect('/users/2fa-setup');
        }
    }
    else {
        req.logout(function (err) {
            if (err) { return next(err) }
            req.session.destroy(function () {
                // cannot access session here
                sessionUtils.onSessionDestroyed(req, res);
            });
        });
    }
}


 