
function addManager() {
    $("#iframeDirector").prop('src', '/substance/entry/'+window.location.pathname.split('/')[3]+'/'+urlPart+'/managers/add');
    $("#AddPerson").modal('show');
}

$(document).on('click', '#addManager', function (event) {
    event.preventDefault()
    addManager()
})



async function deleteManager(id) {
    $.ajax({
        type: "DELETE",
        url: "/substance/entry/"+window.location.pathname.split('/')[3]+"/manager",
        data: {
            _id: id,
            field: urlPart
        },
        success: function( data ) {
            if (data.result) {
                //close modal
                refreshManagers();
            }else{
                Swal.fire('Error',  data.message ? data.message : "Error deleting the manager", 'error');
            }
        },
        dataType: "json"
    });
}

function editManager(id) {
    $("#iframeDirector").prop('src', '/substance/entry/' + window.location.pathname.split('/')[3]+'/'+urlPart+'/managers/'+id+'/edit')
    $("#AddPerson").modal('show')

}

function closeDirectorIFrame(){
    $("#AddPerson").modal("hide");
    toastr.success('Director is saved.')
}
