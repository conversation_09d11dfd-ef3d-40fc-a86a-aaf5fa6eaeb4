# Node.js
# Build a general Node.js project with npm.
# Add steps that analyze code, save build artifacts, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/javascript
trigger:
    - develop-s19.3.1
pool:
  vmImage: windows-latest
jobs:
- job: Build
  workspace:
      clean: all
  steps:
  - task: NodeTool@0
    inputs:
      versionSpec: '20.17.0'
    displayName: 'Install Node.js'

  - script: |
      npm install
    displayName: 'npm install'


  - task: Npm@1
    inputs:
      command: custom
      customCommand: 'run lint'
    displayName: Run ESLint


  - script: |
      npm run build
    displayName: 'npm build'


  - task: CopyFiles@2
    inputs:
      sourceFolder: '$(Build.SourcesDirectory)'
      contents: '**' 
      targetFolder: $(Build.ArtifactStagingDirectory)/npm
    displayName: 'Copy package.json'   
  

  - task: PublishPipelineArtifact@1
    inputs:
      targetPath: '$(Build.ArtifactStagingDirectory)/npm'
      artifactName: npm
    displayName: 'Publish npm artifact'

  - task: ArchiveFiles@2
    inputs:
      rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/npm'
      includeRootFolder: false

  - task: AzureWebApp@1
    inputs:
      azureSubscription: 'NPGDev-AZDevOps-Connection-Trident - Substance'
      appType:   'webApp'
      appName: 'tbvi-clientportal-dev-accountingrec'
      package: 'D:\a\1\a\*.zip'
      replaceExistingArchive: true
      deploymentMethod:  'zipDeploy'
