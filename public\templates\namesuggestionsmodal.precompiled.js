(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['namesuggestionsmodal'] = template({"1":function(container,depth0,helpers,partials,data) {
    var helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, alias5=container.lambda, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "\r\n                <div class=\"custom-control custom-radio ml-2\">\r\n                    <input class=\"custom-control-input form-control\" type=\"radio\"\r\n                           id=\"needsProvideDocuments"
    + alias4(((helper = (helper = lookupProperty(helpers,"index") || (data && lookupProperty(data,"index"))) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"index","hash":{},"data":data,"loc":{"start":{"line":18,"column":52},"end":{"line":18,"column":62}}}) : helper)))
    + "\" name=\"suggestedName\" value=\""
    + alias4(alias5(depth0, depth0))
    + "\">\r\n                    <label class=\"custom-control-label\" for=\"needsProvideDocuments"
    + alias4(((helper = (helper = lookupProperty(helpers,"index") || (data && lookupProperty(data,"index"))) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"index","hash":{},"data":data,"loc":{"start":{"line":19,"column":82},"end":{"line":19,"column":92}}}) : helper)))
    + "\">\r\n                        "
    + alias4(alias5(depth0, depth0))
    + "\r\n                    </label>\r\n                </div>\r\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "<div>\r\n    <div class=\"row\">\r\n        <div class=\"col-md-12\">\r\n            <p>\r\n                The name <b>"
    + container.escapeExpression(container.lambda(((stack1 = (depth0 != null ? lookupProperty(depth0,"incorporation") : depth0)) != null ? lookupProperty(stack1,"name") : stack1), depth0))
    + "</b> has been rejected by the review officer.\r\n            </p>\r\n        </div>\r\n    </div>\r\n    <div class=\"row\">\r\n        <div class=\"col-md-12\">\r\n            <label for=\"suggestName\">Please select one of the suggested names or other to enter a new one to\r\n                submit*</label>\r\n\r\n"
    + ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),((stack1 = ((stack1 = (depth0 != null ? lookupProperty(depth0,"incorporation") : depth0)) != null ? lookupProperty(stack1,"nameReservationInfo") : stack1)) != null ? lookupProperty(stack1,"suggestions") : stack1),{"name":"each","hash":{},"fn":container.program(1, data, 0),"inverse":container.noop,"data":data,"loc":{"start":{"line":14,"column":12},"end":{"line":23,"column":21}}})) != null ? stack1 : "")
    + "            <div class=\"custom-control custom-radio ml-2\">\r\n                <input class=\"custom-control-input form-control\" type=\"radio\"\r\n                       id=\"needsProvideDocumentsOther\" name=\"suggestedName\" value=\"other\"\r\n                >\r\n                <label class=\"custom-control-label\" for=\"needsProvideDocumentsOther\">\r\n                    Other\r\n                </label>\r\n            </div>\r\n\r\n\r\n        </div>\r\n    </div>\r\n</div>\r\n<br>\r\n<div class=\"row hide-element\" id=\"otherNameBox\">\r\n    <div class=\"col-md-12\">\r\n        <label for=\"otherName\">Other name*</label>\r\n        <input type=\"text\" class=\"form-control\" id=\"otherName\"\r\n               name=\"otherName\" placeholder=\"New name...\"\r\n               value=\"\"/>\r\n    </div>\r\n</div>\r\n\r\n</div>\r\n<script type=\"text/javascript\">\r\n\r\n    $('input[name=\"suggestedName\"]').on('change', function () {\r\n        if ($(this).val() !== ''){\r\n            $(\"input[name='suggestedName']\").toggleClass(\"is-invalid\", false);\r\n        }\r\n\r\n        if ($(this).val() === \"other\") {\r\n            $(\"#otherName\").prop('required', true);\r\n            $('#otherNameBox').show(200);\r\n        } else {\r\n            $(\"#otherName\").prop('required', false);\r\n            $('#otherNameBox').hide(200);\r\n        }\r\n    });\r\n\r\n\r\n</script>\r\n";
},"useData":true});
})();