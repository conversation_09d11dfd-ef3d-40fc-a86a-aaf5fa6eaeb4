{{#each boardMeetings}}
    <tr id="board-meetings-table-row-{{_id}}" class="board-meeting-row">
        <td> {{meeting_number}} </td>
        <td> {{name}} </td>
        <td> {{#if physically_present}} Yes {{else}} No {{/if}} </td>
        <td> {{relation_to_entity}} </td>
        <td> {{qualification}} </td>
        <td class="justify-content-center d-flex d-flex-inline">
            <button type="button" class="btn btn-sm royal-blue solid mr-1" data-activity-type="{{../activityType}}"
                data-id="{{../entryId}}" data-board-meeting-id="{{_id}}" data-toggle="modal" data-target="#boardMeetingModal">
                <i class="fa fa-pencil"></i>
            </button>
            <button type="button" class="btn btn-sm btn-danger deleteBoardMeeting"
            data-activity-type="{{../activityType}}" data-id="{{../entryId}}"
                                data-board-meeting-id="{{_id}}">
                <i class="fa fa-times"></i>
            </button>
        </td>
    </tr>
    {{else}}
        <tr>
            <td colspan="6">
                No board meetings found
            </td>
        </tr>
{{/each}}


