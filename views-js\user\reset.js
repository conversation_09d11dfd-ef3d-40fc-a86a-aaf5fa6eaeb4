$("#resetPasswordBtn").on('click', async function (e) {
    e.preventDefault();
    $("#resetPasswordBtn").prop('disabled', true);

    try {
        const recaptcha_site = $("#resetPasswordBtn").data('sitekey');
        const isTokenSet = await setCaptchaToken(recaptcha_site);
        if (isTokenSet) {
            $("#resetPasswordForm").submit();
        } else {
            $("#resetPasswordBtn").prop('disabled', false);
        }
    }
    catch (e) {
        $("#resetPasswordBtn").prop('disabled', false);
    }
});