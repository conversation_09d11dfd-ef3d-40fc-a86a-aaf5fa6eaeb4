let companyIdReview = '';
let statusReview;
let commentReview = '';
let officer = '';
const modalInfoValidateReview = {
    "validateReview": {
        "modalMessage": 'You are about to submit the File Review to a <b>Quality Assurance Officer.</b>',
        "validatedMessage": 'You are about to submit the File Review to a <b>Quality Assurance Officer.</b> Are you sure? Not everything is marked as complete.',
        "successMessage": 'The review was submitted successfully',
        "errorMessage": 'There was an error submitting your review'
    },
    "validateCompliance": {
        "modalMessage": 'Are you sure you want to validate this file review?',
        "successMessage": 'The review was submitted successfully',
        "errorMessage": 'There was an error submitting your review'
    }
};
$('#confirmReviewModal').on('show.bs.modal', function (event) {
    let button = $(event.relatedTarget); // Button that triggered the modal
    companyIdReview = button.data('id');
    statusReview = button.data('status');
    officer = button.data('officer');

    if (officer === "fileReviewer"){
        
        $.ajax({
            type: 'POST',
            url: '/file-reviewer/open-file-review/' + companyIdReview + '/validate-review',
            data: {},
            success: function (response) {
                if (response.success) {
                    if (response.isIncomplete === true) {
                        $('#message_modal_validate_review').html(modalInfoValidateReview[statusReview].validatedMessage);
                    } else {
                        $('#message_modal_validate_review').html(modalInfoValidateReview[statusReview].modalMessage);
                    }

                } else {
                    $('#message_modal_validate_review').html(modalInfoValidateReview[statusReview].errorMessage);
                }

            },
            error: function () {
                Swal.fire('Error', modalInfoValidateReview[statusReview].errorMessage, 'error').then(() => {
                    $('#confirmValidateModal').modal('hide');
                });
            },
        });
    }
    else{
        $('#message_modal_validate_review').html(modalInfoValidateReview[statusReview].modalMessage);
    }

});

$('#confirmReviewButton').on('click', function () {
    commentReview = $('#modalInputReviewComment').val();

    if (officer === "fileReviewer") {
        $.ajax({
            type: 'POST',
            url: '/file-reviewer/submit-file-review',
            data: {
                companyId: companyIdReview, status: statusReview, comment: commentReview
            },
            success: () => {
                Swal.fire('Success', modalInfoValidateReview[statusReview].successMessage, 'success').then(() => {
                    location.href = '/file-reviewer/file-review-list';
                });
            },
            error: (err) => {
                $('#confirmReviewModal').modal('hide');
                Swal.fire('Error', modalInfoValidateReview[statusReview].errorMessage, 'error');
            },
        });
    }
    else{
        $.ajax({
            type: 'POST',
            url: '/file-reviewer/compliances/' + companyIdReview + '/validate',
            data: {
                status: 'compliance', comment: commentReview
            },
            success: () => {
                Swal.fire('Success', modalInfoValidateReview[statusReview].successMessage, 'success').then(() => {
                    location.href = '/file-reviewer/compliances';
                });
            },
            error: (err) => {
                $('#confirmReviewModal').modal('hide');
                Swal.fire('Error', modalInfoValidateReview[statusReview].errorMessage, 'error');
            },
        });
    }


});