var express = require('express');
var router = express.Router();

/* GET home page. */
/*****************************Needs to be addressed later , not used vars is disabled in esling ******
*******************************Implemented for Eslint checking in CI/CD*******************************
*****************************/
/* eslint no-unused-vars:0 */
router.get('/', function(req, res, next) {
  if (req.user)
  {
      res.redirect('/masterclients');
  } else { 
    res.redirect('/users/login');
  }
});

module.exports = router;
