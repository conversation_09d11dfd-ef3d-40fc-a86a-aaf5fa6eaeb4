<div id="naturalForm">
    <!-- NATURAL DETAILS -->
    <div id="naturalDetails">
        <h4>Details</h4>
        <div class="row mt-3">
            <div class="col-2">
                <label for="details-first-name">First Name*</label>
            </div>
            <div class="col-4">
                <input id="details-first-name" name="details[firstName]" type="text" class="form-control"
                       value="{{relation.details.firstName}}" required/>
            </div>
            <div class="col-2">
                <label for="details-middle-name">Middle Name/Initial</label>
            </div>
            <div class="col-4">
                <input id="details-middle-name" name="details[middleName]" type="text" class="form-control"
                       value="{{relation.details.middleName}}" />
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-2">
                <label for="details-last-name">Last Name*</label>
            </div>
            <div class="col-4">
                <input id="details-last-name" name="details[lastName]" type="text" class="form-control"
                       value="{{relation.details.lastName}}" required/>
            </div>
            <div class="col-2">
                <label for="details[countryBirth]">Country of Birth*</label>
            </div>
            <div class="col-4">
                {{>file-reviewer/shared/select-country selectId="details[countryBirth]" group="natural" required="true"
                        value=relation.details.countryBirth}}
            </div>
        </div>
        <div class="row mt-2">            
            <div class="col-2">
                <label for="details-nationality">Nationality*</label>
            </div>
            <div class="col-4">
                {{>file-reviewer/shared/select-country selectId="details[nationality]" group="natural" required="true"
                        value=relation.details.nationality}}
            </div>
            <div class="col-2">
                <label for="details-birth-date">Date Of Birth*</label>
            </div>
            <div class="col-4">
                <input id="details-birth-date" name="details[birthDate]" type="date" class="form-control datepicker" required
                       placeholder="mm/dd/yyyy"
                       value="{{#formatDate relation.details.birthDate "YYYY-MM-DD"}} {{/formatDate }}"/>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-2">
                <label for="details-occupation">Occupation* <i class="fa fa-info-circle" aria-hidden="true" data-toggle="tooltip" data-title="The terms 'businessman/woman', 'entrepreneur', 'director', 'executive assistant' etc. must be qualified by indicating the company or institution in which a position is held and what type of business the company or institution conducts." data-original-title="" title=""></i></label>
            </div>
            <div class="col-10">
                <input id="details-occupation" name="details[occupation]" type="text" class="form-control"
                       value="{{relation.details.occupation}}" required/>
            </div>
        </div>
        <div class="row mt-2">
            <div class="col-2">
                <label for="details-sourceofincome">Source of Income* </label>
            </div>
            <div class="col-10">
                <input id="details-sourceofincome" name="details[source_of_income]" type="text" class="form-control"
                       value="{{relation.details.source_of_income}}" required/>
            </div>
        </div>
    </div>
    <hr class="mt-2"/>

    <!-- IDENTIFICATION DETAILS -->
    <div id="identificationDetails">
        <h4>Identification</h4>
        <div class="row mt-2">
            <div class="col-2">
                <label>Request Electronic ID?*</label>
            </div>
            <div class="col-4">
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" class="custom-control-input" id="natural-is-electronic-id-yes"
                           name="electronicIdInfo[isElectronicId]" required {{#if relation.electronicIdInfo.isElectronicId }} checked {{/if}} value="YES"/>
                    <label class="custom-control-label" for="natural-is-electronic-id-yes">Yes</label>
                </div>
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" class="custom-control-input" id="natural-is-electronic-id-no"
                           name="electronicIdInfo[isElectronicId]" required {{#unless relation.electronicIdInfo.isElectronicId }}
                           checked {{/unless}} value="NO"/>
                    <label class="custom-control-label" for="natural-is-electronic-id-no">No</label>
                </div>
            </div>
        </div>

        <div  id="electronic-id-files" class="row mt-2 {{#unless relation.electronicIdInfo.isElectronicId }} hide-element {{/unless}}">
            <div class="col-2">
                <label for="identification-email">E-mail*</label>
            </div>
            <div class="col-4">
                <input id="identification-email" name="electronicIdInfo[email]" type="email" class="form-control"
                       value="{{relation.electronicIdInfo.email}}" required/>
            </div>
        </div>
        <div id="manual-id-info" {{#if relation.electronicIdInfo.isElectronicId }} class="hide-element" {{/if}}>
            <div class="row mt-2">
                <div class="col-2">
                    <label for="identification[identificationType]">Type of Identification* </label>
                </div>
                <div class="col-4">
                    <select class="form-control w-100" name="identification[identificationType]" id="identification[identificationType]" data-toggle="select2"
                            data-value="{{relation.identification.identificationType}}"
                             required>
                        <option {{#ifEquals relation.identification.identificationType ''}} selected{{/ifEquals}}
                                value="" >Select</option>
                        <option {{#ifEquals relation.identification.identificationType 'passport'}}selected{{/ifEquals}}
                                value="passport">Passport</option>
                        <option {{#ifEquals relation.identification.identificationType 'id'}}selected{{/ifEquals}}
                                value="id">ID Card</option>
                        <option {{#ifEquals relation.identification.identificationType 'driver'}}selected{{/ifEquals}}
                                value="driver">Driver's license/Other</option>
                    </select>
                </div>
                <div class="col-2">
                    <label for="identification[issueCountry]">Country of Issue*</label>
                </div>
                <div class="col-4">
                    {{>file-reviewer/shared/select-country selectId="identification[issueCountry]" group="natural" required="true"
                            value=relation.identification.issueCountry}}
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-2">
                    <label for="identification-expiry-date">Expiry Date*</label>
                </div>
                <div class="col-4">
                    <input id="identification-expiry-date" name="identification[expiryDate]" type="date" required placeholder="mm/dd/yyyy"
                           class="form-control datepicker" value="{{#formatDate relation.identification.expiryDate "YYYY-MM-DD"}}{{/formatDate }}"/>
                </div>

            </div>

            <div class="row mt-2" id="manual-id-files">
                <div class="col-2">
                    <label>Certified legible copy of Passport</label>
                </div>
                <div class="col-4">
                    <button type="button" class="btn solid royal-blue upload-button {{#if relation.identification.files.0.uploadFiles}}uploaded-files-btn{{/if}} " id="btn-identification-0"
                            data-toggle="modal" data-target="#upload-temp-modal"
                            data-id="{{relation.identification.files.0.id }}" data-row="0"
                            data-incorporation-id="{{incorporationId}}"
                            data-mcc="{{masterClientCode}}"
                            data-relation-id="{{relation._id }}" data-file-type="natural"
                        {{#if newRelation}}
                            data-field="{{relation.naturalFiles.identification.0.internal}}"
                        {{else}}
                            data-field="{{relation.identification.files.0.internal}}"
                        {{/if}}
                            data-file-group="identification"
                        {{#if
                                relation.identification.files.0.uploadFiles}}
                        {{/if}}>
                        {{#if relation.identification.files.0.uploadFiles}}Modify{{else}}Upload{{/if}}
                    </button>
                </div>
                <div class="col-2">
                    <label data-toggle="tooltip" data-placement="right"
                           title="Utility bill - not a telephone bill">Certified legible copy of Proof of Address</label>
                </div>
                <div class="col-4">
                    <button type="button" class="btn solid royal-blue upload-button {{#if relation.identification.files.1.uploadFiles}}uploaded-files-btn {{/if}}" id="btn-identification-1"
                            data-toggle="modal" data-target="#upload-temp-modal"
                            data-id="{{ relation.identification.files.1.id }}" data-row="1"
                            data-incorporation-id="{{incorporationId}}"
                            data-mcc="{{masterClientCode}}"
                            data-relation-id="{{relation._id }}" data-file-type="natural" {{#if newRelation}}
                            data-field="{{relation.naturalFiles.identification.1.internal}}" {{else}}
                            data-field="{{relation.identification.files.1.internal}}" {{/if}}
                            data-file-group="identification"
                        >
                        {{#if relation.identification.files.1.uploadFiles}}Modify{{else}}Upload{{/if}}
                    </button>
                </div>
            </div>
        </div>


    </div>
    <hr class="mt-2"/>

    <!-- PRINCIPAL ADDRESS DETAILS -->
    <div id="principalAddressDetails">
        <h4>Principal Address</h4>
        {{>file-reviewer/relations/sections/address-details-form group="natural"
                principalAddress=relation.principalAddress formType="principalAddress"}}
    </div>
    <hr class="mt-2"/>

    <div class="row mt-2">
        <div class="col-6">
            <h4>Is mailing address the same as Principal address?*</h4>
        </div>
        <div class="col-6 d-flex justify-content-end">
            <div class="custom-control custom-radio custom-control-inline">
                <input type="radio" class="custom-control-input" id="natural-is-same-address-yes"
                       name="natural[isSamePrincipalAddress]" required {{#if relation.isSamePrincipalAddress }} checked {{/if}} value="YES"/>
                <label class="custom-control-label" for="natural-is-same-address-yes">Yes</label>
            </div>
            <div class="custom-control custom-radio custom-control-inline">
                <input type="radio" class="custom-control-input" id="natural-is-same-address-no"
                       name="natural[isSamePrincipalAddress]" {{#unless relation.isSamePrincipalAddress }} required
                       checked {{/unless}} value="NO"/>
                <label class="custom-control-label" for="natural-is-same-address-no">No</label>
            </div>
        </div>
    </div>
    <br>

    <!-- MAILING ADDRESS DETAILS -->
    <div id="mailingAddressDetails" {{#if relation.isSamePrincipalAddress}} class="hide-element" {{/if}}>
        <h4>Mailing Address</h4>
        {{>file-reviewer/relations/sections/address-details-form group="natural"
                principalAddress=relation.mailingAddress formType="mailingAddress"}}
    </div>
    <hr class="mt-2"/>

    <!-- COUNTRY OF TAX ADVICE-->
    <div id="countryTaxResidence">
        <h4>Tax Advice</h4>
        <div class="row mt-4">
            <div class="col-5">
                <label for="tax-residence-confirmation">Confirmation Regarding Legal / Tax Advice</label>
            </div>
            <div class="col-7 d-flex justify-content-end">
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" class="custom-control-input" id="tax-residence-confirmation-yes"
                            name="taxResidence[confirmation]" {{#if relation.taxResidence.confirmation }} checked {{/if}} value="YES"/>
                    <label class="custom-control-label" for="tax-residence-confirmation-yes">Yes</label>
                </div>
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" class="custom-control-input" id="tax-residence-confirmation-no"
                            name="taxResidence[confirmation]" {{#unless relation.taxResidence.confirmation }}
                            checked {{/unless}} value="NO"/>
                    <label class="custom-control-label" for="tax-residence-confirmation-no">No</label>
                </div>
            </div>
            <div class="col-5">
                <label for="natural-details-tax-residence">Tax Residence*</label>
            </div>
            <div class="col-3">
                {{>file-reviewer/shared/select-country selectId="taxResidence[taxResidence]" group="natural" required="true"
                        value=relation.taxResidence.taxResidence}}
            </div>
        </div>
        <hr class="mt-2"/>
    </div>

    <!-- ADVISOR DETAILS -->
    <div id="showAdvisorFields" {{#unless relation.taxResidence.confirmation}} class="hide-element" {{/unless}}>
        <div id="advisorDetails">
            <div class="row">
                <h4 data-toggle="tooltip" data-placement="right"
                    title="Not compulsory information - not all clients have an advisor">Advisor Details</h4>
            </div>
            <div class="row mt-4">
                <div class="col-2">
                    <label for="advisor-first-name">First Name*</label>
                </div>
                <div class="col-4">
                    <input id="advisor-first-name" name="advisorDetails[firstName]" type="text" class="form-control" required
                           value="{{relation.advisorDetails.firstName}}"/>
                </div>
                <div class="col-2">
                    <label for="advisor-middle-name">Middle Name</label>
                </div>
                <div class="col-4">
                    <input id="advisor-middle-name" name="advisorDetails[middleName]" type="text" class="form-control"
                           value="{{relation.advisorDetails.middleName}}"/>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-2">
                    <label for="advisor-last-name">Last Name*</label>
                </div>
                <div class="col-4">
                    <input id="advisor-last-name" name="advisorDetails[lastName]" type="text" class="form-control"
                           value="{{relation.advisorDetails.lastName}}" required/>
                </div>
                <div class="col-2">
                    <label for="advisor-firm-name">Name of Firm*</label>
                </div>
                <div class="col-4">
                    <input id="advisor-firm-name" name="advisorDetails[firmName]" type="text" class="form-control"
                           value="{{relation.advisorDetails.firmName}}" required/>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-2">
                    <label for="advisor-phone">Phone*</label>
                </div>
                <div class="col-4">
                    <input id="advisor-phone" name="advisorDetails[phone]" type="text" class="form-control"
                           value="{{relation.advisorDetails.phone}}" required/>
                </div>
                <div class="col-2">
                    <label for="advisor-email">E-mail*</label>
                </div>
                <div class="col-4">
                    <input id="advisor-email" name="advisorDetails[email]" type="email" class="form-control"
                           value="{{relation.advisorDetails.email}}" required/>
                </div>
            </div>
            <div class="row mt-2">
                <div class="col-2">
                    <label for="advisorDetails[nationality]">Nationality*</label>
                </div>
                <div class="col-4">
                    {{>file-reviewer/shared/select-country selectId="advisorDetails[nationality]" group="natural" required="true"
                            value=relation.advisorDetails.nationality}}
                </div>
                <div class="col-2">
                    <label for="advisorDetails[incorporationCountry]">Country of Incorporation*</label>
                </div>
                <div class="col-4">
                    {{>file-reviewer/shared/select-country selectId="advisorDetails[incorporationCountry]" group="natural" required="true"
                            value=relation.advisorDetails.incorporationCountry}}
                </div>
            </div>
        </div>
        <hr class="mt-2"/>

        <!-- PRINCIPAL RESIDENTIAL DETAILS -->
        <div id="residentialAddressDetails">
            <div class="row">
                <h4 data-toggle="tooltip" data-placement="right" title="Not compulsory information if no advisor">Principal
                    Advisor Address</h4>
            </div>
            {{>file-reviewer/relations/sections/address-details-form group="natural"
                    principalAddress=relation.residentialAddress formType="residentialAddress"}}
        </div>
        <hr class="mt-2"/>
    </div>


    <!-- PEP DETAILS -->
    {{#if showPep}}
        <div id="pepDetails">
            <div class="row mt-4">
                <div class="col-5">
                    <h4>Is this a relation PEP?</h4>
                </div>
                <div class="col-7 d-flex justify-content-end">
                    <div class="custom-control custom-radio custom-control-inline">
                        <input type="radio" class="custom-control-input" id="pep-confirmation-check-yes"
                               name="pepDetails[confirmation]" {{#if relation.pep }} checked {{/if}} value="YES"/>
                        <label class="custom-control-label" for="pep-confirmation-check-yes">Yes</label>
                    </div>
                    <div class="custom-control custom-radio custom-control-inline">
                        <input type="radio" class="custom-control-input" id="pep-confirmation-check-no"
                               name="pepDetails[confirmation]" {{#unless relation.pep }}
                               checked {{/unless}} value="NO"/>
                        <label class="custom-control-label" for="pep-confirmation-check-no">No</label>
                    </div>
                </div>
            </div>
            <div id="pepInfo" {{#unless relation.pep }} class="hide-element" {{/unless}}>
                <div class="row mt-2">
                    <div class="col-4">
                        <label>Professional reference letter addressed to TBVI</label>
                    </div>
                    <div class="col-2">
                        <button type="button" class="btn solid royal-blue upload-button {{#if relation.pepDetails.files.0.uploadFiles}}uploaded-files-btn{{/if}} " id="btn-pepDetails-0"
                                data-toggle="modal" data-target="#upload-temp-modal"
                                data-id="{{ relation.pepDetails.files.0.id }}" data-row="0"
                                data-incorporation-id="{{incorporationId}}"
                                data-mcc="{{masterClientCode}}"
                                data-relation-id="{{relation._id }}" data-file-type="natural" {{#if newRelation}}
                                data-field="{{relation.naturalFiles.pep.0.internal}}" {{else}}
                                data-field="{{relation.pepDetails.files.0.internal}}" {{/if}}
                                data-file-group="pepDetails">
                            {{#if relation.pepDetails.files.0.uploadFiles}}Modify{{else}}Upload{{/if}}
                        </button>
                    </div>
                    <div class="col-4">
                        <label>Bank reference letter addressed to TBVI</label>
                    </div>
                    <div class="col-2">
                        <button type="button" class="btn solid royal-blue upload-button {{#if relation.pepDetails.files.1.uploadFiles}}uploaded-files-btn{{/if}} " id="btn-pepDetails-1"
                                data-toggle="modal" data-target="#upload-temp-modal"
                                data-id="{{ relation.pepDetails.files.1.id }}" data-row="1"
                                data-incorporation-id="{{incorporationId}}"
                                data-mcc="{{masterClientCode}}"
                                data-relation-id="{{relation._id }}" data-file-type="natural" {{#if newRelation}}
                                data-field="{{relation.naturalFiles.pep.1.internal}}" {{else}}
                                data-field="{{relation.pepDetails.files.1.internal}}" {{/if}}
                                data-file-group="pepDetails" 
                        >
                            {{#if relation.pepDetails.files.1.uploadFiles}}Modify{{else}}Upload{{/if}}
                        </button>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-4">
                        <label>Curricular Vitae</label>
                    </div>
                    <div class="col-2">
                        <button type="button" class="btn solid royal-blue upload-button {{#if relation.pepDetails.files.2.uploadFiles}}uploaded-files-btn{{/if}} " id="btn-pepDetails-2"
                                data-toggle="modal" data-target="#upload-temp-modal"
                                data-id="{{ relation.pepDetails.files.2.id }}" data-row="2"
                                data-incorporation-id="{{incorporationId}}"
                                data-mcc="{{masterClientCode}}"
                                data-relation-id="{{relation._id }}" data-file-type="natural" {{#if newRelation}}
                                data-field="{{relation.naturalFiles.pep.2.internal}}" {{else}}
                                data-field="{{relation.pepDetails.files.2.internal}}" {{/if}}
                                data-file-group="pepDetails" >
                            {{#if relation.pepDetails.files.2.uploadFiles}}Modify{{else}}Upload{{/if}}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    {{/if}}

</div>

<script type="text/javascript" src="/views-js/partials/file-reviewer/relations/natural-form-component.js"></script>

