<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h2>{{ title }}</h2>
                    </div>
                    <!-- CONTENT -->
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12 pl-0">
                                <div class="table-responsive">
                                    <table id="file-datatable"
                                        class="table table-striped table-sm table-small-font w-100 nowrap ">
                                        <thead>
                                            <tr>
                                                <th scope="col">Order</th>
                                                <th scope="col">File name</th>
                                                <th scope="col"></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {{#if lastDirectory}}
                                            <tr class="bg-clear-yellow">
                                                <td>1</td>
                                                <td>[..]</td>
                                                <td class="text-right">
                                                    <a class="btn btn-sm btn-primary py-1 mr-1 width-lg"
                                                        href="/masterclients/{{masterClientCode}}/files?directory={{encodeURIComponent lastDirectory}}">Go
                                                        back</a>
                                                </td>
                                            </tr>
                                            {{/if}}
                                            {{#ifCond lastDirectory '===' ''}}
                                            <tr class="bg-clear-yellow">
                                                <td>1</td>
                                                <td>[..]</td>
                                                <td class="text-right">
                                                    <a class="btn btn-sm btn-primary py-1 mr-1 width-lg"
                                                        href="/masterclients/{{masterClientCode}}/files">Go back</a>
                                                </td>
                                            </tr>
                                            {{/ifCond}}
                                            {{#each files}}
                                                {{#if isDirectory}}
                                                <tr>
                                                    <td>2</td>
                                                    <td>{{ directoryName }}</td>
                                                    <td class="text-right">
                                                        <a class="btn btn-sm btn-primary py-1 mr-1 width-lg"
                                                            href="/masterclients/{{../masterClientCode}}/files?directory={{encodeURIComponent directoryRoute}}">Open
                                                            folder</a>
                                                    </td>
                                                </tr>
                                                {{else}}
                                                <tr>
                                                    <td>3</td>
                                                    <td>{{ fileName }}</td>
                                                    <td class="text-right">
                                                        <a class="btn btn-sm btn-primary py-1 mr-1 width-lg"
                                                            href="/masterclients/{{../masterClientCode}}/files/download?directory={{encodeURIComponent directory}}&file={{encodeURIComponent fileName}}"
                                                            target="_blank">Download</a>
                                                    </td>
                                                </tr>
                                                {{/if}}
                                            {{else}}
                                                <tr>
                                                    <td>1</td>
                                                    <td class="text-center font-italic" colspan="3">There are no new
                                                        files
                                                    </td>
                                                    <td></td>
                                                </tr>
                                            {{/each}}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <!-- RESPONSIVE TABLE END -->
                        </div>
                    </div>
                    <!-- CONTENT END -->
                    <div class="row mt-2">
                        <div class="col-md-12">
                            <a href="/masterclients" class="btn btn-secondary width-lg waves-effect waves-light">
                                Back
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>
<script type="text/javascript" src="/views-js/master-client-files/list.js"></script>
