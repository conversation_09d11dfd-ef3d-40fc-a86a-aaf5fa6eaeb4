let isEditAsset = false;
let openedAssetId = '';
let isEditFund = false;
let openedFundId = '';
$(document).on('show.bs.modal', '.modal', function () {
    var zIndex = 1040 + (10 * $('.modal:visible').length);
    $(this).css('z-index', zIndex);
    setTimeout(function () {
        $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
    }, 0);
});
$('input[name="companyOwnAssetsControl"]').change(function () {
    if ($(this).val() === 'Yes') {
        $('#assetsAndFunds').show(200);
    } else {
        $('#assetsAndFunds').hide(200);
    }
});

$('#openNewAssetModal').click(function () {
    openNewAssetModal()
})

$(document).on('click', '.deleteAsset', function () {
    deleteAsset($(this).attr('data-id'))
})

$(document).on('click', '.openEditAssetModal', function () {
    openEditAssetModal($(this).attr('data-id'))
})

$(document).on('click', '.deleteFund', function () {
    deleteFund($(this).attr('data-id'))
})

$(document).on('click', '.openEditFundModal', function () {
    openEditFundModal($(this).attr('data-id'))
})
 
$('#openNewFundModal').click(function () {
    openNewFundModal()
})

function deleteAsset(id) {
    Swal.fire(
        {
            title: "Delete?",
            text: "Are you sure you want to delete this asset?",
            icon: "question",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            cancelButtonText: "Cancel",
            confirmButtonText: "Ok"
        }).then(async function (t) {
            if (t.value) {
                
                $.ajax({
                    type: "DELETE",
                    url: "./" +  window.location.pathname.split('/')[4] + "/assets/" + id,
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                        if (data.status === 200) {
                            $('#asset-table-row-' + id).remove();
                        } else {
                            Swal.fire(
                                {
                                    title: "Error!",
                                    text: data.message,
                                    icon: "warning",
                                    showCancelButton: false,
                                    confirmButtonColor: "#3085d6",
                                    confirmButtonText: "Ok"
                                });
                        }
                    },
                    error: function (err) {
                        toastr["warning"]('Asset could not be deleted, please try again later.', 'Error!');
                    }
                });
            }
        });
}

$('#submitAssetBtn').click(async function () {
    $('#newAssetModal input[required]:visible').trigger('keyup');
    $('#newAssetModal select[required]:visible').trigger('change');
    const asset = {
        type: $("#assetTypeControl").val(),
        registrationNumber: $("#assetRegistrationNumberControl").val(),
        details: $("#assetDetailsControl").val(),
        nameOfInstitution: $("#assetNameOfInstitutionControl").val(),
        addressOfInstitution: $("#assetAddressOfInstitutionControl").val(),
        nameOfBank: $("#assetNameOfBankControl").val(),
        addressOfBank: $("#assetAddressOfBankControl").val(),
        nameOfTrust: $("#assetNameOfTrustControl").val(),
        realEstateType: $("#assetRealEstateTypeControl").val(),
        location: $("#assetLocationControl").val(),
        jurisdictionOfRegistration: $("#-assetJurisdistion").val()
    }
    if ($("#newAssetModal .is-invalid:visible").length === 0) {
        
        $.ajax({
            type: isEditAsset ? "PUT" : "POST",
            url: "./" +  window.location.pathname.split('/')[4] + "/assets" + (isEditAsset ? `/${openedAssetId}` : ''),
            data: JSON.stringify(asset),
            contentType: "application/json; charset=utf-8",
            success: function (data) {
                if (data.status === 200) {
                    let template = Handlebars.templates.step4createassetrow;
                    let html = template({asset: data.asset});
                    if (isEditAsset) {
                        $(`#asset-table-row-${data.asset._id}`).replaceWith(html);
                    } else {
                        $('#assetsTableBody').append(html)
                    }

                    $('#newAssetModal').modal('hide');
                    $("#assetTypeControl").val('');
                    $("#assetRegistrationNumberControl").val('');
                    $('#newAssetModal input').val('')
                    $("#-assetJurisdistion").val("");
                    $("#select2--assetJurisdistion-container").html('').attr('title', '');
                    $('#assetRegistrationNumberRow').hide();
                    $('#assetDetailsRow').hide();
                    $('#assetNameOfInstitutionRow').hide();
                    $('#assetAddressOfInstitutionRow').hide();
                    $('#assetNameOfBankRow').hide();
                    $('#assetAddressOfBankRow').hide();
                    $('#assetNameOfTrustRow').hide();
                    $('#assetRealEstateTypeRow').hide();
                    $('#assetLocationRow').hide();
                    $('#assetJurisdistionRow').hide();

                    $('#missingAssetRowStep4').hide(200);
                } else {
                    toastr["warning"](data.message, 'Error!');
                }
            },
            error: function (err) {
                toastr["warning"]('Asset could not be saved, please try again later.', 'Error!');
            }
        });
    }
});

function openNewAssetModal() {
    $('#newAssetModal').modal();
    isEditAsset = false;
}

function openEditAssetModal(id) {
    $.ajax({
        type: "GET",
        url: "./" +  window.location.pathname.split('/')[4] + "/assets/" + id,
        contentType: "application/json; charset=utf-8",
        success: function (data) {
            if (data.status === 200) {
                isEditAsset = true;
                openedAssetId = id;
                $('#newAssetModal').modal();
                $('#assetTypeControl').val(data.asset.type);
                $("#select2-assetTypeControl-container").html(data.asset.type.replace(/</g, '&lt;').replace(/>/g, '&gt;')).attr('title', data.asset.type);

                $("#assetRegistrationNumberControl").val(data.asset.registrationNumber);
                $("#assetDetailsControl").val(data.asset.details);
                $("#assetNameOfInstitutionControl").val(data.asset.nameOfInstitution);
                $("#assetAddressOfInstitutionControl").val(data.asset.addressOfInstitution);
                $("#assetNameOfBankControl").val(data.asset.nameOfBank);
                $("#assetAddressOfBankControl").val(data.asset.addressOfBank);
                $("#assetNameOfTrustControl").val(data.asset.nameOfTrust);
                $("#assetRealEstateTypeControl").val(data.asset.realEstateType);
                $("#assetLocationControl").val(data.asset.location);
                $("#-assetJurisdistion").val(data.asset.jurisdictionOfRegistration);
                $("#select2--assetJurisdistion-container").html(data.asset.jurisdictionOfRegistration.replace(/</g, '&lt;').replace(/>/g, '&gt;')).attr('title', data.asset.jurisdictionOfRegistration);

                $('#assetRegistrationNumberRow').hide();
                $('#assetDetailsRow').hide();
                $('#assetNameOfInstitutionRow').hide();
                $('#assetAddressOfInstitutionRow').hide();
                $('#assetNameOfBankRow').hide();
                $('#assetAddressOfBankRow').hide();
                $('#assetNameOfTrustRow').hide();
                $('#assetRealEstateTypeRow').hide();
                $('#assetLocationRow').hide();
                $('#assetJurisdistionRow').hide();
                if (data.asset.type === 'Aircraft') {
                    $('#assetRegistrationNumberRow').show();
                    $('#assetJurisdistionRow').show();
                } else if (data.asset.type === 'Vessel (ship/yacht)') {
                    $('#assetRegistrationNumberRow').show();
                    $('#assetJurisdistionRow').show();
                } else if (data.asset.type === 'Intellectual property rights') {
                    $('#assetDetailsRow').show();
                } else if (data.asset.type === 'Investment portfolio') {
                    $('#assetNameOfInstitutionRow').show();
                    $('#assetAddressOfInstitutionRow').show();
                } else if (data.asset.type === 'Bank account') {
                    $('#assetNameOfBankRow').show();
                    $('#assetAddressOfBankRow').show();
                } else if (data.asset.type === 'Trust assets') {
                    $('#assetNameOfTrustRow').show();
                } else if (data.asset.type === 'Shares/equity participations') {
                    $('#assetDetailsRow').show();
                } else if (data.asset.type === 'Real estate') {
                    $('#assetRealEstateTypeRow').show();
                    $('#assetLocationRow').show();
                } else if (data.asset.type === 'Debt') {
                    $('#assetDetailsRow').show();
                } else if (data.asset.type === 'Other') {
                    $('#assetDetailsRow').show();
                }
            } else {
                Swal.fire(
                    {
                        title: "Error!",
                        text: data.message,
                        icon: "warning",
                        showCancelButton: false,
                        confirmButtonColor: "#3085d6",
                        confirmButtonText: "Ok"
                    });
            }
        },
        error: function (err) {
            toastr["warning"]('Asset could not be edited, please try again later.', 'Error!');
        }
    });
}

function deleteFund(id) {
    Swal.fire(
        {
            title: "Delete?",
            text: "Are you sure you want to delete this fund?",
            icon: "question",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            cancelButtonText: "Cancel",
            confirmButtonText: "Ok"
        }).then(async function (t) {
            if (t.value) {
                
                $.ajax({
                    type: "DELETE",
                    url: "./" +  window.location.pathname.split('/')[4] + "/funds/" + id,
                    contentType: "application/json; charset=utf-8",
                    success: function (data) {
                        if (data.status === 200) {
                            $('#fund-table-row-' + id).remove();
                        } else {
                            Swal.fire(
                                {
                                    title: "Error!",
                                    text: data.message,
                                    icon: "warning",
                                    showCancelButton: false,
                                    confirmButtonColor: "#3085d6",
                                    confirmButtonText: "Ok"
                                });
                        }
                    },
                    error: function (err) {
                        toastr["warning"]('Fund could not be deleted, please try again later.', 'Error!');
                    }
                });
            }
        });
}

$('#submitFundBtn').click(async function () {
    $('#newFundModal input[required]:visible').trigger('keyup');
    $('#newFundModal select[required]:visible').trigger('change');
    const fund = {
        type: $("#fundTypeControl").val(),
        nameOfFinancialInstitution: $("#fundFinancialInstitutionNameControl").val(),
        details: $("#fundDetailsControl").val(),
        profit: $("#fundProfitControl").val(),
        nameOfSubsidiary: $("#fundNameOfSubsidiaryControl").val(),
        jurisdictionOfOperation: $("#-fundJurisdictionOfOperation").val(),
    }
    if ($("#newFundModal .is-invalid:visible").length === 0) {
        
        $.ajax({
            type: isEditFund ? "PUT" : "POST",
            url: "./" +  window.location.pathname.split('/')[4] + "/funds" + (isEditFund ? `/${openedFundId}` : ''),
            data: JSON.stringify(fund),
            contentType: "application/json; charset=utf-8",
            success: function (data) {
                if (data.status === 200) {
                    let template = Handlebars.templates.step4createfundrow;
                    let html = template({fund: data.fund});
                    if (isEditFund) {
                        $(`#fund-table-row-${data.fund._id}`).replaceWith(html)
                    } else {
                        $('#fundsTableBody').append(html);
                    }
                    $('#newFundModal').modal('hide');
                    $("#fundTypeControl").val('');
                    $('#newFundModal input').val('')
                    $("#-fundJurisdictionOfOperation").val("");
                    $("#select2--fundJurisdictionOfOperation-container").html('').attr('title', '');

                    $('#fundDetailsRow').hide();
                    $('#fundProfitRow').hide();
                    $('#fundFinancialInstitutionNameRow').hide();
                    $('#fundNameOfSubsidiaryRow').hide();
                    $('#fundJurisdictionOfOperationRow').hide();

                    $('#missingFundRowStep4').hide(200);
                } else {
                    toastr["warning"](data.message, 'Error!');
                }
            },
            error: function (err) {
                toastr["warning"]('Fund could not be saved, please try again later.', 'Error!');
            }
        });
    }
});

function openNewFundModal() {
    $('#newFundModal').modal();
    isEditFund = false;
}

function openEditFundModal(id) {
    $.ajax({
        type: "GET",
        url: "./" +  window.location.pathname.split('/')[4] + "/funds/" + id,
        contentType: "application/json; charset=utf-8",
        success: function (data) {
            if (data.status === 200) {
                isEditFund = true;
                openedFundId = id;
                $('#newFundModal').modal();
                $('#fundTypeControl').val(data.fund.type);
                $("#select2-fundTypeControl-container").html(data.fund.type).attr('title', data.fund.type);

                $("#fundFinancialInstitutionNameControl").val(data.fund.nameOfFinancialInstitution);
                $("#fundDetailsControl").val(data.fund.details);
                $("#fundProfitControl").val(data.fund.profit);
                $("#fundNameOfSubsidiaryControl").val(data.fund.nameOfSubsidiary);
                $("#-fundJurisdictionOfOperation").val(data.fund.jurisdictionOfOperation);
                $("#select2--fundJurisdictionOfOperation-container").html(data.fund.jurisdictionOfOperation).attr('title', data.fund.jurisdictionOfOperation);

                $('#fundFinancialInstitutionNameRow').hide();
                $('#fundDetailsRow').hide();
                $('#fundProfitRow').hide();
                $('#fundNameOfSubsidiaryRow').hide();
                $('#fundJurisdictionOfOperationRow').hide();
                if (data.fund.type === 'Loan') {
                    $('#fundFinancialInstitutionNameRow').show();
                } else if (data.fund.type === 'Sale of assets') {
                    $('#fundDetailsRow').show();
                } else if (data.fund.type === 'Business income') {
                    $('#fundProfitRow').show();
                } else if (data.fund.type === 'Dividend from subsidiary') {
                    $('#fundNameOfSubsidiaryRow').show();
                    $('#fundJurisdictionOfOperationRow').show();
                } else if (data.fund.type === 'Other') {
                    $('#fundDetailsRow').show();
                }
            } else {
                Swal.fire(
                    {
                        title: "Error!",
                        text: data.message,
                        icon: "warning",
                        showCancelButton: false,
                        confirmButtonColor: "#3085d6",
                        confirmButtonText: "Ok"
                    });
            }
        },
        error: function (err) {
            toastr["warning"]('Fund could not be edited, please try again later.', 'Error!');
        }
    });
}


$('#newAssetModal').on('hide.bs.modal', function (event) {
    $("#assetTypeControl").val('');
    $("#select2-assetTypeControl-container").html('').attr('title', '');
    $("#assetRegistrationNumberControl").val('');
    $('#newAssetModal input').val('')
    $("#-assetJurisdistion").val("");
    $("#select2--assetJurisdistion-container").html('').attr('title', '');
    $('#assetRegistrationNumberRow').hide();
    $('#assetDetailsRow').hide();
    $('#assetNameOfInstitutionRow').hide();
    $('#assetAddressOfInstitutionRow').hide();
    $('#assetNameOfBankRow').hide();
    $('#assetAddressOfBankRow').hide();
    $('#assetNameOfTrustRow').hide();
    $('#assetRealEstateTypeRow').hide();
    $('#assetLocationRow').hide();
    $('#assetJurisdistionRow').hide();
});

$('#newFundModal').on('hide.bs.modal', function (event) {
    $("#fundTypeControl").val('');
    $("#select2-fundTypeControl-container").html('').attr('title', '');
    $('#newFundModal input').val('')
    $("#-fundJurisdictionOfOperation").val("");
    $("#select2--fundJurisdictionOfOperation-container").html('').attr('title', '');

    $('#fundDetailsRow').hide();
    $('#fundProfitRow').hide();
    $('#fundFinancialInstitutionNameRow').hide();
    $('#fundNameOfSubsidiaryRow').hide();
    $('#fundJurisdictionOfOperationRow').hide();
});