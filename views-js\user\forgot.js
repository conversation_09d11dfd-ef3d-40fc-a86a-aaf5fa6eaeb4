$("#requestResetPasswordBtn").on('click', async function (e) {
    e.preventDefault();
    $("#requestResetPasswordBtn").prop('disabled', true);

    try {
        const recaptcha_site = $("#requestResetPasswordBtn").data('sitekey');
        const isTokenSet = await setCaptchaToken(recaptcha_site);
        if (isTokenSet) {
            $("#requestResetPasswordForm").submit();
        } else {
            $("#requestResetPasswordBtn").prop('disabled', false);
        }
    }
    catch (e) {
        $("#requestResetPasswordBtn").prop('disabled', false);
    }
});