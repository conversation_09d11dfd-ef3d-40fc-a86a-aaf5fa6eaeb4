(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['boardmeetings'] = template({"1":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, alias5=container.lambda, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <tr id=\"board-meetings-table-row-"
    + alias4(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":2,"column":37},"end":{"line":2,"column":44}}}) : helper)))
    + "\" class=\"board-meeting-row\">\r\n        <td> "
    + alias4(((helper = (helper = lookupProperty(helpers,"meeting_number") || (depth0 != null ? lookupProperty(depth0,"meeting_number") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"meeting_number","hash":{},"data":data,"loc":{"start":{"line":3,"column":13},"end":{"line":3,"column":31}}}) : helper)))
    + " </td>\r\n        <td> "
    + alias4(((helper = (helper = lookupProperty(helpers,"name") || (depth0 != null ? lookupProperty(depth0,"name") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"name","hash":{},"data":data,"loc":{"start":{"line":4,"column":13},"end":{"line":4,"column":21}}}) : helper)))
    + " </td>\r\n        <td> "
    + ((stack1 = lookupProperty(helpers,"if").call(alias1,(depth0 != null ? lookupProperty(depth0,"physically_present") : depth0),{"name":"if","hash":{},"fn":container.program(2, data, 0, blockParams, depths),"inverse":container.program(4, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":5,"column":13},"end":{"line":5,"column":63}}})) != null ? stack1 : "")
    + " </td>\r\n        <td> "
    + alias4(((helper = (helper = lookupProperty(helpers,"relation_to_entity") || (depth0 != null ? lookupProperty(depth0,"relation_to_entity") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"relation_to_entity","hash":{},"data":data,"loc":{"start":{"line":6,"column":13},"end":{"line":6,"column":35}}}) : helper)))
    + " </td>\r\n        <td> "
    + alias4(((helper = (helper = lookupProperty(helpers,"qualification") || (depth0 != null ? lookupProperty(depth0,"qualification") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"qualification","hash":{},"data":data,"loc":{"start":{"line":7,"column":13},"end":{"line":7,"column":30}}}) : helper)))
    + " </td>\r\n        <td class=\"justify-content-center d-flex d-flex-inline\">\r\n            <button type=\"button\" class=\"btn btn-sm royal-blue solid mr-1\" data-activity-type=\""
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"activityType") : depths[1]), depth0))
    + "\"\r\n                data-id=\""
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"entryId") : depths[1]), depth0))
    + "\" data-board-meeting-id=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":10,"column":64},"end":{"line":10,"column":71}}}) : helper)))
    + "\" data-toggle=\"modal\" data-target=\"#boardMeetingModal\">\r\n                <i class=\"fa fa-pencil\"></i>\r\n            </button>\r\n            <button type=\"button\" class=\"btn btn-sm btn-danger deleteBoardMeeting\"\r\n            data-activity-type=\""
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"activityType") : depths[1]), depth0))
    + "\" data-id=\""
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"entryId") : depths[1]), depth0))
    + "\"\r\n                                data-board-meeting-id=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":15,"column":55},"end":{"line":15,"column":62}}}) : helper)))
    + "\">\r\n                <i class=\"fa fa-times\"></i>\r\n            </button>\r\n        </td>\r\n    </tr>\r\n";
},"2":function(container,depth0,helpers,partials,data) {
    return " Yes ";
},"4":function(container,depth0,helpers,partials,data) {
    return " No ";
},"6":function(container,depth0,helpers,partials,data) {
    return "        <tr>\r\n            <td colspan=\"6\">\r\n                No board meetings found\r\n            </td>\r\n        </tr>\r\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"boardMeetings") : depth0),{"name":"each","hash":{},"fn":container.program(1, data, 0, blockParams, depths),"inverse":container.program(6, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":1,"column":0},"end":{"line":26,"column":9}}})) != null ? stack1 : "")
    + "\r\n\r\n";
},"useData":true,"useDepths":true});
})();