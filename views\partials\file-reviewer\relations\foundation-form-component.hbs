<div id="foundationForm">

    <!-- CORPORATE DETAILS -->
    <div id="foundationDetails">
        <h4>Details</h4>
        <div class="row mt-2">
            <div class="col-6 d-flex justify-content-between">
                <label>Is this company already a TridentTrust client?*</label>
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" class="custom-control-input" id="foundation-is-already-client-yes"
                           name="foundation[isTridentClient]" required {{#if relation.details.isTridentClient }}
                           checked {{/if}} value="YES"/>
                    <label class="custom-control-label" for="foundation-is-already-client-yes">Yes</label>
                </div>
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" class="custom-control-input" id="foundation-is-already-client-no"
                           name="foundation[isTridentClient]" {{#unless relation.details.isTridentClient }}
                           checked {{/unless}} value="NO"/>
                    <label class="custom-control-label" for="foundation-is-already-client-no">No</label>
                </div>
            </div>
            <div class="col-6">
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-2">
                <label for="foundation-details-organization-name">Company Name*</label>
            </div>
            <div class="col-4">
                <input type="text" class="form-control"  id="foundation-details-organization-name"  name="details[organizationName]"
                       required
                       value="{{relation.details.organizationName}}" />
            </div>
            <div class="col-2">
                <label for="foundation-details-incorporation-number">Incorporation / Formation Number*</label>
            </div>
            <div class="col-4">
                <input id="foundation-details-incorporation-number" class="form-control" type="text"
                       name="details[incorporationNumber]" required
                       value="{{relation.details.incorporationNumber}}"/>
            </div>
        </div>
        <div class="row mt-2 isTridentClient {{#if relation.details.isTridentClient}}
            hide-element {{/if}}">
            <div class="col-2">
                <label for="foundation-details-tax-residence">Tax Residence*</label>
            </div>
            <div class="col-4">
                {{>file-reviewer/shared/select-country selectId="details[taxResidence]" group="foundation" required="true"
                        value=relation.details.taxResidence}}
            </div>
            <div class="col-2">
                <label for="foundation-details-registration-number"
                >Business Registration Number (if applicable)*</label
                >
            </div>
            <div class="col-4">
                <input class="form-control" type="text"  id="foundation-details-registration-number"
                       name="details[businessNumber]" required
                       value="{{relation.details.businessNumber}}"/>
            </div>
        </div>
        <div class="row mt-2 isTridentClient {{#if relation.details.isTridentClient}}
             hide-element {{/if}}">
            <div class="col-2">
                <label for="foundation-details-incorporation-date">Date of Incorporation*</label>
            </div>
            <div class="col-4">
                <input class="form-control datepicker" type="date" id="foundation-details-incorporation-date" placeholder="mm/dd/yyyy"
                       name="details[incorporationDate]" required
                       value="{{#formatDate relation.details.incorporationDate "YYYY-MM-DD"}} {{/formatDate }}"/>
            </div>
            <div class="col-2">
                <label for="details[incorporationCountry]">Country of Incorporation*</label>
            </div>
            <div class="col-4">
                {{>file-reviewer/shared/select-country selectId="details[incorporationCountry]" group="foundation" required="true"
                        value=relation.details.incorporationCountry}}
            </div>
        </div>
        <div class="isTridentClient {{#if relation.details.isTridentClient}}
             hide-element {{/if}}">
            <!-- DETAILS TABLE -->
            {{>file-reviewer/shared/relation-file-table tableId="detailsTable"  name="details" group="foundation"
                    files=(ternary newRelation relation.foundationFiles.details relation.details.files)
                    relationId=relation._id}}

            <!-- DETAILS PARTNER TABLE -->
            {{>file-reviewer/shared/certificate-partner-table group="foundation"
                    partnerFiles=(ternary newRelation relation.foundationFiles.detailsPartner relation.detailsPartner.files)
                    relationId=relation._id}}
        </div>


    </div>

    <div class="isTridentClient {{#if relation.details.isTridentClient}}
         hide-element {{/if}}">
        <hr class="mt-2"/>
        <!-- PRINCIPAL ADDRESS DETAILS -->
        <div id="principalAddressDetails">
            <h4>Principal Address</h4>
            {{>file-reviewer/relations/sections/address-details-form group="foundation"
                    principalAddress=relation.principalAddress formType="principalAddress"}}
        </div>
        <hr class="mt-2" />

        <div class="row mt-2">
            <div class="col-6">
                <h4>Is mailing address the same as Principal address?*</h4>
            </div>
            <div class="col-6 d-flex justify-content-end">
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" class="custom-control-input" id="foundation-is-same-address-yes"
                           name="foundation[isSamePrincipalAddress]" required {{#if relation.isSamePrincipalAddress }} checked {{/if}} value="YES"/>
                    <label class="custom-control-label" for="foundation-is-same-address-yes">Yes</label>
                </div>
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" class="custom-control-input" id="foundation-is-same-address-no"
                           name="foundation[isSamePrincipalAddress]" {{#unless relation.isSamePrincipalAddress }}
                           checked {{/unless}} value="NO"/>
                    <label class="custom-control-label" for="foundation-is-same-address-no">No</label>
                </div>
            </div>
        </div>
        <br>

        <!-- MAILING ADDRESS DETAILS -->
        <div id="mailingAddressDetails {{#if relation.isSamePrincipalAddress}} hide-element {{/if}}">
            <h4>Mailing Address</h4>
            {{>file-reviewer/relations/sections/address-details-form group="foundation"
                    principalAddress=relation.mailingAddress formType="mailingAddress"}}
        </div>
        <hr class="mt-2" />

        <!-- LISTED COMPANY DETAILS -->
        <div class="listedCompanyDetails">
            {{>file-reviewer/relations/sections/listed-company-details-form group="foundation"
                    listedCompany=relation.listedCompanyDetails}}
        </div>
        <hr class="mt-2" />

        <!-- LIMITED COMPANY DETAILS -->
        <div class="limitedCompanyDetails">
            {{>file-reviewer/relations/sections/limited-company-details-form group="foundation"
                    limitedCompany=relation.limitedCompanyDetails}}
        </div>
        <hr class="mt-2" />

        <!-- FOUNDATION DETAILS -->
        <div class="foundationDetails">
            <div class="row">
                <div class="col-5">
                    <h4>Foundation Details</h4>
                </div>
                <div class="col-7 d-flex">
                    <div class="custom-control custom-switch my-auto">
                        <input type="checkbox" class="custom-control-input toggle-section-check"
                               id="foundation-details-confirmation"
                               name="foundation[active]"
                            {{#if relation.foundation.active}} checked {{/if}}
                        >
                        <label class="custom-control-label" for="foundation-details-confirmation"></label>
                    </div>
                </div>
            </div>
            <div id="content-foundation-details-confirmation {{#unless relation.foundation.active }} hide-element {{/unless}}">
                {{>file-reviewer/shared/relation-file-table tableId="foundationTable"  name="foundation"
                        files=(ternary newRelation relation.foundationFiles.foundation relation.foundation.files)
                        relationId=relation._id}}
                <div class="row mt-2">
                    <div class="col-2">
                        <label for="foundation[country]">Country</label>
                    </div>
                    <div class="col-4">
                        {{>file-reviewer/shared/select-country selectId="foundation[country]" group="foundation" required="true"
                                value=relation.foundation.country}}
                    </div>
                </div>
            </div>

        </div>
        <hr class="mt-2"/>

        <!-- MUTUAL FUND DETAILS -->
        <div class="mutualFundDetails">
            {{>file-reviewer/relations/sections/mutual-fund-details-form group="foundation"
                    mutualFund=(ternary newRelation relation.foundationFiles.mutualFund relation.mutualFundDetails) relationId=relation._id}}
        </div>
        <hr class="mt-2" />
    </div>


</div>
<script type="text/javascript" src="/views-js/partials/file-reviewer/relations/foundation-form-component.js"></script>
