const mongoose = require('mongoose');
const fileTypeSchema = require('./file');

const listTypeSchema = new mongoose.Schema({
  naturalFiles: [fileTypeSchema],
  organizationFiles: [fileTypeSchema]
});

const naturalTypeSchema = new mongoose.Schema({
  details: [fileTypeSchema],
  identification: [fileTypeSchema],
  shareholder:  [fileTypeSchema],
  pep:  [fileTypeSchema],
  worldCheck:  [fileTypeSchema],
});

const organizationTypeSchema = new mongoose.Schema({
  details:  [fileTypeSchema],
  detailsPartner:  [fileTypeSchema],
  principalAddress:  [fileTypeSchema],
  limitedCompany:  [fileTypeSchema],
  mutualFund: [fileTypeSchema],
  foundation: [fileTypeSchema],
  worldCheck:  [fileTypeSchema],
});

const TemplateSchema = new mongoose.Schema({
  beneficialOwnerFiles: {type: listTypeSchema, required: true},
  shareholderFiles:{type: listTypeSchema, required: true},
  directorFiles: {type: listTypeSchema, required: true},
  relationFiles: {
    naturalFiles: {type: naturalTypeSchema},
    corporateFiles: {type: organizationTypeSchema},
    foundationFiles: {type: organizationTypeSchema},
    trustFiles: {type: organizationTypeSchema},
    limitedFiles: {type: organizationTypeSchema},
  },
  'nonManagedFiles': [fileTypeSchema],
  managed_questions: [fileTypeSchema],
});

//Export model
module.exports = mongoose.model('template', TemplateSchema);
