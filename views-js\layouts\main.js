
$(document).ready(function () {
 
    setCsrfHeader();
    if (GetIEVersion() > 0) {
        $(".ie-div").show();
    }


    $('#btnLogout').click(function () {
        Swal.fire(
            {
                title: "Are you sure you want to logout?",
                text: "Any unsaved work will not be saved",
                icon: "warning",
                showCancelButton: !0,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                confirmButtonText: "Yes, log out!"
            }).then(function (t) {
                if (t.value) {
                    document.location = '/users/logout';
                }
            })
    });
});


function GetIEVersion() {

    var sAgent = window.navigator.userAgent;
    var Idx = sAgent.indexOf("MSIE");

    // If IE, return version number.
    if (Idx > 0)
        return parseInt(sAgent.substring(Idx + 5, sAgent.indexOf(".", Idx)));

    // If IE 11 then look for Updated user agent string.
    else if (!!navigator.userAgent.match(/Trident\/7\./))
        return 11;

    else
        return 0; //It is not IE
}

function hideIEMessage() {
    heroCheckThisOut$(".ie-div").remove();
}
Handlebars.registerHelper('ifEquals', function (arg1, arg2, options) {
    return arg1 === arg2 ? options.fn(this) : options.inverse(this);
});

Handlebars.registerHelper('ifCond', function (v1, operator, v2, options) {
    switch (operator) {
        case '==':
            return (v1 == v2) ? options.fn(this) : options.inverse(this);
        case '===':
            return (v1 === v2) ? options.fn(this) : options.inverse(this);
        case '!=':
            return (v1 != v2) ? options.fn(this) : options.inverse(this);
        case '!==':
            return (v1 !== v2) ? options.fn(this) : options.inverse(this);
        case '<':
            return (v1 < v2) ? options.fn(this) : options.inverse(this);
        case '<=':
            return (v1 <= v2) ? options.fn(this) : options.inverse(this);
        case '>':
            return (v1 > v2) ? options.fn(this) : options.inverse(this);
        case '>=':
            return (v1 >= v2) ? options.fn(this) : options.inverse(this);
        case '&&':
            return (v1 && v2) ? options.fn(this) : options.inverse(this);
        case '||':
            return (v1 || v2) ? options.fn(this) : options.inverse(this);
        default:
            return options.inverse(this);
    }
});

Handlebars.registerHelper('formatDate', function (date, format) {
    if (date) {
        return moment(date).utc().format(format)
    } else {
        return '';
    }
});
Handlebars.registerHelper('json', function (context) {
    return JSON.stringify(context);
});

Handlebars.registerHelper('decimalValue', function (dVal) {
    return Number(dVal).toLocaleString("en", { minimumFractionDigits: 2 });
});

Handlebars.registerHelper('calculatePercentage', function (total = 0, percentage = 0) {
    let result = 0;
    if (percentage) {
        let calculate = ((total * percentage) / 100);
        result = total + calculate
    }
    return result.toLocaleString("en", { minimumFractionDigits: 2 });
});

Handlebars.registerHelper('add', function (a, b) {
    return a + b;
});


Handlebars.registerHelper('getCountryName', getCountryName)

$('[data-toggle="tooltip"]').tooltip({
    trigger : 'hover',
    container: 'body'

});

function setCsrfHeader(){
    const csrf_token = document.querySelector("meta[name='meta-csrf-token']").getAttribute("content");

    // set on axios
    axios.defaults.headers.post["x-csrf-token"] = csrf_token;
    axios.defaults.headers.put["x-csrf-token"] = csrf_token;
    axios.defaults.headers.delete["x-csrf-token"] = csrf_token;
    axios.defaults.headers.patch["x-csrf-token"] = csrf_token;

    // Axios does not create an object for TRACE method by default, and has to be created manually.
    axios.defaults.headers.trace = {}
    axios.defaults.headers.trace["x-csrf-token"] = csrf_token

    // set on ajax
    $.ajaxSetup({
        beforeSend: function (xhr, settings) {
            if (!csrfSafeMethod(settings.type) && !this.crossDomain) {
                xhr.setRequestHeader("x-csrf-token", csrf_token);
            }
        },
        statusCode: {
            401: function () {
                console.log("redirect invalid session ");
                window.location.href = '/';
            }
        }
    });

}

function csrfSafeMethod(method) {
    // these HTTP methods do not require CSRF protection
    return (/^(GET|HEAD|OPTIONS)$/.test(method));
}
