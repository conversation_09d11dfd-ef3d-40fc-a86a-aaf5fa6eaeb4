<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h3>
                            <span class="font-weight-bold" class="text-capitalize">
                                Edit Relation:
                                {{#ifEquals relation.type 'natural'}}
                                    {{relation.details.firstName}} {{relation.details.middleName}}  {{relation.details.lastName}}
                                {{else}}
                                    {{ relation.details.organizationName }}
                                {{/ifEquals}}</span>
                        </h3>
                    </div>
                    <div class="card-body">
                        <form id="reviewRelationForm" method="POST" autocomplete="off" novalidate>
                            <input type="text" hidden name="csrf-token" value="{{csrfToken}}">
                            <div class="row">
                                <div class="col-12">
                                    <h4>
                                        Type:
                                        <span class="font-weight-bold" class="text-capitalize">{{
                                        relation.type
                                        }}</span>
                                    </h4>
                                </div>
                            </div>
                            <hr class="mt-2"/>
                            <div>
                                <div class="row">
                                    <div class="col-6 d-flex justify-content-between">
                                        <label>Is the company owned by a Natural person or a Corporate entity?</label>
                                    </div>
                                    <div class="col-6">
                                        <div class="custom-control custom-radio custom-control-inline">
                                            <input type="radio" class="custom-control-input"
                                                   id="relation-type-natural-check"
                                                   name="relationType" value="natural"
                                                {{#ifCond relation.type "===" "natural"}} checked {{/ifCond}} disabled/>
                                            <label class="custom-control-label" for="relation-type-natural-check">Natural</label>
                                        </div>
                                        <div class="custom-control custom-radio custom-control-inline">
                                            <input type="radio" class="custom-control-input"
                                                   id="relation-type-corporate-check"
                                                   name="relationType" value="corporate"
                                                {{#ifCond relation.type "!==" "natural"}} checked {{/ifCond}} disabled/>
                                            <label class="custom-control-label" for="relation-type-corporate-check">Corporate</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mt-2">
                                    <div class="col-md-6">
                                        <label for="">Type of relation*</label>
                                    </div>
                                    <div class="col-md-6 d-flex ">
                                        <div class="custom-control custom-checkbox mr-3">
                                            <input type="checkbox" class="custom-control-input" id="beneficialType"
                                                   name="groups[]"
                                                   value="Beneficial Owner"
                                                {{#ifContains "Beneficial Owner" relation.groups}}
                                                   checked {{/ifContains}}
                                            >
                                            <label class="custom-control-label" for="beneficialType">Beneficial
                                                Owner</label>
                                        </div>
                                        <div class="custom-control custom-checkbox mr-3">
                                            <input type="checkbox" class="custom-control-input" id="shareholderType"
                                                   name="groups[]" value="Shareholder"
                                                {{#ifContains "Shareholder" relation.groups}} checked {{/ifContains}}  >
                                            <label class="custom-control-label"
                                                   for="shareholderType">Shareholder</label>
                                        </div>
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="directorType"
                                                   name="groups[]" value="Director"
                                                {{#ifContains "Director" relation.groups}} checked {{/ifContains}}  >
                                            <label class="custom-control-label" for="directorType">Director</label>
                                        </div>
                                    </div>
                                </div>


                                {{#ifCond relation.type "!==" "natural"}}
                                    <div class="row mt-2" id="ownerShipField">
                                        <div class="col-md-6">
                                            <label for="ownerShip">Type of corporation*</label>
                                        </div>
                                        <div class="col-md-6">
                                            <select data-toggle="select2"
                                                    class="form-control w-100" id="ownerShip" name="ownerShip" disabled>
                                                <option id="corporate-option" value="corporate"
                                                    {{#ifEquals relation.ownerShip "corporate"}} selected{{/ifEquals}}>
                                                    Corporate
                                                </option>
                                                <option id="foundation-option" value="foundation"
                                                    {{#ifEquals relation.ownerShip "foundation"}} selected{{/ifEquals}}>
                                                    Foundation
                                                </option>
                                                <option id="trust-option" value="trust"
                                                    {{#ifEquals relation.ownerShip "trust"}} selected{{/ifEquals}}>Trust
                                                </option>
                                                <option id="limited-option" value="limited"
                                                    {{#ifEquals relation.ownerShip "limited"}} selected{{/ifEquals}}>
                                                    Limited Partnership
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                {{/ifCond}}
                                <hr>

                                {{#ifEquals relation.type "natural"}}
                                    {{>file-reviewer/relations/natural-form-component showPep="true" relation=relation}}
                                {{else}}
                                    {{#ifEquals relation.ownerShip "corporate"}}
                                        {{>file-reviewer/relations/corporate-form-component relation=relation}}
                                    {{/ifEquals}}

                                    {{#ifEquals relation.ownerShip "foundation"}}
                                        {{>file-reviewer/relations/foundation-form-component relation=relation}}
                                    {{/ifEquals}}

                                    {{#ifEquals relation.ownerShip "trust"}}
                                        {{>file-reviewer/relations/trust-form-component relation=relation}}
                                    {{/ifEquals}}

                                    {{#ifEquals relation.ownerShip "limited"}}
                                        {{>file-reviewer/relations/limited-partnership-form-component relation=relation}}
                                    {{/ifEquals}}
                                {{/ifEquals}}



                                <div id="shareholderAdditionalForm"
                                    class="{{#ifContains "Shareholder" relation.groups}}
                                        {{#if relation.details.isTridentClient}}
                                     hide-element {{else}}
                                     d-block {{/if}}
                                    {{else}} hide-element {{/ifContains}}" >
                                    <hr class="mt-2"/>
                                    <!-- ADITIONAL REQUIRED INFO FOR SHAREHOLDERS  -->
                                    <h4>Additional Shareholder Details</h4>
                                    <div class="row mt-4">
                                        <div class="col-2">
                                            <label for="additional-percentage">Share Percentage</label>
                                        </div>
                                        <div class="col-4 input-group mb-3">
                                            <input
                                                    name="additional[percentage]"
                                                    id="additional-percentage"
                                                    type="number"
                                                    min="0"
                                                    max="100"
                                                    class="form-control"
                                                    value="{{relation.percentage}}"
                                            />
                                            <div class="input-group-append">
                                                <span class="input-group-text">%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <hr class="mt-2"/>
                            </div>

                        </form>
                    </div>
                    <div class="row mt-4 justify-content-between">
                        <div class="col-2">
                            <a
                                    href="/masterclients/{{masterClientCode}}/incorporate-company/{{incorporationId}}?relations=true"
                                    class="btn btn-secondary width-lg waves-effect waves-light"
                            >
                                Back
                            </a>
                        </div>
                        <div class="col-4 d-flex justify-content-end">
                            <button
                                    id="submitRelationBtn"
                                    type="submit"
                                    form="reviewRelationForm"
                                    class="btn solid royal-blue px-4"
                                    data-mcc="{{masterClientCode}}"
                                    data-id="{{ incorporationId }}" data-type="{{ relation.type }}"
                                    data-index="{{ relation._id}}"
                            >
                                Save
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

{{>file-reviewer/upload-temp-modal}}

<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script type="text/javascript" src="/templates/addpartnerfilerow.precompiled.js"></script>
<script type="text/javascript" src="/views-js/incorporate-company/realtion-view.js"></script>
