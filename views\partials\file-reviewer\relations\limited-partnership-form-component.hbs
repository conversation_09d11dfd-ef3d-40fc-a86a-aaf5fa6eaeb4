<div id="limitedPartnershipForm">
    <!-- CORPORATE DETAILS -->
    <div id="limitedPartnershipDetails">
        <h4>Details</h4>
        <div class="row mt-2">
            <div class="col-6 d-flex justify-content-between">
                <label>Is this company already a TridentTrust client?*</label>
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" class="custom-control-input" id="limited-is-already-client-yes"
                           name="limited[isTridentClient]" required {{#if relation.details.isTridentClient }}
                           checked {{/if}} value="YES"/>
                    <label class="custom-control-label" for="limited-is-already-client-yes">Yes</label>
                </div>
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" class="custom-control-input" id="limited-is-already-client-no"
                           name="limited[isTridentClient]" {{#unless relation.details.isTridentClient }}
                           checked {{/unless}} value="NO"/>
                    <label class="custom-control-label" for="limited-is-already-client-no">No</label>
                </div>
            </div>
            <div class="col-6">
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-2">
                <label for="limitedPartnership-details-organization-name">Company Name*</label>
            </div>
            <div class="col-4">
                <input type="text" class="form-control"  id="limitedPartnership-details-organization-name"  name="details[organizationName]"
                       value="{{relation.details.organizationName}}" required/>
            </div>
            <div class="col-2">
                <label for="limitedPartnership-details-incorporation-number">Incorporation / Formation Number*</label>
            </div>
            <div class="col-4">
                <input id="limitedPartnership-details-incorporation-number" class="form-control" type="text" name="details[incorporationNumber]"
                       value="{{relation.details.incorporationNumber}}" required/>
            </div>
        </div>
        <div class="row mt-2 isTridentClient {{#if relation.details.isTridentClient}}
             hide-element {{/if}}">
            <div class="col-2">
                <label for="limitedPartnership-details-tax-residence">Tax Residence*</label>
            </div>
            <div class="col-4">
                {{>file-reviewer/shared/select-country selectId="details[taxResidence]" group="limitedPartnership" required="true"
                        value=relation.details.taxResidence}}
            </div>
            <div class="col-2">
                <label for="limitedPartnership-details-registration-number"
                >Business Registration Number (if applicable)*</label
                >
            </div>
            <div class="col-4">
                <input class="form-control" type="text"  id="limitedPartnership-details-registration-number"
                       name="details[businessNumber]" required
                       value="{{relation.details.businessNumber}}"/>
            </div>
        </div>
        <div class="row mt-2 isTridentClient {{#if relation.details.isTridentClient}}
             hide-element {{/if}}">
            <div class="col-2">
                <label for="limitedPartnership-details-incorporation-date">Date of Incorporation*</label>
            </div>
            <div class="col-4">
                <input class="form-control datepicker" type="date" id="limitedPartnership-details-incorporation-date"
                       name="details[incorporationDate]" required placeholder="mm/dd/yyyy"
                       value="{{#formatDate relation.details.incorporationDate "YYYY-MM-DD"}} {{/formatDate }}"/>
            </div>
            <div class="col-2">
                <label for="details[incorporationCountry]">Country of Incorporation*</label>
            </div>
            <div class="col-4">
                {{>file-reviewer/shared/select-country selectId="details[incorporationCountry]"
                        group="limitedPartnership" required="true"
                        value=relation.details.incorporationCountry}}
            </div>
        </div>


        <div class="isTridentClient {{#if relation.details.isTridentClient}}
             hide-element {{/if}}">
            <!-- DETAILS TABLE -->
            {{>file-reviewer/shared/relation-file-table tableId="detailsTable"  name="details" group="limitedPartnership"
                    files=(ternary newRelation relation.limitedFiles.details relation.details.files)
                    relationId=relation._id}}

            <!-- DETAILS PARTNER TABLE -->
            {{>file-reviewer/shared/certificate-partner-table group="limited"
                    partnerFiles=(ternary newRelation relation.limitedFiles.detailsPartner relation.detailsPartner.files)
                    relationId=relation._id}}
        </div>


    </div>


    <!-- PRINCIPAL ADDRESS DETAILS -->
    <div class="isTridentClient {{#if relation.details.isTridentClient}}
         hide-element {{/if}}">
        <hr class="mt-2"/>
        <div id="principalAddressDetails">
            <h4>Principal Address</h4>
            {{>file-reviewer/relations/sections/address-details-form group="limitedPartnership"
                    principalAddress=relation.principalAddress formType="principalAddress"}}
        </div>
        <hr class="mt-2" />

        <div class="row mt-2">
            <div class="col-6">
                <h4>Is mailing address the same as Principal address?*</h4>
            </div>
            <div class="col-6 d-flex justify-content-end">
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" class="custom-control-input" id="limited-is-same-address-yes"
                           name="limited[isSamePrincipalAddress]" required {{#if relation.isSamePrincipalAddress }} checked {{/if}} value="YES"/>
                    <label class="custom-control-label" for="limited-is-same-address-yes">Yes</label>
                </div>
                <div class="custom-control custom-radio custom-control-inline">
                    <input type="radio" class="custom-control-input" id="limited-is-same-address-no"
                           name="limited[isSamePrincipalAddress]" {{#unless relation.isSamePrincipalAddress }}
                           checked {{/unless}} value="NO"/>
                    <label class="custom-control-label" for="limited-is-same-address-no">No</label>
                </div>
            </div>
        </div>
        <br>

        <!-- MAILING ADDRESS DETAILS -->
        <div id="mailingAddressDetails {{#if relation.isSamePrincipalAddress}} hide-element {{/if}}">
            <h4>Mailing Address</h4>
            {{>file-reviewer/relations/sections/address-details-form group="limitedPartnership"
                    principalAddress=relation.mailingAddress formType="mailingAddress"}}
        </div>
        <hr class="mt-2" />

        <!-- LISTED COMPANY DETAILS -->
        <div class="listedCompanyDetails">
            {{>file-reviewer/relations/sections/listed-company-details-form group="limitedPartnership"
                    listedCompany=relation.listedCompanyDetails}}
        </div>
        <hr class="mt-2" />

        <!-- LIMITED COMPANY DETAILS -->
        <div class="limitedCompanyDetails">
            {{>file-reviewer/relations/sections/limited-company-details-form group="limitedPartnership" showTable="true"
                    limitedCompany=(ternary newRelation relation.limitedFiles.limitedCompany relation.limitedCompanyDetails)}}
        </div>
        <hr class="mt-2" />

        <!-- MUTUAL FUND DETAILS -->
        <div class="mutualFundDetails">
            {{>file-reviewer/relations/sections/mutual-fund-details-form group="limitedPartnership"
                    mutualFund=(ternary newRelation relation.limitedFiles.mutualFund relation.mutualFundDetails)
                    relationId=relation._id}}
        </div>
        <hr class="mt-2" />
    </div>

</div>
<script type="text/javascript" src="/views-js/partials/file-reviewer/relations/limited-partnership-form-component.js"></script>
