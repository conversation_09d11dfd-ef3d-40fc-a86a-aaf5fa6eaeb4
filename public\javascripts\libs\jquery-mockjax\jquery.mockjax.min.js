!function(t,r){"use strict";if("function"==typeof define&&define.amd&&define.amd.jQuery)define(["jquery"],function(e){return r(e,t)});else{if("object"!=typeof exports)return r(t.jQuery||t.$,t);module.exports=r}}(this,function(g,i){"use strict";var d=g.ajax,x=[],m=[],h=[],u=/=\?(&|$)/,l=(new Date).getTime(),n=500;function y(t,r){k.debug(t,["Checking mock data against request data",t,r]);var n=!0;if(g.isFunction(t))return!!t(r);if("string"==typeof r){if(g.isFunction(t.test))return t.test(r);if("object"!=typeof t)return t===r;r=function(e){var t,r,n,o,s={},a=String(e).split(/&/);for(t=0,r=a.length;t<r;++t){n=a[t];try{n=(n=decodeURIComponent(n.replace(/\+/g," "))).split(/=/)}catch(e){continue}s[n[0]]?(s[n[0]].splice||(o=s[n[0]],s[n[0]]=[],s[n[0]].push(o)),s[n[0]].push(n[1])):s[n[0]]=n[1]}return k.debug(null,["Getting query params from string",e,s]),s}(r)}return g.each(t,function(e){return void 0===r[e]?n=!1:void(n="object"==typeof r[e]&&null!==r[e]?(n&&g.isArray(r[e])&&(n=g.isArray(t[e])&&r[e].length===t[e].length),n&&y(t[e],r[e])):t[e]&&g.isFunction(t[e].test)?n&&t[e].test(r[e]):n&&t[e]===r[e])}),n}function a(e,t){return e[t]===g.mockjaxSettings[t]}function o(e){return"number"==typeof e&&0<=e}function c(e){if(g.isArray(e)&&2===e.length){var t=e[0],r=e[1];if(o(t)&&o(r))return Math.floor(Math.random()*(r-t))+t}else if(o(e))return e;return n}function T(r,n,t){k.debug(r,["Sending fake XHR request",r,n,t]);var o,s=(o=this,function(){return function(){this.status=r.status,this.statusText=r.statusText,this.readyState=1;function e(){var e,t;this.readyState=4,"json"===n.dataType&&"object"==typeof r.responseText?this.responseText=JSON.stringify(r.responseText):"xml"===n.dataType?"string"==typeof r.responseXML?(this.responseXML=function(e){void 0===i.DOMParser&&i.ActiveXObject&&(i.DOMParser=function(){},DOMParser.prototype.parseFromString=function(e){var t=new ActiveXObject("Microsoft.XMLDOM");return t.async="false",t.loadXML(e),t});try{var t=(new DOMParser).parseFromString(e,"text/xml");if(!g.isXMLDoc(t))throw new Error("Unable to parse XML");if(1===g("parsererror",t).length)throw new Error("Error: "+g(t).text());return t}catch(e){var r=void 0===e.name?e:e.name+": "+e.message;return void g(document).trigger("xmlParseError",[r])}}(r.responseXML),this.responseText=r.responseXML):this.responseXML=r.responseXML:"object"==typeof r.responseText&&null!==r.responseText?(r.contentType="application/json",this.responseText=JSON.stringify(r.responseText)):this.responseText=r.responseText,g.isArray(r.status)?(t=Math.floor(Math.random()*r.status.length),this.status=r.status[t]):"number"!=typeof r.status&&"string"!=typeof r.status||(this.status=r.status),"string"==typeof r.statusText&&(this.statusText=r.statusText),e=this.onload||this.onreadystatechange,g.isFunction(e)?(r.isTimeout&&(this.status=-1),e.call(this,r.isTimeout?"timeout":void 0)):r.isTimeout&&(this.status=-1)}if(g.isFunction(r.response)){if(2===r.response.length)return void r.response(t,function(){e.call(o)});r.response(t)}e.call(o)}.apply(o)});r.proxy?(k.info(r,["Retrieving proxy file: "+r.proxy,r]),d({global:!1,url:r.proxy,type:r.proxyType,data:r.data,async:n.async,dataType:"script"===n.dataType?"text/plain":n.dataType,complete:function(e){r.responseXML=r.responseText=e.responseText,a(r,"status")&&(r.status=e.status),a(r,"statusText")&&(r.statusText=e.statusText),!1===n.async?s():this.responseTimer=setTimeout(s,c(r.responseTime))}})):!1===n.async?s():this.responseTimer=setTimeout(s,c(r.responseTime))}function j(e,t,r){var n;if("GET"===(n=e).type.toUpperCase()?u.test(n.url)||(n.url+=(/\?/.test(n.url)?"&":"?")+(n.jsonp||"callback")+"=?"):n.data&&u.test(n.data)||(n.data=(n.data?n.data+"&":"")+(n.jsonp||"callback")+"=?"),e.dataType="json",e.data&&u.test(e.data)||u.test(e.url)){!function(e,t,r){var n=r&&r.context||e,o="string"==typeof e.jsonpCallback&&e.jsonpCallback||"jsonp"+l++;e.data&&(e.data=(e.data+"").replace(u,"="+o+"$1"));e.url=e.url.replace(u,"="+o+"$1"),i[o]=i[o]||function(){f(e,n,t),v(e,n),i[o]=void 0;try{delete i[o]}catch(e){}},e.jsonpCallback=o}(e,t,r);var o=/^(\w+:)?\/\/([^\/?#]+)/.exec(e.url),s=o&&(o[1]&&o[1]!==location.protocol||o[2]!==location.host);if(e.dataType="script","GET"===e.type.toUpperCase()&&s){var a=function(t,r,e){k.debug(r,["Performing JSONP request",r,t,e]);var n=e&&e.context||t,o=g.Deferred?new g.Deferred:null;if(r.response&&g.isFunction(r.response))r.response(e);else if("object"==typeof r.responseText)g.globalEval("("+JSON.stringify(r.responseText)+")");else{if(r.proxy)return k.info(r,["Performing JSONP proxy request: "+r.proxy,r]),d({global:!1,url:r.proxy,type:r.proxyType,data:r.data,dataType:"script"===t.dataType?"text/plain":t.dataType,complete:function(e){g.globalEval("("+e.responseText+")"),p(t,r,n,o)}}),o;g.globalEval("("+("string"==typeof r.responseText?'"'+r.responseText+'"':r.responseText)+")")}return p(t,r,n,o),o}(e,t,r);return a||!0}}return null}function p(e,t,r,n){var o;setTimeout(function(){if(f(e,r,t),v(e,r),n){try{o=g.parseJSON(t.responseText)}catch(e){}n.resolveWith(r,[o||t.responseText]),k.log(t,["JSONP mock call complete",t,n])}},c(t.responseTime))}function f(e,t,r){e.success&&e.success.call(t,r.responseText||"","success",{}),e.global&&(e.context?g(e.context):g.event).trigger("ajaxSuccess",[{},e])}function v(e,t){e.complete&&e.complete.call(t,{statusText:"success",status:200},"success"),e.global&&(e.context?g(e.context):g.event).trigger("ajaxComplete",[{},e]),e.global&&!--g.active&&g.event.trigger("ajaxStop")}g.extend({ajax:function e(t,n){var r,o,s,a;k.debug(null,["Ajax call intercepted",t,n]),"object"==typeof t?(n=t,t=void 0):(n=n||{}).url=t||n.url,(o=g.ajaxSetup({},n)).type=o.method=o.method||o.type,a=function(e,t){var r=n[e.toLowerCase()];return function(){g.isFunction(r)&&r.apply(this,[].slice.call(arguments)),t["onAfter"+e]()}};for(var i=0;i<x.length;i++){var u=g.mockjaxSettings.matchInRegistrationOrder?i:x.length-1-i,l=x[u];if(l){if(s=function(e,r){if(g.isFunction(e))return e(r);var t,n=e.namespace||void 0===e.namespace&&g.mockjaxSettings.namespace;if(g.isFunction(e.url.test)){if(n&&(n=n.replace(/(\/+)$/,""),t=e.url.source.replace(/^(\^+)/,"").replace(/^/,"^("+n+")?/?"),e.url=new RegExp(t)),!e.url.test(r.url))return null}else{var o=e.url;n&&(o=[n.replace(/(\/+)$/,""),e.url.replace(/^(\/+)/,"")].join("/"));var s=o.indexOf("*");if(o!==r.url&&-1===s||!new RegExp(o.replace(/[-[\]{}()+?.,\\^$|#\s]/g,"\\$&").replace(/\*/g,".+")).test(r.url))return null}if(e.requestHeaders){if(void 0===r.headers)return null;var a=!1;if(g.each(e.requestHeaders,function(e,t){if(r.headers[e]!==t)return!(a=!0)}),a)return null}return!(!e.data||r.data&&y(e.data,r.data))||e&&e.type&&e.type.toLowerCase()!==r.type.toLowerCase()?null:e}(l,o)){if(g.mockjaxSettings.retainAjaxCalls&&m.push(o),k.info(s,["MOCK "+o.type.toUpperCase()+": "+o.url,g.ajaxSetup({},o)]),301!==s.status&&302!==s.status||"GET"!==o.type.toUpperCase()&&"HEAD"!==o.type.toUpperCase()||!s.headers.Location)return o.dataType&&"JSONP"===o.dataType.toUpperCase()&&(r=j(o,s,n))||(n.crossDomain=!1,s.cache=o.cache,s.timeout=o.timeout,s.global=o.global,s.isTimeout&&(1<s.responseTime?n.timeout=s.responseTime-1:(s.responseTime=2,n.timeout=1)),g.isFunction(s.onAfterSuccess)&&(n.success=a("Success",s)),g.isFunction(s.onAfterError)&&(n.error=a("Error",s)),g.isFunction(s.onAfterComplete)&&(n.complete=a("Complete",s)),function(e,t){if(e.url instanceof RegExp&&e.hasOwnProperty("urlParams")){var r=e.url.exec(t.url);if(1!==r.length){r.shift();for(var n=0,o=r.length,s=e.urlParams.length,a=Math.min(o,s),i={};n<a;n++){var u=e.urlParams[n];i[u]=r[n]}t.urlParams=i}}}(s,n),function(o,s,a,i){r=d.call(g,g.extend(!0,{},a,{xhr:function(){return t=o,r=s,e=a,n=i,k.debug(t,["Creating new mock XHR object",t,r,e,n]),void 0===(t=g.extend(!0,{},g.mockjaxSettings,t)).headers&&(t.headers={}),void 0===r.headers&&(r.headers={}),t.contentType&&(t.headers["content-type"]=t.contentType),{status:t.status,statusText:t.statusText,readyState:1,open:function(){},send:function(){n.fired=!0,T.call(this,t,r,e)},abort:function(){clearTimeout(this.responseTimer)},setRequestHeader:function(e,t){r.headers[e]=t},getResponseHeader:function(e){return t.headers&&t.headers[e]?t.headers[e]:"last-modified"===e.toLowerCase()?t.lastModified||(new Date).toString():"etag"===e.toLowerCase()?t.etag||"":"content-type"===e.toLowerCase()?t.contentType||"text/plain":void 0},getAllResponseHeaders:function(){var r="";return t.contentType&&(t.headers["content-type"]=t.contentType),g.each(t.headers,function(e,t){r+=e+": "+t+"\n"}),r}};var t,r,e,n}}))}(s,o,n,l)),r;k.debug("Doing mock redirect to",s.headers.Location,o.type);for(var c={},p=Object.keys(n),f=0;f<p.length;f++)c[p[f]]=n[p[f]];return c.url=s.headers.Location,c.headers={Referer:n.url},e(c)}k.debug(l,["Mock does not match request",t,o])}}if(k.log(null,["No mock matched to request",t,n]),g.mockjaxSettings.retainAjaxCalls&&h.push(n),!0===g.mockjaxSettings.throwUnmocked)throw new Error("AJAX not mocked: "+n.url);return k.log("Real ajax call to",n.url),d.apply(g,[n])}});var k={_log:function(e,t,r){var n=g.mockjaxSettings.logging;if(e&&void 0!==e.logging&&(n=e.logging),r=0===r?r:r||s.LOG,t=t.splice?t:[t],!(!1===n||n<r))return g.mockjaxSettings.log?g.mockjaxSettings.log(e,t[1]||t[0]):g.mockjaxSettings.logger&&g.mockjaxSettings.logger[g.mockjaxSettings.logLevelMethods[r]]?g.mockjaxSettings.logger[g.mockjaxSettings.logLevelMethods[r]].apply(g.mockjaxSettings.logger,t):void 0},debug:function(e,t){return k._log(e,t,s.DEBUG)},log:function(e,t){return k._log(e,t,s.LOG)},info:function(e,t){return k._log(e,t,s.INFO)},warn:function(e,t){return k._log(e,t,s.WARN)},error:function(e,t){return k._log(e,t,s.ERROR)}},s={DEBUG:4,LOG:3,INFO:2,WARN:1,ERROR:0};return g.mockjaxSettings={log:null,logger:i.console,logging:2,logLevelMethods:["error","warn","info","log","debug"],matchInRegistrationOrder:!0,namespace:null,status:200,statusText:"OK",responseTime:n,isTimeout:!1,throwUnmocked:!1,retainAjaxCalls:!0,contentType:"text/plain",response:"",responseText:"",responseXML:"",proxy:"",proxyType:"GET",lastModified:null,etag:"",headers:{etag:"IJF@H#@923uf8023hFO@I#H#","content-type":"text/plain"}},g.mockjax=function(e){if(g.isArray(e))return g.map(e,function(e){return g.mockjax(e)});var t=x.length;return x[t]=e,k.log(e,["Created new mock handler",e]),t},g.mockjax._logger=k,g.mockjax.clear=function(e){"string"==typeof e||e instanceof RegExp?x=function(t){for(var e,r=[],n=t instanceof RegExp?function(e){return t.test(e)}:function(e){return t===e},o=0,s=x.length;o<s;o++)n((e=x[o]).url)?k.log(e,["Clearing mock: "+(e&&e.url),e]):r.push(e);return r}(e):e||0===e?(k.log(x[e],["Clearing mock: "+(x[e]&&x[e].url),x[e]]),x[e]=null):(k.log(null,"Clearing all mocks"),x=[]),m=[],h=[]},g.mockjax.clearRetainedAjaxCalls=function(){m=[],h=[],k.debug(null,"Cleared retained ajax calls")},g.mockjax.handler=function(e){if(1===arguments.length)return x[e]},g.mockjax.handlers=function(){return x},g.mockjax.mockedAjaxCalls=function(){return m},g.mockjax.unfiredHandlers=function(){for(var e=[],t=0,r=x.length;t<r;t++){var n=x[t];null===n||n.fired||e.push(n)}return e},g.mockjax.unmockedAjaxCalls=function(){return h},g.mockjax});
