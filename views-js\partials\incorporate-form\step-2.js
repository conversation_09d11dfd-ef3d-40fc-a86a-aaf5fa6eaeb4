
$(document).on('show.bs.modal', '.modal', function () {
    var zIndex = 1040 + (10 * $('.modal:visible').length);
    $(this).css('z-index', zIndex);
    setTimeout(function () {
        $('.modal-backdrop').not('.modal-stack').css('z-index', zIndex - 1).addClass('modal-stack');
    }, 0);
});

function reloadRelationTable(incorporationId, mcc, relations) {
    $('#relationsTableDiv').html('');
    if (relations && relations.length > 0) {
        $('#missingRelationRowStep2').hide(200);
    }

    let template = Handlebars.templates.step2relationstable;
    let html = template({relations: relations, mcc: mcc, incorporationId: incorporationId});
    $('#relationsTableDiv').html(html);
    
}