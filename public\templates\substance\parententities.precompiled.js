(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['parententities'] = template({"1":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, helper, alias1=container.lambda, alias2=container.escapeExpression, alias3=depth0 != null ? depth0 : (container.nullContext || {}), alias4=container.hooks.helperMissing, alias5="function", lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <tr id=\""
    + alias2(alias1((depths[1] != null ? lookupProperty(depths[1],"type") : depths[1]), depth0))
    + "-parent-table-row-"
    + alias2(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":2,"column":41},"end":{"line":2,"column":48}}}) : helper)))
    + "\" class=\""
    + alias2(alias1((depths[1] != null ? lookupProperty(depths[1],"type") : depths[1]), depth0))
    + "-parent-row\">\r\n        <td> "
    + alias2(((helper = (helper = lookupProperty(helpers,"parentName") || (depth0 != null ? lookupProperty(depth0,"parentName") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"parentName","hash":{},"data":data,"loc":{"start":{"line":3,"column":13},"end":{"line":3,"column":27}}}) : helper)))
    + " </td>\r\n        <td> "
    + alias2(((helper = (helper = lookupProperty(helpers,"alternativeName") || (depth0 != null ? lookupProperty(depth0,"alternativeName") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"alternativeName","hash":{},"data":data,"loc":{"start":{"line":4,"column":13},"end":{"line":4,"column":32}}}) : helper)))
    + " </td>\r\n        <td>"
    + ((stack1 = lookupProperty(helpers,"if").call(alias3,(depth0 != null ? lookupProperty(depth0,"jurisdictionName") : depth0),{"name":"if","hash":{},"fn":container.program(2, data, 0, blockParams, depths),"inverse":container.program(4, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":5,"column":12},"end":{"line":5,"column":106}}})) != null ? stack1 : "")
    + " </td>\r\n        <td> "
    + alias2(((helper = (helper = lookupProperty(helpers,"incorporationNumber") || (depth0 != null ? lookupProperty(depth0,"incorporationNumber") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"incorporationNumber","hash":{},"data":data,"loc":{"start":{"line":6,"column":13},"end":{"line":6,"column":36}}}) : helper)))
    + " </td>\r\n        <td> "
    + alias2(((helper = (helper = lookupProperty(helpers,"TIN") || (depth0 != null ? lookupProperty(depth0,"TIN") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"TIN","hash":{},"data":data,"loc":{"start":{"line":7,"column":13},"end":{"line":7,"column":20}}}) : helper)))
    + " </td>\r\n        <td class=\"justify-content-center d-flex d-flex-inline\">\r\n            <button type=\"button\" class=\"btn btn-sm royal-blue solid mr-1\" data-parent-type=\""
    + alias2(alias1((depths[1] != null ? lookupProperty(depths[1],"type") : depths[1]), depth0))
    + "\"\r\n                data-id=\""
    + alias2(alias1((depths[1] != null ? lookupProperty(depths[1],"entryId") : depths[1]), depth0))
    + "\" data-parent-id=\""
    + alias2(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":10,"column":57},"end":{"line":10,"column":64}}}) : helper)))
    + "\" data-toggle=\"modal\" data-target=\"#entityParentModal\">\r\n                <i class=\"fa fa-pencil\"></i>\r\n            </button>\r\n            <button type=\"button\" class=\"btn btn-sm btn-danger deleteParentEntity\"\r\n                    data-type=\""
    + alias2(alias1((depths[1] != null ? lookupProperty(depths[1],"type") : depths[1]), depth0))
    + "\" data-id=\""
    + alias2(alias1((depths[1] != null ? lookupProperty(depths[1],"entryId") : depths[1]), depth0))
    + "\" data-parent-id=\""
    + alias2(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias4),(typeof helper === alias5 ? helper.call(alias3,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":14,"column":85},"end":{"line":14,"column":92}}}) : helper)))
    + "\">\r\n                <i class=\"fa fa-times\"></i>\r\n            </button>\r\n        </td>\r\n    </tr>\r\n";
},"2":function(container,depth0,helpers,partials,data) {
    var helper, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return " "
    + container.escapeExpression(((helper = (helper = lookupProperty(helpers,"jurisdictionName") || (depth0 != null ? lookupProperty(depth0,"jurisdictionName") : depth0)) != null ? helper : container.hooks.helperMissing),(typeof helper === "function" ? helper.call(depth0 != null ? depth0 : (container.nullContext || {}),{"name":"jurisdictionName","hash":{},"data":data,"loc":{"start":{"line":5,"column":37},"end":{"line":5,"column":57}}}) : helper)))
    + " ";
},"4":function(container,depth0,helpers,partials,data) {
    var lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return " "
    + container.escapeExpression((lookupProperty(helpers,"getCountryName")||(depth0 && lookupProperty(depth0,"getCountryName"))||container.hooks.helperMissing).call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"jurisdiction") : depth0),{"name":"getCountryName","hash":{},"data":data,"loc":{"start":{"line":5,"column":67},"end":{"line":5,"column":98}}}))
    + " ";
},"6":function(container,depth0,helpers,partials,data) {
    var helper, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "        <tr>\r\n            <td colspan=\"6\">\r\n                No "
    + container.escapeExpression(((helper = (helper = lookupProperty(helpers,"type") || (depth0 != null ? lookupProperty(depth0,"type") : depth0)) != null ? helper : container.hooks.helperMissing),(typeof helper === "function" ? helper.call(depth0 != null ? depth0 : (container.nullContext || {}),{"name":"type","hash":{},"data":data,"loc":{"start":{"line":22,"column":19},"end":{"line":22,"column":27}}}) : helper)))
    + " entity parents found\r\n            </td>\r\n        </tr>\r\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"parentEntities") : depth0),{"name":"each","hash":{},"fn":container.program(1, data, 0, blockParams, depths),"inverse":container.program(6, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":1,"column":0},"end":{"line":25,"column":9}}})) != null ? stack1 : "")
    + "\r\n\r\n";
},"useData":true,"useDepths":true});
})();