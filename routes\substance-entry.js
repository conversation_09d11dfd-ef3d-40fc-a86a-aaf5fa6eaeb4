const express = require('express');
const router = express.Router();
const uploadController = require('../controllers/uploadController')

// Require controller modules.
const substance_controller = require('../controllers/substanceController');

router.get('/:id/uploaded-files', substance_controller.ensureAuthenticated, substance_controller.getUploadedFiles); // jquery / ajax
router.get('/:id/managers', substance_controller.ensureAuthenticated, substance_controller.getManagers); // jquery / ajax
router.get('/:id/premises', substance_controller.ensureAuthenticated, substance_controller.getPremises); // jquery / ajax

router.get('/:id/:business/premises/add',substance_controller.ensureAuthenticated, substance_controller.addPremises);
router.get('/:id/:business/managers/add',substance_controller.ensureAuthenticated, substance_controller.addManager);
router.post('/:id/:business/premises/add',substance_controller.ensureAuthenticated, substance_controller.savePremises); 
router.post('/:id/:business/managers/add',substance_controller.ensureAuthenticated, substance_controller.saveManager); 
router.get('/:id/:business/premises/:premisesId/edit',substance_controller.ensureAuthenticated, substance_controller.editPremises);
router.get('/:id/:business/managers/:managerId/edit',substance_controller.ensureAuthenticated, substance_controller.editManager);
router.post('/:id/:business/premises/:premisesId/edit',substance_controller.ensureAuthenticated, substance_controller.savePremises);
router.post('/:id/:business/managers/:managerId/edit',substance_controller.ensureAuthenticated, substance_controller.saveManager);

router.get('/:id/financial-period', substance_controller.ensureAuthenticated, substance_controller.entryFinancialPeriod);
router.post('/:id/financial-period', substance_controller.ensureAuthenticated, substance_controller.saveFinancialPeriod);

router.get('/:id/entity-details', substance_controller.ensureAuthenticated, substance_controller.entryEntityDetails);
router.post('/:id/entity-details', substance_controller.ensureAuthenticated, substance_controller.saveEntityDetails);
router.get('/:id/tax-residency', substance_controller.ensureAuthenticated, substance_controller.entryTaxResidency);
router.post('/:id/tax-residency', substance_controller.ensureAuthenticated, substance_controller.saveTaxResidency);
router.post('/:id/relevant-activities', substance_controller.ensureAuthenticated, substance_controller.saveRelevantActivities);
router.get('/:id/relevant-activities', substance_controller.ensureAuthenticated, substance_controller.entryRelevantActivities);
router.get('/:id/banking-business', substance_controller.ensureAuthenticated, substance_controller.entryBankingBusiness);
router.post('/:id/banking-business', substance_controller.ensureAuthenticated, substance_controller.saveBankingBusiness);
router.get('/:id/insurance-business', substance_controller.ensureAuthenticated, substance_controller.entryInsuranceBusiness);
router.post('/:id/insurance-business', substance_controller.ensureAuthenticated, substance_controller.saveInsuranceBusiness);
router.get('/:id/fund-management-business', substance_controller.ensureAuthenticated, substance_controller.entryFundManagementBusiness);
router.post('/:id/fund-management-business', substance_controller.ensureAuthenticated, substance_controller.saveFundManagementBusiness);
router.get('/:id/finance-leasing-business', substance_controller.ensureAuthenticated, substance_controller.entryFinanceLeasingBusiness);
router.post('/:id/finance-leasing-business', substance_controller.ensureAuthenticated, substance_controller.saveFinanceLeasingBusiness);
router.get('/:id/headquarters-business', substance_controller.ensureAuthenticated, substance_controller.entryHeadquartersBusiness);
router.post('/:id/headquarters-business', substance_controller.ensureAuthenticated, substance_controller.saveHeadquartersBusiness);
router.get('/:id/shipping-business', substance_controller.ensureAuthenticated, substance_controller.entryShippingBusiness);
router.post('/:id/shipping-business', substance_controller.ensureAuthenticated, substance_controller.saveShippingBusiness);
router.get('/:id/intellectual-property-business', substance_controller.ensureAuthenticated, substance_controller.entryIntellectualPropertyBusiness);
router.post('/:id/intellectual-property-business', substance_controller.ensureAuthenticated, substance_controller.saveIntellectualPropertyBusiness);
router.get('/:id/holding-business', substance_controller.ensureAuthenticated, substance_controller.entryHoldingBusiness);
router.post('/:id/holding-business', substance_controller.ensureAuthenticated, substance_controller.saveHoldingBusiness);
router.get('/:id/service-centre-business', substance_controller.ensureAuthenticated, substance_controller.entryServiceCentreBusiness);
router.post('/:id/service-centre-business', substance_controller.ensureAuthenticated, substance_controller.saveServiceCentreBusiness);
router.get('/:id/supporting-details', substance_controller.ensureAuthenticated, substance_controller.entrySupportingDetails);
router.post('/:id/supporting-details', substance_controller.ensureAuthenticated, substance_controller.saveSupportingDetails);
router.get('/:id/confirmation', substance_controller.ensureAuthenticated, substance_controller.entryConfirmation);
router.post('/:id/confirmation', substance_controller.ensureAuthenticated, substance_controller.saveConfirmation);
router.get('/:id/payment', substance_controller.ensureAuthenticated, substance_controller.entryPayment);
router.post('/:id/payment', substance_controller.ensureAuthenticated, substance_controller.savePayment);

router.post('/:id/upload-document', substance_controller.ensureAuthenticated, uploadController.uploadErrorHandler(uploadController.uploadFile.fields([{ name: 'SubmitEvidence', maxCount:5 }])), uploadController.saveUpload);
router.delete('/:id/manager', substance_controller.ensureAuthenticated, substance_controller.deleteManager); // jquery / ajax
router.delete('/:id/premises', substance_controller.ensureAuthenticated, substance_controller.deletePremises); // jquery / ajax
router.delete('/:id/uploaded-file', substance_controller.ensureAuthenticated, uploadController.deleteFile); // jquery / ajax
router.delete('/:id/tax-residency/evidences', substance_controller.ensureAuthenticated, substance_controller.deleteTaxResidencyEvidences); // jquery / ajax


// Entity Parents
router.get('/:id/parent-entities/', substance_controller.ensureAuthenticated, substance_controller.getParentEntities); // jquery / ajax
router.post('/:id/parent-entities/', substance_controller.ensureAuthenticated, substance_controller.createParentEntity); // jquery / ajax
router.put('/:id/parent-entities/:parentEntityId', substance_controller.ensureAuthenticated, substance_controller.editParentEntity); // jquery / ajax
router.delete('/:id/parent-entities/:type/:parentEntityId', substance_controller.ensureAuthenticated, substance_controller.deleteParentEntity); // jquery / ajax

// Activity business details
router.get('/:id/activity-business/', substance_controller.ensureAuthenticated, substance_controller.getActivityBusinessByType); // jquery / ajax

// Board meeting details
router.post('/:id/board-meetings/', substance_controller.ensureAuthenticated, substance_controller.saveBoardMeeting); // jquery / ajax
router.put('/:id/board-meetings/:boardMeetingId', substance_controller.ensureAuthenticated, substance_controller.saveBoardMeeting); // jquery / ajax
router.delete('/:id/board-meetings/:type/:boardMeetingId', substance_controller.ensureAuthenticated, substance_controller.deleteBoardMeeting); // jquery / ajax

// Employees details
router.post('/:id/employees/', substance_controller.ensureAuthenticated, substance_controller.saveEmployee); // jquery / ajax
router.put('/:id/employees/:employeeId', substance_controller.ensureAuthenticated, substance_controller.saveEmployee); // jquery / ajax
router.delete('/:id/employees/:type/:employeeId', substance_controller.ensureAuthenticated, substance_controller.deleteEmployee); // jquery / ajax

// Outsourcing providers details
router.post('/:id/outsourcing-providers/', substance_controller.ensureAuthenticated, substance_controller.saveOutsourcingProvider); // jquery / ajax
router.put('/:id/outsourcing-providers/:outsourcingProviderId', substance_controller.ensureAuthenticated, substance_controller.saveOutsourcingProvider); // jquery / ajax
router.delete('/:id/outsourcing-providers/:type/:outsourcingProviderId', substance_controller.ensureAuthenticated, substance_controller.deleteOutsourcingProvider); // jquery / ajax

module.exports = router;
