{{#if files}}
    <div class="table-responsive">
        <table class="table table-striped mb-0">
            <thead>
            <tr>
                <th class="header-60-percent">Uploaded Files</th>
                <th class="header-20-percent">Download</th>
                <th class="header-40-percent">Delete</th>
            </tr>
            </thead>
            <tbody>
            {{#each files}}
                <tr>
                    <td>{{originalName}}</td>
                    <td class="pl-2 py-1 text-center align-middle">
                        <a target="_blank" class="btn btn btn-xs solid royal-blue"
                           href="/masterclients/{{../masterClientCode}}/incorporate-company/{{../incorporationId}}/files/{{fileId}}/download?relation={{../id}}&fileType={{../fileTypeId}}&fileGroup={{../group}}&row={{../row}}"
                        >
                        Download
                        </a>
                    </td>
                    <td>
                        <button class="demo-delete-row btn btn-danger btn-xs btn-icon deleteRelationFile"
                                data-mcc="{{../masterClientCode}}" data-incorporation-id="{{../incorporationId}}" data-id="{{../id}}" data-group="{{../group}}" data-field="{{../fileTypeId}}" data-field-id="{{fileId}}" data-blobName="{{ blobName }}">
                            <i class="fa fa-times"></i>
                        </button>
                    </td>
                </tr>
            {{/each}}
            </tbody>
        </table>
    </div>
{{/if}}
