
exports.validate = function(entry) {
    let errors = [];

    if(entry.tax_residency.resident_in_BVI === undefined){
        errors.push({msg: "Please make a selection", field: "ResidencyOutsideBVIYesOrNo"})
    }

    if (entry.tax_residency.resident_in_BVI === false ){
        if (entry.tax_residency.entity_jurisdiction === undefined || entry.tax_residency.entity_jurisdiction.trim().length === 0)
        {
            errors.push({msg: "Please provide the entity’s jurisdiction of tax residency", field: "EntityJurisdiction"})
        }

        if (entry.tax_residency.foreign_tax_id_number === undefined || entry.tax_residency.foreign_tax_id_number.trim().length === 0) {
            errors.push({ msg: "Please provide the Foreign Tax ID Number", field: "foreign_tax_id_number" })
        }

        if(parseFloat(entry.version)< 5){
 

            if (entry.tax_residency.have_parent_entity === undefined) {
                errors.push({ msg: "Please indicate whether the entity has a parent entity", field: "haveParentEntityYesOrNo" })
            }

            if (entry.tax_residency.have_parent_entity === true) {
                if (entry.tax_residency.parent_entity_name === "") {
                    errors.push({ msg: "Please provide the parent entity name", field: "parent_entity_name" })
                }

                if (entry.tax_residency.parent_entity_jurisdiction === "") {
                    errors.push({ msg: "Please provide the parent entity's jurisdiction of formation", field: "parent_entity_jurisdiction" })
                }

                if (entry.tax_residency.parent_entity_incorporation_number === "") {
                    errors.push({ msg: "Please provide the parent entity's incorporation/ formation number", field: "parent_entity_incorporation_number" })
                }
            }
        }

        if (!entry.tax_residency.evidence_type || entry.tax_residency.evidence_type === '')
        {
            errors.push({msg: "Please select the type of evidence you want to upload", field: "evidence_type"})
        }


        if (entry.tax_residency.evidence_type === "non residency" &&
          (entry.tax_residency.evidence_non_residency === undefined || entry.tax_residency.evidence_non_residency?.length === 0))
        {
            errors.push({msg: "Please provide evidence of non residency", field: "SubmitEvidenceNonResidency"})
        }
        if (entry.tax_residency.evidence_type === "provisional treatment" &&
          (entry.tax_residency.evidence_provisional_treatment_non_residency === undefined || entry.tax_residency.evidence_provisional_treatment_non_residency?.length === 0))
        {
            errors.push({msg: "Please provide evidence of provisional treatment", field: "SubmitEvidenceNonResidency"})
        }


    }




    return errors;
}
