
<tr>
    <td>{{file.external}}</td>
    <td class="text-center">
        <button
                type="button"
                class="btn solid royal-blue {{#if file.present}}present-file-btn{{/if}}"
                data-toggle="modal"
                data-target="#upload-temp-modal"
                id="btn-detailsPartner-{{row}}"
                data-incorporation-id="{{incorporationId}}"
                data-mcc="{{mcc}}"
                data-id="{{ file.id }}"
                data-review-id="{{ reviewId}}"
                data-relation-id="{{ relationId}}"
                data-row="{{row}}"
                data-file-type="{{group}}"
                data-field="{{file.internal}}"
                data-file-group="detailsPartner"
        >
            {{#if file.present}}Modify{{else}}Upload{{/if}}
        </button>
    </td>
    <td>
            <textarea
                    class="form-control"
                    name="detailsPartner[files][{{row}}][explanation]"
                    id="{{group}}-detailsPartner-{{row}}-explanation-file"
                    rows="1"
                    data-value="{{file.explanation}}"
            ></textarea>
        <label for="{{group}}-detailsPartner-{{row}}-explanation-file" hidden></label>
    </td>
</tr>