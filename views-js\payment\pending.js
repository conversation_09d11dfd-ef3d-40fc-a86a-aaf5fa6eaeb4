let totalAmount = 0;
let totalIncorporationAmount = 0;
// let totalFinancialAmount = 0;
$('#btnSubmit').hide();

$(document).on('click', 'input[name="selected_incorporation"]', function () {
    calculateIncorporation(this, $(this).attr('data-index'), 'incorporation')
})

function calculateIncorporation(field, idx, type) {
    let checked = $(`#${type}-${idx}`).is(':checked');
    if (checked) {
        totalIncorporationAmount += parseFloat($(`#amount-${type}-${idx}`).text());
    } else {
        totalIncorporationAmount -= parseFloat($(`#amount-${type}-${idx}`).text());
    }
    if (totalIncorporationAmount > 0) {
        $('#btnSubmit').show();
    } else {
        $('#btnSubmit').hide();
    }
    $('#totalIncorporationAmount').text(totalIncorporationAmount);
}
