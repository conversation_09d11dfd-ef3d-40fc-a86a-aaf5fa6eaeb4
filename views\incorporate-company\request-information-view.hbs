<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <div class="card-title">
                        <h3>
                            Company incorporation:<span class="font-weight-bold pl-1">{{ incorporationName }}</span>
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12">
                                <h5>
                                    Please provide the information requested and upload the required files.
                                </h5>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="table-responsive">
                                    <table id="requestTable" class="table  w-100 table-striped">
                                        <thead>
                                        <tr>
                                            <th class="header-15-percent">Requested At</th>
                                            <th class="header-45-percent">Question</th>
                                            <th class="header-40-percent">Answer</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        {{#each requestInformation}}
                                            <tr>
                                                <td>
                                                    {{formatDate requestedAt "YYYY-MM-DD HH:mm"}}
                                                </td>
                                                <td>
                                                    {{ comment }}
                                                </td>
                                                <td>
                                                    {{ answer }}
                                                </td>
                                            </tr>
                                        {{else}}
                                            <tr>
                                                <td colspan="2" class="text-center font-italic">
                                                    There are no questions
                                                </td>
                                            </tr>
                                        {{/each}}

                                        </tbody>
                                    </table>
                                </div>
                            </div>

                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <h5>Check required Files</h5>
                                <div class="table-responsive">
                                    <table id="requestTableFiles" class="table w-100 table-striped">
                                        <thead>
                                        <tr>
                                            <th class="header-15-percent">Requested At</th>
                                            <th class="header-70-percent">File Name</th>
                                            <th class="text-center header-10-percent">Download</th>
                                        </tr>
                                        </thead>
                                        <tbody>

                                        {{#each requestedFiles}}
                                            <tr>
                                                <td>
                                                    {{formatDate requestedAt "YYYY-MM-DD HH:mm"}}
                                                </td>
                                                <td>{{originalName}} </td>
                                                <td class="text-center">
                                                    <a class="btn solid royal-blue"
                                                       href="/masterclients/{{../masterClientCode}}/incorporate-company/{{../incorporationId}}/files/{{fileId}}/download?requestDataId={{requestId}}"
                                                       target="_blank">
                                                        Download
                                                    </a>
                                                </td>
                                            </tr>
                                        {{else}}
                                            <tr>
                                                <td colspan="3" class="text-center font-italic">
                                                    There are no required files to provide
                                                </td>
                                            </tr>
                                        {{/each}}
                                        </tbody>
                                    </table>
                                </div>

                            </div>
                        </div>
                        <br>

                        <div class="row">
                            <div class="col-md-12">
                                <h5>Provided Files</h5>
                                <div class="table-responsive">
                                    <table id="providedTableFiles" class="table w-100 table-striped">
                                        <thead>
                                        <tr>
                                            <th class="header-15-percent">Provided At</th>
                                            <th class="header-70-percent">File Name</th>
                                            <th class="text-center header-10-percent">Download</th>
                                        </tr>
                                        </thead>
                                        <tbody>

                                        {{#each providedFiles}}
                                            <tr>
                                                <td>
                                                    {{formatDate returnedAt "YYYY-MM-DD HH:mm"}}
                                                </td>
                                                <td>{{originalName}} </td>
                                                <td class="text-center">
                                                    <a class="btn solid royal-blue"
                                                       href="/masterclients/{{../masterClientCode}}/incorporate-company/{{../incorporationId}}/files/{{fileId}}/download?informationId={{fileTypeId}}"
                                                    target="_blank">
                                                    Download
                                                    </a>
                                                </td>
                                            </tr>
                                        {{else}}
                                            <tr>
                                                <td colspan="3" class="text-center font-italic">
                                                    There are no provided files
                                                </td>
                                            </tr>
                                        {{/each}}
                                        </tbody>
                                    </table>
                                </div>

                            </div>
                        </div>
                        <br>
                        <form method="POST" id="submitForm" class='enquiry' autocomplete="off">
                            <input type="text" hidden name="csrf-token" value="{{csrfToken}}">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group mb-3">
                                        <h3>DOCUMENTATION</h3>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="clientAnswer">Please answer the requested information.</label>
                                        <textarea
                                                required
                                                name="clientAnswer"
                                                id="clientAnswer"
                                                class="form-control"
                                                form="submitForm"
                                                placeholder="Message..."
                                                rows="2"
                                        ></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <label>Please add the requested documents in the box below.</label>
                                    <div id="frmUploadModal" class="dropzone">
                                        <div class="fallback">
                                            <input name="SubmitEvidence" type="file" multiple/>
                                        </div>
                                        <div class="dz-message needsclick">
                                            <i class="h1 text-muted dripicons-cloud-upload"></i>
                                            <h3>Drop files here or click to upload.</h3>
                                            <span class="text-muted font-13">Allowed filetypes: PDF,
                                                                JPG, PNG, GIF</span>
                                        </div>
                                    </div>
                                </div> <!-- end col -->
                            </div>
                        </form>



                    </div>

                    <!-- CARD BODY END -->
                    <!-- CARD FOOTER NAV -->
                    <div class="row mt-2">
                        <div class="d-flex col-md-12 justify-content-between">
                            <a type="button"
                               class="btn btn-secondary waves-effect waves-light width-xl"
                               href="/masterclients/{{masterClientCode}}/incorporate-company">Back to
                                dashboard
                            </a>

                            <button type="button" class="btn btn-success  width-lg waves-effect waves-light" id="btnSubmit" form="submitForm"
                            > Submit
                            </button>


                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script type="text/javascript" src="/javascripts/form-advanced.init.js"></script>
<script type="text/javascript" src="/views-js/incorporate-company/request-information-view.js"></script>
