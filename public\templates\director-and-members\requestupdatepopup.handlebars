<div class="text-left">

    <div class="form-group mb-2" >
        <label for="changeType">Type of request:</label>
        <select name="changeType" id="changeType" class="form-control w-100" >
            {{#ifEquals type "director"}}
                <option value="" hidden>Select an option</option>

                {{#if hasMissingData}}
                    <option value="Missing Director(s) info">Missing Director(s) info</option>
                {{/if}}
                <option value="Change of Director(s)" >Change of Director(s)</option>
                <option value="Change of Director(s) Particulars">Change of Director(s) Particulars</option>
                <option value="Change of Director(s) Address">Change of Director(s) Address</option>
                <option value="Other update for Director(s)">Other update for Director(s)</option>
                <option value="Confirmation for Licensed Director to provide service">Confirmation for Licensed Director to provide service</option>
                <option value="Revert confirmation for Licensed Director to provide service">Revert confirmation for Licensed Director to provide service</option>
            {{else ifEquals type "member"}}
                <option value="" hidden>Select an option</option>

                {{#if hasMissingData}}
                    <option value="Missing Info">Missing Info</option>
                {{/if}}
                <option value="Change of Shareholder" >Change of Shareholder</option>
                <option value="Change of Shareholder Particulars" >Change of Shareholder Particulars</option>
                <option value="Change of Shareholder Address" >Change of Shareholder Address</option>
                <option value="Other update for Shareholder">Other update for Shareholder</option>
                <option value="Confirmation for Nominee Arrangement">Confirmation for Nominee Arrangement</option>
                <option value="Revert confirmation for Nominee Arrangement">Revert confirmation for Nominee Arrangement</option>
            {{else}}
                <option value="" hidden>Select an option</option>

                {{#if hasMissingData}}
                    <option value="Missing Company Info">Missing Company Info</option>
                {{/if}}
                <option value="Change of Particular(s)" >Change of Particular(s)</option>
                <option value="Cease Exemption" >Cease Exemption</option>
                <option value="Other changes" >Other changes</option>
            {{/ifEquals}}

        </select>

    </div>
    <div  class="form-group w-100 ">
        <label for="changeReason">Additional information if necessary:</label>
        <textarea name="changeReason" class="form-control w-100"
                  id="changeReason" rows="3" placeholder="..." maxlength="500"></textarea>
    </div>
</div>
