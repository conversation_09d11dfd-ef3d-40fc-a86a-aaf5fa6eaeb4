<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class='contour'>

					{{#ifCond entry.version '<' 5}}
						<h2>2. Relevant Activity Declaration</h2>
					{{else}}
						<h2>3. Relevant Activity Declaration</h2>
					{{/ifCond}}
                    {{# if validationErrors }}
                        {{# each validationErrors }}
                            {{renderValidationMessage this.msg this.field}}
                        {{/each}}
                    {{/if}}
                    <form method="POST" class='enquiry' autocomplete="off">
						<input type="text" hidden name="csrf-token" value="{{csrfToken}}">
					<div class="container-fluid">
						<h4 class="page-title"></h4>
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="card">
                                    <div class="card-body">
                                        <h4 class="header-title">Select all relevant activities in which the entity has been engaged during <br/>the financial period from <b>{{formatDate entry.entity_details.financial_period_begins "YYYY-MM-DD"}} to {{formatDate entry.entity_details.financial_period_ends "YYYY-MM-DD"}}</b>.</h4>
                                        <p class="sub-header">
                                        </p>
										<div class="row">
                                            <div class="col-md-3">
												<div class="form-group mb-3">
													<label class="mb-2" for="Header1">Relevant Activity</label>
												</div>
											</div>
                                            <div class="col-md-3">
												<div class="form-group mb-3">
													<label class="mb-2" for="Header2">Carried on for only part of Financial Period?</label>
												</div>
											</div>
											<div class="col-md-3">
												<div class="form-group mb-3">
													<label class="mb-2" for="Header3">Start Date</label>
												</div>
											</div>
											<div class="col-md-3">
												<div class="form-group mb-3">
													<label class="mb-2" for="Header4">End Date</label>
												</div>
											</div>
										</div>
										<div class="row">
                                           <div class="col-md-3">
												<div class="custom-control custom-switch">
                                                    <input type="checkbox" class="custom-control-input" id="NoneCheckBox" name="NoneCheckBox" {{#if entry.relevant_activities.none.selected}}checked{{/if}} >
													<label class="custom-control-label" for="NoneCheckBox" data-toggle="tooltip" placement="top" title="To be chosen if the company does not carry on a relevant activity as defined by the ES Act.">None</label>
                                                </div>
											</div>
                                            <div class="col-md-1">
												<div class="radio form-check-inline">
													<input type="radio" id="NonePartYes" name="NonePart"  value="Yes" disabled>
												<label for="NonePartYes">Yes</label>
												</div>
											</div>
                                            <div class="col-md-2">
												<div class="radio form-check-inline">
													<input type="radio" id="NonePartNo" name="NonePart"  value="No" {{#if entry.relevant_activities.none.selected}}checked{{/if}} >
													<label for="NonePartNo">No</label>
												</div>
											</div>
											<div class="col-md-6">
											{{#unless entry.relevant_activities.none.financial_periods}}
													<div class="row">
														<div class="col-md-6">
															<div class="form-group">
																<input type="text" name="NonePartStartDate" id="NonePartStartDate" class="form-control datepicker" label="NonePartStartDate" value="">
															</div>
														</div>
														<div class="col-md-6">
															<div class="form-group">
																<input type="text" name="NonePartEndDate" id="NonePartEndDate" class="form-control datepicker" label="NonePartEndDate" value="">
															</div>
														</div>
													</div>
												{{/unless}}
												{{#if entry.relevant_activities.none.financial_periods}}
													{{#each entry.relevant_activities.none.financial_periods}}
														<div class="row">
															<div class="col-md-6">
																<div class="form-group">
																	<input type="text" name="NonePartStartDate" id="NonePartStartDate" class="form-control datepicker" label="NonePartStartDate"
                                                                           value="{{formatDate ../entry.entity_details.financial_period_begins "YYYY-MM-DD"}}">
																</div>
															</div>
															<div class="col-md-6">
																<div class="form-group">
																	<input type="text" name="NonePartEndDate" id="NonePartEndDate" class="form-control datepicker" label="NonePartEndDate"
                                                                           value="{{formatDate ../entry.entity_details.financial_period_ends "YYYY-MM-DD"}}">
																</div>
															</div>
														</div>
													{{/each}}
												{{/if}}
											</div>
										</div>
										<div class="row">
											<div class="col-md-3">
												<div class="custom-control custom-switch">
                                                    <input type="checkbox" class="custom-control-input activity-checkbox" id="HoldingBusinessCheckBox" name="HoldingBusinessCheckBox" {{#if entry.relevant_activities.holding_business.selected}}checked{{/if}}>
													<label class="custom-control-label" for="HoldingBusinessCheckBox" data-toggle="tooltip" placement="top" title="To be chosen if the company carries on a relevant activity as defined by the ES Act. Please refer to the list below. A company may carry on more than one relevant activity">Holding Business (Pure Equity Holding entities)</label>
                                                </div>
											</div>
                                            <div class="col-md-1">
												<div class="radio form-check-inline">
													<input type="radio" id="HoldingBusinessPartYes" name="HoldingBusinessPart" value="Yes"
                                                           {{#partlyPeriodYes entry.relevant_activities.holding_business.selected entry.relevant_activities.holding_business.part_of_financial_period}}checked{{/partlyPeriodYes}} >
												<label for="HoldingBusinessPartYes">Yes</label>
												</div>
											</div>
                                            <div class="col-md-2">
												<div class="radio form-check-inline">
													<input type="radio" id="HoldingBusinessPartNo" name="HoldingBusinessPart" value="No" {{#partlyPeriodNo entry.relevant_activities.holding_business.selected entry.relevant_activities.holding_business.part_of_financial_period}}checked{{/partlyPeriodNo}} >
													<label for="HoldingBusinessPartNo">No</label>
												</div>
											</div>
											<div class="col-md-6">
											{{#unless entry.relevant_activities.holding_business.financial_periods}}
													<div class="row">
														<div class="col-md-6">
															<div class="form-group">
																<input type="text" name="HoldingBusinessStartDate" id="HoldingBusinessStartDate" class="form-control datepicker"
                                                                       label="HoldingBusinessStartDate" value="">
															</div>
														</div>
														<div class="col-md-6">
															<div class="form-group">
																<input type="text" name="HoldingBusinessEndDate" id="HoldingBusinessEndDate" class="form-control datepicker" label="HoldingBusinessEndDate" value="">
															</div>
														</div>
													</div>
												{{/unless}}
												{{#if entry.relevant_activities.holding_business.financial_periods}}
													{{#each entry.relevant_activities.holding_business.financial_periods}}
														<div class="row">
															<div class="col-md-6">
																<div class="form-group">
																	<input type="text" name="HoldingBusinessStartDate" id="HoldingBusinessStartDate" class="form-control datepicker" label="HoldingBusinessStartDate"
                                                                        {{#partlyPeriodNo ../entry.relevant_activities.holding_business.selected  ../entry.relevant_activities.holding_business.part_of_financial_period}}

                                                                           value="{{formatDate ../entry.entity_details.financial_period_begins "YYYY-MM-DD"}}"
                                                                        {{else}}
                                                                           value="{{formatDate financial_period_begins "YYYY-MM-DD"}}"
                                                                        {{/partlyPeriodNo}}>
																</div>
															</div>
															<div class="col-md-6">
																<div class="form-group">
																	<input type="text" name="HoldingBusinessEndDate" id="HoldingBusinessEndDate" class="form-control  datepicker" label="HoldingBusinessEndDate"
                                                                        {{#partlyPeriodNo ../entry.relevant_activities.holding_business.selected  ../entry.relevant_activities.holding_business.part_of_financial_period}}
                                                                           value="{{formatDate ../entry.entity_details.financial_period_ends "YYYY-MM-DD"}}"
                                                                        {{else}}
                                                                           value="{{formatDate financial_period_ends "YYYY-MM-DD"}}"
                                                                        {{/partlyPeriodNo}}>
																</div>
															</div>
														</div>
													{{/each}}
												{{/if}}
											</div>
										</div>
										<div class="row">
                                           <div class="col-md-3">
												<div class="custom-control custom-switch">
                                                    <input type="checkbox" class="custom-control-input activity-checkbox" id="FinanceAndLeasingBusinessCheckBox" name="FinanceAndLeasingBusinessCheckBox" {{#if entry.relevant_activities.finance_leasing_business.selected}}checked{{/if}}>
													<label class="custom-control-label" for="FinanceAndLeasingBusinessCheckBox" data-toggle="tooltip" placement="top" title="To be chosen if the company carries on a relevant activity as defined by the ES Act. Please refer to the list below. A company may carry on more than one relevant activity">Finance and leasing business</label>
                                                </div>
											</div>
                                            <div class="col-md-1">
												<div class="radio form-check-inline">
													<input type="radio" id="FinanceAndLeasingBusinessPartYes" name="FinanceAndLeasingBusinessPart" value="Yes" {{#partlyPeriodYes entry.relevant_activities.finance_leasing_business.selected entry.relevant_activities.finance_leasing_business.part_of_financial_period}}checked{{/partlyPeriodYes}} data-toggle="tooltip" placement="top" title="To be chosen if the company carries on a relevant activity as defined by the ES Act. Please refer to the list below. A company may carry on more than one relevant activity">
												<label for="FinanceAndLeasingBusinessPartYes">Yes</label>
												</div>
											</div>
                                            <div class="col-md-2">
												<div class="radio form-check-inline">
													<input type="radio" id="FinanceAndLeasingBusinessPartNo" name="FinanceAndLeasingBusinessPart" value="No" {{#partlyPeriodNo entry.relevant_activities.finance_leasing_business.selected entry.relevant_activities.finance_leasing_business.part_of_financial_period}}checked{{/partlyPeriodNo}} data-toggle="tooltip" placement="top" title="To be chosen if the copany does not carry on a relevant activity as defined by the ES Act.">
													<label for="FinanceAndLeasingBusinessPartNo">No</label>
												</div>
											</div>
											<div class="col-md-6">
											{{#unless entry.relevant_activities.finance_leasing_business.financial_periods}}
													<div class="row">
														<div class="col-md-6">
															<div class="form-group">
																<input type="text" name="FinanceAndLeasingBusinessStartDate" id="FinanceAndLeasingBusinessStartDate" class="form-control datepicker" label="FinanceAndLeasingBusinessStartDate" value="">
															</div>
														</div>
														<div class="col-md-6">
															<div class="form-group">
																<input type="text" name="FinanceAndLeasingBusinessEndDate" id="FinanceAndLeasingBusinessEndDate" class="form-control datepicker" label="FinanceAndLeasingBusinessEndDate" value="">
															</div>
														</div>
													</div>
												{{/unless}}
												{{#if entry.relevant_activities.finance_leasing_business.financial_periods}}
													{{#each entry.relevant_activities.finance_leasing_business.financial_periods}}
														<div class="row">
															<div class="col-md-6">
																<div class="form-group">
																	<input type="text" name="FinanceAndLeasingBusinessStartDate" id="FinanceAndLeasingBusinessStartDate" class="form-control datepicker" label="FinanceAndLeasingBusinessStartDate"
                                                                        {{#partlyPeriodNo ../entry.relevant_activities.finance_leasing_business.selected  ../entry.relevant_activities.finance_leasing_business.part_of_financial_period}}
                                                                           value="{{formatDate ../entry.entity_details.financial_period_begins "YYYY-MM-DD"}}"
                                                                        {{else}}
                                                                           value="{{formatDate financial_period_begins "YYYY-MM-DD"}}"
                                                                        {{/partlyPeriodNo}}>
																</div>
															</div>
															<div class="col-md-6">
																<div class="form-group">
																	<input type="text" name="FinanceAndLeasingBusinessEndDate" id="FinanceAndLeasingBusinessEndDate" class="form-control  datepicker" label="FinanceAndLeasingBusinessEndDate"
                                                                        {{#partlyPeriodNo ../entry.relevant_activities.finance_leasing_business.selected  ../entry.relevant_activities.finance_leasing_business.part_of_financial_period}}
                                                                           value="{{formatDate ../entry.entity_details.financial_period_ends "YYYY-MM-DD"}}"
                                                                        {{else}}
                                                                           value="{{formatDate financial_period_ends "YYYY-MM-DD"}}"
                                                                        {{/partlyPeriodNo}}>
																</div>
															</div>
														</div>
													{{/each}}
												{{/if}}
											</div>
										</div>

										<div class="row">
                                            <div class="col-md-3">
												<div class="custom-control custom-switch">
                                                    <input type="checkbox" class="custom-control-input activity-checkbox" id="BankingBusinessCheckBox" name="BankingBusinessCheckBox"  {{#if entry.relevant_activities.banking_business.selected}}checked{{/if}}>
													<label class="custom-control-label" for="BankingBusinessCheckBox" data-toggle="tooltip" placement="top" title="To be chosen if the company carries on a relevant activity as defined by the ES Act. Please refer to the list below. A company may carry on more than one relevant activity">Banking Business</label>
                                                </div>
											</div>
											<div class="col-md-1">
												<div class="radio form-check-inline">
													<input type="radio" id="BankingBusinessYes" name="BankingBusinessPart" value="Yes" {{#partlyPeriodYes entry.relevant_activities.banking_business.selected entry.relevant_activities.banking_business.part_of_financial_period}}checked{{/partlyPeriodYes}}>
												<label for="BankingBusinessYes">Yes</label>
												</div>
											</div>
                                            <div class="col-md-2">
												<div class="radio form-check-inline">
													<input type="radio" id="BankingBusinessNo" name="BankingBusinessPart" value="No" {{#partlyPeriodNo entry.relevant_activities.banking_business.selected  entry.relevant_activities.banking_business.part_of_financial_period}}checked{{/partlyPeriodNo}}>
													<label for="BankingBusinessNo">No</label>
												</div>
											</div>
											<div class="col-md-6">
												{{#unless entry.relevant_activities.banking_business.financial_periods}}
													<div class="row">
														<div class="col-md-6">
															<div class="form-group">
																<input type="text" name="BankingBusinessStartDate" id="BankingBusinessStartDate" class="form-control datepicker" label="BankingBusinessStartDate" value="">
															</div>
														</div>
														<div class="col-md-6">
															<div class="form-group">
																<input type="text" name="BankingBusinessEndDate" id="BankingBusinessEndDate" class="form-control  datepicker" label="BankingBusinessEndDate" value="">
															</div>
														</div>
													</div>
												{{/unless}}
												{{#if entry.relevant_activities.banking_business.financial_periods}}
													{{#each entry.relevant_activities.banking_business.financial_periods}}
														<div class="row">
															<div class="col-md-6">
																<div class="form-group">
																	<input type="text" name="BankingBusinessStartDate" id="BankingBusinessStartDate" class="form-control datepicker" label="BankingBusinessStartDate"
                                                                       {{#partlyPeriodNo ../entry.relevant_activities.banking_business.selected  ../entry.relevant_activities.banking_business.part_of_financial_period}}
                                                                            value="{{formatDate ../entry.entity_details.financial_period_begins "YYYY-MM-DD"}}"
                                                                       {{else}}
                                                                            value="{{formatDate financial_period_begins "YYYY-MM-DD"}}"
                                                                       {{/partlyPeriodNo}}>
																</div>
															</div>
															<div class="col-md-6">
																<div class="form-group">
																	<input type="text" name="BankingBusinessEndDate" id="BankingBusinessEndDate" class="form-control  datepicker" label="BankingBusinessEndDate"
                                                                        {{#partlyPeriodNo ../entry.relevant_activities.banking_business.selected  ../entry.relevant_activities.banking_business.part_of_financial_period}}
                                                                            value="{{formatDate ../entry.entity_details.financial_period_ends "YYYY-MM-DD"}}"
                                                                        {{else}}
                                                                            value="{{formatDate financial_period_ends "YYYY-MM-DD"}}"
                                                                        {{/partlyPeriodNo}}>
																</div>
															</div>
														</div>
													{{/each}}
												{{/if}}
											</div>
										</div>
										<div class="row">
                                            <div class="col-md-3">
												<div class="custom-control custom-switch">
                                                    <input type="checkbox" class="custom-control-input activity-checkbox" id="InsuranceBusinessCheckBox" name="InsuranceBusinessCheckBox" {{#if entry.relevant_activities.insurance_business.selected}}checked{{/if}}>
													<label class="custom-control-label" for="InsuranceBusinessCheckBox" data-toggle="tooltip" placement="top" title="To be chosen if the company carries on a relevant activity as defined by the ES Act. Please refer to the list below. A company may carry on more than one relevant activity">Insurance Business</label>
                                                </div>
											</div>
                                            <div class="col-md-1">
												<div class="radio form-check-inline">
													<input type="radio" id="InsuranceBusinessPartYes" name="InsuranceBusinessPart" value="Yes" {{#partlyPeriodYes entry.relevant_activities.insurance_business.selected entry.relevant_activities.insurance_business.part_of_financial_period}}checked{{/partlyPeriodYes}}>
												<label for="InsuranceBusinessPartYes">Yes</label>
												</div>
											</div>
                                            <div class="col-md-2">
												<div class="radio form-check-inline">
													<input type="radio" id="InsuranceBusinessPartNo" name="InsuranceBusinessPart" value="No" {{#partlyPeriodNo entry.relevant_activities.insurance_business.selected entry.relevant_activities.insurance_business.part_of_financial_period}}checked{{/partlyPeriodNo}}>
													<label for="InsuranceBusinessPartNo">No</label>
												</div>
											</div>
											<div class="col-md-6">
											{{#unless entry.relevant_activities.insurance_business.financial_periods}}
													<div class="row">
														<div class="col-md-6">
															<div class="form-group">
																<input type="text" name="InsuranceBusinessStartDate" id="InsuranceBusinessStartDate" class="form-control datepicker" label="InsuranceBusinessStartDate" value="">
															</div>
														</div>
														<div class="col-md-6">
															<div class="form-group">
																<input type="text" name="InsuranceBusinessEndDate" id="InsuranceBusinessEndDate" class="form-control  datepicker" label="InsuranceBusinessEndDate" value="">
															</div>
														</div>
													</div>
												{{/unless}}
												{{#if entry.relevant_activities.insurance_business.financial_periods}}
													{{#each entry.relevant_activities.insurance_business.financial_periods}}
														<div class="row">
															<div class="col-md-6">
																<div class="form-group">
																	<input type="text" name="InsuranceBusinessStartDate" id="InsuranceBusinessStartDate" class="form-control datepicker" label="InsuranceBusinessStartDate"
                                                                        {{#partlyPeriodNo ../entry.relevant_activities.insurance_business.selected  ../entry.relevant_activities.insurance_business.part_of_financial_period}}
                                                                           value="{{formatDate ../entry.entity_details.financial_period_begins "YYYY-MM-DD"}}"
                                                                        {{else}}
                                                                           value="{{formatDate financial_period_begins "YYYY-MM-DD"}}"
                                                                        {{/partlyPeriodNo}}>
																</div>
															</div>
															<div class="col-md-6">
																<div class="form-group">
																	<input type="text" name="InsuranceBusinessEndDate" id="InsuranceBusinessEndDate" class="form-control  datepicker" label="InsuranceBusinessEndDate"
                                                                        {{#partlyPeriodNo ../entry.relevant_activities.insurance_business.selected  ../entry.relevant_activities.insurance_business.part_of_financial_period}}
                                                                           value="{{formatDate ../entry.entity_details.financial_period_ends "YYYY-MM-DD"}}"
                                                                        {{else}}
                                                                           value="{{formatDate financial_period_ends "YYYY-MM-DD"}}"
                                                                        {{/partlyPeriodNo}}>
																</div>
															</div>
														</div>
													{{/each}}
												{{/if}}
											</div>
										</div>

										<div class="row">
                                            <div class="col-md-3">
												<div class="custom-control custom-switch">
                                                    <input type="checkbox" class="custom-control-input activity-checkbox" id="FundManagementBusinessCheckBox" name="FundManagementBusinessCheckBox" {{#if entry.relevant_activities.fund_management_business.selected}}checked{{/if}}>
													<label class="custom-control-label" for="FundManagementBusinessCheckBox" data-toggle="tooltip" placement="top" title="To be chosen if the company carries on a relevant activity as defined by the ES Act. Please refer to the list below. A company may carry on more than one relevant activity">Fund management business</label>
                                                </div>
											</div>
                                            <div class="col-md-1">
												<div class="radio form-check-inline">
													<input type="radio" id="FundManagementBusinessPartYes" name="FundManagementBusinessPart" value="Yes" {{#partlyPeriodYes entry.relevant_activities.fund_management_business.selected entry.relevant_activities.fund_management_business.part_of_financial_period}}checked{{/partlyPeriodYes}}>
												<label for="FundManagementBusinessPartYes">Yes</label>
												</div>
											</div>
                                            <div class="col-md-2">
												<div class="radio form-check-inline">
													<input type="radio" id="FundManagementBusinessNo" name="FundManagementBusinessPart" value="No" {{#partlyPeriodNo entry.relevant_activities.fund_management_business.selected entry.relevant_activities.fund_management_business.part_of_financial_period}}checked{{/partlyPeriodNo}}>
													<label for="FundManagementBusinessNo">No</label>
												</div>
											</div>
											<div class="col-md-6">
											{{#unless entry.relevant_activities.fund_management_business.financial_periods}}
													<div class="row">
														<div class="col-md-6">
															<div class="form-group">
																<input type="text" name="FundManagementBusinesStartDate" id="FundManagementBusinesStartDate" class="form-control datepicker" label="FundManagementBusinesStartDate" value="">
															</div>
														</div>
														<div class="col-md-6">
															<div class="form-group">
																<input type="text" name="FundManagementBusinesEndDate" id="FundManagementBusinesEndDate" class="form-control datepicker" label="FundManagementBusinesEndDate" value="">
															</div>
														</div>
													</div>
												{{/unless}}
												{{#if entry.relevant_activities.fund_management_business.financial_periods}}
													{{#each entry.relevant_activities.fund_management_business.financial_periods}}
														<div class="row">
															<div class="col-md-6">
																<div class="form-group">
																	<input type="text" name="FundManagementBusinesStartDate" id="FundManagementBusinesStartDate" class="form-control datepicker" label="FundManagementBusinesStartDate"
                                                                        {{#partlyPeriodNo ../entry.relevant_activities.fund_management_business.selected  ../entry.relevant_activities.fund_management_business.part_of_financial_period}}
                                                                           value="{{formatDate ../entry.entity_details.financial_period_begins "YYYY-MM-DD"}}"
                                                                        {{else}}
                                                                           value="{{formatDate financial_period_begins "YYYY-MM-DD"}}"
                                                                        {{/partlyPeriodNo}}>
																</div>
															</div>
															<div class="col-md-6">
																<div class="form-group">
																	<input type="text" name="FundManagementBusinesEndDate" id="FundManagementBusinesEndDate" class="form-control  datepicker" label="FundManagementBusinesEndDate"

                                                                        {{#partlyPeriodNo ../entry.relevant_activities.fund_management_business.selected  ../entry.relevant_activities.fund_management_business.part_of_financial_period}}
                                                                           value="{{formatDate ../entry.entity_details.financial_period_ends "YYYY-MM-DD"}}"
                                                                        {{else}}
                                                                           value="{{formatDate financial_period_ends "YYYY-MM-DD"}}"
                                                                        {{/partlyPeriodNo}}>
																</div>
															</div>
														</div>
													{{/each}}
												{{/if}}
											</div>
										</div>

										<div class="row">
                                            <div class="col-md-3">
												<div class="custom-control custom-switch">
                                                    <input type="checkbox" class="custom-control-input activity-checkbox" id="HeadquartersBusinessCheckBox" name="HeadquartersBusinessCheckBox" {{#if entry.relevant_activities.headquarters_business.selected}}checked{{/if}}>
													<label class="custom-control-label" for="HeadquartersBusinessCheckBox" data-toggle="tooltip" placement="top" title="To be chosen if the company carries on a relevant activity as defined by the ES Act. Please refer to the list below. A company may carry on more than one relevant activity">Headquarters business</label>
                                                </div>
											</div>
                                            <div class="col-md-1">
												<div class="radio form-check-inline">
													<input type="radio" id="HeadquartersBusinessPartYes" name="HeadquartersBusinessPart" value="Yes" {{#partlyPeriodYes entry.relevant_activities.headquarters_business.selected entry.relevant_activities.headquarters_business.part_of_financial_period}}checked{{/partlyPeriodYes}}>
												<label for="HeadquartersBusinessPartYes">Yes</label>
												</div>
											</div>
                                            <div class="col-md-2">
												<div class="radio form-check-inline">
													<input type="radio" id="HeadquartersBusinessPartNo" name="HeadquartersBusinessPart" value="No" {{#partlyPeriodNo entry.relevant_activities.headquarters_business.selected entry.relevant_activities.headquarters_business.part_of_financial_period}}checked{{/partlyPeriodNo}}>
													<label for="HeadquartersBusinessPartNo">No</label>
												</div>
											</div>
											<div class="col-md-6">
											{{#unless entry.relevant_activities.headquarters_business.financial_periods}}
													<div class="row">
														<div class="col-md-6">
															<div class="form-group">
																<input type="text" name="HeadquartersBusinessStartDate" id="HeadquartersBusinessStartDate" class="form-control datepicker" label="HeadquartersBusinessStartDate" value="">
															</div>
														</div>
														<div class="col-md-6">
															<div class="form-group">
																<input type="text" name="HeadquartersBusinessEndDate" id="HeadquartersBusinessEndDate" class="form-control datepicker" label="HeadquartersBusinessEndDate" value="">
															</div>
														</div>
													</div>
												{{/unless}}
												{{#if entry.relevant_activities.headquarters_business.financial_periods}}
													{{#each entry.relevant_activities.headquarters_business.financial_periods}}
														<div class="row">
															<div class="col-md-6">
																<div class="form-group">
																	<input type="text" name="HeadquartersBusinessStartDate" id="HeadquartersBusinessStartDate" class="form-control datepicker" label="HeadquartersBusinessStartDate"
                                                                        {{#partlyPeriodNo ../entry.relevant_activities.headquarters_business.selected  ../entry.relevant_activities.headquarters_business.part_of_financial_period}}
                                                                           value="{{formatDate ../entry.entity_details.financial_period_begins "YYYY-MM-DD"}}"
                                                                        {{else}}
                                                                           value="{{formatDate financial_period_begins "YYYY-MM-DD"}}"
                                                                        {{/partlyPeriodNo}}>
																</div>
															</div>
															<div class="col-md-6">
																<div class="form-group">
																	<input type="text" name="HeadquartersBusinessEndDate" id="HeadquartersBusinessEndDate" class="form-control  datepicker" label="HeadquartersBusinessEndDate"
                                                                        {{#partlyPeriodNo ../entry.relevant_activities.headquarters_business.selected  ../entry.relevant_activities.headquarters_business.part_of_financial_period}}
                                                                           value="{{formatDate ../entry.entity_details.financial_period_ends "YYYY-MM-DD"}}"
                                                                        {{else}}
                                                                           value="{{formatDate financial_period_ends "YYYY-MM-DD"}}"
                                                                        {{/partlyPeriodNo}}>
																</div>
															</div>
														</div>
													{{/each}}
												{{/if}}
											</div>
										</div>

										<div class="row">
                                            <div class="col-md-3">
												<div class="custom-control custom-switch">
                                                    <input type="checkbox" class="custom-control-input activity-checkbox" id="ShippingBusinessCheckBox" name="ShippingBusinessCheckBox" {{#if entry.relevant_activities.shipping_business.selected}}checked{{/if}}>
													<label class="custom-control-label" for="ShippingBusinessCheckBox" data-toggle="tooltip" placement="top" title="To be chosen if the company carries on a relevant activity as defined by the ES Act. Please refer to the list below. A company may carry on more than one relevant activity">Shipping business</label>
                                                </div>
											</div>
                                            <div class="col-md-1">
												<div class="radio form-check-inline">
													<input type="radio" id="ShippingBusinessPartYes" name="ShippingBusinessPart" value="Yes" {{#partlyPeriodYes entry.relevant_activities.shipping_business.selected entry.relevant_activities.shipping_business.part_of_financial_period}}checked{{/partlyPeriodYes}}>
												<label for="ShippingBusinessPartYes">Yes</label>
												</div>
											</div>
                                            <div class="col-md-2">
												<div class="radio form-check-inline">
													<input type="radio" id="ShippingBusinessPartNo" name="ShippingBusinessPart" value="No" {{#partlyPeriodNo entry.relevant_activities.shipping_business.selected entry.relevant_activities.shipping_business.part_of_financial_period}}checked{{/partlyPeriodNo}}>
													<label for="ShippingBusinessPartNo">No</label>
												</div>
											</div>
											<div class="col-md-6">
											{{#unless entry.relevant_activities.shipping_business.financial_periods}}
													<div class="row">
														<div class="col-md-6">
															<div class="form-group">
																<input type="text" name="ShippingBusinessStartDate" id="ShippingBusinessStartDate" class="form-control datepicker" label="ShippingBusinessStartDate" value="">
															</div>
														</div>
														<div class="col-md-6">
															<div class="form-group">
																<input type="text" name="ShippingBusinessEndDate" id="ShippingBusinessEndDate" class="form-control datepicker" label="ShippingBusinessEndDate" value="">
															</div>
														</div>
													</div>
												{{/unless}}
												{{#if entry.relevant_activities.shipping_business.financial_periods}}
													{{#each entry.relevant_activities.shipping_business.financial_periods}}
														<div class="row">
															<div class="col-md-6">
																<div class="form-group">
																	<input type="text" name="ShippingBusinessStartDate" id="ShippingBusinessStartDate" class="form-control datepicker" label="ShippingBusinessStartDate"
                                                                        {{#partlyPeriodNo ../entry.relevant_activities.shipping_business.selected  ../entry.relevant_activities.shipping_business.part_of_financial_period}}
                                                                           value="{{formatDate ../entry.entity_details.financial_period_begins "YYYY-MM-DD"}}"
                                                                        {{else}}
                                                                           value="{{formatDate financial_period_begins "YYYY-MM-DD"}}"
                                                                        {{/partlyPeriodNo}}>
																</div>
															</div>
															<div class="col-md-6">
																<div class="form-group">
																	<input type="text" name="ShippingBusinessEndDate" id="ShippingBusinessEndDate" class="form-control  datepicker" label="ShippingBusinessEndDate"
                                                                        {{#partlyPeriodNo ../entry.relevant_activities.shipping_business.selected  ../entry.relevant_activities.shipping_business.part_of_financial_period}}
                                                                           value="{{formatDate ../entry.entity_details.financial_period_ends "YYYY-MM-DD"}}"
                                                                        {{else}}
                                                                           value="{{formatDate financial_period_ends "YYYY-MM-DD"}}"
                                                                        {{/partlyPeriodNo}}>
																</div>
															</div>
														</div>
													{{/each}}
												{{/if}}
											</div>
										</div>



										<div class="row">
                                             <div class="col-md-3">
												<div class="custom-control custom-switch">
                                                    <input type="checkbox" class="custom-control-input activity-checkbox" id="IntellectualPropertyBusinessCheckBox" name="IntellectualPropertyBusinessCheckBox" {{#if entry.relevant_activities.intellectual_property_business.selected}}checked{{/if}}>
													<label class="custom-control-label" for="IntellectualPropertyBusinessCheckBox" data-toggle="tooltip" placement="top" title="To be chosen if the company carries on a relevant activity as defined by the ES Act. Please refer to the list below. A company may carry on more than one relevant activity">Intellectual property business</label>
                                                </div>
											</div>
                                            <div class="col-md-1">
												<div class="radio form-check-inline">
													<input type="radio" id="IntellectualPropertyBusinessPartYes" name="IntellectualPropertyBusinessPart" value="Yes" {{#partlyPeriodYes entry.relevant_activities.intellectual_property_business.selected entry.relevant_activities.intellectual_property_business.part_of_financial_period}}checked{{/partlyPeriodYes}}>
												<label for="IntellectualPropertyBusinessPartYes">Yes</label>
												</div>
											</div>
                                            <div class="col-md-2">
												<div class="radio form-check-inline">
													<input type="radio" id="IntellectualPropertyBusinessPartNo" name="IntellectualPropertyBusinessPart" value="No" {{#partlyPeriodNo entry.relevant_activities.intellectual_property_business.selected entry.relevant_activities.intellectual_property_business.part_of_financial_period}}checked{{/partlyPeriodNo}}>
													<label for="IntellectualPropertyBusinessPartNo">No</label>
												</div>
											</div>
											<div class="col-md-6">
											{{#unless entry.relevant_activities.intellectual_property_business.financial_periods}}
													<div class="row">
														<div class="col-md-6">
															<div class="form-group">
																<input type="text" name="IntellectualPropertyBusinessStartDate" id="IntellectualPropertyBusinessStartDate" class="form-control datepicker" label="IntellectualPropertyBusinessStartDate" value="">
															</div>
														</div>
														<div class="col-md-6">
															<div class="form-group">
																<input type="text" name="IntellectualPropertyBusinessEndDate" id="IntellectualPropertyBusinessEndDate" class="form-control datepicker" label="IntellectualPropertyBusinessEndDate" value="">
															</div>
														</div>
													</div>
												{{/unless}}
												{{#if entry.relevant_activities.intellectual_property_business.financial_periods}}
													{{#each entry.relevant_activities.intellectual_property_business.financial_periods}}
														<div class="row">
															<div class="col-md-6">
																<div class="form-group">
																	<input type="text" name="IntellectualPropertyBusinessStartDate" id="IntellectualPropertyBusinessStartDate" class="form-control datepicker" label="IntellectualPropertyBusinessStartDate"
                                                                        {{#partlyPeriodNo ../entry.relevant_activities.intellectual_property_business.selected  ../entry.relevant_activities.intellectual_property_business.part_of_financial_period}}
                                                                           value="{{formatDate ../entry.entity_details.financial_period_begins "YYYY-MM-DD"}}"
                                                                        {{else}}
                                                                           value="{{formatDate financial_period_begins "YYYY-MM-DD"}}"
                                                                        {{/partlyPeriodNo}}>
																</div>
															</div>
															<div class="col-md-6">
																<div class="form-group">
																	<input type="text" name="IntellectualPropertyBusinessEndDate" id="IntellectualPropertyBusinessEndDate" class="form-control  datepicker" label="IntellectualPropertyBusinessEndDate"
                                                                        {{#partlyPeriodNo ../entry.relevant_activities.intellectual_property_business.selected  ../entry.relevant_activities.intellectual_property_business.part_of_financial_period}}
                                                                           value="{{formatDate ../entry.entity_details.financial_period_ends "YYYY-MM-DD"}}"
                                                                        {{else}}
                                                                           value="{{formatDate financial_period_ends "YYYY-MM-DD"}}"
                                                                        {{/partlyPeriodNo}}>
																</div>
															</div>
														</div>
													{{/each}}
												{{/if}}
											</div>
										</div>

										<div class="row">
                                            <div class="col-md-3">
												<div class="custom-control custom-switch">
                                                    <input type="checkbox" class="custom-control-input activity-checkbox" id="DistributionAndServiceCheckBox" name="DistributionAndServiceCheckBox" {{#if entry.relevant_activities.service_centre_business.selected}}checked{{/if}}>
													<label class="custom-control-label" for="DistributionAndServiceCheckBox" data-toggle="tooltip" placement="top" title="To be chosen if the company carries on a relevant activity as defined by the ES Act. Please refer to the list below. A company may carry on more than one relevant activity">Distribution and service centre business</label>
                                                </div>
											</div>
                                            <div class="col-md-1">
												<div class="radio form-check-inline">
													<input type="radio" id="DistributionAndServicePartYes" name="DistributionAndServicePart" value="Yes" {{#partlyPeriodYes entry.relevant_activities.service_centre_business.selected entry.relevant_activities.service_centre_business.part_of_financial_period}}checked{{/partlyPeriodYes}}>
												<label for="DistributionAndServicePartYes">Yes</label>
												</div>
											</div>
                                            <div class="col-md-2">
												<div class="radio form-check-inline">
													<input type="radio" id="DistributionAndServicePartNo" name="DistributionAndServicePart" value="No" {{#partlyPeriodNo entry.relevant_activities.service_centre_business.selected entry.relevant_activities.service_centre_business.part_of_financial_period}}checked{{/partlyPeriodNo}}>
													<label for="DistributionAndServicePartNo">No</label>
												</div>
											</div>
											<div class="col-md-6">
											{{#unless entry.relevant_activities.service_centre_business.financial_periods}}
													<div class="row">
														<div class="col-md-6">
															<div class="form-group">
																<input type="text" name="DistributionAndServiceStartDate" id="DistributionAndServiceStartDate" class="form-control datepicker" label="DistributionAndServiceStartDate" value="">
															</div>
														</div>
														<div class="col-md-6">
															<div class="form-group">
																<input type="text" name="DistributionAndServiceEndDate" id="DistributionAndServiceEndDate" class="form-control datepicker" label="DistributionAndServiceEndDate" value="">
															</div>
														</div>
													</div>
												{{/unless}}
												{{#if entry.relevant_activities.service_centre_business.financial_periods}}
													{{#each entry.relevant_activities.service_centre_business.financial_periods}}
														<div class="row">
															<div class="col-md-6">
																<div class="form-group">
																	<input type="text" name="DistributionAndServiceStartDate" id="DistributionAndServiceStartDate" class="form-control datepicker" label="DistributionAndServiceStartDate"
                                                                        {{#partlyPeriodNo ../entry.relevant_activities.service_centre_business.selected  ../entry.relevant_activities.service_centre_business.part_of_financial_period}}
                                                                           value="{{formatDate ../entry.entity_details.financial_period_begins "YYYY-MM-DD"}}"
                                                                        {{else}}
                                                                           value="{{formatDate entry.entity_details.financial_period_begins "YYYY-MM-DD"}}"
                                                                        {{/partlyPeriodNo}}>
																</div>
															</div>
															<div class="col-md-6">
																<div class="form-group">
																	<input type="text" name="DistributionAndServiceEndDate" id="DistributionAndServiceEndDate" class="form-control datepicker" label="DistributionAndServiceEndDate"
                                                                        {{#partlyPeriodNo ../entry.relevant_activities.service_centre_business.selected  ../entry.relevant_activities.service_centre_business.part_of_financial_period}}
                                                                           value="{{formatDate ../entry.entity_details.financial_period_ends "YYYY-MM-DD"}}"
                                                                        {{else}}
                                                                           value="{{formatDate financial_period_ends "YYYY-MM-DD"}}"
                                                                        {{/partlyPeriodNo}}>
																</div>
															</div>
														</div>
													{{/each}}
												{{/if}}
											</div>
										</div>

										<div class="row">
											<div class="col-md-12">
												<div class="progress">
													{{#ifCond entry.version '<' 5}} 
														<div id="progressBarV4" class="progress-bar" role="progressbar" aria-valuenow="2" aria-valuemin="0" aria-valuemax="6">2 of 6</div>
													{{else}}
														<div id="progressBarV5"  class="progress-bar" role="progressbar" aria-valuenow="3" aria-valuemin="0"aria-valuemax="7">3 of 7</div>
													{{/ifCond}}
													
												</div>
											</div>
										</div>

									</div>  <!-- end card-body -->
                                </div>  <!-- end card -->
                            </div>  <!-- end col -->
                        </div>
						<div class="row">
							<div class="col-md-8">
								<div class="form-group mb-2">
									<input type="submit" name="submit"  value="Previous page"  class="btn btn-secondary waves-effect waves-light width-xl" />
								</div>
							</div>
							<div class="col-md-4">
								<div class="form-group mb-2" align="right">
									<input type="submit" name="submit" value="Save & next page"  class="btn btn-primary waves-effect waves-light width-xl" />
								</div>
							</div>
						</div>
						</div>
					</form>
				</div>
			</div>
        </div>
    </div>
</main>
{{> uploadModal entryId=entry._id}}
<script src="/templates/uploadedfiles.precompiled.js"></script>
<script type="text/javascript" src="/views-js/entry/Relevant-Activities.js"></script>