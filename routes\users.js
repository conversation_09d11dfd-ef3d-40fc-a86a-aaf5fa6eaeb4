const appInsightsClient = require("applicationinsights").defaultClient;
const User = require('../models/user');
const user_controller = require('../controllers/userController');
const sessionUtils = require('../utils/sessionUtils');

module.exports = function(app, passport) {
    app.use('/users/*', (req, res, next) => {
      res.locals.RECAPTCHA_SITE_KEY = process.env.RECAPTCHA_SITE_KEY;

      next();
    });

    app.get('/users/register', user_controller.register);
    app.post('/users/register', user_controller.validateCaptcha, user_controller.doRegistration);

    app.get('/users/activate/:token', user_controller.activate);
    app.post('/users/activate/:token', user_controller.validateCaptcha, user_controller.doActivate);

    app.get('/users/reset/:token', user_controller.reset);
    app.post('/users/reset/:token', user_controller.validateCaptcha, user_controller.doReset);

    app.get('/users/forgot', user_controller.forgot);
    app.post('/users/forgot', user_controller.validateCaptcha, user_controller.doForgot);
    
    app.get('/users/change-password', user_controller.ensureAuthenticated, user_controller.getChangePasswordView);
    app.post('/users/change-password', user_controller.ensureAuthenticated, user_controller.startChangePasswordProcess);


    app.get('/users/2fa-setup', user_controller.ensureAuthenticated, user_controller.getSetup2fa);
    app.post('/users/2fa-setup', user_controller.ensureAuthenticated, user_controller.doSetup2fa);
    app.post('/users/send-email-code', user_controller.ensureAuthenticated, user_controller.sendNewEmailCode); // jquery / ajax

    app.get('/users/2fa-code', user_controller.ensureAuthenticated, user_controller.getCode2fa);
    app.post('/users/2fa-code', user_controller.ensureAuthenticated, user_controller.doCode2fa);
    
    app.get(
      "/users/security-method", user_controller.ensureAuthenticated, user_controller.getVerificationMethodView);
    app.post(
      "/users/security-method", user_controller.ensureAuthenticated, user_controller.verificationMethod);
        
/*****************************Needs to be addressed later , not used vars is disabled in esling ******
*******************************Implemented for Eslint checking in CI/CD*******************************
*****************************/
      /* eslint no-unused-vars:0 */
    app.get('/users/login', user_controller.login);
  
  app.post('/users/login', user_controller.validateCaptcha,  function(req, res, next) {

        passport.authenticate('local-login', function(err, user, info) {
            console.log(req.session);
            if (err) { return next(err); }
            if (!user) { 
                var sessData = req.session;
                if (sessData.invalidLoginAttempts) {
                    sessData.invalidLoginAttempts += 1;
                    if (sessData.invalidLoginAttempts >= 5) {
                        User.findOne({email: req.body.email}, function(err, user) {
                            if(!err && user) {
                                User.findOneAndUpdate({ _id : user._id, email: user.email }, { locked: true }, function(err, savedUser) {
                                    console.log(savedUser);
                                });
                            }
                        });
                    }
                } else {
                    sessData.invalidLoginAttempts = 1;
                }
                return res.redirect('/'); 
            }
            //generate a new session id
            req.logIn(user, function (err) {
              if (err) {
                return next(err);
              }

              user.sessionId = req.session.id;
              User.findOneAndUpdate(
                { _id: user.id, email: user.email.toLowerCase() },
                user,
                {},
                function (err) {
                  if (err) {
                    return next(err);
                  }
                  appInsightsClient.trackEvent({
                    name: "user logged in",
                    properties: { email: user.email.toLowerCase() },
                  });
                  if (user.mfa_type != null || user.secret_2fa != null) {
                    return res.redirect("/");
                  }
                  else {
                    return res.redirect("/users/security-method");
                  }
                }
              );
            });
        })(req, res, next);
    });

    app.get('/users/logout', function(req,res,next) {        
        appInsightsClient.trackEvent({name: "user logged out", properties: {email: req.user?.email || ''}});
        req.logout(function (err) {
          if (err) { return next(err) }
          req.session.destroy(function () {
            // cannot access session here
            sessionUtils.onSessionDestroyed(req, res);
          });
        });       
    });
}
