<div id="{{group}}-limitedForm">
    <div class="row">
        <div class="col-5">
            <h4>Regulated Limited Company Details</h4>
        </div>
        <div class="col-7 d-flex">
            <div class="custom-control custom-switch my-auto">
                <input type="checkbox" class="custom-control-input toggle-section-check"
                       id="limited-company-confirmation-{{group}}"
                       name="limitedCompanyDetails[active]"
                       {{#if limitedCompany.active}} checked {{/if}}
                >
                <label class="custom-control-label" for="limited-company-confirmation-{{group}}"></label>
            </div>
        </div>
    </div>
    <div id="content-limited-company-confirmation-{{group}}" {{#unless limitedCompany.active }} class="hide-element" {{/unless}}>
        {{#if showTable}}
            {{>file-reviewer/shared/relation-file-table tableId="limitedTable"  name="limitedCompanyDetails"
                    files=(ternary newRelation limitedCompany limitedCompanyDetails.files) relationId=relation._id}}
        {{/if}}

        <div class="row mt-2">
            <div class="col-2">
                <label for="{{group}}-limited-registration-number">Registration Number*</label>
            </div>
            <div class="col-4">
                <input type="text" id="{{group}}-limited-registration-number" name="limitedCompanyDetails[registrationNumber]" class="form-control"
                    value="{{limitedCompany.registrationNumber}}" required/>
            </div>
            <div class="col-2">
                <label for="{{group}}-limited-registration-date">Registration Date*</label>
            </div>
            <div class="col-4">
                <input type="date" id="{{group}}-limited-registration-date" name="limitedCompanyDetails[registrationDate]"
                       class="form-control datepicker" placeholder="mm/dd/yyyy"
                       value="{{#formatDate limitedCompany.registrationDate "YYYY-MM-DD"}} {{/formatDate }}" required/>
            </div>
        </div>
    </div>

</div>
