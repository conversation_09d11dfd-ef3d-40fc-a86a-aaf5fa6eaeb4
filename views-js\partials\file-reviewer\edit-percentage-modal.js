$editButton = $('#editPercentageButton');
let companyPercentageId;
let percentage;
let relationFileId;
let fullName;
let percentageValue;
let typeRelation;
let typeGroup;
const myForm = $('#modalPercentageForm')[0];
$('#editPercentageModal').on('show.bs.modal', function (event) {
    let button = $(event.relatedTarget); // Button that triggered the modal
    companyPercentageId = button.data('review-id');
    relationFileId = button.data('relation-id');
    typeRelation = button.data('type');
    fullName = button.data('name');
    typeGroup = button.data('group');
    percentageValue = button.data('percentage');
    $('#percentageTitle').text(fullName + '- Edit percentage');
    $('#modalInputPercentage').val(percentageValue);
});
$editButton.click(async function () {
    percentage = $('#modalInputPercentage').val();
    if (!myForm.checkValidity()) {
    if (myForm.reportValidity) {
      myForm.reportValidity();
      return;
    }
    }
    $.ajax({
        type: 'POST',
        url: '/file-reviewer/open-file-review/'+ companyPercentageId + '/edit-percentage',
        data: { companyId: companyPercentageId, relationId: relationFileId,
        type: typeRelation, group: typeGroup, percentage: percentage },
        success: () => {
            Swal.fire('Success', 'The percentage has been edited successfully', 'success').then(() => {
                location.href = '/file-reviewer/open-file-review/'+ companyPercentageId + '/beneficial-owners';
        });
        },
        error: (err) => {
            Swal.fire('Error', 'There was an error editing the percentage', 'error').then(() => {
                location.href = '/file-reviewer/open-file-review/'+ companyPercentageId + '/beneficial-owners';
        });
        },
    });
});