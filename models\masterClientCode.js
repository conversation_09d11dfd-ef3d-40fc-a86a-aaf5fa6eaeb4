const mongoose = require('mongoose');

const invoiceItemSchema = new mongoose.Schema({
  title: { type: String, required: false },
  description: { type: String, required: true },
  value: { type: Number, required: true },
});

const feesSchema = new mongoose.Schema({
  fees: [invoiceItemSchema],
  disbursements : [invoiceItemSchema],
});

const MasterClientCodeSchema = new mongoose.Schema(
  {
    code: { type: String, required: true, max: 100 },
    owners: [String],
    date_invited: { type: Date, required: false },
    partitionkey: { type: String, required: true },
    customIncorporationPayment: { type: Boolean, required: false, default: false },
    customFinancialPayment: { type: Boolean, required: false, default: false },
    incorporation: { type: feesSchema, required: false },
    financialReport: { type: feesSchema, required: false },
    hasFiles: { type: Boolean, required: false, default: false },
    openedFiles: { type: Boolean, required: false, default: false },
  }
);


//Export model
module.exports = mongoose.model('MasterClientCode', MasterClientCodeSchema);
