exports.validate = function(entry) {
    const errors = [];
    if (!entry.confirmation.confirmed) {
        errors.push({msg: "Confirm the submission", field: "confirmed"})     
    }
    if (!entry.confirmation.confirmed_authority) {
        errors.push({msg: "Confirm the submission", field: "confirmed_authority"})     
    }
    if (!entry.confirmation.confirmed_conditions) {
        errors.push({msg: "Confirm the submission", field: "confirmed_conditions"})     
    }
    if (!entry.confirmation.confirmed_payment) {
        errors.push({msg: "Confirm the submission", field: "confirmed_payment"})     
    }
    if (entry.confirmation.user_fullname == undefined || entry.confirmation.user_fullname.trim().length == 0) {
        errors.push({msg: "Provide full name", field: "user_fullname"})     
    }
    if (entry.confirmation.user_phonenumber == undefined || entry.confirmation.user_phonenumber.trim().length == 0) {
        errors.push({msg: "Provide phone number", field: "user_phonenumber"})     
    }

    if (entry.confirmation.relation_to_entity == undefined || entry.confirmation.relation_to_entity.trim().length == 0) {
        errors.push({msg: "Provide relation to entity", field: "relation_to_entity"})     
    } else if (entry.confirmation.relation_to_entity == "Other (please specify)" && (entry.confirmation.relation_to_entity_other == undefined || entry.confirmation.relation_to_entity_other.trim().length == 0)) {
        errors.push({msg: "Specify relation to entity", field: "relation_to_entity"}) 
    }

    return errors;
}