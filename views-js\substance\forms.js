$(document).ready(function () {
    $("#searchText").on("keyup", function () {
        var value = $(this).val().toLowerCase();
        $(".container .col-lg-4").filter(function () {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
        });
    });
});

$(document).ready(function () {
    $('.deleteForm').on('submit', function (e) {
        event.preventDefault();
        var form = this;
        Swal.fire(
            {
                title: "Are you sure?",
                text: "You won't be able to revert this!",
                icon: "warning",
                showCancelButton: !0,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                confirmButtonText: "Yes, delete it!"
            }).then(function (t) {
                if (t.value) {
                    form.submit()
                }
            })
    });
});

$(document).on('click', '.showReopenReason', function () {
    showReopenReason($(this).attr('data-id'), $(this).attr('data-reopen-id'))
})

$(document).on('click', '.reopenForm', function () {
    reopenForm($(this).attr('data-id'))
})
function reopenForm(id) {
    Swal.fire(
        {
            title: "Are you sure you want to reopen this submission to edit?",
            text: "Submission will no longer be scheduled and will be returned to SAVED status",
            icon: "warning",
            showCancelButton: !0,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Accept"
        }).then(function (t) {
            if (t.value) {
                window.location.href = "./forms/" + id + "/select";
            }
        })
}

function showReopenReason(id, reopenedId) {
    $(".open-reopen-reason-btn").prop('disabled', true);
    $.ajax({
        type: 'GET',
        url: './forms/' + id + '/reopened-info/' + reopenedId,
        timeout: 5000,
        success: function (data) {
            if (data.status === 200) {
                Swal.fire({
                    title: "Re-opened submission",
                    icon: "warning",
                    backdrop: true,
                    showCancelButton: true,
                    confirmButtonColor: "#005C81",
                    confirmButtonText: 'Continue',
                    reverseButtons: true,
                    allowOutsideClick: false,
                    html: "<div class='text-justify'> Please note the following message from your Trident Trust Officer: " + data.reopenedData.reason + "</div> " +
                        (data.reopenedData.change_financial_period_dates === true ?
                            "<hr> " +
                            "<div class='text-justify'> <h5>  Note that your Financial Period has been changed by a " +
                            "Trident Officer according to the ITA guidelines. Please make sure the information on " +
                            "the remaining pages of this submission is correct and in accordance with the amended dates.  </h5> </div>" :
                            ""),
                }).then((result) => {
                    if (result.isConfirmed){
                        window.location.href = "./forms/" + id + "/select";
                    }else{
                        $(".open-reopen-reason-btn").prop('disabled', false);
                    }
                });
            } else {
                Swal.fire('Error', 'There was an error opening the re-open submission... Please try again later.', 'error').then(() => {
                    $(".open-reopen-reason-btn").prop('disabled', false);
                });
            }

        },
        error: function (err) {
            console.log(err);
            Swal.fire('Error', 'There was an error opening the re-open submission... Please try again later.', 'error').then(() => {
                $(".open-reopen-reason-btn").prop('disabled', false);
            });
        },
    });


}
