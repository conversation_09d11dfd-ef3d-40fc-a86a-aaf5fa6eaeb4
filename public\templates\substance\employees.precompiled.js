(function() {
  var template = Handlebars.template, templates = Handlebars.templates = Handlebars.templates || {};
templates['employees'] = template({"1":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var helper, alias1=depth0 != null ? depth0 : (container.nullContext || {}), alias2=container.hooks.helperMissing, alias3="function", alias4=container.escapeExpression, alias5=container.lambda, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return "    <tr id=\"employees-table-row-"
    + alias4(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":2,"column":32},"end":{"line":2,"column":39}}}) : helper)))
    + "\" >\r\n        <td> "
    + alias4(((helper = (helper = lookupProperty(helpers,"name") || (depth0 != null ? lookupProperty(depth0,"name") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"name","hash":{},"data":data,"loc":{"start":{"line":3,"column":13},"end":{"line":3,"column":21}}}) : helper)))
    + " </td>\r\n        <td> "
    + alias4(((helper = (helper = lookupProperty(helpers,"qualification") || (depth0 != null ? lookupProperty(depth0,"qualification") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"qualification","hash":{},"data":data,"loc":{"start":{"line":4,"column":13},"end":{"line":4,"column":30}}}) : helper)))
    + " </td>\r\n        <td> "
    + alias4(((helper = (helper = lookupProperty(helpers,"experience_years") || (depth0 != null ? lookupProperty(depth0,"experience_years") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"experience_years","hash":{},"data":data,"loc":{"start":{"line":5,"column":13},"end":{"line":5,"column":33}}}) : helper)))
    + " </td>\r\n        <td class=\"justify-content-center d-flex d-flex-inline\">\r\n            <button type=\"button\" class=\"btn btn-sm royal-blue solid mr-1\" data-activity-type=\""
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"activityType") : depths[1]), depth0))
    + "\"\r\n                data-id=\""
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"entryId") : depths[1]), depth0))
    + "\" data-employee-id=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":8,"column":59},"end":{"line":8,"column":66}}}) : helper)))
    + "\" data-toggle=\"modal\" data-target=\"#employeeModal\">\r\n                <i class=\"fa fa-pencil\"></i>\r\n            </button>\r\n            <button type=\"button\" class=\"btn btn-sm btn-danger deleteEmployee\"\r\n                    data-activity-type=\""
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"activityType") : depths[1]), depth0))
    + "\"\r\n                    data-id=\""
    + alias4(alias5((depths[1] != null ? lookupProperty(depths[1],"entryId") : depths[1]), depth0))
    + "\" data-employee-id=\""
    + alias4(((helper = (helper = lookupProperty(helpers,"_id") || (depth0 != null ? lookupProperty(depth0,"_id") : depth0)) != null ? helper : alias2),(typeof helper === alias3 ? helper.call(alias1,{"name":"_id","hash":{},"data":data,"loc":{"start":{"line":13,"column":63},"end":{"line":13,"column":70}}}) : helper)))
    + "\">\r\n                <i class=\"fa fa-times\"></i>\r\n            </button>\r\n        </td>\r\n    </tr>\r\n";
},"3":function(container,depth0,helpers,partials,data) {
    return "        <tr>\r\n            <td colspan=\"4\">\r\n                No employees found\r\n            </td>\r\n        </tr>\r\n";
},"compiler":[8,">= 4.3.0"],"main":function(container,depth0,helpers,partials,data,blockParams,depths) {
    var stack1, lookupProperty = container.lookupProperty || function(parent, propertyName) {
        if (Object.prototype.hasOwnProperty.call(parent, propertyName)) {
          return parent[propertyName];
        }
        return undefined
    };

  return ((stack1 = lookupProperty(helpers,"each").call(depth0 != null ? depth0 : (container.nullContext || {}),(depth0 != null ? lookupProperty(depth0,"employees") : depth0),{"name":"each","hash":{},"fn":container.program(1, data, 0, blockParams, depths),"inverse":container.program(3, data, 0, blockParams, depths),"data":data,"loc":{"start":{"line":1,"column":0},"end":{"line":24,"column":9}}})) != null ? stack1 : "")
    + "\r\n\r\n";
},"useData":true,"useDepths":true});
})();