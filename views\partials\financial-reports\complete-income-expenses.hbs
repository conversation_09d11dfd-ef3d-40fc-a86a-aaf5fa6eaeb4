<div class="col-lg-12">
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-md-12">
                    <h4>
                        <label class="mb-2 lbl-read-only color-black cursor-default" for="authShares">
                            STATEMENT OF INCOME AND EXPENSES FOR THE PERIOD FROM 
                            <span class="startPrdEndText">{{formatDate report.financialPeriod.start "YYYY-MM-DD"}}</span> TO <span class="finPrdEndText">{{formatDate report.financialPeriod.end "YYYY-MM-DD"}}</span>
                        </label>
                    </h4>
                </div>
            </div>
            <br>
            <div class="row">
                <div class="col-md-8">
                    <h4>
                        <label for="completeReportCurrency" class="mb-2 lbl-read-only color-black cursor-default">
                            Reporting currency:
                        </label>
                    </h4>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3 input-group">
                        <select name="completeReportCurrency" id="completeReportCurrency" class="form-control w-100" data-toggle="select2" data-value="{{report.currency}}">
                            {{#each currencies}}
                            <option value="{{cc}}" {{#if ../report.currency}} {{#ifEquals cc ../report.currency }} selected
                                {{/ifEquals}} {{else}} {{#ifEquals cc 'USD' }} selected {{/ifEquals}} {{/if}}>
                                {{cc}} - {{name}}
                            </option>
                            {{/each}}
            
                        </select>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <h4>Income</h4>
                </div>
            </div>
        
            <div class="row" id="completeDetailsRevenueRow">
                <div class="col-md-8">
                    <div class="form-group ml-2">
                        <label class="lbl-read-only mb-0" for="completeDetailsRevenue">Revenue</label> 
                        <span>
                            <small class="font-weight-bold">Please consider: Dividend income, Coupon interest income, Bank Interest Income, Loan interest income, Sale of Goods /
                            Services, Gain from the sale of Investments / Listed Financial Assets / Tangible / Intangible Assets, Unrealized Gain on
                            the Market Value of Listed Financial Assets or any other type of income.</small>
                        </span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group input-group mb-1">
                        <div class="input-group-prepend">
                            <span class="input-group-text prepend-currency complete-currency"> {{report.currency}}</span>
                        </div>
                        <input type="text" id="completeDetailsRevenue" class="form-control autonumber text-right complete-income" data-a-sep="," 
                            placeholder="0.00"  value="{{report.completeDetails.income.total}}"
                            data-m-dec="2" name="completeDetails[revenue]">
                    </div>
                </div>
            </div>

            <div class="row" id="completeDetailsCostOfSalesRow">
                <div class="col-md-8">
                    <div class="form-group ml-2" >
                        <label class="lbl-read-only mb-0" for="completeDetailsCostOfSales">Cost of Sales </label>
                        <span>
                            <small class="font-weight-bold">Please consider the direct costs incurred for the generation of income:
                                Purchase of Goods / Services, Loss from the sale
                                of Investments / Listed Financial Assets / Tangible / Intangible Assets, Unrealized Loss on the Market Value of Listed
                                Financial Assets, Loan interest expense, etc.</small>
                        </span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group input-group mb-1">
                        <div class="input-group-prepend" >
                            <span class="input-group-text prepend-currency complete-currency"> {{report.currency}}</span>
                        </div>
                        <input type="text" id="completeDetailsCostOfSales" class="form-control autonumber text-right complete-income" data-a-sep="," placeholder="0.00"
                            placeholder="0.00"  value="{{report.completeDetails.income.costOfSales}}"
                            data-m-dec="2" name="completeDetails[costOfSales]">
                    </div>
                </div>
            </div>

            <br>
            <div class="row" id="completeDetailsGrossProfitRow">
                <div class="col-md-8">
                    <div class="form-group ml-2">
                        <label class="lbl-read-only"  for="completeDetailsGrossProfit">
                            <b>GROSS PROFIT</b>
                        </label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group input-group mb-1">
                        <div class="input-group-prepend">
                            <span class="input-group-text prepend-currency complete-currency"> {{report.currency}}</span>
                        </div>
                        <input type="text" id="completeDetailsGrossProfit" class="form-control autonumber text-right " data-a-sep=","
                            placeholder="0.00"  
                            data-m-dec="2" name="completeDetails[grossProfit]" value="{{report.completeDetails.grossProfit}}" readonly>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <h4>Expenses</h4>
                </div>
            </div>

            <div class="row" id="completeDetailsOperatingExpensesRow">
                <div class="col-md-8">
                    <div class="form-group ml-2">
                        <label class="lbl-read-only mb-0" for="completeDetailsOperatingExpenses">Operating Expenses</label>
                        <span>
                            <small class="font-weight-bold">Please consider the expenses incurred regarding the Company’s operational activities: Professional fees paid, Company
                            Administration fees, Portfolio management fees and related Services, etc.</small>
                        </span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group input-group mb-1">
                        <div class="input-group-prepend">
                            <span class="input-group-text prepend-currency complete-currency"> {{report.currency}}</span>
                        </div>
                        <input type="text" id="completeDetailsOperatingExpenses" class="form-control autonumber text-right complete-expenses"
                            placeholder="0.00"  value="{{report.completeDetails.expenses.operatingExpenses}}"
                            data-a-sep="," data-m-dec="2" name="completeDetails[operatingExpenses]">
                    </div>
                </div>
            </div>
            
            <div class="row" id="completeDetailsTotalOtherExpensesRow">
                <div class="col-md-8">
                    <div class="form-group ml-2">
                        <label class="lbl-read-only mb-0" for="completeDetailsTotalOtherExpenses">Other Expenses</label>
                        <span>
                            <small class="font-weight-bold">
                                Please consider the expenses that are not related to the activities of the Company: Balances Written-Off, Bank fees, Government fees, Impairment expense, Accounting / Audit Fees, Foreign exchange loss, etc.
                            </small>
                        </span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group input-group mb-1">
                        <div class="input-group-prepend">
                            <span class="input-group-text prepend-currency complete-currency"> {{report.currency}}</span>
                        </div>
                        <input type="text" id="completeDetailsTotalOtherExpenses"
                            class="form-control autonumber text-right complete-expenses" placeholder="0.00"
                            value="{{report.completeDetails.expenses.totalOtherExpenses}}" data-a-sep="," data-m-dec="2"
                            name="completeDetails[totalOtherExpenses]">
                    </div>
                </div>
            </div>

            <div class="row" id="completeDetailsIncomeTaxExpenseRow">
                <div class="col-md-8">
                    <div class="form-group ml-2">
                        <label class="lbl-read-only" for="completeDetailsIncomeTaxExpense">Income Tax Expenses</label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group input-group mb-1">
                        <div class="input-group-prepend">
                            <span class="input-group-text prepend-currency complete-currency"> {{report.currency}}</span>
                        </div>
                        <input type="text" id="completeDetailsIncomeTaxExpense" class="form-control autonumber text-right complete-expenses"
                            placeholder="0.00" value="{{report.completeDetails.expenses.incomeTax}}" data-a-sep="," data-m-dec="2"
                            name="completeDetails[incomeTax]">
                    </div>
                </div>
            </div>
            
            <br>

            <div class="row" id="completeDetailsExpensesTotalRow">
                <div class="col-md-8">
                    <div class="form-group ml-2">
                        <label class="lbl-read-only" for="completeDetailsExpensesTotal">
                            <b>TOTAL EXPENSES</b>
                        </label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group input-group mb-1">
                        <div class="input-group-prepend">
                            <span class="input-group-text prepend-currency complete-currency"> {{report.currency}}</span>
                        </div>
                        <input type="text" id="completeDetailsExpensesTotal" class="form-control autonumber text-right" data-a-sep=","
                            data-m-dec="2" name="completeDetails[expensesTotal]"
                            placeholder="0.00"  
                            value="{{report.completeDetails.expenses.totalExpenses}}" readonly>
                    </div>
                </div>
            </div>

            <hr>

            <div class="row" id="completeDetailsNetIncomeRow">
                <div class="col-md-8">
                    <div class="form-group ml-2">
                        <label class="lbl-read-only" for="completeDetailsNetIncome"> <b>NET INCOME</b>
                        </label>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group input-group mb-1">
                        <div class="input-group-prepend">
                            <span class="input-group-text prepend-currency complete-currency"> {{report.currency}}</span>
                        </div>
                        <input type="text" id="completeDetailsNetIncome" class="form-control autonumber net-income-value text-right"
                            data-a-sep="," data-m-dec="2" name="completeDetails[netIncome]"
                            placeholder="0.00" 
                            value="{{report.completeDetails.netIncome}}" readonly>
                    </div>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-md-12">
                    <div class="progress">
                        <div class="progress-bar width-28" role="progressbar" aria-valuenow="2" aria-valuemin="0"
                            aria-valuemax="5">2 of 5
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
<script type="text/javascript" src="/views-js/partials/financial-reports/complete-income-expenses.js"></script>