let selectedFiles = [];
let table;

$(document).ready(function () {
    table = $("#file-datatable").DataTable({
        order: [0, 'asc'],
        "scrollX": !0,
        select: {
            style: 'single',
            selector: 'tr>td:nth-child(1)'
        },
        "columnDefs": [{
            orderable: false,
            targets: [1, 2]
        },
        { "visible": false, "targets": [0] }],
        pageLength: 100,
        lengthMenu: [10, 25, 50, 100, 500],
        language: {
            paginate: {
                previous: "<i class='mdi mdi-chevron-left'>",
                next: "<i class='mdi mdi-chevron-right'>",
            },
        },
        drawCallback: function () {
            $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
        },
    });
});