let outsourcingProviderId = '';
let isEditOutsourcingProvider = false;
let resetOutsourcingProviderForm = false;

$('#outsourcingProviderModal').on('show.bs.modal', function (event) {
    $('#loadingOutsourcingProvider').hide();
    const button = $(event.relatedTarget); // Button that triggered the modal
    const activityType = button.data('activity-type');
    const entityId = button.data('id');

    outsourcingProviderId = button.data('provider-id');
    resetOutsourcingProviderForm = false;
    $("#activityType").val(activityType);

    if (outsourcingProviderId) {
        isEditOutsourcingProvider = true;

        const queryString = $.param({
            type: activityType
        });

        $.ajax({
            type: "GET",
            url: `/substance/entry/${entityId}/activity-business?${queryString}`,
            success: (response) => {
                if (response.status === 200) {
                    const outsourcingProviders = response.data?.outsourcing_providers || [];
                    if (outsourcingProviders.length > 0) {
                        const outsourcingProvider = outsourcingProviders.find((b) => b._id === outsourcingProviderId);
                        if (outsourcingProvider) {
                            $("#outsourcingEntityName").val(outsourcingProvider.entity_name);
                            $("#outsourcingResourceDetails").val(outsourcingProvider.resource_details);
                            $("#outsourcingStaffCount").val(outsourcingProvider.staff_count);
                            $("#outsourcingHoursPerMonth").val(outsourcingProvider.hours_per_month);

                            if (outsourcingProvider.monitoring_control === true) {
                                $("#outsourcingMonitoringControlYes").prop('checked', true).trigger('change');
                            } else {
                                $("#outsourcingMonitoringControlNo").prop('checked', true).trigger('change');
                            }
                        }
                    }

                } else {
                    Swal.fire('Error', 'There was an error getting the information.', 'error').then(() => {
                        $('#outsourcingProviderModal').modal('hide');
                    });
                }
            },
            error: (err) => {
                Swal.fire('Error', 'There was an error getting the information.', 'error').then(() => {
                    $('#outsourcingProviderModal').modal('hide');
                });
            },
        });
    } else {
        isEditOutsourcingProvider = false;
    }

    if (isEditOutsourcingProvider) {
        $("#outsourcingProviderModalLbl").html('Edit Outsourcing Provider');
    } else {
        $("#outsourcingProviderModalLbl").html('New Outsourcing Provider');
    }
});

$('#outsourcingProviderModal').on('hidden.bs.modal', function (event) {
    resetOutsourcingProviderForm = true;
    $("#submitOutsourcingProvider").prop('disabled', false);
    $("#outsourcingProviderForm")[0].reset();
    $("#outsourcingStaffCount").val('');
    $("#outsourcingHoursPerMonth").val('');
});

$('#outsourcingProviderForm').on('submit', async function (event) {
    event.preventDefault();
    $("#submitOutsourcingProvider").prop('disabled', true);

    if (!this.checkValidity()) {
        this.classList.add('was-validated');
        $("#submitOutsourcingProvider").prop('disabled', false);
        return false;
    }


    $('#submitOutsourcingProvider').hide();
    $('#loadingOutsourcingProvider').show();

    const responseSave = await saveOutsourcingProvider();

    $('#submitOutsourcingProvider').show();
    $('#loadingOutsourcingProvider').hide();

    if (responseSave) {
        refreshOutsourcingProviderTable(responseSave.entryId, responseSave.activityType);

        Swal.fire('Success', 'Outsourcing Provider saved successfully.', 'success').then(() => {
            $('#outsourcingProviderModal').modal('hide');
        });
    }
    $("#submitOutsourcingProvider").prop('disabled', false);
});

async function saveOutsourcingProvider() {
    try {
        const outsourcingProvider = {
            type: $("#activityType").val(),
            entityName: $("#outsourcingEntityName").val(),
            resourceDetails: $("#outsourcingResourceDetails").val(),
            staffCount: $("#outsourcingStaffCount").val(),
            hoursPerMonth: $("#outsourcingHoursPerMonth").val(),
            monitoringControl: $("input[name='outsourcingMonitoringControl']:checked").val(),
        };
        
        const response = await $.ajax({
            type: isEditOutsourcingProvider ? "PUT" : "POST",
            url: `./outsourcing-providers${isEditOutsourcingProvider ? `/${outsourcingProviderId}` : ''}`,
            data: JSON.stringify(outsourcingProvider),
            dataType: "json",
            contentType: "application/json; charset=utf-8",
            
        });

        if (response.status === 200) {
            return response
        } else {
            const error = response.error || 'Outsourcing provider could not be saved, please try again later.';
            toastr["warning"](error, 'Error!');
            return false;
        }
    } catch (error) {
        toastr["warning"](error.responseJSON.error || 'Outsourcing provider could not be saved, please try again later.', 'Error!');
        return false;
    }
}

$(document).on('click', '.deleteOutsourcingProvider', function () {
    deleteOutsourcingProvider($(this).attr('data-entry-id'),$(this).attr('data-activity-type'),$(this).attr('data-id'))
})

async function deleteOutsourcingProvider(entryId, activityType, outsourcingProviderId) {
    
    const result = await Swal.fire({
        icon: 'warning',
        title: 'Are you sure?',
        showCancelButton: true,
        confirmButtonText: 'Yes, delete it',
        reverseButtons: true,
        showLoaderOnConfirm: true,
        allowOutsideClick: () => !Swal.isLoading(),
        backdrop: true,
        preConfirm: async () => {
            try {
                
                const response = await $.ajax({
                    type: 'DELETE',
                    url: `/substance/entry/${entryId}/outsourcing-providers/${activityType}/${outsourcingProviderId}`,
                    dataType: 'json',
                });

                if (response.status === 200) {
                    return response;
                } else {
                    Swal.fire('Error', response.error ? response.error : 'Error deleting the Outsourcing Provider', 'error');
                    return false;
                }
            } catch (error) {
                Swal.fire('Error', 'An error occurred while deleting the Outsourcing Provider', 'error');
                return false;
            }
        }
    });

    if (result.isConfirmed && result.value) {
        refreshOutsourcingProviderTable(entryId, activityType);
        Swal.fire('Success', result.value?.message ? result.value.message : 'Outsourcing Provider deleted successfully.', 'success');
    }
}

function refreshOutsourcingProviderTable(entryId, activityType) {
    let template = Handlebars.templates.outsourcingproviders;
    const queryString = $.param({ type: activityType });


    $.ajax({
        type: "GET",
        url: `/substance/entry/${entryId}/activity-business?${queryString}`,
        success: (response) => {
            if (response.status === 200) {
                let rows = template({
                    outsourcingProviders: response.data?.outsourcing_providers || [],
                    entryId: entryId,
                    activityType: activityType
                });
                $("#outsourcingProvidersTableBody").html(rows);
            } else {
                toastr["error"]('There was an error getting the Outsourcing Providers.', 'Error');
            }
        },
        error: (err) => {
            toastr["error"]('There was an error getting the Outsourcing Providers.', 'Error');
        },
    });
}