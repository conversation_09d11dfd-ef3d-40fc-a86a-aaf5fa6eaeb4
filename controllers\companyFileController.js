const httpConstants = require('http2').constants;
const RequestedFileModel = require('../models/requestedFile');
const {filereview: FileReviewModel} = require('../models/filereview');
const ClientReviewModel = require('../models/client');
const EntryModel = require('../models/entry').EntryModel;
const uploadController = require('../controllers/uploadController');
const mailController = require('../controllers/mailController');
const mailFormatter = require('../controllers/mailFormatController');
const FinancialReportModel = require('../models/financial-report')
const {v4: uuidv4} = require('uuid');
const moment = require('moment');
const verifyExtFile = require('../utils/verifyExtensionFile');
const { REPORT_REQUEST_INFO_STATUS } = require('../utils/financialReportConstants.js')

exports.getRequestedFilesDashboard = async function (req, res, next) {
  try {
    req.session.company = {};

    let filter = {masterClientCode: req.params.masterclientcode};
    let filterSubstance = { 'company_data.masterclientcode': req.params.masterclientcode};
    if (req.query.filter_company && req.query.filter_company.length > 2) {
      filter['companyName'] = {$regex: req.query.filter_company, $options: 'i'};
      filterSubstance['company_data.name'] = {$regex: req.query.filter_company, $options: 'i'};
    }
    if (req.query.filter_company_code && req.query.filter_company_code.length > 2) {
      filter['companyCode'] = {$regex: req.query.filter_company_code, $options: 'i'};
      filterSubstance['company'] = {$regex: req.query.filter_company_code, $options: 'i'};
      filterSubstance['company_data.incorporationcode'] = {$regex: req.query.filter_company_code, $options: 'i'};
    }
    filter['status'] = {"$in": ["IN PROGRESS", "NOT STARTED"]};
    filterSubstance['status'] = 'INFORMATION REQUEST';
    const results = await RequestedFileModel.find(filter);
    let substanceResults = await EntryModel.find(filterSubstance, {'_id': 1, 'company_data': 1, 'company': 1, 'requested_information': 1, 'entity_details': 1});
    if (results && results.length > 0){
      results.sort((a,b)=> b.createdAt -  a.createdAt);
    }

    if (substanceResults && substanceResults.length > 0){
      
      substanceResults = substanceResults.map((sResult) => {
        const requestedInformationList = sResult.requested_information?.details || [];

        if (requestedInformationList?.length > 0){
          const lastRequestInfo = requestedInformationList[requestedInformationList?.length - 1];
          console.log("lastRequestInfo:", lastRequestInfo)
          sResult.deadline_at = lastRequestInfo?.deadline_at ? lastRequestInfo.deadline_at : moment(lastRequestInfo.requested_at).utc().add(2, "weeks").toDate();
        }

        return sResult;
      });

      substanceResults.sort((a,b)=> a.deadline_at -  b.deadline_at);
    }

    const financialReportFilter= {
      masterClientCode: req.params.masterclientcode,
      status: 'INFORMATION REQUEST'
    }
    const financialReportResults = await FinancialReportModel.find(financialReportFilter,  {'_id': 1, 'companyData': 1, 'financialPeriod': 1, 'requestedInformation.details': { $slice: -1 } })
  
    res.render('company-requested-files/list', {
      title: `Requested Files for Master Client Code ${req.params.masterclientcode}`,
      masterClientCode:req.params.masterclientcode,
      user: req.user,
      results,
      substanceResults,
      financialReportResults
    });
  } catch (e) {
    console.log(e);
    return next(e);
  }

};


exports.getRequestedFileView = async function (req, res, next) {
  try {

    let companyRequestFile = await  RequestedFileModel.findOne({_id: req.params.requestId, masterClientCode: req.params.masterclientcode });

    if (!companyRequestFile){
      let err = new Error('Company request not found');
      err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
      return next(err);
    }

    if (companyRequestFile.status === 'SUBMITTED' || companyRequestFile.status === 'CANCELLED') {
      return res.render('error', { message: 'This request is not longer active.' });
    }

    if (companyRequestFile.status === "NOT STARTED"){
      companyRequestFile.status = "IN PROGRESS";
      await companyRequestFile.save();
    }

    req.session.requestedTempFiles = {};
    res.render('company-requested-files/open-requested-file-view', {
      title: `Requested files for company ${companyRequestFile.companyName}`,
      masterClientCode:req.params.masterclientcode,
      user: req.user,
      companyRequest: companyRequestFile
    });
  } catch (e) {
    console.log(e);
    return next(e);
  }

};

exports.getRequestedFileViewSubstance = async function (req, res, next) {
    try {

      let entry = await EntryModel.findOne({_id: req.params.entryId, 'company_data.masterclientcode': req.params.masterclientcode, company: req.params.company });

      if (!entry){
        let err = new Error('Information request not found');
        err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
        return next(err);
      }

      if (entry.status !== 'INFORMATION REQUEST') {
        return res.render('error', { message: 'This request is not longer active.' });
      }


      req.session.requestedTempFiles = {};
      const requestedInformationList = entry.requested_information?.details || [];

      res.render('company-requested-files/open-substance-requested-files', {
        title: `Requested files for company ${entry.company_data.name}`,
        masterClientCode: req.params.masterclientcode,
        user: req.user,
        request: requestedInformationList[requestedInformationList?.length - 1],
        entry
      });
    } catch (e) {
      console.log(e);
      return next(e);
    }

  };

exports.getRequestedFileViewFinancialReport = async function(req, res, next) {
  try {

    let report = await FinancialReportModel.findOne({_id: req.params.reportId, 'companyData.masterclientcode': req.params.masterclientcode});

    if (!report){
      let err = new Error('Information request not found');
      err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
      return next(err);
    }

    if (report.status !== 'INFORMATION REQUEST') {
      return res.render('error', { message: 'This request is not longer active.' });
    }


    req.session.requestedTempFiles = {};
    const requestedInformationList = report.requestedInformation?.details || [];
    console.log(requestedInformationList)

    res.render('company-requested-files/open-financial-report-requested-files', {
      title: `Requested files for company ${report.companyData.name}`,
      masterClientCode: req.params.masterclientcode,
      user: req.user,
      request: requestedInformationList[requestedInformationList?.length - 1],
      report
    });
  } catch (e) {
    console.log(e);
    return next(e);
  } 
}
 
exports.saveRequestedFileInfo = async function (req, res) {
  try {
    const companyRequest = await RequestedFileModel.findOne({_id: req.params.requestId, masterClientCode: req.params.masterclientcode});
    if (!companyRequest) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({status: httpConstants.HTTP_STATUS_NOT_FOUND, message: 'Company Request not found'});
    }

    if (companyRequest.status === 'SUBMITTED' || companyRequest.status === 'CANCELLED') {
      return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
        message: 'This request is not longer active.' });
    }

    if (req.session && req.session.requestedTempFiles){
      const requestTempFiles = req.session.requestedTempFiles;

      for (const [key, value] of Object.entries(requestTempFiles)) {
        const fileIndex = companyRequest.files.findIndex((f) => f.id === key);

        if (fileIndex > -1){
          const files =companyRequest.files[fileIndex];
          files.uploadFiles = [...files.uploadFiles, ...value];
          if (files.uploadFiles.length > 0){
            files.present = true;
          }
          companyRequest.files[fileIndex] = files
        }
      }
    }

    if (req.body.comment){
      companyRequest.comments = req.body.comment;
    }

    if (req.body.status && req.body.status.toLowerCase() === "submitted"){
      let hasFiles = companyRequest.files.filter((f) => f && f.uploadFiles.length > 0).map((f) => {return f.name});

      if (hasFiles.length !== companyRequest.files.length){
        return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({status: httpConstants.HTTP_STATUS_BAD_REQUEST,
          message: 'Please upload all the required files.'});
      }

      companyRequest.status = "SUBMITTED";
      companyRequest.submittedBy = req.user.email.toLowerCase();
      companyRequest.submittedAt = new Date();

      let fileReview = await FileReviewModel.findOne({_id: companyRequest.referenceId, masterClientCode: companyRequest.masterClientCode});

      if (!fileReview){
        return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({status: httpConstants.HTTP_STATUS_NOT_FOUND,
          message: 'File Review not found'});
      }

      let clientReview = await ClientReviewModel.findById(fileReview.clientId);

      if (!clientReview){
        return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({status: httpConstants.HTTP_STATUS_NOT_FOUND,
          message: 'File Review client not found'});
      }

      const submitComment = {
        username: req.user.email.toLowerCase(),
        role: 'CL',
        comment: 'Files submitted by client, Client comment: ' + (req.body.comment || ''),
        date: new Date(),
        from: 'CL',
        to: 'FR',
      };
      fileReview.comments.push(submitComment);

      //start move files from requested container to filereview container
      let blobNames = [];
      companyRequest.files.forEach((fileType) => {
        if (fileType.uploadFiles.length > 0){
          let fileNames = fileType.uploadFiles.map((f) => {return companyRequest._id.toString() +  "/" + f.blobName});
          blobNames = [...blobNames, ...fileNames];
        }
      });
      await uploadController.copyFilesFromContainerToOther(companyRequest._id.toString(), fileReview._id.toString(),
        process.env.AZURE_STORAGE_CONTAINER_FILEREVIEW_REQUESTED_FILES,  process.env.AZURE_STORAGE_CONTAINER_FILEREVIEW_UPLOADS, blobNames);

      let newFiles = [];
      for (let i = 0; i < companyRequest.files.length; i++) {
        const fileType = companyRequest.files[i].toObject();
        if (fileType && fileType.uploadFiles && fileType.uploadFiles.length > 0){
          let newFrFiles = fileType.uploadFiles.map((file) => {
            file.container =  process.env.AZURE_STORAGE_CONTAINER_FILEREVIEW_UPLOADS;
            file.url = file.url.replace(process.env.AZURE_STORAGE_CONTAINER_FILEREVIEW_REQUESTED_FILES,
              process.env.AZURE_STORAGE_CONTAINER_FILEREVIEW_UPLOADS)
              .replace(companyRequest._id.toString(), fileReview._id.toString());
            return file;
          });

          let index = fileReview.files.findIndex((f) => f.id === fileType.id);

          if (index > -1 && newFrFiles.length > 0){
            newFiles = [...newFiles, ...newFrFiles];
            let frFiles = fileReview.files[index];
            let fileReferences = newFrFiles.map((nf) => {return nf.fileId});
            frFiles.uploadFiles = [...frFiles.uploadFiles, ...fileReferences];
            frFiles.provided = true;
            fileReview.files[index] = frFiles;
          }
        }

      }

      if (newFiles.length > 0){
        clientReview.files = [...clientReview.files, ...newFiles];
        clientReview.markModified('files');
        await clientReview.save();
      }
      //end move files

      fileReview.markModified('files');
      fileReview.status = {
        code: "ASSIGNED",
        statusDate: new Date()
      };
      fileReview.updatedAt = new Date();
      await fileReview.save();

      let email = mailFormatter.generateProvidedRequestedFilesEmail(fileReview.companyName);
      await mailController.asyncSend(
        fileReview.fileReview.username,
        'Trident Trust Client Notification',
        email.textString,
        email.htmlString
      );
    }
    else{
      if (companyRequest.status !== 'SUBMITTED'){
        companyRequest.status = "IN PROGRESS"
      }
    }

    companyRequest.updatedAt = new Date();
    companyRequest.markModified('files');
    await companyRequest.save();

    return res.status(httpConstants.HTTP_STATUS_OK).json({status: httpConstants.HTTP_STATUS_OK, message: 'Information updated successfully'});
  } catch (error) {
    console.log("error: ", error);
    return res.status(500).end();
  }
};

exports.saveRequestedFileInfoSubstance = async function (req, res, next) {
    try {
        let entry = await EntryModel.findOne({ _id: req.params.entryId, 'company_data.masterclientcode': req.params.masterclientcode, company: req.params.company });

        if (!entry) {
            let err = new Error('Information request not found');
            err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
            return next(err);
        }

        if (entry.status !== 'INFORMATION REQUEST') {
            return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
                message: 'This request is not longer active.' });
        }

        if (!req.body.comment) {
            return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({status: httpConstants.HTTP_STATUS_BAD_REQUEST,
                message: 'Please provide the comment.' });
        }


        // Get last returned information
        const requestedInformationList = entry.requested_information?.details || [];
        const requestInformation = requestedInformationList[requestedInformationList?.length - 1];
        const newReturnInformation = {
            request_id: requestInformation.id,
            username: req.user.email.toLowerCase(),
            returned_at: new Date(),
            comment: req.body.comment,
            files: req.session.requestedTempFiles?.substance || []
        }
        let clientReturnedInformationList = entry.client_returned_information?.details || [];
        if (clientReturnedInformationList && clientReturnedInformationList.length > 0) {
          clientReturnedInformationList.push(newReturnInformation);
        } else {
          clientReturnedInformationList = [newReturnInformation];
        }
        entry.client_returned_information = { details: clientReturnedInformationList }

        requestInformation.status = 'RETURNED'
        entry.status = entry.payment && entry.payment.payment_received_at ? 'PAID' : 'SUBMITTED';
        if (entry.status === 'PAID') {
          let email = mailFormatter.generateClientSubmittedAttachmentsEmail(entry.company_data.name);
          await mailController.asyncSend(
            process.env.SUPER_USER_EMAIL_GROUP,
            'Trident Trust Client Notification',
            email.textString,
            email.htmlString
          );
        }

        await EntryModel.findOneAndUpdate({_id : req.params.entryId, company: req.params.company}, entry);
        let email = mailFormatter.generateProvidedRequestedFilesSubstanceEmail(entry.company_data.name, req.params.company);
        await mailController.asyncSend(
            requestInformation.username,
            'Trident Trust Client Notification',
            email.textString,
            email.htmlString
        );

        return res.status(httpConstants.HTTP_STATUS_OK).json({ 
          status: httpConstants.HTTP_STATUS_OK,
          message: 'Information updated successfully',
          uploadedFiles: req.session.requestedTempFiles?.substance?.length || 0
         });
    } catch (error) {
        console.log("error: ", error);
        return res.status(500).end();
    }
};

exports.saveRequestedFileViewFinancialReport = async function (req, res, next) {
  try {
      let report = await FinancialReportModel.findOne({ _id: req.params.reportId, 'companyData.masterclientcode': req.params.masterclientcode, 'companyData.code': req.params.company });

      if (!report) {
          let err = new Error('Information request not found');
          err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
          return next(err);
      }

      if (report.status !== 'INFORMATION REQUEST') {
          return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
              message: 'This request is not longer active.' });
      }

      if (!req.body.comment) {
          return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({status: httpConstants.HTTP_STATUS_BAD_REQUEST,
              message: 'Please provide the comment.' });
      }


      // Get last returned information
      const requestedInformationList = report.requestedInformation?.details || [];
      const requestInformation = requestedInformationList[requestedInformationList?.length - 1];
      const newReturnInformation = {
          requestId: requestInformation.id,
          username: req.user.email.toLowerCase(),
          returnedAt: new Date(),
          comment: req.body.comment,
          files: req.session.requestedTempFiles?.financialReport || []
      }
      let clientReturnedInformationList = report.clientResponseInformation?.details || [];
      if (clientReturnedInformationList && clientReturnedInformationList.length > 0) {
        clientReturnedInformationList.push(newReturnInformation);
      } else {
        clientReturnedInformationList = [newReturnInformation];
      }
      report.clientResponseInformation = { details: clientReturnedInformationList }
      requestInformation.status = REPORT_REQUEST_INFO_STATUS.RETURNED
      report.status = report.previousStatus;
      report.previousStatus = null
      await FinancialReportModel.findOneAndUpdate({_id : req.params.reportId, 'companyData.code': req.params.company}, report);

      let email = mailFormatter.generateProvidedRequestedFilesAccountingRecordsEmail(report.companyData.name, report.companyData.code);
      await mailController.asyncSend(
        requestInformation.username,
        'Trident Trust Client Notification',
        email.textString,
        email.htmlString
      );

      return res.status(httpConstants.HTTP_STATUS_OK).json({ status: httpConstants.HTTP_STATUS_OK, message: 'Information updated successfully' });
  } catch (error) {
      console.log("error: ", error);
      return res.status(500).end();
  }
};

exports.getFileList = async function (req, res) {
  try {
    const companyRequest = await RequestedFileModel.findOne({_id: req.params.requestId, masterClientCode: req.params.masterclientcode });
    if (!companyRequest) {
      return res.status(httpConstants.HTTP_STATUS_NOT_FOUND).json({result: false, error: "Company Request not found"});
    }
    let filesToReturn = [];
    if (companyRequest.files) {
      const files = companyRequest.files.find((f) => f.id === req.query.fileTypeId);
      filesToReturn = files && files.uploadFiles && files.uploadFiles.length > 0 ? files.uploadFiles : [];
    }
    if (req.session && req.session.requestedTempFiles){
      const requestTempFiles = req.session.requestedTempFiles[req.query.fileTypeId];
      if (requestTempFiles && requestTempFiles.length > 0){
        filesToReturn = [...filesToReturn, ...requestTempFiles];
      }

    }
    return res.json({result: true, files: filesToReturn});

  } catch (error) {
    console.log("error: ", error);
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
  }
};

exports.getFileListSubstance = async function (req, res, next) {
    try {
        let entry = await EntryModel.findOne({ _id: req.params.entryId, 'company_data.masterclientcode': req.params.masterclientcode, company: req.params.company });

        if (!entry) {
            let err = new Error('Information request not found');
            err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
            return next(err);
        }
        let filesToReturn = [];

        if (req.session && req.session.requestedTempFiles) {
            const requestTempFiles = req.session.requestedTempFiles.substance;
            if (requestTempFiles && requestTempFiles.length > 0) {
                filesToReturn = [...filesToReturn, ...requestTempFiles];
            }

        }
        return res.json({ result: true, files: filesToReturn });

    } catch (error) {
        console.log("error: ", error);
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
    }
};

exports.getFileListFinancialReport = async function (req, res, next) {
  try {
      let report = await FinancialReportModel.findOne({ _id: req.params.reportId, 'companyData.masterclientcode': req.params.masterclientcode, 'companyData.code': req.params.company }, {'_id':1});

      if (!report) {
          let err = new Error('Information request not found');
          err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
          return next(err);
      }
      let filesToReturn = [];

      console.log(req.session)
      if (req.session && req.session.requestedTempFiles) {
          const requestTempFiles = req.session.requestedTempFiles.financialReport;
          if (requestTempFiles && requestTempFiles.length > 0) {
              filesToReturn = [...filesToReturn, ...requestTempFiles];
          }

      }
      return res.json({ result: true, files: filesToReturn });

  } catch (error) {
      console.log("error: ", error);
      return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
  }
};

exports.saveTemporalFile = async function (req, res, next) {
  try {
    const companyRequest = await RequestedFileModel.findOne({_id: req.params.requestId, masterClientCode: req.params.masterclientcode });
    if (!companyRequest) {
      const err = new Error('Company Request not found');
      err.status = 404;
      return next(err);
    }

    let sessData = req.session;
    if (!sessData.requestedTempFiles) {
      sessData.requestedTempFiles = {}
    }

    let uploadedFiles = req.files['fileUploaded'];
    let newFiles = [];
    if (uploadedFiles && uploadedFiles.length > 0) {
      for (let i = 0; i < uploadedFiles.length; i++) {
        const itemToUpload = uploadedFiles[i];

        if (itemToUpload.mimetype !== 'application/pdf' ||
          !verifyExtFile.isValidExtensionFile(itemToUpload.originalname) ||
          !verifyExtFile.isValidExtensionFile(itemToUpload.blob) ||
          !verifyExtFile.isValidExtensionFile(itemToUpload.blobName)

        ) {
          console.log("Incorrect file type");
          return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({result: false, message: "Incorrect file type" });
        }
        uploadController.moveUploadFile(req.params.requestId, itemToUpload, req.body.fieldName,
          process.env.AZURE_STORAGE_CONTAINER_FILEREVIEW_REQUESTED_FILES).catch((reason => {
          if (reason) {
            console.log(reason);
          }
        }));
        let newFile = {
          fileId: uuidv4(),
          fileTypeId: req.body.fileTypeId,
          fieldName: itemToUpload.fieldname.replace(/fileUploaded/i, req.body.fieldName),
          blob: itemToUpload.blob.replace(/fileUploaded/i, req.body.fieldName),
          blobName: itemToUpload.blobName.replace(/fileUploaded/i, req.body.fieldName),
          url: itemToUpload.url.replace(/fileUploaded/i, req.params.requestId + '/' + req.body.fieldName),
          originalName: itemToUpload.originalname,
          encoding: itemToUpload.encoding,
          mimeType: itemToUpload.mimetype,
          container: itemToUpload.container,
          blobType: itemToUpload.blobType,
          size: itemToUpload.size,
          etag: itemToUpload.etag
        };

        newFiles.push(newFile);
      }

      if (sessData.requestedTempFiles && sessData.requestedTempFiles[req.body.fileTypeId]) {
        sessData.requestedTempFiles[req.body.fileTypeId] = [...sessData.requestedTempFiles[req.body.fileTypeId], ...newFiles];
      } else {
        sessData.requestedTempFiles[req.body.fileTypeId] = newFiles;
      }

    }

    return res.json({result: true});

  } catch (error) {
    console.log("error: ", error);
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
  }
};

exports.saveTemporalFileSubstance = async function (req, res, next) {
  try {
    let entry = await EntryModel.findOne({_id: req.params.entryId, 'company_data.masterclientcode': req.params.masterclientcode, company: req.params.company });

    if (!entry){
        let err = new Error('Information request not found');
        err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
        return next(err);
    }

    if (entry.status !== 'INFORMATION REQUEST') {
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
            message: 'This request is not longer active.' });
    }

    let sessData = req.session;
    if (!sessData.requestedTempFiles) {
      sessData.requestedTempFiles = {}
    }

    let uploadedFiles = req.files['fileUploaded'];
    let newFiles = [];
    if (uploadedFiles && uploadedFiles.length > 0) {
      for (let i = 0; i < uploadedFiles.length; i++) {
        const itemToUpload = uploadedFiles[i];

        if (itemToUpload.mimetype !== 'application/pdf' ||
          !verifyExtFile.isValidExtensionFile(itemToUpload.originalname) ||
          !verifyExtFile.isValidExtensionFile(itemToUpload.blob) ||
          !verifyExtFile.isValidExtensionFile(itemToUpload.blobName)
        ) {
          console.log("Incorrect file type");
          return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({result: false, message: "Incorrect file type" });
        }
        uploadController.moveUploadFile(req.params.entryId, itemToUpload, 'request-information',
          process.env.AZURE_STORAGE_CONTAINER).catch((reason => {
          if (reason) {
            console.log(reason);
          }
        }));
        let newFile = {
          fileId: uuidv4(),
          fileTypeId: req.body.fileTypeId,
          fieldname: itemToUpload.fieldname.replace(/fileUploaded/i, 'request-information'),
          blob: itemToUpload.blob.replace(/fileUploaded/i, 'request-information'),
          blobName: itemToUpload.blobName.replace(/fileUploaded/i, 'request-information'),
          url: itemToUpload.url.replace(/fileUploaded/i, req.params.entryId + '/' + 'request-information'),
          originalname: itemToUpload.originalname,
          encoding: itemToUpload.encoding,
          mimetype: itemToUpload.mimetype,
          container: itemToUpload.container,
          blobType: itemToUpload.blobType,
          size: itemToUpload.size,
          etag: itemToUpload.etag
        };


        newFiles.push(newFile);
      }

      if (sessData.requestedTempFiles && sessData.requestedTempFiles.substance) {
        sessData.requestedTempFiles.substance = [...sessData.requestedTempFiles.substance, ...newFiles];
      } else {
        sessData.requestedTempFiles.substance = newFiles;
      }

    }

    return res.json({result: true});

  } catch (error) {
    console.log("error: ", error);
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
  }
};

exports.saveTemporalFileFinancialReport = async function (req, res, next) {
    try {
      console.log(req.files)
      let report = await FinancialReportModel.findOne(
        {_id: req.params.reportId, 'companyData.masterclientcode': req.params.masterclientcode, 'companyData.code': req.params.company },
        {'_id': 1, 'status': 1});

    if (!report){
        let err = new Error('Information request not found');
        err.status = httpConstants.HTTP_STATUS_NOT_FOUND;
        return next(err);
    }

    if (report.status !== 'INFORMATION REQUEST') {
        return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).json({status: httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR,
            message: 'This request is not longer active.' });
    }

    let sessData = req.session;
    if (!sessData.requestedTempFiles) {
      sessData.requestedTempFiles = {}
    }

    let uploadedFiles = req.files['fileUploaded'];

    let newFiles = [];
    if (uploadedFiles && uploadedFiles.length > 0) {
      for (let i = 0; i < uploadedFiles.length; i++) {
        const itemToUpload = uploadedFiles[i];
        
        if (itemToUpload.mimetype !== 'application/pdf' ||
          !verifyExtFile.isValidExtensionFile(itemToUpload.originalname) ||
          !verifyExtFile.isValidExtensionFile(itemToUpload.blob) ||
          !verifyExtFile.isValidExtensionFile(itemToUpload.blobName)
        ) {
          console.log("Incorrect file type");
          return res.status(httpConstants.HTTP_STATUS_BAD_REQUEST).json({result: false, message: "Incorrect file type" });
        }
        uploadController.moveUploadFile(req.params.reportId, itemToUpload, 'request-information',
          process.env.AZURE_STORAGE_CONTAINER_FINANCIAL_REPORTS).catch((reason => {
          if (reason) {
            console.log(reason);
          }
        }));
        let newFile = {
          fileId: uuidv4(),
          fileTypeId: req.body.fileTypeId,
          fieldName: itemToUpload.fieldname.replace(/fileUploaded/i, 'request-information'),
          blob: itemToUpload.blob.replace(/fileUploaded/i, 'request-information'),
          blobName: itemToUpload.blobName.replace(/fileUploaded/i, 'request-information'),
          url: itemToUpload.url.replace(/fileUploaded/i, req.params.reportId + '/' + 'request-information'),
          originalName: itemToUpload.originalname,
          encoding: itemToUpload.encoding,
          mimeType: itemToUpload.mimetype,
          container: itemToUpload.container,
          blobType: itemToUpload.blobType,
          size: itemToUpload.size,
          etag: itemToUpload.etag
        };


        newFiles.push(newFile);
      }

      if (sessData.requestedTempFiles && sessData.requestedTempFiles.financialReport) {
        sessData.requestedTempFiles.financialReport = [...sessData.requestedTempFiles.financialReport, ...newFiles];
      } else {
        sessData.requestedTempFiles.financialReport = newFiles;
      }

    }

    return res.json({result: true});

  } catch (error) {
    console.log("error: ", error);
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
  }
};



exports.deleteFile = async function (req, res) {
  try {
    let fileDeleted = false;
    const companyRequest = await RequestedFileModel.findOne({_id: req.params.requestId, masterClientCode: req.params.masterclientcode });
    if (!companyRequest) {
      return res.json({status: httpConstants.HTTP_STATUS_NOT_FOUND, message: 'Company Request not found'});
    }

    if (companyRequest.files) {
      const fileTypeIndex = companyRequest.files.findIndex((f) => f.id === req.body.fileTypeId);
      if (fileTypeIndex > -1) {
        let files =  companyRequest.files[fileTypeIndex];
        const fileIndex = files.uploadFiles.findIndex((f) => f.fileId === req.body.fileId);
        if (fileIndex > -1) {
          files.uploadFiles.splice(fileIndex, 1);
          companyRequest.files[fileTypeIndex] = files;
          companyRequest.markModified('files');
          await companyRequest.save();
          fileDeleted = true;
        }

      }
    }

    if (!fileDeleted && req.session && req.session.requestedTempFiles){
      let tempFiles = req.session.requestedTempFiles[req.body.fileTypeId];
      if (tempFiles && tempFiles.length > 0) {
        const fileIndex = tempFiles.findIndex((f) => f.fileId && f.fileId.toString() === req.body.fileId);
        if (fileIndex > -1) {
          tempFiles.splice(fileIndex, 1);
          req.session.requestedTempFiles[req.body.fileTypeId] = tempFiles;
          fileDeleted = true;
        }
      }
    }

    if (fileDeleted){
      return res.json({status: httpConstants.HTTP_STATUS_OK, message: 'File deleted successfully'});
    }
    else{
      return res.json({status: httpConstants.HTTP_STATUS_BAD_REQUEST, message: 'Error deleting the file'});
    }

  } catch (error) {
    console.log("error: ", error);
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
  }
};

exports.deleteFileSubstance = async function (req, res) {
  try {
    const tempFiles = req.session.requestedTempFiles.substance;
    req.session.requestedTempFiles.substance = tempFiles.filter((f) => f.fileId !== req.params.fileId);

    return res.json({status: httpConstants.HTTP_STATUS_OK, message: 'File deleted successfully'});


  } catch (error) {
    console.log("error: ", error);
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
  }
};


exports.deleteFileFinancialReport = async function (req, res) {
  try {
    const tempFiles = req.session.requestedTempFiles.financialReport;
    req.session.requestedTempFiles.financialReport = tempFiles.filter((f) => f.fileId !== req.params.fileId);

    return res.json({status: httpConstants.HTTP_STATUS_OK, message: 'File deleted successfully'});


  } catch (error) {
    console.log("error: ", error);
    return res.status(httpConstants.HTTP_STATUS_INTERNAL_SERVER_ERROR).end();
  }
}

