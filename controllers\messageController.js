const MasterClientMessageModel = require('../models/master-client-message');
const MessageModel = require('../models/message');
const MasterClientCode = require('../models/masterClientCode');
const sessionUtils = require('../utils/sessionUtils');

exports.getMessages = async function (req, res, next) {
    try {
        let mcList = (await MasterClientCode.find({ 'owners': req.user.email.toLowerCase() }, ['code'])).map((mc) => mc.code);
        if (req.query.masterClientCode) {
            if (mcList.find((mc) => mc === req.query.masterClientCode)) {
                mcList = [req.query.masterClientCode];
            } else {
                throw new Error('Masterclient not found!')
            }
        }
        const messageList = await MasterClientMessageModel.aggregate([
            {
                '$match': { masterClientCode: { $in: mcList } }
            },
            {
                '$lookup': {
                    'from': 'messages',
                    'localField': 'messageId',
                    'foreignField': '_id',
                    'as': 'message'
                }
            },
            {
                '$unwind': '$message'
            },
            {
                '$project': {
                    '_id': 1,
                    'masterClientCode': 1,
                    'sentAt': 1,
                    'messageId': 1,
                    'openedAt': 1,
                    'subject': '$message.subject',
                    'emailSubject': '$message.emailSubject',
                }
            },
        ]);
        let results = [];
        for (const message of messageList) {
            const dupMessage = results.find((m) => m.messageId.toString() === message.messageId.toString());
            console.log(dupMessage);
            if (!dupMessage) {
                results.push({
                    ids: [message._id.toString()],
                    masterClients: [message.masterClientCode],
                    messageId: message.messageId.toString(),
                    subject: message.subject,
                    emailSubject: message.emailSubject ? message.emailSubject: message.subject,
                    opened: !!message.openedAt,
                    sentAt: message.sentAt
                })
            } else {
                results = results.map((m) => {
                    if (m.messageId.toString() === message.messageId.toString()) {
                        m.ids.push(message._id.toString())
                        m.masterClients.push(message.masterClientCode)
                        m.opened = m.opened || !!message.openedAt;
                    }
                    return m;
                })
            }
        }
        let messages = 0;
        for (const result of results) {
            const uniqueMasterClients = [...new Set(result.masterClients)];
            result.masterClientsText = uniqueMasterClients.join(', ');
            result.firstMasterClients = uniqueMasterClients.slice(0, 5).join(', ');
            result.masterClientAmount = uniqueMasterClients.length;
            if (!result.opened) {
                messages++;
            }
        }

        results = results.sort((a,b)=> b.sentAt -  a.sentAt);
        if (!req.query.masterClientCode) {
            req.session.messages = messages;
        }
        const title = req.query.masterClientCode ? `Messages for Master Client Code ${req.query.masterClientCode}` : 'Messages';
        res.render('messages/list', { title: title, user: req.user, messages: req.session.messages, results });
    } catch (e) {
        console.log(e);
        return next(e);
    }

};

exports.getMessageContent = async function (req, res, next) {
    try {
        
        const masterClientList = [];

        const message = await MessageModel.findById(req.params.id);
        if (!message) {
            return res.send({ success: false, error: "Message not found" });
        }

        const masterClientMessages = await MasterClientMessageModel.find({ _id: { $in: req.body.ids.split(',') } });
        for await(const masterClientMessage of masterClientMessages) {
            masterClientList.push(masterClientMessage.masterClientCode);
            if (!masterClientMessage.openedAt) {
                masterClientMessage.openedAt = new Date();
                masterClientMessage.openedBy = req.user.email;
                await masterClientMessage.save();
            }
        }

        // check if the user has access to read the message
        if (message.sendToAll === false) {
            const mccs = await MasterClientCode.find({ code: { $in: masterClientList }, owners: req.user.email }, {code: 1 });

            if (mccs.length === 0) {
                return res.send({ success: false, error: "Sorry, but you do not have permission to access and read this message" });
            } else {
                const existsValidMcc = mccs.some(mcc => message.masterClientCodes.includes(mcc.code));

                if (!existsValidMcc) {
                    return res.send({ success: false, error: "Sorry, but you do not have permission to access and read this message" });
                }
            }
        }

        res.send({ success: true, message });
    } catch (e) {
        console.log(e);
        return next(e);
    }
};

exports.ensureAuthenticated = function (req, res, next) {
    if ((req.user && req.session.id == req.user.sessionId) && req.session.auth2fa) {
        const sessData = req.session;
        //check if compancode in session is the same as the company code in the url
        if (!req.params.companyCode || req.params.companyCode == sessData.company.code) {
            next();
        } else {
            req.logout(function (err) {
                if (err) { return next(err) }
                req.session.destroy(function () {
                    sessionUtils.onSessionDestroyed(req, res);
                });
            });
        }
    } else if ((req.user && req.session.id == req.user.sessionId) && !req.session.auth2fa) {
        if (req.user.secret_2fa) {
            res.redirect('/users/2fa-code');
        } else {
            res.redirect('/users/2fa-setup');
        }
    } else {
        req.logout(function (err) {
            if (err) { return next(err) }
            req.session.destroy(function () {
                // cannot access session here
                sessionUtils.onSessionDestroyed(req, res);
            });
        });
    }
};
