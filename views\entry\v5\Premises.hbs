<form method="POST">
	<input type="text" hidden name="csrf-token" value="{{csrfToken}}">
	<input type="text" id="hasErrorVal" hidden readonly value="{{hasError}}">
	<div class="contour container-fluid mt-4">
		{{#if validationErrors }}
			{{#if hasError}}
				{{#each validationErrors }}
					{{renderValidationMessage this.msg this.field}}
				{{/each}}
			{{/if}}
			<br>
		{{/if}}

		<div class="card">
			<div class="card-body">
				
				<div class="row">
					<div class="col-md-8">
						<div class="form-group mb-3">
							<label class="form-label">Are there physical offices or premises in the Virgin Islands used in connection with the relevant activity?</label>
						</div>
					</div>
					<div class="col-md-4" align="right">
				
						<div class="radio form-check form-check-inline">
							<input type="radio" class="form-check-input" id="arePhysicalOfficesYes"
								{{#ifCond data.are_physical_offices '===' true}} checked {{/ifCond}}
								name="arePhysicalOffices" value="Yes">
							<label class="form-check-label" for="arePhysicalOfficesYes">Yes</label>
						</div>
						<div class="radio form-check form-check-inline">
							<input type="radio" class="form-check-input" id="arePhysicalOfficesNo"
								{{#ifCond data.are_physical_offices '===' false}} checked {{/ifCond}}
								name="arePhysicalOffices" value="No" >
							<label class="form-check-label" for="arePhysicalOfficesNo">No</label>
						</div>
				
					</div>
				</div>

				<div class="row ">
					<div class="col-md-4">
						<div class="form-group mb-3">
							<label class="mb-2" for="PremisesAddressLine1">Address line 1</label>
						</div>
					</div>
					<div class="col-md-8">
						<div class="form-group mb-3">
							<input type="text" name="PremisesAddressLine1" id="PremisesAddressLine1"
								class="form-control" label="PremisesAddressLine1" value="{{data.address_line1}}" />
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-md-4">
						<div class="form-group mb-3">
							<label class="mb-2" for="PremisesAddressLine2">Optional Address line 2</label>
						</div>
					</div>
					<div class="col-md-8">
						<div class="form-group mb-3">
							<input type="text" name="PremisesAddressLine2" id="PremisesAddressLine2"
								class="form-control" label="PremisesAddressLine2" value="{{data.address_line2}}" />
						</div>
					</div>
				</div>

				<div class="row">
					<div class="col-md-4">
						<div class="form-group mb-3">
							<label class="mb-2" for="PremisesCountry">Country</label>
						</div>
					</div>
					<div class="col-md-8">
						<div class="form-group mb-3">
							<input type="text" name="PremisesCountry" id="PremisesCountry" class="form-control"
								label="PremisesCountry" value="Virgin Islands, British"  readonly/>
						</div>
					</div>
				</div>

				<div class="clearfix text-right mt-3">
					<button type="submit" class="btn solid royal-blue"> <i class="mdi mdi-send mr-1"></i> Save</button>
				</div>
			</div> <!-- end card-body-->
		</div> <!-- end card-->
	</div>
</form>

<script type='text/javascript' src="/views-js/entry/v5/Premises.js"></script>