<div class="modal fade" id="newPropertyModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable " role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 id="modal-Property-title" class="modal-title">Property</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div id="modal-Property-body" class="modal-body p-3">
                <form action="" class="form" id="newPropertyForm no-border p-1">
                   
                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="propertyTypeControl">Property Type*</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group mb-3">
                                <select name="propertyType" id="propertyTypeControl"
                                    class="form-control w-100" data-toggle="select2" required>
                                    <option value="" hidden>Select an option</option>
                                    <option value="Aircraft">Aircraft</option>
                                    <option value="Building">Building</option>
                                    <option value="Equipment">Equipment</option>
                                    <option value="Land">Land</option>
                                    <option value="Motor Vehicle">Motor Vehicle</option>
                                    <option value="Vessel">Vessel</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row hide-element" id="otherPropertyTypeValueRow">
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="otherPropertyTypeValue">Please specific the type*</label>
                            </div>
                        </div>
                        <div class="col-md-4" >
                            <input type="text" name="otherPropertyTypeValue" id="otherPropertyTypeValue"
                                   class="form-control" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="propertyValue">Property Value*</label>
                            </div>
                        </div>
                        <div class="col-md-4">                            
                            <div class="form-group input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text prepend-currency"> {{report.currency}}</span>
                                </div>
                                <input type="text" id="propertyValue" class="form-control autonumber text-right" data-a-sep="," required
                                    data-m-dec="2" name="propertyValue">
                            </div>
                        </div>
                    </div>
                    
                </form>
            </div>
            <div class="modal-footer justify-content-end">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>

                <button class="btn solid royal-blue" type="button" id="submitPropertyBtn">Save</button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript" src="/views-js/partials/financial-reports/modals/create-property-modal.js">
</script>
