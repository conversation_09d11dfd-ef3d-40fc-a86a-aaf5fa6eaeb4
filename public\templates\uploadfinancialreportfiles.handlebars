{{#if files}}
<div class="table-responsive">
    <table class="table table-striped mb-0">
        <thead>
            <tr>
                <th>File name: {{title}}</th>
                <th class="header-20-percent">Download</th>
                <th class="header-150">Delete File</th>
            </tr>
        </thead>
        <tbody>
            {{#each files}}
            <tr>
                <td>
                    {{#if originalName}}
                    {{originalName}}
                    {{else}}
                    {{originalname}}
                    {{/if}}
                </td>
                <td>
                    <a type="button" class="btn btn btn-xs solid royal-blue downloadFinancialReportFile"
                        data-id="{{../id}}" data-group="{{../group}}" data-field="{{fileId}}">
                        Download</a>
                </td>
                <td>
                    <button class="demo-delete-row btn btn-danger btn-xs btn-icon deleteFinancialReportFile"
                        data-id="{{../id}}" data-group="{{../group}}" data-field="{{fileId}}"><i
                            class="fa fa-times"></i>
                    </button>
                </td>
            </tr>
            {{/each}}
        </tbody>
    </table>
</div>
{{/if}}

