{{#each outsourcingProviders}}
    <tr id="outsourcing-providers-table-row-{{_id}}" class="outsourcing-row">
        <td> {{entity_name}} </td>
        <td class="text-truncate header-max-width-150" > {{resource_details}} </td>
        <td> {{staff_count}} </td>
        <td> {{hours_per_month}} </td>
        <td>{{#if monitoring_control}} Yes {{else}} No {{/if}}</td>
        <td class="justify-content-center d-flex d-flex-inline">
            <button type="button" class="btn btn-sm royal-blue solid mr-1" data-activity-type="{{../activityType}}"
                data-id="{{../entryId}}" data-provider-id="{{_id}}" data-toggle="modal"
                data-target="#outsourcingProviderModal">
                <i class="fa fa-pencil"></i>
            </button>
            <button type="button" class="btn btn-sm btn-danger deleteOutsourcingProvider"
                    data-activity-type="{{../activityType}}"
                    data-entry-id="{{../entryId}}" data-id="{{_id}}">
                <i class="fa fa-times"></i>
            </button>
        </td>
    </tr>
{{else}}
    <tr>
        <td colspan="6">
            No outsourcing providers found
        </td>
    </tr>
{{/each}}


