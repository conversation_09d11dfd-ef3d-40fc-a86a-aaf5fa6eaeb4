const urlParts = [] = window.location.pathname.split('/')
const urlPart = urlParts[urlParts.length-1]

Dropzone.autoDiscover = false;
$(async function(){
    var field = '';
    var entry = '';
    const csrfToken = $("input[name='csrf-token']").val()
    var myDropZone = new Dropzone("#frmUploadModal", {
        url : "/",
        acceptedFiles: 'application/pdf',
        uploadMultiple : true,
        autoProcessQueue : true,
        parallelUploads: 3,
        maxFiles: 3,
        maxFilesize: 5,
        dictDefaultMessage: 'Drop image here (or click) to capture/upload',
        paramName: function() { return 'SubmitEvidence'; },
        headers: {
            'x-csrf-token': csrfToken
        },
        init : function(){
             this.on("processing", function(file) {
                  this.options.url = "/substance/entry/"+entry+"/upload-document";
            })
            this.on("success", function(){

            })
            this.on("sending", function(file, xhr, formData){
                if (!formData.has('filetype')) {
                    formData.append("filetype", field);
                }

            })

            this.on("errormultiple", function(files, response){
            })

            this.on("maxfilesexceeded", function(file){

            });

            this.on('resetFiles', function() {
                if(this.files.length != 0){
                    for(i=0; i<this.files.length; i++){
                        this.files[i].previewElement.remove();
                    }
                    this.files.length = 0;
                }
            });
        }
    })

    $('#upload-modal').on('show.bs.modal', function (event) {
        var button = $(event.relatedTarget) // Button that triggered the modal
        field = button.data('field') + urlPart;

        entry = button.data('entry');
        var modal = $(this);
        var objDZ = Dropzone.forElement("#frmUploadModal");
        objDZ.emit("resetFiles");
        $("#dz-message").show();

    });

     $('#upload-modal').on('hide.bs.modal', function (event) {
        refreshUploadedFiles();
    });
})

$(document).on('click', '.deleteFile', async function (event) {
    event.preventDefault()
    await deleteFile($(this).attr('data-field'), $(this).attr('data-field-id'))
})

async function deleteFile(field, id) {
    const entryId = window.location.pathname.split('/')[3]
    $.ajax({
        type: "DELETE",
        url: "/substance/entry/"+entryId+"/uploaded-file",
        data: {
            _id: id,
            field: field
        },
        success: function( data ) {
            if (data.result) {
                refreshUploadedFiles();
            }
        },
        dataType: "json"
    });
    return false;
}