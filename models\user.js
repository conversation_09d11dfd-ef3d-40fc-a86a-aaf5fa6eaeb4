const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const passwordHistorySchema = new mongoose.Schema({
  details: { type: [String], required: false, default: [] }
});


var userSchema = new mongoose.Schema({
    username: { type: String, required: true, unique: true },
    email: { type: String, required: true, unique: true },
    password: { type: String, required: false },
    passwordHistoy: { type: passwordHistorySchema, required: false },
    active: {type: Boolean, required: true },
    secret_2fa: {type: String, required: false},
    locked: {type: Boolean, required: false },
    resetPasswordToken: { type: String, required: false },
    resetPasswordExpires: { type: Date, required: false },
    sessionId: { type: String, required: false },
    mfa_type: { type: String, required: false },
    lastResetPasswordDate: { type: Date, required: false },
  });

 userSchema.methods.validPassword = function(password) {
    return bcrypt.compareSync(password, this.password);
};
  

  //Export model
module.exports = mongoose.model('User', userSchema);