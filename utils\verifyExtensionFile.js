const path = require('path');

//verify if the extension file is in the allowed extensions
exports.isValidExtensionFile = function(filename, allowedExtensions = ['.pdf']) {
    try{
        if(!filename){
            return false;
        }

        const fileExtension = path.extname(filename).toLowerCase();

        return allowedExtensions.includes(fileExtension);
    } catch (e){
        console.log(e);
        return false;
    }

}
