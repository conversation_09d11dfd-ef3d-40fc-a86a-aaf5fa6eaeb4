const ObjectId = require('mongoose').Types.ObjectId;
const { ACCOUNTING_SERVICE_TYPES_LABELS} = require('../utils/financialReportConstants');

function traverseSchema(schema, parentPath = '', document = {}) {
    const properties = [];



    schema.eachPath((path, schemaType) => {
        if (path === '_id' || schemaType instanceof ObjectId || document[path] === null || document[path] === "") {
            return;
        }

        const fullPath = parentPath ? `${parentPath}.${path}` : path;
        const order = schemaType.options.order || 0; // Use the specified order or default to 0
        const hideFromDisplay = schemaType.options.hideFromDisplay || false
        const isCalculated = schemaType.options.isCalculated || false

        if (schemaType.schema ) {

            // It's a sub-schema, so recursively traverse it
            const subProperties = traverseSchema(schemaType.schema, fullPath, document[path] || {});
            properties.push(...subProperties);
        } else if (schemaType.options.isArray === true) {

            if (Array.isArray(document[path])) {
                const arrayProperties = [];

                document[path].forEach((arrayElement, index) => {
                    const arrayElementFullPath = `${fullPath}.${index}`;
                    const arrayElementOrder = schemaType.options.order || 0;
                    let subProperties = traverseSchema(schemaType.options.childType, arrayElementFullPath, arrayElement);

                    subProperties = subProperties.map(prop => ({
                        ...prop,
                        order: arrayElementOrder + prop.order,
                        label: prop.label + ` (${index + 1})`
                    }))

                    arrayProperties.push(subProperties);
                });

                properties.push({
                    path: fullPath,
                    label: schemaType.options.label || path,
                    value: arrayProperties, // Use the value from the document
                    order: order,
                    isArray: schemaType.options.isArray,
                    hideFromDisplay: hideFromDisplay,
                    isCalculated: isCalculated
                })
            }
        }
        else {
            // It's a leaf node, add the property with value and order

            const keys = path.split('.');

            let resultValue = document;
            for (const key of keys) {
                resultValue = resultValue[key];
            }

            const documentValue = schemaType.options.get ? schemaType.options.get(resultValue) : resultValue;
    
            properties.push({
                path: fullPath,
                label: schemaType.options.label || path,
                value: documentValue, // Use the value from the document
                order: order,
                hideFromDisplay: hideFromDisplay,
                isCalculated: isCalculated
            });
        }
    });



    // Sort the properties based on the order
    properties.sort((a, b) => a.order - b.order);

    return properties;
}


function getBooleanFormat(value){
    return value === true ? "Yes" : value === false ? "No" : "";
}

function getServiceTypeFormat(value){
    return ACCOUNTING_SERVICE_TYPES_LABELS[value];
}

module.exports = {
    traverseSchema,
    getBooleanFormat,
    getServiceTypeFormat
};