<main class="">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="card-box">
                    <h4 class="mt-3">Overview of all companies related to Master Client Code :
                        <B>{{masterclientcode}}</B>
                    </h4>
                    <br>
                    <form method='GET'>
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label for="filter_company">Company Name</label>
                                <input class='form-control' type='text' name='filter_company' id='filter_company' />
                            </div>
                            <div class="col-md-3">
                                <label for="filter_company_code">Incorporation Code</label>
                                <input class='form-control' type='text' name='filter_company_code'
                                    id='filter_company_code' />
                            </div>
                            <div class="col-md-3  input-div">
                                <input type='SUBMIT' class='btn btn-light waves-effect' value='Search' />
                            </div>
                        </div>
                    </form>
                    <div class="table-responsive">
                        <table class="table table-striped mb-0">
                            <thead>
                                <tr>
                                    <th class="header-30-percent">Entity Name</th>
                                    <th class="header-20-percent">Incorporation Code </th>
                                    <th class="text-center header-10-percent">Information Request</th>
                                    <th class="header-20-percent"></th>
                                    <th class="header-20-percent"></th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each companies}}

                                <tr>
                                    <td>{{name}}</td>
                                    <td>{{incorporationcode}} </td>
                                    <td class="text-center">{{#if informationRequestSubmission}}
                                        <a href="/masterclients/{{../masterclientcode}}/company-files" title="Information request"
                                            data-toggle="tooltip" data-placement="top">
                                            <span class="fa-stack mr-2">
                                                <i class="fa fa-info-circle fa-stack-2x blue-icon"></i>
                                            </span>
                                        </a>
                                        {{/if}}
                                    </td>
                                    <td>
                                        {{#if hasSubmissions}}
                                            <a href="/masterclients/{{masterclientcode}}/substance/companies/select?companyCode={{code}}"
                                                class="btn solid royal-blue width-xl submission-btn"
                                               title="Click here for a submission that is currently in process"
                                               data-toggle="tooltip" data-placement="top">
                                                Retrieve submissions
                                            </a>
                                        {{/if}}
                                    </td>
                                    <td>
                                        {{#if inProgressSubmission}}
                                            {{#ifEquals inProgressSubmission.status 'RE-OPEN'}}
                                                <button data-code="{{code}}" data-id="{{inProgressSubmission._id}}" data-reopened-id="{{inProgressSubmission.reopenedId}}"
                                                        class="btn solid royal-blue width-xl submission-btn showReopenContinueModal">
                                                    Continue Draft
                                                </button>
                                            {{else}}
                                                <button data-code="{{code}}" data-id="{{inProgressSubmission._id}}"
                                                        class="btn solid royal-blue width-xl submission-btn openSubmission">Continue Draft</button>
                                            {{/ifEquals}}
                                        {{else}}
                                            {{#if scheduledSubmission}}
                                                <div data-toggle="tooltip" placement="top" class="tooltip-wrapper"
                                                    title="You have a submission scheduled for {{formatDate scheduledSubmission.scheduled_at 'YYYY-MM-DD'}}. Once that submission is sent in, you are able to start a new one.">
                                                    <button class="btn solid royal-blue width-xl submission-btn" disabled>New
                                                        submission</button>
                                                </div>
                                            {{else}}
                                                <button data-code="{{code}}"
                                                    class="btn solid royal-blue width-xl submission-btn CreateSubmission" data-toggle="tooltip"
                                                    placement="top" title="Click here to present a new submission">New
                                                    submission</button>
                                            {{/if}}
                                        {{/if}}
                                    </td>
                                </tr>
                                {{/each}}
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </tbody>
                        </table>
                    </div> <!-- end .padding -->
                    <br>
                    <a href="/masterclients/{{masterclientcode}}"
                        class="btn btn-secondary waves-effect waves-light width-xl">Back</a>&nbsp;&nbsp;<a
                        href="/masterclients/{{masterclientcode}}/payments"
                        class="btn btn-secondary waves-effect waves-light width-xl">Payments</a></td>
                </div> <!-- end card-box-->
            </div> <!-- end col -->
        </div> <!-- end table-responsive-->
    </div> <!-- end card-box -->
</main>

<script type="text/javascript" src="/views-js/substance/companies.js"></script>
