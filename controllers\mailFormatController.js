const fs = require('fs');

exports.generateIdPalEmail = function (url) {
  let htmlString = fs
    .readFileSync('notification-templates/id-pal-notification-email.html', { encoding: 'utf-8' })
    .replace('##URL##', url);


  let textString = "Hello, \n" +
    "You are receiving this email as part of the Trident Trust Electronic ID verification system powered by ID-Pal." +
    " You can learn more about ID-Pal’s key features here.\n" +
    "Please follow the steps as shown below:\n" +
    " 1. Click on the link below to open the ID-Pal app.\n" +
    url + "\n" +
    " 2. Use the ID-Pal app to capture your ID information. You will need to have your passport and/or driver license available.\n" +
    " 3. Verify your information and submit. Trident Trust will receive a client due diligence report upon completion.\n" +
    "For any questions or assistance, please contact <NAME_EMAIL>";
  return { textString, htmlString };
};

exports.generateClientProvidedInformationEmail = function (mcc) {
  let htmlString = fs
    .readFileSync('notification-templates/client-portal-provided-information-email.html', { encoding: 'utf-8' })
    .replace('##MCC##', mcc);

  let textString =
    'Dear Trident Officer, \n\n' +
    'A client with MCC '+ mcc+' has submitted information regarding a new incorporation request. \n' +
    'Please login to the management portal to review the message.\n\n' +
    'Kind regards';
  return { textString, htmlString };
};

exports.generateClientSubmittedAttachmentsEmail = function (companyName) {
  let htmlString = fs
    .readFileSync('notification-templates/generate-client-submitted-attachments-email.html', { encoding: 'utf-8' })
    .replace('##COMPANY_NAME##', companyName);

  let textString =
    'Dear Trident Officer, \n\n' +
    'A client with the company name '+ companyName+' has submitted economic substance supporting information attachments.\n' +
    'Please login to the management portal to review the submitted information.\n\n' +
    'Kind regards';
  return { textString, htmlString };
};

exports.generateProvidedRequestedFilesEmail = function (companyName) {
  let htmlString = fs
    .readFileSync('notification-templates/client-portal-submit-requested-information-email.html', { encoding: 'utf-8' })
    .replace('##COMPANY_NAME##', companyName);

  let textString =
    'Dear Trident Officer, \n\n' +
    'A client with the company name '+ companyName+' has submitted information that was requested by the file reviewer. \n' +
    'Please login to the management portal to review the submitted information.\n\n' +
    'Kind regards';
  return { textString, htmlString };
};

exports.generateProvidedRequestedFilesSubstanceEmail = function (companyName, companyCode) {
    let htmlString = fs
      .readFileSync('notification-templates/client-portal-submit-requested-information-substance-email.html', { encoding: 'utf-8' })
      .replace('##COMPANY_NAME##', companyName)
      .replace('##COMPANY_CODE##', companyCode);

    let textString =
      'Dear TBVI ES Team: \n\n' +
      'Request for Information – Completed\n' +
      `[${companyCode} - ${companyName}] - the "Entity"\n` +
      'The request for information for the Entity has been completed and resubmitted for processing. \n' +
      'Please access the portal to export and upload the client’s response to the ITA. ';
    return { textString, htmlString };
};

exports.generateProvidedRequestedFilesAccountingRecordsEmail = function (companyName, companyCode) {
  let htmlString = fs
    .readFileSync('notification-templates/client-portal-submit-requested-information-ar-email.html', { encoding: 'utf-8' })
    .replace('##COMPANY_NAME##', companyName)
    .replace('##COMPANY_CODE##', companyCode);

  let textString =
    'Dear AR Team member: \n\n' +
    'Request for Information – Completed\n' +
    `[${companyCode} - ${companyName}] - the "Entity"\n` +
    'The request for information in regards to the AFR submission for the Entity has been completed and submitted for processing. \n' +
    'Please access the portal to determine the next actions. \n\n' +
    'Kind regards';
  return { textString, htmlString };
};

exports.generateDirBoRequestUpdateEmail = function (data) {
  let htmlString = fs
    .readFileSync('notification-templates/dir-bo-request-update-email.html', { encoding: 'utf-8' })
    .replace('##COMPANY_NAME##', data.companyName)
    .replace('##COMPANY_CODE##', data.companyCode)
    .replace('##MCC##', data.mcc)
    .replace("##DIRECTOR_CODE##", data.directorCode)
    .replace('##REQUESTOR##', data.requestor)
    .replace('##REQUEST_TYPE##', data.requestType)
    .replace('##COMMENT##', data.comment);


  let textString = "Entity Name: " + data.companyName + "\n" +
    "Entity Code: " + data.companyCode + "\n" +
    "Master Client Code: " + data.mcc + "\n" +
    "VP BO/DIR Masterfile Code: " + data.directorCode + "\n"
    "Requestor: " + data.requestor + "\n" +
    "Type of Request: " + data.requestType + "\n" +
    "Comment: " + data.comment;
  return { textString, htmlString };
};

exports.generateDirRequestUpdateEmail = function (data) {
  let htmlString = fs
    .readFileSync('notification-templates/dir-mem-request-update-email.html', { encoding: 'utf-8' })
    .replace('##COMPANY_NAME##', data.companyName)
    .replace('##COMPANY_CODE##', data.companyCode)
    .replace('##MCC##', data.mcc)
    .replace("##DIRECTOR_CODE##", data.directorCode)
    .replace('##REQUESTOR##', data.requestor)
    .replace('##REQUEST_TYPE##', data.requestType)
    .replace('##COMMENT##', data.comment)
    .replace('##POSITION##', data.position)
    .replace('##RELATION_TYPE##', data.relationType)
    .replace('##LICENSED_DIRECTOR##', data.licensedDirector)
    .replace('##MISSING_INFORMATION##', data.missingInformation);

  let textString = "Entity Name: " + data.companyName + "\n" +
    "Entity Code: " + data.companyCode + "\n" +
    "Master Client Code: " + data.mcc + "\n" +
    "VP Director Masterfile Code: " + data.directorCode + "\n" +
    "Position: " + data.position + "\n" +
    "Relation Type: " + data.relationType + "\n" +
    "Licensed Director: " + data.licensedDirector + "\n" +
    "Requestor: " + data.requestor + "\n" +
    "Type of Request: " + data.requestType + "\n" +
    "Missing Information: " + data.missingInformation + "\n" +
    "Comment: " + data.comment;
  return { textString, htmlString };
};

exports.generateMemberRequestUpdateEmail = function (data) {
  let htmlString = fs
    .readFileSync('notification-templates/member-request-update-email.html', { encoding: 'utf-8' })
    .replace('##COMPANY_NAME##', data.companyName)
    .replace('##COMPANY_CODE##', data.companyCode)
    .replace("##MEMBER_CODE##", data.memberCode)
    .replace('##MCC##', data.mcc)
    .replace('##REQUESTOR##', data.requestor)
    .replace('##REQUEST_TYPE##', data.requestType)
    .replace('##COMMENT##', data.comment)
    .replace('##POSITION##', data.position)
    .replace('##NOMINEE_ARRANGEMENT##', data.nomineeArrangement)
    .replace('##RELATION_TYPE##', data.relationType)
    .replace('##MISSING_INFORMATION##', data.missingInformation);

  let textString = "Entity Name: " + data.companyName + "\n" +
    "Entity Code: " + data.companyCode + "\n" +
    "Master Client Code: " + data.mcc + "\n" +
    "VP Member Masterfile Code: " + data.memberCode + "\n" +
    "Position: " + data.position + "\n" +
    "Nominee Arrangement: " + data.nomineeArrangement + "\n" +
    "Relation Type: " + data.relationType + "\n" +
    "Requestor: " + data.requestor + "\n" +
    "Type of Request: " + data.requestType + "\n" +
    "Missing Information: " + data.missingInformation + "\n" +
    "Comment: " + data.comment;
  return { textString, htmlString };
};

exports.generateStockOrFundRequestUpdateEmail = function (data) {
  let htmlString = fs
    .readFileSync('notification-templates/stock-or-fund-request-update-email.html', { encoding: 'utf-8' })
    .replace('##COMPANY_NAME##', data.companyName)
    .replace('##COMPANY_CODE##', data.companyCode)
    .replace('##MCC##', data.mcc)
    .replace('##REQUESTOR##', data.requestor)
    .replace('##REQUEST_TYPE##', data.requestType)
    .replace('##COMMENT##', data.comment)
    .replace('##RELATION_TYPE##', data.relationType)
    .replace('##MISSING_INFORMATION##', data.missingInformation);

  let textString = "Entity Name: " + data.companyName + "\n" +
    "Entity Code: " + data.companyCode + "\n" +
    "Master Client Code: " + data.mcc + "\n" +
    "Relation Type: " + data.relationType + "\n" +
    "Requestor: " + data.requestor + "\n" +
    "Type of Request: " + data.requestType + "\n" +
    "Missing Information: " + data.missingInformation + "\n" +
    "Comment: " + data.comment;
  return { textString, htmlString };
};

exports.generateDirMemRequestAssistanceEmail = function (data) {
    let htmlString = fs
      .readFileSync('notification-templates/dir-mem-request-assistance-email.html', { encoding: 'utf-8' })
      .replace('##COMPANY_NAME##', data.companyName)
      .replace('##COMPANY_CODE##', data.companyCode)
      .replace('##MCC##', data.mcc)
      .replace('##REQUESTOR##', data.requestor)
      .replace('##REQUEST_TYPE##', data.requestType);
  
    let textString = "Entity Name: " + data.companyName + "\n" +
      "Entity Code: " + data.companyCode + "\n" +
      "Master Client Code: " + data.mcc + "\n" +
      "Requestor: " + data.requestor + "\n" +
      "Type of Request: " + data.requestType;
    return { textString, htmlString };
  };
  

exports.generateReOpenSubmittedEmail = function (companyName) {
  let htmlString = fs
    .readFileSync('notification-templates/client-portal-submit-re-open-information-email.html', { encoding: 'utf-8' })
    .replace('##COMPANY_NAME##', companyName);

  let textString =
    `Dear TBVI ES Team:\n\n
    Completed Amendment\n\n
    [${companyName}] the "Entity"\n\n
    The client has resubmitted the re-opened ES application for processing. \n
    Please access to the portal, review the amended submission and/or upload the changes to the ITA.\n\n
    Kind regards\n `;
  return { textString, htmlString };
};
