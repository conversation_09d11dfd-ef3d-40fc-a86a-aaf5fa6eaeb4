<div id="boardMeetingModal" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="entitParentModalLbl"
    aria-hidden="true">
    <div class="modal-dialog modal-lg contour container-fluid">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="boardMeetingModalLbl" >Add Board Meeting</h4>
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
            </div>
            <div class="modal-body">
                <form class="form no-border p-1" novalidate id="boardMeetingForm">
                    <input hidden type="text" name="activityType" id="activityType" value="" /> 
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="meetingNumber" class="form-label">
                                Meeting #:
                            </label>
                        </div>

                        <div class="col-md-6">
                            <input type="text" id="meetingNumber" class="form-control autonumber" data-firstclickvalueifempty="0" 
                                data-decimals="0" data-toggle="touchspin" data-min="0" data-max="1000000000" 
                                name="highRiskGrossIncomeRoyalties" value="{{data.meetingNumber}}" placeholder="0"  required/>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label for="meetingDirectorName"
                                class="form-label">Name:</label>
                        </div>
                        <div class="col-md-6">
                            <input type="text" name="meetingDirectorName" id="meetingDirectorName"
                                class="form-control" maxlength="150" value="" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <div class="form-group mb-3">
                                <label class="mb-2" for="meetingPhysicallyPresent">Physically present?</label>
                            </div>
                        </div>
                        <div class="col-md-4" align="right">
                            <div class="radio form-check form-check-inline">
                                <input type="radio" class="form-check-input" id="meetingPhysicallyPresentYes" name="meetingPhysicallyPresent"
                                    value="Yes" required>
                                <label class="form-check-label" for="meetingPhysicallyPresentYes">Yes</label>
                            </div>
                            <div class="radio form-check form-check-inline">
                                <input type="radio" class="form-check-input" id="meetingPhysicallyPresentNo" name="meetingPhysicallyPresent"
                                    value="No" required>
                                <label class="form-check-label" for="meetingPhysicallyPresentNo">No</label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label id="meetingRelationToEntityLbl" for="meetingRelationToEntity" class="form-label">
                                Relation to Entity:
                            </label>
                        </div>
                        <div class="col-md-6">
                            <input type="text" name="meetingRelationToEntity" id="meetingRelationToEntity" required
                                class="form-control" maxlength="100" value="" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <label id="meetingQualificationLbl" for="meetingQualification" class="form-label">
                                Qualification:
                            </label>
                        </div>
                        <div class="col-md-6">
                            <input type="text" name="meetingQualification" id="meetingQualification" required
                                class="form-control" maxlength="100" value="" />
                        </div>
                    </div>

                </form>

            </div>

            <div class="modal-footer justify-content-end">
                <button type="button" class="btn btn-outline-secondary" data-dismiss="modal">
                    Close
                </button>

                <button id="loadingBoardMeeting" class="btn btn-primary" type="button" disabled>
                    <span class="spinner-border spinner-border-sm" aria-hidden="true"></span>
                    Loading...
                </button>
                <button class="btn solid royal-blue" type="submit" form="boardMeetingForm"
                    id="submitBoardMeeting">Save</button>
            </div>

        </div>
    </div>
</div>


<script type='text/javascript' src='/templates/substance/boardmeetings.precompiled.js'></script>
<script type='text/javascript' src="/views-js/partials/substance/modals/board-meeting-modal.js"></script>
